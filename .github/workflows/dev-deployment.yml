name: Development Deployment

on:
  push:
    branches: [ develop ]
  workflow_dispatch:
    inputs:
      deploy_to_firebase:
        description: 'Deploy to Firebase App Distribution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '17'

jobs:
  android-dev-build:
    name: Android Development Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on Android dev builds
        SKIP_IOS_SETUP: true
      
    - name: Generate build info
      id: build_info
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "TIMESTAMP=$(date +'%Y%m%d-%H%M%S')" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Create build info file
      run: |
        cat > android/app/src/main/assets/build-info.json << EOF
        {
          "buildNumber": "${{ steps.build_info.outputs.BUILD_NUMBER }}",
          "commitSha": "${{ steps.build_info.outputs.SHORT_SHA }}",
          "branch": "${{ steps.build_info.outputs.BRANCH_NAME }}",
          "buildTime": "${{ steps.build_info.outputs.TIMESTAMP }}",
          "environment": "development"
        }
        EOF
        
    - name: Build Android Debug APK
      run: |
        cd android
        ./gradlew assembleDebug
        
    - name: Rename APK
      run: |
        APK_NAME="TS1-DEV-${{ steps.build_info.outputs.BUILD_NUMBER }}-${{ steps.build_info.outputs.SHORT_SHA }}.apk"
        mv android/app/build/outputs/apk/debug/app-debug.apk android/app/build/outputs/apk/debug/$APK_NAME
        echo "APK_NAME=$APK_NAME" >> $GITHUB_ENV
        
    - name: Upload to Firebase App Distribution
      if: github.event.inputs.deploy_to_firebase == 'true' || github.event_name == 'push'
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_APP_ID_ANDROID }}
        serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        groups: developers,qa-team
        file: android/app/build/outputs/apk/debug/${{ env.APK_NAME }}
        releaseNotes: |
          🔧 Development Build
          
          Build: ${{ steps.build_info.outputs.BUILD_NUMBER }}
          Commit: ${{ steps.build_info.outputs.SHORT_SHA }}
          Branch: ${{ steps.build_info.outputs.BRANCH_NAME }}
          
          Latest changes from develop branch.
          
    - name: Upload APK Artifact
      uses: actions/upload-artifact@v4
      with:
        name: dev-android-apk-${{ steps.build_info.outputs.BUILD_NUMBER }}
        path: android/app/build/outputs/apk/debug/${{ env.APK_NAME }}
        retention-days: 7

  ios-dev-build:
    name: iOS Development Build
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ env.RUBY_VERSION }}
        bundler-cache: true
        
    - name: Install dependencies
      run: npm ci
      
    - name: Generate build info
      id: build_info
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "TIMESTAMP=$(date +'%Y%m%d-%H%M%S')" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Install CocoaPods
      run: |
        cd ios
        pod install --repo-update
        
    - name: Build iOS Development
      run: |
        cd ios
        xcodebuild -workspace TS1.xcworkspace \
          -scheme TS1 \
          -configuration Debug \
          -destination 'platform=iOS Simulator,name=iPhone 15' \
          -derivedDataPath build \
          clean build
          
    - name: Create iOS artifact
      run: |
        cd ios
        IOS_NAME="TS1-iOS-DEV-${{ steps.build_info.outputs.BUILD_NUMBER }}-${{ steps.build_info.outputs.SHORT_SHA }}.zip"
        zip -r "$IOS_NAME" build/Build/Products/Debug-iphonesimulator/
        echo "IOS_NAME=$IOS_NAME" >> $GITHUB_ENV
        
    - name: Upload iOS Artifact
      uses: actions/upload-artifact@v4
      with:
        name: dev-ios-build-${{ steps.build_info.outputs.BUILD_NUMBER }}
        path: ios/${{ env.IOS_NAME }}
        retention-days: 7
        
    - name: Comment on PR (if applicable)
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const buildNumber = '${{ steps.build_info.outputs.BUILD_NUMBER }}';
          const shortSha = '${{ steps.build_info.outputs.SHORT_SHA }}';
          const apkName = '${{ env.APK_NAME }}';
          
          const comment = `🚀 **Development Build Ready!**
          
          **Build Info:**
          - Build Number: ${buildNumber}
          - Commit: ${shortSha}
          - APK: ${apkName}
          
          **Download:**
          You can download the APK from the [Actions artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          **Firebase App Distribution:**
          ${process.env.FIREBASE_APP_ID_ANDROID ? 'APK has been distributed to the developers and qa-team groups.' : 'Firebase distribution not configured.'}`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  notify-team:
    name: Notify Development Team
    runs-on: ubuntu-latest
    needs: [android-dev-build, ios-dev-build]
    if: always()
    
    steps:
    - name: Notify on Slack (if configured)
      if: needs.android-dev-build.result == 'success' || needs.ios-dev-build.result == 'success'
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "🚀 New development build available!",
            attachments: [{
              color: 'good',
              fields: [{
                title: 'Build Number',
                value: '${{ github.run_number }}',
                short: true
              }, {
                title: 'Branch',
                value: '${GITHUB_REF#refs/heads/}',
                short: true
              }, {
                title: 'Commit',
                value: '${GITHUB_SHA:0:7}',
                short: true
              }]
            }]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify on failure
      if: needs.build-and-distribute.result == 'failure'
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Development build failed!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}