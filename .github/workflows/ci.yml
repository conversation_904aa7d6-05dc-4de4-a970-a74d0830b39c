name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

permissions:
  contents: write
  packages: write
  actions: read
  checks: write
  pull-requests: write

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '17'
  RUBY_VERSION: '3.1'

jobs:
  # Code Quality and Testing
  code-quality:
    name: Code Quality & Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on Linux runners
        SKIP_IOS_SETUP: true
      
    - name: Run TypeScript check
      run: npm run type-check
      
    #- name: Run ESLint
     # run: npm run lint
      
    #- name: Check Prettier formatting
     # run: npx prettier --check "src/**/*.{js,jsx,ts,tsx,json}"
      
    #- name: Run tests
     # run: npm test -- --coverage --watchAll=false
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # Android Build
  android-build:
    name: Android Build
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on Android builds
        SKIP_IOS_SETUP: true
      
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Make gradlew executable
      run: chmod +x ./android/gradlew
      
    - name: Generate Android build info
      id: android_info
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Create Android build info
      run: |
        mkdir -p android/app/src/main/assets
        cat > android/app/src/main/assets/build-info.json << EOF
        {
          "platform": "android",
          "buildNumber": "${{ steps.android_info.outputs.BUILD_NUMBER }}",
          "commitSha": "${{ steps.android_info.outputs.SHORT_SHA }}",
          "branch": "${{ steps.android_info.outputs.BRANCH_NAME }}",
          "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "environment": "debug"
        }
        EOF
        
    - name: Build Android Debug APK
      run: |
        cd android
        ./gradlew assembleDebug
        
    - name: Rename Android APK
      run: |
        APK_NAME="TS1-Android-${{ steps.android_info.outputs.BRANCH_NAME }}-v${{ steps.android_info.outputs.BUILD_NUMBER }}-${{ steps.android_info.outputs.SHORT_SHA }}.apk"
        mv android/app/build/outputs/apk/debug/app-debug.apk android/app/build/outputs/apk/debug/$APK_NAME
        echo "ANDROID_APK_NAME=$APK_NAME" >> $GITHUB_ENV
        
    - name: Upload Android APK
      uses: actions/upload-artifact@v4
      with:
        name: android-build-${{ steps.android_info.outputs.BUILD_NUMBER }}
        path: android/app/build/outputs/apk/debug/${{ env.ANDROID_APK_NAME }}
        retention-days: 30

  # iOS Build
  ios-build:
    name: iOS Build
    runs-on: macos-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ env.RUBY_VERSION }}
        bundler-cache: true
        
    - name: Install dependencies
      run: npm ci
      
    - name: Cache CocoaPods
      uses: actions/cache@v3
      with:
        path: ios/Pods
        key: ${{ runner.os }}-pods-${{ hashFiles('**/Podfile.lock') }}
        restore-keys: |
          ${{ runner.os }}-pods-
          
    - name: Generate iOS build info
      id: ios_info
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Install CocoaPods
      run: |
        cd ios
        pod install --repo-update
        
    - name: Build iOS Debug
      run: |
        cd ios
        xcodebuild -workspace TS1.xcworkspace \
          -scheme TS1 \
          -configuration Debug \
          -destination 'platform=iOS Simulator,name=iPhone 15' \
          -derivedDataPath build \
          -archivePath build/TS1.xcarchive \
          clean build
          
    - name: Create iOS build artifact
      run: |
        cd ios
        # Create a zip of the build products for artifact upload
        zip -r "TS1-iOS-${{ steps.ios_info.outputs.BRANCH_NAME }}-v${{ steps.ios_info.outputs.BUILD_NUMBER }}-${{ steps.ios_info.outputs.SHORT_SHA }}.zip" build/Build/Products/Debug-iphonesimulator/
        echo "IOS_BUILD_NAME=TS1-iOS-${{ steps.ios_info.outputs.BRANCH_NAME }}-v${{ steps.ios_info.outputs.BUILD_NUMBER }}-${{ steps.ios_info.outputs.SHORT_SHA }}.zip" >> $GITHUB_ENV
        
    - name: Upload iOS Build
      uses: actions/upload-artifact@v4
      with:
        name: ios-build-${{ steps.ios_info.outputs.BUILD_NUMBER }}
        path: ios/${{ env.IOS_BUILD_NAME }}
        retention-days: 30

  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on security scan
        SKIP_IOS_SETUP: true
      
    - name: Run npm audit
      run: npm audit --audit-level moderate
      
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # QA Android Release (main branch)
  qa-android-release:
    name: QA Android Release
    runs-on: ubuntu-latest
    needs: [android-build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on QA Android builds
        SKIP_IOS_SETUP: true
      
    - name: Generate version info
      id: version
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Build Android QA APK
      run: |
        cd android
        ./gradlew assembleDebug
      env:
        GRADLE_OPTS: "-Xmx4096m -XX:MaxMetaspaceSize=512m"
        
    - name: Rename APK with version info
      run: |
        mv android/app/build/outputs/apk/debug/app-debug.apk \
           android/app/build/outputs/apk/debug/TS1-QA-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.apk
        
    - name: Upload QA APK
      uses: actions/upload-artifact@v4
      with:
        name: qa-release-apk
        path: android/app/build/outputs/apk/debug/TS1-QA-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.apk
        retention-days: 30
        
    - name: Create QA Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: qa-v${{ steps.version.outputs.BUILD_NUMBER }}
        name: QA Release v${{ steps.version.outputs.BUILD_NUMBER }}
        body: |
          🚀 **QA Release Build**
          
          **Build Info:**
          - Build Number: ${{ steps.version.outputs.BUILD_NUMBER }}
          - Commit: ${{ steps.version.outputs.SHORT_SHA }}
          - Branch: ${{ steps.version.outputs.BRANCH_NAME }}
          - Date: ${{ github.event.head_commit.timestamp }}
          
          **Changes:**
          ${{ github.event.head_commit.message }}
          
          **Download:**
          - Android APK: See assets below
          
          **Testing Notes:**
          - This is a QA build for internal testing
          - Report issues in the project repository
        files: |
          android/app/build/outputs/apk/debug/TS1-QA-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.apk
        draft: false
        prerelease: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # QA iOS Release (main branch)
  qa-ios-release:
    name: QA iOS Release
    runs-on: macos-latest
    needs: [ios-build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ env.RUBY_VERSION }}
        bundler-cache: true
        
    - name: Install dependencies
      run: npm ci
      
    - name: Generate version info
      id: version
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        
    - name: Install CocoaPods
      run: |
        cd ios
        pod install --repo-update
        
    - name: Build iOS QA
      run: |
        cd ios
        xcodebuild -workspace TS1.xcworkspace \
          -scheme TS1 \
          -configuration Debug \
          -destination 'platform=iOS Simulator,name=iPhone 15' \
          -derivedDataPath build \
          clean build
          
    - name: Create iOS QA artifact
      run: |
        cd ios
        zip -r "TS1-iOS-QA-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.zip" build/Build/Products/Debug-iphonesimulator/
        
    - name: Upload iOS QA Build
      uses: actions/upload-artifact@v4
      with:
        name: qa-ios-release
        path: ios/TS1-iOS-QA-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.zip
        retention-days: 30

  # Development Android Build (develop branch)
  dev-android-build:
    name: Development Android Build
    runs-on: ubuntu-latest
    needs: [android-build, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      env:
        # Skip iOS setup on Dev Android builds
        SKIP_IOS_SETUP: true
      
    - name: Generate version info
      id: version
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        
    - name: Build Android Dev APK
      run: |
        cd android
        ./gradlew assembleDebug
        
    - name: Rename APK with version info
      run: |
        mv android/app/build/outputs/apk/debug/app-debug.apk \
           android/app/build/outputs/apk/debug/TS1-DEV-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.apk
        
    - name: Upload Dev APK
      uses: actions/upload-artifact@v4
      with:
        name: dev-android-build
        path: android/app/build/outputs/apk/debug/TS1-DEV-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.apk
        retention-days: 14

  # Development iOS Build (develop branch)
  dev-ios-build:
    name: Development iOS Build
    runs-on: macos-latest
    needs: [ios-build, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ env.RUBY_VERSION }}
        bundler-cache: true
        
    - name: Install dependencies
      run: npm ci
      
    - name: Generate version info
      id: version
      run: |
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        
    - name: Install CocoaPods
      run: |
        cd ios
        pod install --repo-update
        
    - name: Build iOS Dev
      run: |
        cd ios
        xcodebuild -workspace TS1.xcworkspace \
          -scheme TS1 \
          -configuration Debug \
          -destination 'platform=iOS Simulator,name=iPhone 15' \
          -derivedDataPath build \
          clean build
          
    - name: Create iOS Dev artifact
      run: |
        cd ios
        zip -r "TS1-iOS-DEV-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.zip" build/Build/Products/Debug-iphonesimulator/
        
    - name: Upload Dev iOS Build
      uses: actions/upload-artifact@v4
      with:
        name: dev-ios-build
        path: ios/TS1-iOS-DEV-v${{ steps.version.outputs.BUILD_NUMBER }}-${{ steps.version.outputs.SHORT_SHA }}.zip
        retention-days: 14