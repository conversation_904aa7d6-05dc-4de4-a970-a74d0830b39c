name: QA Release Distribution

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release Type'
        required: true
        default: 'qa'
        type: choice
        options:
        - qa
        - staging
        - hotfix

permissions:
  contents: write
  packages: write
  actions: read

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '17'
  RUBY_VERSION: '3.1'

jobs:
  qa-android-build:
    name: QA Android Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      
    - name: Generate release info
      id: release_info
      run: |
        RELEASE_TYPE="${{ github.event.inputs.release_type || 'qa' }}"
        BUILD_NUMBER="${{ github.run_number }}"
        SHORT_SHA="$(git rev-parse --short HEAD)"
        TIMESTAMP="$(date +'%Y%m%d-%H%M%S')"
        
        echo "RELEASE_TYPE=$RELEASE_TYPE" >> $GITHUB_OUTPUT
        echo "BUILD_NUMBER=$BUILD_NUMBER" >> $GITHUB_OUTPUT
        echo "SHORT_SHA=$SHORT_SHA" >> $GITHUB_OUTPUT
        echo "TIMESTAMP=$TIMESTAMP" >> $GITHUB_OUTPUT
        echo "APK_NAME=TS1-${RELEASE_TYPE^^}-v${BUILD_NUMBER}-${SHORT_SHA}.apk" >> $GITHUB_OUTPUT
        
    - name: Create release info file
      run: |
        mkdir -p android/app/src/main/assets
        cat > android/app/src/main/assets/release-info.json << EOF
        {
          "releaseType": "${{ steps.release_info.outputs.RELEASE_TYPE }}",
          "buildNumber": "${{ steps.release_info.outputs.BUILD_NUMBER }}",
          "commitSha": "${{ steps.release_info.outputs.SHORT_SHA }}",
          "buildTime": "${{ steps.release_info.outputs.TIMESTAMP }}",
          "environment": "${{ steps.release_info.outputs.RELEASE_TYPE }}"
        }
        EOF
        
    - name: Build Android APK
      run: |
        cd android
        ./gradlew assembleDebug
        
    - name: Rename APK
      run: |
        mv android/app/build/outputs/apk/debug/app-debug.apk \
           android/app/build/outputs/apk/debug/${{ steps.release_info.outputs.APK_NAME }}
        
    - name: Upload to Firebase App Distribution
      if: github.event.inputs.release_type != 'hotfix'
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_APP_ID_ANDROID }}
        serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        groups: qa-team,stakeholders
        file: android/app/build/outputs/apk/debug/${{ steps.release_info.outputs.APK_NAME }}
        releaseNotes: |
          🚀 **${{ steps.release_info.outputs.RELEASE_TYPE }} Release**
          
          **Build Info:**
          - Type: ${{ steps.release_info.outputs.RELEASE_TYPE }}
          - Build: ${{ steps.release_info.outputs.BUILD_NUMBER }}
          - Commit: ${{ steps.release_info.outputs.SHORT_SHA }}
          - Date: ${{ steps.release_info.outputs.TIMESTAMP }}
          
          **Release Notes:**
          ${{ github.event.release.body || 'Manual release triggered' }}
          
    - name: Upload APK Artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ steps.release_info.outputs.RELEASE_TYPE }}-release-apk
        path: android/app/build/outputs/apk/debug/${{ steps.release_info.outputs.APK_NAME }}
        retention-days: 60
        
    - name: Update Release with APK
      if: github.event_name == 'release'
      uses: softprops/action-gh-release@v1
      with:
        files: android/app/build/outputs/apk/debug/${{ steps.release_info.outputs.APK_NAME }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  notify-qa-team:
    name: Notify QA Team
    runs-on: ubuntu-latest
    needs: qa-android-build
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.qa-android-build.result == 'success'
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "🎯 New QA release is ready for testing!",
            attachments: [{
              color: 'good',
              fields: [{
                title: 'Release Type',
                value: '${{ github.event.inputs.release_type || "qa" }}',
                short: true
              }, {
                title: 'Build Number',
                value: '${{ github.run_number }}',
                short: true
              }, {
                title: 'Download',
                value: 'Check Firebase App Distribution or GitHub Release',
                short: false
              }]
            }]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify on failure
      if: needs.qa-android-build.result == 'failure'
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ QA release build failed!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}