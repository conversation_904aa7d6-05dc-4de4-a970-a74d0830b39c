buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"

        // FFmpeg-Kit configuration
        ffmpegKitPackage = "full-gpl"
        ffmpegKitVersion = "6.0-2"
    }
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs "$rootDir/libs"
        }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.3.14')
        
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven {
            url "https://artifactory.appodeal.com/appodeal-public"
            name "Appodeal Artifactory"
        }
    }
}

apply plugin: "com.facebook.react.rootproject"