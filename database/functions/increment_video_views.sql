-- Function to increment video views atomically
-- This function safely increments both views_count and views columns

CREATE OR REPLACE FUNCTION increment_video_views(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    views_count = COALESCE(views_count, 0) + 1,
    views = COALESCE(views, 0) + 1,
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_video_views(text) TO authenticated;
