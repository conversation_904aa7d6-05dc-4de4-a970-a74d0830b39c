# Database Setup for Video System

This directory contains the database schema and functions needed for the video system.

## Setup Instructions

### 1. Create the Videos Table

The videos table should already exist in your database. If not, run the SQL from your main schema file.

### 2. Create Database Functions

Run the migration script in your Supabase SQL editor:

```sql
-- Copy and paste the contents of migrations/001_create_video_functions.sql
```

This will create the following functions:
- `increment_video_views(video_id_param text)` - Atomically increment video view counts
- `increment_video_likes(video_id_param text)` - Atomically increment video likes
- `decrement_video_likes(video_id_param text)` - Atomically decrement video likes
- `increment_video_comments(video_id_param text)` - Atomically increment comment counts
- `increment_video_shares(video_id_param text)` - Atomically increment share counts

### 3. Create Indexes

The migration script also creates optimized indexes for:
- User ID lookups
- Video ID lookups
- Privacy settings
- Draft status
- Published date
- Video type
- Sport category
- Tags (using GIN index for array searches)

### 4. Create Views

A `public_videos` view is created for commonly queried public videos:

```sql
CREATE OR REPLACE VIEW public_videos AS
SELECT *
FROM videos
WHERE privacy_setting = 'public' 
  AND is_draft = false
  AND published_at IS NOT NULL;
```

## Usage

### Creating Videos

```typescript
import { createVideo } from '../services/database/videoService';

const videoRecord = await createVideo({
  user_id: 'user-uuid',
  title: 'My Video',
  description: 'Video description',
  video_url: 'https://storage.url/video.mp4',
  privacy_setting: 'public',
  allow_comments: true,
  allow_downloads: true,
  type: 'For fun',
  sport: 'Football',
  tags: ['highlight', 'goal'],
});
```

### Fetching Videos

```typescript
import { getPublicVideos, getVideosByUserId } from '../services/database/videoService';

// Get public videos for feed
const publicVideos = await getPublicVideos(20, 0);

// Get user's videos
const userVideos = await getVideosByUserId('user-uuid', false, 20, 0);
```

### Incrementing Stats

```typescript
import { incrementVideoViews } from '../services/database/videoService';

// Increment view count when video is played
await incrementVideoViews('vid_123abc');
```

## Data Types

### VideoRecord Interface

```typescript
interface VideoRecord {
  id: string;
  user_id: string;
  title: string | null;
  description?: string | null;
  video_url: string;
  thumbnail_url?: string | null;
  duration?: number | null;
  privacy_setting: string;
  allow_comments: boolean | null;
  allow_downloads: boolean | null;
  type?: string | null;
  sport?: string | null;
  tags?: string[] | null;
  // ... other fields
}
```

### Utility Functions

```typescript
import { 
  getVideoTitle, 
  getVideoDescription, 
  getVideoStats,
  getVideoPermissions 
} from '../services/database/videoService';

// Safe access to nullable fields
const title = getVideoTitle(video); // Returns 'Untitled Video' if null
const stats = getVideoStats(video); // Returns { views: 0, likes: 0, ... } with fallbacks
const permissions = getVideoPermissions(video); // Returns boolean values with defaults
```

## Security

- All functions have proper permissions for authenticated users
- Row Level Security (RLS) should be enabled on the videos table
- Users can only modify their own videos
- Public videos are accessible to all authenticated users

## Performance

- Indexes are optimized for common query patterns
- The `public_videos` view reduces query complexity for feed displays
- Atomic increment functions prevent race conditions in stat updates
