-- Migration: Create video-related database functions
-- Run this in your Supabase SQL editor

-- Function to increment video views atomically
CREATE OR REPLACE FUNCTION increment_video_views(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    views_count = COALESCE(views_count, 0) + 1,
    views = COALESCE(views, 0) + 1,
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_video_views(text) TO authenticated;

-- Function to increment video likes
CREATE OR REPLACE FUNCTION increment_video_likes(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    likes_count = COALESCE(likes_count, 0) + 1,
    likes = COALESCE(likes, 0) + 1,
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_video_likes(text) TO authenticated;

-- Function to decrement video likes
CREATE OR REPLACE FUNCTION decrement_video_likes(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    likes_count = GREATEST(0, COALESCE(likes_count, 0) - 1),
    likes = GREATEST(0, COALESCE(likes, 0) - 1),
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION decrement_video_likes(text) TO authenticated;

-- Function to increment video comments count
CREATE OR REPLACE FUNCTION increment_video_comments(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    comments_count = COALESCE(comments_count, 0) + 1,
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_video_comments(text) TO authenticated;

-- Function to decrement video comments count
CREATE OR REPLACE FUNCTION decrement_video_comments(video_id_param text, decrement_by integer DEFAULT 1)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos
  SET
    comments_count = GREATEST(0, COALESCE(comments_count, 0) - decrement_by),
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION decrement_video_comments(text, integer) TO authenticated;

-- Function to increment video shares count
CREATE OR REPLACE FUNCTION increment_video_shares(video_id_param text)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE videos 
  SET 
    shares_count = COALESCE(shares_count, 0) + 1,
    updated_at = now()
  WHERE video_id = video_id_param;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_video_shares(text) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_video_id ON videos(video_id);
CREATE INDEX IF NOT EXISTS idx_videos_privacy_setting ON videos(privacy_setting);
CREATE INDEX IF NOT EXISTS idx_videos_is_draft ON videos(is_draft);
CREATE INDEX IF NOT EXISTS idx_videos_published_at ON videos(published_at);
CREATE INDEX IF NOT EXISTS idx_videos_type ON videos(type);
CREATE INDEX IF NOT EXISTS idx_videos_sport ON videos(sport);
CREATE INDEX IF NOT EXISTS idx_videos_tags ON videos USING GIN(tags);

-- Create a view for public videos (commonly queried)
CREATE OR REPLACE VIEW public_videos AS
SELECT *
FROM videos
WHERE privacy_setting = 'public' 
  AND is_draft = false
  AND published_at IS NOT NULL;

-- Grant access to the view
GRANT SELECT ON public_videos TO authenticated;
