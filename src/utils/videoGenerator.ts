import RNFS from 'react-native-fs';
import { FFmpegKit, ReturnCode, FFmpegKitConfig, Statistics } from 'ffmpeg-kit-react-native';
import logger from './logger';

// Progress callback type
export type ProgressCallback = (progress: number, message: string) => void;

export interface TextOverlay {
  id: string;
  text: string;
  x: number; // Position as percentage (0-100)
  y: number; // Position as percentage (0-100)
  fontSize: number;
  color: string;
  fontFamily: string;
  backgroundColor?: string;
  rotation?: number;
  animation?: 'none' | 'fadeIn' | 'slideIn' | 'bounce' | 'typewriter';
  startTime: number; // in seconds
  duration: number; // in seconds
}

export interface VideoGenerationOptions {
  imageUri: string;
  duration: number; // in seconds (3-60)
  textOverlays: TextOverlay[];
  musicUri?: string;
  musicVolume?: number; // 0-1
  outputPath?: string;
  quality: 'low' | 'medium' | 'high';
  fps: number; // 24, 30, or 60
  onProgress?: ProgressCallback; // Progress callback
}

export interface VideoGenerationResult {
  videoPath: string;
  thumbnailPath: string;
  duration: number;
  size: number; // file size in bytes
}

/**
 * Generate video from static image with text overlays and music
 */
export const generateVideoFromPhoto = async (
  options: VideoGenerationOptions
): Promise<VideoGenerationResult> => {
  try {
    logger.debug('Starting video generation from photo:', options);

    // Validate inputs
    if (!options.imageUri) {
      throw new Error('Image URI is required');
    }

    if (options.duration < 3 || options.duration > 60) {
      throw new Error('Duration must be between 3 and 60 seconds');
    }

    // Check if image exists
    const imageExists = await RNFS.exists(options.imageUri.replace('file://', ''));
    if (!imageExists) {
      throw new Error(`Image file does not exist: ${options.imageUri}`);
    }

    // Generate output paths
    const timestamp = Date.now();
    const outputDir = `${RNFS.DocumentDirectoryPath}/generated_videos`;
    const videoPath = options.outputPath || `${outputDir}/video_${timestamp}.mp4`;
    const thumbnailPath = `${outputDir}/thumb_${timestamp}.jpg`;

    // Ensure output directory exists
    await RNFS.mkdir(outputDir);

    // Generate video with FFmpeg
    const result = await generateVideoWithFFmpeg(options, videoPath, thumbnailPath);

    logger.debug('Video generation completed:', result);
    return result;

  } catch (error) {
    logger.error('Video generation error:', error);
    throw error;
  }
};

/**
 * Generate video using FFmpeg
 * Creates a real video file from static image with overlays and music
 */
const generateVideoWithFFmpeg = async (
  options: VideoGenerationOptions,
  outputPath: string,
  thumbnailPath: string
): Promise<VideoGenerationResult> => {
  try {
    logger.debug('Starting FFmpeg video generation with options:', options);

    // Clean the image URI
    const cleanImageUri = options.imageUri.replace('file://', '');
    
    // Verify image exists
    const imageExists = await RNFS.exists(cleanImageUri);
    if (!imageExists) {
      throw new Error(`Source image not found: ${cleanImageUri}`);
    }

    // Get quality settings
    const qualitySettings = getQualitySettings(options.quality);
    
    // Build FFmpeg command
    let ffmpegCommand = buildFFmpegCommand(
      cleanImageUri,
      outputPath,
      options,
      qualitySettings
    );

    logger.debug('Executing FFmpeg command:', ffmpegCommand);
    console.log('🎬 FFmpeg Command:', ffmpegCommand); // Debug log

    // Set up progress tracking
    if (options.onProgress) {
      options.onProgress(0, 'Starting video generation...');

      // Enable statistics callback for progress tracking
      FFmpegKitConfig.enableStatisticsCallback((statistics: Statistics) => {
        if (options.onProgress && statistics.getTime() > 0) {
          const progress = Math.min(100, (statistics.getTime() / (options.duration * 1000)) * 100);
          options.onProgress(progress, `Processing: ${Math.round(progress)}%`);
        }
      });
    }

    // Execute FFmpeg command
    const session = await FFmpegKit.execute(ffmpegCommand);
    const returnCode = await session.getReturnCode();

    if (!ReturnCode.isSuccess(returnCode)) {
      const failStackTrace = await session.getFailStackTrace();
      const logs = await session.getAllLogsAsString();
      throw new Error(`FFmpeg execution failed with code: ${returnCode}. Error: ${failStackTrace || logs}`);
    }

    if (options.onProgress) {
      options.onProgress(100, 'Video generation completed!');
    }

    // Verify output file was created
    const outputExists = await RNFS.exists(outputPath);
    if (!outputExists) {
      throw new Error('FFmpeg completed but output file was not created');
    }

    // Create thumbnail (copy of original image)
    await RNFS.copyFile(cleanImageUri, thumbnailPath);

    // Get file stats
    const stats = await RNFS.stat(outputPath);

    logger.debug('FFmpeg video generation completed:', {
      outputPath,
      thumbnailPath,
      duration: options.duration,
      size: stats.size
    });

    return {
      videoPath: `file://${outputPath}`,
      thumbnailPath: `file://${thumbnailPath}`,
      duration: options.duration,
      size: stats.size,
    };

  } catch (error: any) {
    logger.error('FFmpeg video generation error:', error);
    throw new Error(`Failed to generate video from photo: ${error?.message || 'Unknown error'}`);
  }
};

/**
 * Build FFmpeg command for video generation
 */
const buildFFmpegCommand = (
  imagePath: string,
  outputPath: string,
  options: VideoGenerationOptions,
  qualitySettings: { width: number; height: number; bitrate: string; fps: number }
): string => {
  const parts: string[] = [];

  // Input: Loop the image
  parts.push(`-loop 1`);
  parts.push(`-i "${imagePath}"`);

  // Add music if provided
  if (options.musicUri) {
    const cleanMusicUri = options.musicUri.replace('file://', '');
    parts.push(`-i "${cleanMusicUri}"`);
  }

  // Duration
  parts.push(`-t ${options.duration}`);

  // Video codec and settings
  parts.push(`-c:v libx264`);
  parts.push(`-pix_fmt yuv420p`);
  parts.push(`-r ${qualitySettings.fps}`);
  parts.push(`-b:v ${qualitySettings.bitrate}`);

  // Build video filters
  const videoFilters: string[] = [];

  // Scale filter
  videoFilters.push(`scale=${qualitySettings.width}:${qualitySettings.height}`);

  // Add text overlays if any (temporarily disabled for debugging)
  if (options.textOverlays && options.textOverlays.length > 0) {
    console.log('🎬 Adding text overlays:', options.textOverlays.length);
    const textFilters = buildTextFilters(options.textOverlays, qualitySettings);
    if (textFilters) {
      console.log('🎬 Text filters:', textFilters);
      videoFilters.push(textFilters);
    }
  }

  // Apply video filters
  if (videoFilters.length > 0) {
    parts.push(`-vf "${videoFilters.join(',')}"`);
  }

  // Audio settings
  if (options.musicUri) {
    parts.push(`-c:a aac`);
    parts.push(`-b:a 128k`);
    parts.push(`-shortest`); // Stop when shortest input ends

    if (options.musicVolume && options.musicVolume !== 1) {
      parts.push(`-filter:a "volume=${options.musicVolume}"`);
    }
  } else {
    parts.push(`-an`); // No audio
  }

  // Output settings
  parts.push(`-movflags +faststart`); // Optimize for streaming
  parts.push(`-y`); // Overwrite output file
  parts.push(`"${outputPath}"`);

  return parts.join(' ');
};

/**
 * Build text overlay filters for FFmpeg
 */
const buildTextFilters = (
  textOverlays: TextOverlay[],
  qualitySettings: { width: number; height: number }
): string => {
  if (!textOverlays || textOverlays.length === 0) {
    return '';
  }

  const filters: string[] = [];

  textOverlays.forEach((overlay) => {
    // Calculate position in pixels
    const x = Math.round((overlay.x / 100) * qualitySettings.width);
    const y = Math.round((overlay.y / 100) * qualitySettings.height);

    // Escape text for FFmpeg
    const escapedText = overlay.text
      .replace(/\\/g, '\\\\')  // Escape backslashes
      .replace(/'/g, "\\'")    // Escape single quotes
      .replace(/:/g, '\\:')    // Escape colons
      .replace(/,/g, '\\,');   // Escape commas

    // Build drawtext filter parts
    const filterParts: string[] = [];
    filterParts.push(`text='${escapedText}'`);
    filterParts.push(`x=${x}`);
    filterParts.push(`y=${y}`);
    filterParts.push(`fontsize=${overlay.fontSize}`);
    filterParts.push(`fontcolor=${overlay.color}`);

    // Add background if specified
    if (overlay.backgroundColor && overlay.backgroundColor !== 'transparent') {
      filterParts.push(`box=1`);
      filterParts.push(`boxcolor=${overlay.backgroundColor}`);
      filterParts.push(`boxborderw=5`);
    }

    // Add timing if specified
    if (overlay.startTime > 0 || overlay.duration < 999) {
      const endTime = overlay.startTime + overlay.duration;
      filterParts.push(`enable='between(t,${overlay.startTime},${endTime})'`);
    }

    const filter = `drawtext=${filterParts.join(':')}`;
    filters.push(filter);
  });

  return filters.join(',');
};

/**
 * Get video quality settings
 */
export const getQualitySettings = (quality: 'low' | 'medium' | 'high'): {
  width: number;
  height: number;
  bitrate: string;
  fps: number;
} => {
  switch (quality) {
    case 'low':
      return {
        width: 480,
        height: 854, // 9:16 aspect ratio
        bitrate: '500k',
        fps: 24,
      };
    case 'medium':
      return {
        width: 720,
        height: 1280,
        bitrate: '1500k',
        fps: 30,
      };
    case 'high':
      return {
        width: 1080,
        height: 1920,
        bitrate: '3000k',
        fps: 30,
      };
    default:
      return getQualitySettings('medium');
  }
};

/**
 * Validate text overlay configuration
 */
export const validateTextOverlay = (overlay: TextOverlay): boolean => {
  if (!overlay.text || overlay.text.trim().length === 0) {
    return false;
  }

  if (overlay.x < 0 || overlay.x > 100 || overlay.y < 0 || overlay.y > 100) {
    return false;
  }

  if (overlay.fontSize < 12 || overlay.fontSize > 72) {
    return false;
  }

  if (overlay.startTime < 0 || overlay.duration <= 0) {
    return false;
  }

  return true;
};

/**
 * Generate default text overlay
 */
export const createDefaultTextOverlay = (text: string): TextOverlay => {
  return {
    id: `text_${Date.now()}`,
    text,
    x: 50, // Center horizontally
    y: 80, // Near bottom
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'System',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    rotation: 0,
    animation: 'fadeIn',
    startTime: 0,
    duration: 3, // Show for 3 seconds by default
  };
};

/**
 * Estimate video file size
 */
export const estimateVideoSize = (
  duration: number,
  quality: 'low' | 'medium' | 'high'
): number => {
  const settings = getQualitySettings(quality);
  const bitrateKbps = parseInt(settings.bitrate.replace('k', ''));

  // Rough estimation: bitrate * duration / 8 (convert bits to bytes)
  const estimatedSizeBytes = (bitrateKbps * 1000 * duration) / 8;

  return estimatedSizeBytes;
};

/**
 * Validate FFmpeg availability
 */
export const validateFFmpegAvailability = async (): Promise<boolean> => {
  try {
    const session = await FFmpegKit.execute('-version');
    const returnCode = await session.getReturnCode();
    return ReturnCode.isSuccess(returnCode);
  } catch (error) {
    logger.error('FFmpeg validation error:', error);
    return false;
  }
};

/**
 * Clean up temporary files
 */
export const cleanupTempFiles = async (filePaths: string[]): Promise<void> => {
  try {
    for (const filePath of filePaths) {
      const cleanPath = filePath.replace('file://', '');
      const exists = await RNFS.exists(cleanPath);
      if (exists) {
        await RNFS.unlink(cleanPath);
        logger.debug('Cleaned up temp file:', cleanPath);
      }
    }
  } catch (error) {
    logger.error('Cleanup error:', error);
  }
};

/**
 * Test FFmpeg command generation (for debugging)
 */
export const testFFmpegCommand = (
  imagePath: string,
  outputPath: string,
  options: VideoGenerationOptions
): string => {
  const qualitySettings = getQualitySettings(options.quality);
  const command = buildFFmpegCommand(imagePath, outputPath, options, qualitySettings);
  console.log('🧪 Test FFmpeg Command:', command);
  return command;
};
