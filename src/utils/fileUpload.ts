import { supabase } from '../integrations/supabase/client';
import logger from './logger';
import RNFS from 'react-native-fs';

export interface FileUploadResult {
  publicUrl: string;
  filePath: string;
}


export const uploadFileToSupabase = async (
  file: { uri: string; type: string; name: string },
  bucket: string,
  folder: string,
): Promise<FileUploadResult> => {
  try {

    const fileName = `${Date.now()}-${file.name}`;
    const filePath = folder ? `${folder}/${fileName}` : fileName;

    logger.debug('Starting file upload:', {
      fileName,
      filePath,
      bucket: bucket,
      fileType: file.type,
      originalUri: file.uri
    });


    // Clean the file URI
    const cleanUri = file.uri.replace('file://', '');

    // Check if file exists
    const fileExists = await RNFS.exists(cleanUri);
    if (!fileExists) {
      throw new Error(`File does not exist: ${cleanUri}`);
    }

    // Get file stats
    const fileStats = await RNFS.stat(cleanUri);
    logger.debug('File stats:', { size: fileStats.size, path: cleanUri });

    // Read the file as binary data (ArrayBuffer)
    const fileData = await RNFS.readFile(cleanUri, 'base64');

    // Convert base64 to ArrayBuffer for proper binary upload
    const binaryString = atob(fileData);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    logger.debug('File converted to binary:', {
      originalBase64Size: fileData.length,
      binarySize: bytes.length,
      fileSize: fileStats.size,
      contentType: file.type
    });

    // Upload the binary data
    const uploadResult = await supabase.storage
      .from(bucket)
      .upload(filePath, bytes.buffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type,
      });

    if (uploadResult.error) {
      logger.error('Upload error:', uploadResult.error);
      throw uploadResult.error;
    }

    logger.debug('Upload result:', uploadResult.data);

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(filePath);

    logger.debug('Upload successful:', { publicUrl, filePath });

    // Verify the upload by checking if the file is accessible
    try {
      const response = await fetch(publicUrl, { method: 'HEAD' });
      logger.debug('Upload verification:', {
        status: response.status,
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length')
      });
    } catch (verifyError) {
      logger.warn('Could not verify upload:', verifyError);
      // Don't fail the upload for verification issues
    }

    return {
      publicUrl,
      filePath,
    };
  } catch (error) {
    logger.error('File upload error:', error);
    throw error;
  }
};

/**
 * Test function to verify if an uploaded file is readable
 */
export const testFileReadability = async (publicUrl: string): Promise<boolean> => {
  try {
    const response = await fetch(publicUrl);
    const isReadable = response.ok && response.status === 200;
    logger.debug('File readability test:', {
      url: publicUrl,
      status: response.status,
      readable: isReadable,
      contentType: response.headers.get('content-type')
    });
    return isReadable;
  } catch (error) {
    logger.error('File readability test failed:', error);
    return false;
  }
};
