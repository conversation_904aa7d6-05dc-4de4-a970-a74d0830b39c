import { PixelRatio, Platform } from 'react-native';
import { dimensions } from '../styles/dimensions';

const { screenWidth: SCREEN_WIDTH } = dimensions;

// Based on iPhone 13 scale
const scale = SCREEN_WIDTH / 390;

export function normalize(size: number) {
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
}

export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';
