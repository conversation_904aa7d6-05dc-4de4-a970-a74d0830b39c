import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../integrations/supabase/client';
import logger from './logger';
import { AppDispatch } from '../store';

/**
 * Real-time subscription manager for Supabase
 */

export interface SubscriptionConfig {
  table: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  schema?: string;
}

export interface SubscriptionHandler {
  onInsert?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onUpdate?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onDelete?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onError?: (error: any) => void;
}

class RealtimeManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  /**
   * Subscribe to real-time changes
   */
  subscribe(
    channelName: string,
    config: SubscriptionConfig,
    handler: SubscriptionHandler,
    dispatch?: AppDispatch
  ): RealtimeChannel {
    try {
      // Unsubscribe existing channel if it exists
      this.unsubscribe(channelName);

      logger.info(`Subscribing to real-time channel: ${channelName}`);

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: config.event || '*',
            schema: config.schema || 'public',
            table: config.table,
            filter: config.filter,
          },
          (payload) => {
            logger.debug(`Real-time event received on ${channelName}:`, payload);

            try {
              switch (payload.eventType) {
                case 'INSERT':
                  handler.onInsert?.(payload);
                  break;
                case 'UPDATE':
                  handler.onUpdate?.(payload);
                  break;
                case 'DELETE':
                  handler.onDelete?.(payload);
                  break;
              }
            } catch (error) {
              logger.error(`Error handling real-time event on ${channelName}:`, error);
              handler.onError?.(error);
            }
          }
        )
        .subscribe((status) => {
          logger.info(`Channel ${channelName} subscription status:`, status);

          if (status === 'SUBSCRIBED') {
            // Reset reconnect attempts on successful connection
            this.reconnectAttempts.set(channelName, 0);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            // Handle connection errors
            this.handleConnectionError(channelName, config, handler, dispatch);
          }
        });

      this.channels.set(channelName, channel);
      return channel;
    } catch (error) {
      logger.error(`Failed to subscribe to channel ${channelName}:`, error);
      handler.onError?.(error);
      throw error;
    }
  }

  /**
   * Unsubscribe from a channel
   */
  unsubscribe(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (channel) {
      logger.info(`Unsubscribing from channel: ${channelName}`);
      supabase.removeChannel(channel);
      this.channels.delete(channelName);
      this.reconnectAttempts.delete(channelName);
    }
  }

  /**
   * Unsubscribe from all channels
   */
  unsubscribeAll(): void {
    logger.info('Unsubscribing from all real-time channels');
    this.channels.forEach((_, channelName) => {
      this.unsubscribe(channelName);
    });
  }

  /**
   * Handle connection errors and implement reconnection logic
   */
  private handleConnectionError(
    channelName: string,
    config: SubscriptionConfig,
    handler: SubscriptionHandler,
    dispatch?: AppDispatch
  ): void {
    const attempts = this.reconnectAttempts.get(channelName) || 0;

    if (attempts < this.maxReconnectAttempts) {
      const delay = this.reconnectDelay * Math.pow(2, attempts); // Exponential backoff
      
      logger.warn(
        `Connection error on channel ${channelName}. Reconnecting in ${delay}ms (attempt ${attempts + 1}/${this.maxReconnectAttempts})`
      );

      setTimeout(() => {
        this.reconnectAttempts.set(channelName, attempts + 1);
        this.subscribe(channelName, config, handler, dispatch);
      }, delay);
    } else {
      logger.error(`Max reconnection attempts reached for channel ${channelName}`);
      handler.onError?.(new Error('Max reconnection attempts reached'));
    }
  }

  /**
   * Get channel status
   */
  getChannelStatus(channelName: string): string | null {
    const channel = this.channels.get(channelName);
    return channel?.state || null;
  }

  /**
   * Get all active channels
   */
  getActiveChannels(): string[] {
    return Array.from(this.channels.keys());
  }

  /**
   * Check if a channel is active
   */
  isChannelActive(channelName: string): boolean {
    const channel = this.channels.get(channelName);
    return channel?.state === 'joined';
  }
}

// Singleton instance
export const realtimeManager = new RealtimeManager();

// Video Likes Real-time Subscription
export const subscribeToVideoLikes = (
  videoId: string,
  dispatch?: any,
  handlers?: {
    onLikeAdded?: (videoId: string, userId: string) => void;
    onLikeRemoved?: (videoId: string, userId: string) => void;
  }
) => {
  const channel = supabase
    .channel(`video_likes:${videoId}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'video_likes',
        filter: `video_id=eq.${videoId}`,
      },
      (payload) => {
        logger.debug('Video like added:', payload);
        handlers?.onLikeAdded?.(videoId, (payload as any).new?.user_id);
      }
    )
    .on(
      'postgres_changes',
      {
        event: 'DELETE',
        schema: 'public',
        table: 'video_likes',
        filter: `video_id=eq.${videoId}`,
      },
      (payload) => {
        logger.debug('Video like removed:', payload);
        handlers?.onLikeRemoved?.(videoId, (payload as any).old?.user_id);
      }
    )
    .subscribe();

  return () => {
    channel.unsubscribe();
  };
};


/**
 * User follows real-time subscription
 */
export const subscribeToUserFollows = (
  userId: string,
  dispatch: AppDispatch,
  handlers: {
    onFollowerAdded: (followerId: string, followingId: string) => void;
    onFollowerRemoved: (followerId: string, followingId: string) => void;
  }
): RealtimeChannel => {
  return realtimeManager.subscribe(
    `user_follows_${userId}`,
    {
      table: 'user_follows',
      filter: `following_id=eq.${userId}`,
    },
    {
      onInsert: (payload) => {
        const { follower_id, following_id } = payload.new;
        handlers.onFollowerAdded(follower_id, following_id);
      },
      onDelete: (payload) => {
        const { follower_id, following_id } = payload.old;
        handlers.onFollowerRemoved(follower_id, following_id);
      },
      onError: (error) => {
        logger.error(`User follows subscription error for user ${userId}:`, error);
      },
    },
    dispatch
  );
};

/**
 * Comments real-time subscription
 */
export const subscribeToVideoComments = (
  videoId: string,
  dispatch: AppDispatch,
  handlers: {
    onCommentAdded: (comment: any) => void;
    onCommentUpdated: (comment: any) => void;
    onCommentDeleted: (commentId: string) => void;
  }
): RealtimeChannel => {
  return realtimeManager.subscribe(
    `video_comments_${videoId}`,
    {
      table: 'comments',
      filter: `video_id=eq.${videoId}`,
    },
    {
      onInsert: (payload) => {
        handlers.onCommentAdded(payload.new);
      },
      onUpdate: (payload) => {
        handlers.onCommentUpdated(payload.new);
      },
      onDelete: (payload) => {
        handlers.onCommentDeleted(payload.old.id);
      },
      onError: (error) => {
        logger.error(`Video comments subscription error for video ${videoId}:`, error);
      },
    },
    dispatch
  );
};

/**
 * Comment likes real-time subscription
 */
export const subscribeToCommentLikes = (
  commentId: string,
  dispatch: AppDispatch,
  handlers: {
    onLikeAdded: (commentId: string, userId: string) => void;
    onLikeRemoved: (commentId: string, userId: string) => void;
  }
): RealtimeChannel => {
  return realtimeManager.subscribe(
    `comment_likes_${commentId}`,
    {
      table: 'comment_likes',
      filter: `comment_id=eq.${commentId}`,
    },
    {
      onInsert: (payload) => {
        const { comment_id, user_id } = payload.new;
        handlers.onLikeAdded(comment_id, user_id);
      },
      onDelete: (payload) => {
        const { comment_id, user_id } = payload.old;
        handlers.onLikeRemoved(comment_id, user_id);
      },
      onError: (error) => {
        logger.error(`Comment likes subscription error for comment ${commentId}:`, error);
      },
    },
    dispatch
  );
};

/**
 * Messages real-time subscription
 */
export const subscribeToConversationMessages = (
  conversationId: string,
  dispatch: AppDispatch,
  handlers: {
    onMessageAdded: (message: any) => void;
    onMessageUpdated: (message: any) => void;
    onMessageDeleted: (messageId: string) => void;
  }
): RealtimeChannel => {
  return realtimeManager.subscribe(
    `conversation_messages_${conversationId}`,
    {
      table: 'messages',
      filter: `conversation_id=eq.${conversationId}`,
    },
    {
      onInsert: (payload) => {
        handlers.onMessageAdded(payload.new);
      },
      onUpdate: (payload) => {
        handlers.onMessageUpdated(payload.new);
      },
      onDelete: (payload) => {
        handlers.onMessageDeleted(payload.old.id);
      },
      onError: (error) => {
        logger.error(`Conversation messages subscription error for conversation ${conversationId}:`, error);
      },
    },
    dispatch
  );
};

/**
 * Cleanup all subscriptions (call this when app goes to background or unmounts)
 */
export const cleanupRealtimeSubscriptions = (): void => {
  logger.info('Cleaning up all real-time subscriptions');
  realtimeManager.unsubscribeAll();
};

/**
 * Get real-time connection status
 */
export const getRealtimeStatus = (): {
  activeChannels: string[];
  channelStatuses: { [key: string]: string };
} => {
  const activeChannels = realtimeManager.getActiveChannels();
  const channelStatuses: { [key: string]: string } = {};

  activeChannels.forEach(channelName => {
    channelStatuses[channelName] = realtimeManager.getChannelStatus(channelName) || 'unknown';
  });

  return {
    activeChannels,
    channelStatuses
  };
};
