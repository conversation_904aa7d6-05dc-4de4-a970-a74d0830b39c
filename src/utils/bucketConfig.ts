import logger from './logger';

/**
 * Bucket configuration for different media types and contexts
 */
export interface BucketConfig {
  name: string;
  description: string;
  allowedExtensions: string[];
  maxFileSize: number; // in MB
  folder?: string;
}

/**
 * Media upload context types
 */
export type MediaContext = 
  | 'message'           // Chat/messaging media
  | 'feed'             // Main app feed content
  | 'profile'          // Profile pictures and banners
  | 'story'            // Story content
  | 'music'            // Music library
  | 'temp'             // Temporary uploads
  | 'avatar';          // User avatars

/**
 * Media type categories
 */
export type MediaType = 'image' | 'video' | 'audio' | 'document';

/**
 * Bucket configurations for different contexts
 *
 * New Convention:
 * - Videos/Feeds → 'videos' bucket → {userId}/ folder → content
 * - Profile Pictures → 'profile' bucket → {userId}/ folder → content
 * - Messages Media → 'messages' bucket → {conversationId}/ folder → content
 */
const BUCKET_CONFIGS: Record<MediaContext, Record<MediaType, BucketConfig>> = {
  message: {
    image: {
      name: 'messages',
      description: 'Images shared in chat messages',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      maxFileSize: 10, // 10MB
      folder: '', // Will be set to conversationId dynamically
    },
    video: {
      name: 'messages',
      description: 'Videos shared in chat messages',
      allowedExtensions: ['mp4', 'mov', 'm4v', 'avi', 'mkv'],
      maxFileSize: 100, // 100MB
      folder: '', // Will be set to conversationId dynamically
    },
    audio: {
      name: 'messages',
      description: 'Audio files shared in chat messages',
      allowedExtensions: ['aac', 'm4a', 'mp3', 'wav', 'ogg'],
      maxFileSize: 25, // 25MB
      folder: '', // Will be set to conversationId dynamically
    },
    document: {
      name: 'messages',
      description: 'Documents shared in chat messages',
      allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
      maxFileSize: 50, // 50MB
      folder: '', // Will be set to conversationId dynamically
    },
  },
  feed: {
    image: {
      name: 'videos',
      description: 'Images for main app feed',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      maxFileSize: 20, // 20MB
      folder: '', // Will be set to userId dynamically
    },
    video: {
      name: 'videos',
      description: 'Videos for main app feed',
      allowedExtensions: ['mp4', 'mov', 'm4v'],
      maxFileSize: 500, // 500MB for high-quality feed videos
      folder: '', // Will be set to userId dynamically
    },
    audio: {
      name: 'videos',
      description: 'Audio for main app feed',
      allowedExtensions: ['aac', 'm4a', 'mp3', 'wav'],
      maxFileSize: 50, // 50MB
      folder: '', // Will be set to userId dynamically
    },
    document: {
      name: 'videos',
      description: 'Documents for main app feed',
      allowedExtensions: ['pdf'],
      maxFileSize: 25, // 25MB
      folder: '', // Will be set to userId dynamically
    },
  },
  profile: {
    image: {
      name: 'profile',
      description: 'Profile pictures and banners',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      maxFileSize: 5, // 5MB
      folder: '', // Will be set to userId dynamically
    },
    video: {
      name: 'profile',
      description: 'Profile videos',
      allowedExtensions: ['mp4', 'mov'],
      maxFileSize: 50, // 50MB
      folder: '', // Will be set to userId dynamically
    },
    audio: {
      name: 'profile',
      description: 'Profile audio',
      allowedExtensions: ['aac', 'm4a', 'mp3'],
      maxFileSize: 10, // 10MB
      folder: '', // Will be set to userId dynamically
    },
    document: {
      name: 'profile',
      description: 'Profile documents',
      allowedExtensions: ['pdf'],
      maxFileSize: 5, // 5MB
      folder: '', // Will be set to userId dynamically
    },
  },
  story: {
    image: {
      name: 'videos',
      description: 'Story images',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      maxFileSize: 15, // 15MB
      folder: '', // Will be set to userId dynamically
    },
    video: {
      name: 'videos',
      description: 'Story videos',
      allowedExtensions: ['mp4', 'mov'],
      maxFileSize: 200, // 200MB
      folder: '', // Will be set to userId dynamically
    },
    audio: {
      name: 'videos',
      description: 'Story audio',
      allowedExtensions: ['aac', 'm4a', 'mp3'],
      maxFileSize: 25, // 25MB
      folder: '', // Will be set to userId dynamically
    },
    document: {
      name: 'videos',
      description: 'Story documents',
      allowedExtensions: ['pdf'],
      maxFileSize: 10, // 10MB
      folder: '', // Will be set to userId dynamically
    },
  },
  music: {
    image: {
      name: 'videos',
      description: 'Music thumbnails and artwork',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      maxFileSize: 5, // 5MB
      folder: 'music/artwork',
    },
    video: {
      name: 'videos',
      description: 'Music videos',
      allowedExtensions: ['mp4', 'mov'],
      maxFileSize: 300, // 300MB
      folder: 'music/videos',
    },
    audio: {
      name: 'videos',
      description: 'Music library files',
      allowedExtensions: ['aac', 'm4a', 'mp3', 'wav', 'flac'],
      maxFileSize: 100, // 100MB
      folder: 'music/tracks',
    },
    document: {
      name: 'videos',
      description: 'Music metadata and lyrics',
      allowedExtensions: ['txt', 'json'],
      maxFileSize: 1, // 1MB
      folder: 'music/metadata',
    },
  },
  temp: {
    image: {
      name: 'videos',
      description: 'Temporary image uploads',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      maxFileSize: 20, // 20MB
      folder: 'temp/images',
    },
    video: {
      name: 'videos',
      description: 'Temporary video uploads',
      allowedExtensions: ['mp4', 'mov', 'm4v'],
      maxFileSize: 200, // 200MB
      folder: 'temp/videos',
    },
    audio: {
      name: 'videos',
      description: 'Temporary audio uploads',
      allowedExtensions: ['aac', 'm4a', 'mp3', 'wav'],
      maxFileSize: 50, // 50MB
      folder: 'temp/audio',
    },
    document: {
      name: 'videos',
      description: 'Temporary document uploads',
      allowedExtensions: ['pdf', 'txt'],
      maxFileSize: 25, // 25MB
      folder: 'temp/documents',
    },
  },
  avatar: {
    image: {
      name: 'profile',
      description: 'User avatar images',
      allowedExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      maxFileSize: 5, // 5MB
      folder: '', // Will be set to userId dynamically
    },
    video: {
      name: 'profile',
      description: 'User avatar videos',
      allowedExtensions: ['mp4', 'mov'],
      maxFileSize: 25, // 25MB
      folder: '', // Will be set to userId dynamically
    },
    audio: {
      name: 'profile',
      description: 'User avatar audio',
      allowedExtensions: ['aac', 'm4a', 'mp3'],
      maxFileSize: 5, // 5MB
      folder: '', // Will be set to userId dynamically
    },
    document: {
      name: 'profile',
      description: 'User avatar documents',
      allowedExtensions: ['pdf'],
      maxFileSize: 2, // 2MB
      folder: '', // Will be set to userId dynamically
    },
  },
};

/**
 * Get media type from file extension
 */
export const getMediaTypeFromExtension = (extension: string): MediaType => {
  const ext = extension.toLowerCase();
  
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'];
  const videoExts = ['mp4', 'mov', 'm4v', 'avi', 'mkv', 'webm', 'flv'];
  const audioExts = ['aac', 'm4a', 'mp3', 'wav', 'ogg', 'flac', 'wma'];
  
  if (imageExts.includes(ext)) return 'image';
  if (videoExts.includes(ext)) return 'video';
  if (audioExts.includes(ext)) return 'audio';
  
  return 'document';
};

/**
 * Get bucket configuration for a specific context and media type
 */
export const getBucketConfig = (
  context: MediaContext,
  mediaType: MediaType
): BucketConfig => {
  const config = BUCKET_CONFIGS[context]?.[mediaType];
  
  if (!config) {
    logger.warn(`No bucket config found for context: ${context}, mediaType: ${mediaType}`);
    // Fallback to temp bucket
    return BUCKET_CONFIGS.temp[mediaType];
  }
  
  return config;
};

/**
 * Get bucket configuration from file extension and context
 */
export const getBucketConfigFromFile = (
  filePath: string,
  context: MediaContext
): BucketConfig => {
  const extension = filePath.split('.').pop() || '';
  const mediaType = getMediaTypeFromExtension(extension);
  
  return getBucketConfig(context, mediaType);
};

/**
 * Validate file against bucket configuration
 */
export const validateFileForBucket = (
  filePath: string,
  fileSize: number, // in bytes
  config: BucketConfig
): { isValid: boolean; error?: string } => {
  const extension = filePath.split('.').pop()?.toLowerCase() || '';
  
  // Check extension
  if (!config.allowedExtensions.includes(extension)) {
    return {
      isValid: false,
      error: `File type .${extension} is not allowed. Allowed types: ${config.allowedExtensions.join(', ')}`
    };
  }
  
  // Check file size (convert MB to bytes)
  const maxSizeBytes = config.maxFileSize * 1024 * 1024;
  if (fileSize > maxSizeBytes) {
    return {
      isValid: false,
      error: `File size (${(fileSize / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${config.maxFileSize}MB)`
    };
  }
  
  return { isValid: true };
};

/**
 * Get all available contexts
 */
export const getAvailableContexts = (): MediaContext[] => {
  return Object.keys(BUCKET_CONFIGS) as MediaContext[];
};

/**
 * Get bucket name for context and media type
 */
export const getBucketName = (context: MediaContext, mediaType: MediaType): string => {
  return getBucketConfig(context, mediaType).name;
};
