import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '../store/slices/authSlice';
import logger from './logger';

const AUTH_STORAGE_KEY = '@TS1_auth_state';
const USER_STORAGE_KEY = '@TS1_user_data';

export interface StoredAuthState {
  isAuthenticated: boolean;
  lastLoginTime: string;
  userId?: string;
}

export const authStorage = {
  // Store authentication state
  async storeAuthState(isAuthenticated: boolean, userId?: string): Promise<void> {
    try {
      const authState: StoredAuthState = {
        isAuthenticated,
        lastLoginTime: new Date().toISOString(),
        userId,
      };
      await AsyncStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authState));
      logger.debug('Auth state stored successfully');
    } catch (error) {
      logger.error('Error storing auth state:', error);
    }
  },

  // Get stored authentication state
  async getAuthState(): Promise<StoredAuthState | null> {
    try {
      const storedState = await AsyncStorage.getItem(AUTH_STORAGE_KEY);
      if (storedState) {
        const authState: StoredAuthState = JSON.parse(storedState);
        logger.debug('Auth state retrieved:', authState);
        return authState;
      }
      return null;
    } catch (error) {
      logger.error('Error getting auth state:', error);
      return null;
    }
  },

  // Store user data
  async storeUserData(user: User): Promise<void> {
    try {
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      logger.debug('User data stored successfully');
    } catch (error) {
      logger.error('Error storing user data:', error);
    }
  },

  // Get stored user data
  async getUserData(): Promise<User | null> {
    try {
      const storedUser = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (storedUser) {
        const user: User = JSON.parse(storedUser);
        logger.debug('User data retrieved:', user.email);
        return user;
      }
      return null;
    } catch (error) {
      logger.error('Error getting user data:', error);
      return null;
    }
  },

  // Clear all stored auth data
  async clearAuthData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([AUTH_STORAGE_KEY, USER_STORAGE_KEY]);
      logger.debug('Auth data cleared successfully');
    } catch (error) {
      logger.error('Error clearing auth data:', error);
    }
  },

  // Check if session is still valid (within 30 days)
  isSessionValid(authState: StoredAuthState): boolean {
    if (!authState.isAuthenticated || !authState.lastLoginTime) {
      return false;
    }

    const lastLogin = new Date(authState.lastLoginTime);
    const now = new Date();
    const daysDiff = (now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60 * 24);
    
    // Consider session valid for 30 days
    return daysDiff <= 30;
  },
};
