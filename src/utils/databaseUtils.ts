import { supabase } from '../integrations/supabase/client';
import logger from './logger';
import NetInfo from '@react-native-community/netinfo';

/**
 * Database utility functions with error handling, retry logic, and offline support
 */

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

export interface DatabaseError {
  code: string;
  message: string;
  details?: any;
  hint?: string;
}

/**
 * Check if the device is online
 */
export const isOnline = async (): Promise<boolean> => {
  try {
    const netInfo = await NetInfo.fetch();
    return netInfo.isConnected === true && netInfo.isInternetReachable === true;
  } catch (error) {
    logger.error('Error checking network status:', error);
    return false;
  }
};

/**
 * Check if an error is retryable
 */
export const isRetryableError = (error: any): boolean => {
  if (!error) return false;

  // Network errors
  if (error.message?.includes('fetch') || error.message?.includes('network')) {
    return true;
  }

  // Supabase specific retryable errors
  const retryableCodes = [
    'PGRST301', // Connection timeout
    'PGRST302', // Connection failed
    '23505',    // Unique violation (can be retried with different data)
    '40001',    // Serialization failure
    '40P01',    // Deadlock detected
    '53300',    // Too many connections
    '08000',    // Connection exception
    '08003',    // Connection does not exist
    '08006',    // Connection failure
  ];

  return retryableCodes.includes(error.code) || error.status >= 500;
};

/**
 * Calculate delay for exponential backoff
 */
export const calculateDelay = (
  attempt: number,
  baseDelay: number = 1000,
  maxDelay: number = 30000,
  backoffFactor: number = 2
): number => {
  const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
  // Add jitter to prevent thundering herd
  return delay + Math.random() * 1000;
};

/**
 * Sleep for a given number of milliseconds
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry a database operation with exponential backoff
 */
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    backoffFactor = 2
  } = options;

  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Check if we're online before attempting the operation
      if (!(await isOnline())) {
        throw new Error('Device is offline');
      }

      const result = await operation();
      
      // Log successful retry if this wasn't the first attempt
      if (attempt > 0) {
        logger.info(`Operation succeeded after ${attempt} retries`);
      }
      
      return result;
    } catch (error: any) {
      lastError = error;
      
      logger.warn(`Operation failed on attempt ${attempt + 1}:`, error);

      // Don't retry if this is the last attempt or error is not retryable
      if (attempt === maxRetries || !isRetryableError(error)) {
        break;
      }

      // Calculate delay and wait before retrying
      const delay = calculateDelay(attempt, baseDelay, maxDelay, backoffFactor);
      logger.info(`Retrying operation in ${delay}ms...`);
      await sleep(delay);
    }
  }

  // All retries failed
  logger.error(`Operation failed after ${maxRetries + 1} attempts:`, lastError);
  throw lastError;
};

/**
 * Enhanced Supabase query with retry logic
 */
export const executeQuery = async <T>(
  queryBuilder: any,
  options: RetryOptions = {}
): Promise<{ data: T | null; error: DatabaseError | null }> => {
  try {
    const result = await retryOperation(async () => {
      const { data, error } = await queryBuilder;
      
      if (error) {
        throw {
          code: error.code || 'UNKNOWN_ERROR',
          message: error.message || 'Unknown database error',
          details: error.details,
          hint: error.hint
        } as DatabaseError;
      }
      
      return data;
    }, options);

    return { data: result, error: null };
  } catch (error: any) {
    return {
      data: null,
      error: {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || 'Unknown database error',
        details: error.details,
        hint: error.hint
      } as DatabaseError
    };
  }
};

/**
 * Enhanced Supabase mutation with retry logic
 */
export const executeMutation = async <T>(
  mutationBuilder: any,
  options: RetryOptions = {}
): Promise<{ data: T | null; error: DatabaseError | null }> => {
  return executeQuery<T>(mutationBuilder, options);
};

/**
 * Batch operations with transaction-like behavior
 */
export const executeBatch = async (
  operations: Array<() => Promise<any>>,
  options: RetryOptions = {}
): Promise<{ success: boolean; results: any[]; errors: DatabaseError[] }> => {
  const results: any[] = [];
  const errors: DatabaseError[] = [];

  try {
    await retryOperation(async () => {
      // Execute all operations
      for (const operation of operations) {
        try {
          const result = await operation();
          results.push(result);
        } catch (error: any) {
          const dbError: DatabaseError = {
            code: error.code || 'BATCH_OPERATION_ERROR',
            message: error.message || 'Batch operation failed',
            details: error.details,
            hint: error.hint
          };
          errors.push(dbError);
          throw dbError; // Fail fast on batch operations
        }
      }
    }, options);

    return { success: true, results, errors: [] };
  } catch (error: any) {
    return { success: false, results, errors };
  }
};

/**
 * Check database connection health
 */
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
      .single();

    return !error;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
};

/**
 * Get database connection info
 */
export const getDatabaseInfo = async (): Promise<{
  isHealthy: boolean;
  isOnline: boolean;
  latency?: number;
}> => {
  const startTime = Date.now();
  const online = await isOnline();
  const healthy = online ? await checkDatabaseHealth() : false;
  const latency = healthy ? Date.now() - startTime : undefined;

  return {
    isHealthy: healthy,
    isOnline: online,
    latency
  };
};

/**
 * Offline queue for storing operations when offline
 */
class OfflineQueue {
  private queue: Array<{
    id: string;
    operation: () => Promise<any>;
    timestamp: number;
    retries: number;
  }> = [];

  add(operation: () => Promise<any>): string {
    const id = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.queue.push({
      id,
      operation,
      timestamp: Date.now(),
      retries: 0
    });
    return id;
  }

  async processQueue(): Promise<void> {
    if (!(await isOnline())) {
      logger.info('Still offline, skipping queue processing');
      return;
    }

    logger.info(`Processing ${this.queue.length} offline operations`);

    const processedIds: string[] = [];

    for (const item of this.queue) {
      try {
        await item.operation();
        processedIds.push(item.id);
        logger.info(`Successfully processed offline operation ${item.id}`);
      } catch (error) {
        item.retries++;
        logger.error(`Failed to process offline operation ${item.id} (attempt ${item.retries}):`, error);
        
        // Remove operations that have failed too many times
        if (item.retries >= 3) {
          processedIds.push(item.id);
          logger.error(`Removing offline operation ${item.id} after 3 failed attempts`);
        }
      }
    }

    // Remove processed operations from queue
    this.queue = this.queue.filter(item => !processedIds.includes(item.id));
  }

  getQueueSize(): number {
    return this.queue.length;
  }

  clear(): void {
    this.queue = [];
  }
}

export const offlineQueue = new OfflineQueue();

/**
 * Execute operation with offline support
 */
export const executeWithOfflineSupport = async <T>(
  operation: () => Promise<T>,
  fallbackData?: T
): Promise<T> => {
  try {
    if (await isOnline()) {
      return await operation();
    } else {
      // Add to offline queue
      offlineQueue.add(operation);
      
      if (fallbackData !== undefined) {
        return fallbackData;
      }
      
      throw new Error('Operation requires internet connection');
    }
  } catch (error) {
    logger.error('Operation failed:', error);
    
    // If we have fallback data, return it
    if (fallbackData !== undefined) {
      return fallbackData;
    }
    
    throw error;
  }
};
