export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export const validateUsername = (username: string): ValidationResult => {
  if (!username.trim()) {
    return { isValid: false, error: 'Username is required' };
  }

  if (username.length < 3) {
    return { isValid: false, error: 'Username must be at least 3 characters long' };
  }

  if (username.length > 30) {
    return { isValid: false, error: 'Username cannot exceed 30 characters' };
  }

  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  if (!usernameRegex.test(username)) {
    return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };
  }

  return { isValid: true };
};

export const validateFullName = (fullName: string): ValidationResult => {
  if (!fullName.trim()) {
    return { isValid: false, error: 'Name is required' };
  }

  if (fullName.length > 50) {
    return { isValid: false, error: 'Name cannot exceed 50 characters' };
  }

  return { isValid: true };
};

export const validateBio = (bio: string): ValidationResult => {
  if (bio.length > 80) {
    return { isValid: false, error: '<PERSON><PERSON> cannot exceed 80 characters' };
  }

  return { isValid: true };
};

export const validateWebsite = (website: string): ValidationResult => {
  if (!website.trim()) {
    return { isValid: true }; // Website is optional
  }

  const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
  if (!urlRegex.test(website)) {
    return { isValid: false, error: 'Please enter a valid website URL' };
  }

  return { isValid: true };
};

export const validatePronouns = (pronouns: string): ValidationResult => {
  if (pronouns.length > 20) {
    return { isValid: false, error: 'Pronouns cannot exceed 20 characters' };
  }

  return { isValid: true };
};
