import { supabase } from '../integrations/supabase/client';
import logger from './logger';

/**
 * Database schema validation utilities
 */

export interface TableInfo {
  name: string;
  exists: boolean;
  columns?: string[];
  policies?: string[];
}

export interface SchemaValidationResult {
  isValid: boolean;
  missingTables: string[];
  missingColumns: { [table: string]: string[] };
  errors: string[];
}

/**
 * Required tables and their essential columns
 */
const REQUIRED_SCHEMA = {
  users: [
    'id',
    'auth_user_id',
    'username',
    'full_name',
    'email',
    'phone_number',
    'date_of_birth',
    'created_at',
    'updated_at'
  ],
  profiles: [
    'user_id',
    'bio',
    'profile_picture_url',
    'banner_image_url',
    'user_tag',
    'created_at',
    'updated_at'
  ],
  videos: [
    'id',
    'user_id',
    'title',
    'description',
    'video_url',
    'thumbnail_url',
    'duration_sec',
    'privacy_setting',
    'is_draft',
    'likes_count',
    'comments_count',
    'shares_count',
    'views_count',
    'created_at',
    'updated_at'
  ],
  video_likes: [
    'id',
    'video_id',
    'user_id',
    'created_at'
  ],
  comments: [
    'id',
    'video_id',
    'user_id',
    'parent_id',
    'text',
    'likes',
    'reply_count',
    'is_pinned',
    'created_at',
    'updated_at'
  ],
  comment_likes: [
    'id',
    'comment_id',
    'user_id',
    'created_at'
  ],
  user_follows: [
    'id',
    'follower_id',
    'following_id',
    'created_at'
  ],
  conversations: [
    'id',
    'type',
    'name',
    'created_by',
    'created_at',
    'updated_at'
  ],
  conversation_participants: [
    'id',
    'conversation_id',
    'user_id',
    'joined_at',
    'left_at'
  ],
  messages: [
    'id',
    'conversation_id',
    'sender_id',
    'content',
    'message_type',
    'media_url',
    'reply_to_id',
    'created_at',
    'updated_at'
  ],
  notification_settings: [
    'user_id',
    'push_notifications',
    'email_notifications',
    'like_notifications',
    'comment_notifications',
    'follow_notifications',
    'live_notifications',
    'message_notifications',
    'created_at',
    'updated_at'
  ],
  privacy_settings: [
    'user_id',
    'profile_visibility',
    'allow_messages_from',
    'show_online_status',
    'allow_downloads',
    'allow_duets',
    'allow_comments',
    'created_at',
    'updated_at'
  ],
  blocked_users: [
    'id',
    'blocker_id',
    'blocked_id',
    'created_at'
  ]
};

/**
 * Check if a table exists
 */
export const checkTableExists = async (tableName: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from(tableName)
      .select('*')
      .limit(0);

    return !error;
  } catch (error) {
    logger.error(`Error checking table ${tableName}:`, error);
    return false;
  }
};

/**
 * Get table columns
 */
export const getTableColumns = async (tableName: string): Promise<string[]> => {
  try {
    // Try to get a single row to inspect the structure
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    // If we have data, return the keys
    if (data) {
      return Object.keys(data);
    }

    // If no data, we can't determine columns this way
    // In a real implementation, you might query the information_schema
    return [];
  } catch (error) {
    logger.error(`Error getting columns for table ${tableName}:`, error);
    return [];
  }
};

/**
 * Validate database schema
 */
export const validateSchema = async (): Promise<SchemaValidationResult> => {
  const result: SchemaValidationResult = {
    isValid: true,
    missingTables: [],
    missingColumns: {},
    errors: []
  };

  try {
    // Check each required table
    for (const [tableName, requiredColumns] of Object.entries(REQUIRED_SCHEMA)) {
      logger.info(`Validating table: ${tableName}`);

      // Check if table exists
      const tableExists = await checkTableExists(tableName);
      
      if (!tableExists) {
        result.missingTables.push(tableName);
        result.isValid = false;
        logger.error(`Missing table: ${tableName}`);
        continue;
      }

      // Check columns
      const existingColumns = await getTableColumns(tableName);
      const missingColumns = requiredColumns.filter(
        col => !existingColumns.includes(col)
      );

      if (missingColumns.length > 0) {
        result.missingColumns[tableName] = missingColumns;
        result.isValid = false;
        logger.error(`Missing columns in ${tableName}:`, missingColumns);
      }
    }

    // Additional validation checks
    await validateConstraints(result);
    await validatePolicies(result);

  } catch (error: any) {
    result.errors.push(`Schema validation error: ${error.message}`);
    result.isValid = false;
    logger.error('Schema validation failed:', error);
  }

  return result;
};

/**
 * Validate database constraints
 */
const validateConstraints = async (result: SchemaValidationResult): Promise<void> => {
  try {
    // Test foreign key constraints by attempting to insert invalid data
    // This is a simplified check - in production you might query system tables

    // Check user_follows constraint
    const { error: followError } = await supabase
      .from('user_follows')
      .select('follower_id, following_id')
      .limit(1);

    if (followError && !followError.message.includes('relation') && !followError.code) {
      result.errors.push('user_follows table constraint validation failed');
    }

    // Check video_likes constraint
    const { error: likesError } = await supabase
      .from('video_likes')
      .select('video_id, user_id')
      .limit(1);

    if (likesError && !likesError.message.includes('relation') && !likesError.code) {
      result.errors.push('video_likes table constraint validation failed');
    }

  } catch (error: any) {
    result.errors.push(`Constraint validation error: ${error.message}`);
    logger.error('Constraint validation failed:', error);
  }
};

/**
 * Validate Row Level Security policies
 */
const validatePolicies = async (result: SchemaValidationResult): Promise<void> => {
  try {
    // Test RLS by attempting operations that should be restricted
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      result.errors.push('Cannot validate RLS policies - no authenticated user');
      return;
    }

    // Test profiles RLS
    const { error: profilesError } = await supabase
      .from('profiles')
      .select('user_id')
      .limit(1);

    if (profilesError && profilesError.message.includes('policy')) {
      result.errors.push('Profiles RLS policy validation failed');
    }

    // Test videos RLS
    const { error: videosError } = await supabase
      .from('videos')
      .select('id')
      .eq('privacy_setting', 'public')
      .limit(1);

    if (videosError && videosError.message.includes('policy')) {
      result.errors.push('Videos RLS policy validation failed');
    }

  } catch (error: any) {
    result.errors.push(`RLS policy validation error: ${error.message}`);
    logger.error('RLS policy validation failed:', error);
  }
};

/**
 * Generate schema validation report
 */
export const generateValidationReport = (result: SchemaValidationResult): string => {
  let report = '=== Database Schema Validation Report ===\n\n';

  if (result.isValid) {
    report += '✅ Schema validation PASSED\n';
    report += 'All required tables and columns are present.\n';
  } else {
    report += '❌ Schema validation FAILED\n\n';

    if (result.missingTables.length > 0) {
      report += 'Missing Tables:\n';
      result.missingTables.forEach(table => {
        report += `  - ${table}\n`;
      });
      report += '\n';
    }

    if (Object.keys(result.missingColumns).length > 0) {
      report += 'Missing Columns:\n';
      Object.entries(result.missingColumns).forEach(([table, columns]) => {
        report += `  ${table}:\n`;
        columns.forEach(column => {
          report += `    - ${column}\n`;
        });
      });
      report += '\n';
    }

    if (result.errors.length > 0) {
      report += 'Validation Errors:\n';
      result.errors.forEach(error => {
        report += `  - ${error}\n`;
      });
      report += '\n';
    }
  }

  report += `\nValidation completed at: ${new Date().toISOString()}`;
  return report;
};

/**
 * Run schema validation and log results
 */
export const runSchemaValidation = async (): Promise<boolean> => {
  logger.info('Starting database schema validation...');
  
  const result = await validateSchema();
  const report = generateValidationReport(result);
  
  if (result.isValid) {
    logger.info('Schema validation passed');
    logger.debug(report);
  } else {
    logger.error('Schema validation failed');
    logger.error(report);
  }

  return result.isValid;
};

/**
 * Initialize database connection and validate schema
 */
export const initializeDatabase = async (): Promise<{
  connected: boolean;
  schemaValid: boolean;
  errors: string[];
}> => {
  const errors: string[] = [];

  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      errors.push(`Database connection failed: ${error.message}`);
      return { connected: false, schemaValid: false, errors };
    }

    logger.info('Database connection established');

    // Validate schema
    const schemaValid = await runSchemaValidation();

    return {
      connected: true,
      schemaValid,
      errors
    };

  } catch (error: any) {
    errors.push(`Database initialization failed: ${error.message}`);
    logger.error('Database initialization failed:', error);
    
    return {
      connected: false,
      schemaValid: false,
      errors
    };
  }
};
