import { Platform } from 'react-native';
import logger from './logger';

export interface CameraRecordingConfig {
  flash: 'on' | 'off';
  fileType: 'mp4';
  videoCodec: 'h264';
  videoBitRate?: 'low' | 'normal' | 'high';
  videoStabilizationMode?: 'off' | 'standard' | 'cinematic' | 'auto';
  fps?: number;
}

export const getCameraRecordingConfig = (
  flashMode: 'off' | 'on' | 'auto',
  quality: 'low' | 'normal' | 'high' = 'normal'
): CameraRecordingConfig => {
  const baseConfig: CameraRecordingConfig = {
    flash: flashMode === 'on' ? 'on' : 'off',
    fileType: 'mp4',
    videoCodec: 'h264',
    videoBitRate: quality,
    videoStabilizationMode: 'auto',
  };

  // Android-specific optimizations to prevent MediaCodec errors
  if (Platform.OS === 'android') {
    logger.debug('Applying Android-specific camera optimizations');
    
    // Use more conservative settings for Android to avoid MediaCodec issues
    return {
      ...baseConfig,
      videoBitRate: quality === 'high' ? 'normal' : quality, // Reduce bitrate for stability
      videoStabilizationMode: 'standard', // Use standard stabilization
      fps: 30, // Lock to 30fps for better compatibility
    };
  }

  // iOS can handle higher quality settings more reliably
  return {
    ...baseConfig,
    fps: quality === 'high' ? 60 : 30,
  };
};

export const getRecommendedCameraSettings = () => {
  if (Platform.OS === 'android') {
    return {
      quality: 'normal' as const,
      stabilization: true,
      fps: 30,
      tips: [
        'Close other camera apps before recording',
        'Ensure sufficient storage space (at least 1GB free)',
        'Restart the app if recording fails repeatedly',
        'Try recording shorter videos if issues persist',
      ],
    };
  }

  return {
    quality: 'high' as const,
    stabilization: true,
    fps: 60,
    tips: [
      'Ensure sufficient storage space',
      'Use good lighting for best results',
    ],
  };
};

export const handleCameraError = (error: any): string => {
  logger.error('Camera error details:', error);

  // MediaCodec specific errors
  if (error.code === 'capture/recorder-error') {
    if (error.message?.includes('MediaCodec')) {
      return 'Camera recording failed due to device limitations. Please try restarting the app or freeing up storage space.';
    }
    return 'Recording failed. Please try again or restart the app.';
  }

  // Storage related errors
  if (error.code === 'capture/insufficient-storage') {
    return 'Not enough storage space. Please free up some space and try again.';
  }

  if (error.code === 'capture/file-io-error') {
    return 'Unable to save video file. Please check storage permissions.';
  }

  // Permission errors
  if (error.code === 'capture/no-recording-in-progress') {
    return 'No recording in progress. Please start recording first.';
  }

  if (error.code === 'permission/camera-permission-denied') {
    return 'Camera permission denied. Please enable camera access in settings.';
  }

  if (error.code === 'permission/microphone-permission-denied') {
    return 'Microphone permission denied. Please enable microphone access in settings.';
  }

  // Device capability errors
  if (error.code === 'device/camera-not-available') {
    return 'Camera is not available. Please check if another app is using it.';
  }

  if (error.code === 'device/microphone-unavailable') {
    return 'Microphone is not available. Please check if another app is using it.';
  }

  // Format/codec errors
  if (error.code === 'capture/invalid-photo-format') {
    return 'Invalid photo format. Please try again.';
  }

  if (error.code === 'capture/invalid-video-format') {
    return 'Invalid video format. Please try again.';
  }

  // Generic fallback
  return error.message || 'An unexpected camera error occurred. Please try again.';
};
