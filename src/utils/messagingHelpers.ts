import { NavigationProp } from '@react-navigation/native';
import { MainStackParamList } from '../navigation/types';
import { messagingApi } from '../store/api/messagingApi';
import { store } from '../store';
import logger from './logger';

/**
 * Helper function to start a conversation with a user
 * This can be called from user profiles, search results, etc.
 */
export const startConversationWithUser = async (
  userId: string,
  navigation: NavigationProp<MainStackParamList>
) => {
  try {
    logger.debug('Starting conversation with user:', userId);
    
    // Check if conversation already exists
    const state = store.getState();
    const apiState = state.messagingApi || {};
    const queries = apiState.queries || {};
    const conversationsQuery = queries['getConversations(undefined)'] || {};
    const conversations = conversationsQuery.data || [];
    
    // The userId here is the auth_user_id, but in our conversations we store public user IDs
    // We need to find the conversation where the other participant has this auth_user_id
    let existingConversation = null;
    for (const conv of conversations) {
      if (conv.other_participant?.id === userId) {
        existingConversation = conv;
        logger.debug('Found existing conversation:', conv.id);
        break;
      }
    }

    if (existingConversation) {
      // Navigate to existing conversation
      navigation.navigate('Chat', { conversationId: existingConversation.id });
      return;
    }

    logger.debug('Creating new conversation with user:', userId);
    
    // Create new conversation
    // The createConversation mutation now handles the conversion from auth_user_id to public user ID
    const result = await store.dispatch(
      messagingApi.endpoints.createConversation.initiate({ other_user_id: userId })
    ).unwrap();

    logger.debug('Created new conversation:', result);
    
    // Navigate to new conversation
    navigation.navigate('Chat', { conversationId: result });
  } catch (error) {
    logger.error('Error starting conversation:', error);
    throw new Error('Failed to start conversation');
  }
};

/**
 * Format timestamp for message display
 */
export const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInHours = diffInMs / (1000 * 60 * 60);
  const diffInDays = diffInHours / 24;

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    return diffInMinutes < 1 ? 'now' : `${diffInMinutes}m`;
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h`;
  } else if (diffInDays < 7) {
    return `${Math.floor(diffInDays)}d`;
  } else {
    return date.toLocaleDateString();
  }
};

/**
 * Format last message preview
 */
export const formatLastMessagePreview = (content: string, maxLength: number = 50): string => {
  if (content.length <= maxLength) {
    return content;
  }
  return content.substring(0, maxLength - 3) + '...';
};

/**
 * Get conversation display name
 */
export const getConversationDisplayName = (
  conversation: any,
  currentUserId: string
): string => {
  if (conversation.type === 'group') {
    return conversation.name || 'Group Chat';
  }
  
  // For direct conversations, return the other participant's name
  return conversation.other_participant?.full_name || 
         conversation.other_participant?.username || 
         'Unknown User';
};

/**
 * Check if user is online (this would need to be implemented with presence)
 */
export const isUserOnline = (userId: string): boolean => {
  // This would check against the online users from the messaging slice
  const onlineUsers = store.getState().messaging?.onlineUsers;
  return onlineUsers?.has(userId) || false;
};

// TikTok-style messaging helper functions

/**
 * Enhanced format last message preview for TikTok-style chat list
 */
export const formatLastMessagePreviewEnhanced = (message: any): string => {
  if (!message) return '';

  switch (message.type) {
    case 'image':
      return message.content ? `📷 ${message.content}` : '📷 Photo';

    case 'video':
      return message.content ? `🎥 ${message.content}` : '🎥 Video';

    case 'voice':
    case 'audio':
      return '🎵 Voice message';

    case 'gif':
      return '🎭 GIF';

    case 'sticker':
      return '😊 Sticker';

    case 'location':
      return '📍 Location';

    case 'contact':
      return '👤 Contact';

    case 'file':
      return `📎 ${message.file_name || 'File'}`;

    case 'system':
      return message.content || 'System message';

    default:
      return message.content || '';
  }
};

/**
 * Check if messages should be grouped (same sender, within time threshold)
 */
export const shouldGroupMessages = (
  currentMessage: any,
  previousMessage: any | null,
  timeThresholdMinutes: number = 5
): boolean => {
  if (!previousMessage) return false;

  // Different senders
  if (currentMessage.sender_id !== previousMessage.sender_id) return false;

  // System messages don't group
  if (currentMessage.type === 'system' || previousMessage.type === 'system') return false;

  // Check time difference
  const currentTime = new Date(currentMessage.created_at).getTime();
  const previousTime = new Date(previousMessage.created_at).getTime();
  const diffInMinutes = (currentTime - previousTime) / (1000 * 60);

  return diffInMinutes <= timeThresholdMinutes;
};

/**
 * Check if message should show timestamp
 */
export const shouldShowTimestamp = (
  currentMessage: any,
  nextMessage: any | null,
  timeThresholdMinutes: number = 5
): boolean => {
  if (!nextMessage) return true;

  // Different senders
  if (currentMessage.sender_id !== nextMessage.sender_id) return true;

  // Check time difference
  const currentTime = new Date(currentMessage.created_at).getTime();
  const nextTime = new Date(nextMessage.created_at).getTime();
  const diffInMinutes = (nextTime - currentTime) / (1000 * 60);

  return diffInMinutes > timeThresholdMinutes;
};

/**
 * Get reaction emoji for quick reactions - TikTok style
 */
export const getQuickReactionEmojis = (): string[] => {
  return ['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'];
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Format duration for audio/video messages
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Generate message date separator text
 */
export const getDateSeparatorText = (timestamp: string): string => {
  const messageDate = new Date(timestamp);
  const now = new Date();

  const isToday = now.toDateString() === messageDate.toDateString();
  if (isToday) {
    return 'Today';
  }

  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = yesterday.toDateString() === messageDate.toDateString();
  if (isYesterday) {
    return 'Yesterday';
  }

  const diffInDays = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays < 7) {
    return messageDate.toLocaleDateString([], { weekday: 'long' });
  }

  const isThisYear = messageDate.getFullYear() === now.getFullYear();

  if (isThisYear) {
    return messageDate.toLocaleDateString([], {
      month: 'long',
      day: 'numeric'
    });
  } else {
    return messageDate.toLocaleDateString([], {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }
};