// User mock data
export const mockUsers = [
  {
    id: '1',
    username: 'aymen',
    full_name: 'TikTok User',
    avatar_url: 'https://randomuser.me/api/portraits/men/1.jpg',
    bio: 'Just creating content for fun! 🎬',
    followers: 12500,
    following: 342,
    likes: 45600,
    is_following: false,
    videos : [
      {
        id: '1',
        thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
        likes: 12500,
        views: '1.2M',
      },
    ],
  },
  {
    id: '2',
    username: 'dancequeen',
    full_name: '<PERSON>',
    avatar_url: 'https://randomuser.me/api/portraits/women/44.jpg',
    bio: 'Professional dancer 💃\nDM for collabs!',
    followers: 84200,
    following: 512,
    likes: 1200000,
    is_following: true,
  },
  {
    id: '3',
    username: 'comedyking',
    full_name: '<PERSON>',
    avatar_url: 'https://randomuser.me/api/portraits/men/22.jpg',
    bio: 'Making people laugh since 2019',
    followers: 35600,
    following: 124,
    likes: 890000,
    is_following: false,
  },
];

// Video mock data
export const mockVideos = [
  {
    id: '1',
    user: mockUsers[0],
    uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    description: 'Check out this cool trick! #shorts',
    song: 'Original Sound - TikTok User',
    likes: 12500,
    comments: 342,
    shares: 456,
    views: '1.2M',
    timestamp: '2023-05-15T14:30:00Z',
  },
  {
    id: '2',
    user: mockUsers[1],
    uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    thumbnail: 'https://i.ytimg.com/vi/yPYZpwSpKmA/maxresdefault.jpg',
    description: 'New dance challenge! Try it out 💃 #dance #viral',
    song: 'Popular Song - Famous Artist',
    likes: 84200,
    comments: 1200,
    shares: 2400,
    views: '5.7M',
    timestamp: '2023-05-10T09:15:00Z',
  },
  {
    id: '3',
    user: mockUsers[2],
    uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    thumbnail: 'https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg',
    description: 'When you try to be cool but fail miserably 😂',
    song: 'Funny Sound Effect',
    likes: 35600,
    comments: 890,
    shares: 1200,
    views: '2.1M',
    timestamp: '2023-05-05T18:45:00Z',
  },
];

// Conversation mock data
export const mockConversations = [
  {
    id: '1',
    user: mockUsers[1],
    lastMessage: 'Hey! How are you doing?',
    timestamp: '2023-05-15T14:30:00Z',
    unread: true,
    messages: [
      {
        id: '1',
        text: 'Hey there!',
        isMe: false,
        timestamp: '2023-05-15T14:25:00Z',
      },
      {
        id: '2',
        text: 'Hey! How are you doing?',
        isMe: false,
        timestamp: '2023-05-15T14:30:00Z',
      },
    ],
  },
  {
    id: '2',
    user: mockUsers[2],
    lastMessage: 'Check out my new video!',
    timestamp: '2023-05-14T10:15:00Z',
    unread: false,
    messages: [
      {
        id: '1',
        text: 'Hi Mike!',
        isMe: true,
        timestamp: '2023-05-14T10:10:00Z',
      },
      {
        id: '2',
        text: 'Check out my new video!',
        isMe: false,
        timestamp: '2023-05-14T10:15:00Z',
      },
    ],
  },
];

// Notification mock data
export const mockNotifications = [
  {
    id: '1',
    type: 'like',
    user: mockUsers[0],
    video: mockVideos[0],
    timestamp: '2023-05-15T15:30:00Z',
    read: false,
  },
  {
    id: '2',
    type: 'comment',
    user: mockUsers[1],
    video: mockVideos[1],
    comment: 'This is amazing!',
    timestamp: '2023-05-15T14:45:00Z',
    read: true,
  },
  {
    id: '3',
    type: 'follow',
    user: mockUsers[2],
    timestamp: '2023-05-15T12:20:00Z',
    read: false,
  },
];

// Trending topics mock data
export const mockTrendingTopics = [
  {
    id: '1',
    name: '#SummerVibes',
    views: '120M',
  },
  {
    id: '2',
    name: '#DanceChallenge',
    views: '85.4M',
  },
  {
    id: '3',
    name: '#FoodTok',
    views: '72.1M',
  },
  {
    id: '4',
    name: '#POV',
    views: '68.9M',
  },
  {
    id: '5',
    name: '#Comedy',
    views: '55.3M',
  },
];

// Current user mock data
export const currentUser = {
  id: 'current-user',
  username: 'yourusername',
  full_name: 'Your Name',
  avatar_url: 'https://randomuser.me/api/portraits/men/32.jpg',
  bio: 'Just having fun on TikTok!',
  followers: 1243,
  following: 567,
  likes: 8765,
  videos: mockVideos.filter(video => video.user.id === '1'),
};

// For profile screens
export const mockFollowers = mockUsers.slice(0, 3);
export const mockFollowing = mockUsers.slice(1, 4);
