/**
 * Configuration module to manage environment variables and application settings.
 * This module exports a configuration object that contains settings for Supabase,
 * social media integrations, and general application information.
 */


// Note: dotenv is not available in React Native environment
// Environment variables should be configured through metro.config.js or build process

export const config = {
  supabase: {
    url: process.env.SUPABASE_URL || '',
    anonKey: process.env.SUPABASE_ANON_KEY || '',
  },
  social: {
    googleWebClientId: process.env.GOOGLE_WEB_CLIENT_ID || '',
    facebookAppId: process.env.FACEBOOK_APP_ID || '',
  },
  app: {
    name: process.env.APP_NAME || 'TS1',
    version: process.env.APP_VERSION || '1.0.0',
  },
};
