import { supabase } from '../../integrations/supabase/client';
import { Platform } from 'react-native';
import logger from '../../utils/logger';

/**
 * Twilio Verify Service
 * Wrapper for Twilio Verify edge functions
 */

/**
 * Log security activity
 */
const logSecurityActivity = async (activityType: string) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {return;}

    const deviceInfo = Platform.OS === 'ios' ? 'iOS Device' : 'Android Device';

    await supabase
      .from('security_activities')
      .insert({
        user_id: user.id,
        activity_type: activityType,
        ip_address: '0.0.0.0', // Would get real IP in production
        device_info: deviceInfo,
        location: 'Unknown', // Would use geolocation in production
      });
  } catch (error) {
    logger.error('Failed to log security activity:', error);
  }
};

export interface VerificationResult {
  success: boolean;
  error?: string;
  sid?: string;
}

export interface VerificationCheckResult {
  success: boolean;
  error?: string;
  valid?: boolean;
}

/**
 * Send SMS verification code using Twilio Verify
 */
export const sendSMSVerification = async (phoneNumber: string): Promise<VerificationResult> => {
  try {
    const { data, error } = await supabase.functions.invoke('send-sms-verification', {
      body: { phoneNumber },
    });

    if (error) {
      logger.error('Twilio Verify send error:', error);
      // Log failed attempt
      await logSecurityActivity('phone_verification_failed');
      return {
        success: false,
        error: error.message || 'Failed to send verification code',
      };
    }

    if (data?.success) {
      // Log successful SMS send
      await logSecurityActivity('phone_verification_sent');
      return {
        success: true,
        sid: data.sid,
      };
    } else {
      await logSecurityActivity('phone_verification_failed');
      return {
        success: false,
        error: data?.error || 'Failed to send verification code',
      };
    }
  } catch (error: any) {
    logger.error('SMS verification error:', error);
    await logSecurityActivity('phone_verification_failed');
    return {
      success: false,
      error: error.message || 'Network error occurred',
    };
  }
};

/**
 * Verify SMS code using Twilio Verify
 */
export const verifySMSCode = async (
  phoneNumber: string,
  code: string
): Promise<VerificationCheckResult> => {
  try {
    const { data, error } = await supabase.functions.invoke('verify-sms-code', {
      body: {
        phoneNumber,
        code,
      },
    });

    if (error) {
      logger.error('Twilio Verify check error:', error);
      await logSecurityActivity('phone_verification_failed');
      return {
        success: false,
        error: error.message || 'Failed to verify code',
      };
    }

    if (data?.success) {
      await logSecurityActivity('phone_verification_success');
      return {
        success: true,
        valid: true,
      };
    } else {
      await logSecurityActivity('phone_verification_failed');
      return {
        success: false,
        valid: false,
        error: data?.error || 'Invalid verification code',
      };
    }
  } catch (error: any) {
    logger.error('SMS verification check error:', error);
    await logSecurityActivity('phone_verification_failed');
    return {
      success: false,
      error: error.message || 'Network error occurred',
    };
  }
};

/**
 * Send verification code for 2FA login
 * This could be SMS or other methods depending on user preference
 */
export const send2FAVerification = async (
  phoneNumber: string,
  method: 'sms' | 'call' = 'sms'
): Promise<VerificationResult> => {
  try {
    // Use the same SMS verification endpoint for 2FA
    const { data, error } = await supabase.functions.invoke('send-sms-verification', {
      body: { phoneNumber },
    });

    if (error) {
      logger.error('2FA send error:', error);
      await logSecurityActivity('2fa_sms_failed');
      return {
        success: false,
        error: error.message || 'Failed to send 2FA code',
      };
    }

    if (data?.success) {
      await logSecurityActivity('2fa_sms_sent');
      return {
        success: true,
        sid: data.sid,
      };
    } else {
      await logSecurityActivity('2fa_sms_failed');
      return {
        success: false,
        error: data?.error || 'Failed to send 2FA code',
      };
    }
  } catch (error: any) {
    logger.error('2FA verification error:', error);
    await logSecurityActivity('2fa_sms_failed');
    return {
      success: false,
      error: error.message || 'Network error occurred',
    };
  }
};

/**
 * Verify 2FA code
 */
export const verify2FACode = async (
  phoneNumber: string,
  code: string
): Promise<VerificationCheckResult> => {
  try {
    // Use the same SMS verification endpoint for 2FA verification
    const { data, error } = await supabase.functions.invoke('verify-sms-code', {
      body: {
        phoneNumber,
        code,
      },
    });

    if (error) {
      logger.error('2FA verify error:', error);
      await logSecurityActivity('2fa_login_failed');
      return {
        success: false,
        error: error.message || 'Failed to verify 2FA code',
      };
    }

    if (data?.success) {
      await logSecurityActivity('2fa_login_success');
      return {
        success: true,
        valid: true,
      };
    } else {
      await logSecurityActivity('2fa_login_failed');
      return {
        success: false,
        valid: false,
        error: data?.error || 'Invalid 2FA code',
      };
    }
  } catch (error: any) {
    logger.error('2FA verification check error:', error);
    await logSecurityActivity('2fa_login_failed');
    return {
      success: false,
      error: error.message || 'Network error occurred',
    };
  }
};
