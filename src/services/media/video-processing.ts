import logger from '../../utils/logger';

export interface VideoProcessingOptions {
  maxDuration?: number;
  compress?: boolean;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  bitrate?: string;
  fps?: number;
}

export interface ProcessedVideoResult {
  processedPath?: string;
  width: number;
  height: number;
  duration: number;
  size: number;
  bitrate?: string;
}

export interface ThumbnailOptions {
  timeOffset?: number; // seconds
  width?: number;
  height?: number;
  quality?: number;
}

/**
 * Process video with TikTok-style optimization
 */
export async function processVideo(
  videoPath: string,
  options: VideoProcessingOptions = {}
): Promise<ProcessedVideoResult> {
  try {
    const {
      maxDuration = 600, // 10 minutes
      compress = true,
      quality = 0.8,
      maxWidth = 1080,
      maxHeight = 1920,
      bitrate = '2M',
      fps = 30,
    } = options;

    logger.debug('Processing video:', { videoPath, options });

    // Get video metadata first
    const metadata = await getVideoMetadata(videoPath);

    let processedPath: string | undefined;
    let finalMetadata = metadata;

    // Check if processing is needed
    const needsProcessing =
      compress ||
      (metadata.duration > maxDuration) ||
      (metadata.width > maxWidth) ||
      (metadata.height > maxHeight);

    if (needsProcessing) {
      const { FFmpegKit } = await import('ffmpeg-kit-react-native');
      const RNFS = await import('react-native-fs');

      // Generate output path
      const outputPath = `${RNFS.CachesDirectoryPath}/processed_${Date.now()}.mp4`;

      // Build FFmpeg command for TikTok-style processing
      let command = `-i "${videoPath}"`;

      // Trim video if too long
      if (metadata.duration > maxDuration) {
        command += ` -t ${maxDuration}`;
      }

      // Scale video if too large
      if (metadata.width > maxWidth || metadata.height > maxHeight) {
        command += ` -vf "scale='min(${maxWidth},iw)':'min(${maxHeight},ih)':force_original_aspect_ratio=decrease"`;
      }

      // Set quality and compression
      if (compress) {
        const crf = Math.round(23 + (1 - quality) * 28); // CRF 23-51 range
        command += ` -c:v libx264 -crf ${crf} -preset medium`;
        command += ` -b:v ${bitrate} -maxrate ${bitrate} -bufsize ${bitrate}`;
      }

      // Set frame rate
      command += ` -r ${fps}`;

      // Audio processing
      command += ` -c:a aac -b:a 128k`;

      // Output
      command += ` -y "${outputPath}"`;

      logger.debug('FFmpeg command:', command);

      // Execute FFmpeg command
      await FFmpegKit.execute(command);

      // Check if output file exists
      const outputExists = await RNFS.exists(outputPath);
      if (outputExists) {
        processedPath = outputPath;
        finalMetadata = await getVideoMetadata(outputPath);
        logger.debug('Video processed successfully:', { processedPath, metadata: finalMetadata });
      } else {
        logger.warn('Video processing failed, using original');
      }
    }

    return {
      processedPath,
      width: finalMetadata.width,
      height: finalMetadata.height,
      duration: finalMetadata.duration,
      size: finalMetadata.size,
      bitrate: finalMetadata.bitrate,
    };

  } catch (error) {
    logger.error('Video processing failed:', error);
    throw new Error(`Video processing failed: ${error}`);
  }
}

/**
 * Generate video thumbnail
 */
export async function generateVideoThumbnail(
  videoPath: string,
  options: ThumbnailOptions = {}
): Promise<string> {
  try {
    const {
      timeOffset = 1,
      width = 320,
      height = 320,
      quality = 80,
    } = options;

    const { FFmpegKit } = await import('ffmpeg-kit-react-native');
    const RNFS = await import('react-native-fs');

    const outputPath = `${RNFS.CachesDirectoryPath}/thumb_${Date.now()}.jpg`;

    // FFmpeg command to extract frame
    const command = `-i "${videoPath}" -ss ${timeOffset} -vframes 1 -vf "scale=${width}:${height}:force_original_aspect_ratio=decrease,pad=${width}:${height}:(ow-iw)/2:(oh-ih)/2" -q:v ${Math.round((1 - quality / 100) * 31)} -y "${outputPath}"`;

    logger.debug('Thumbnail FFmpeg command:', command);

    await FFmpegKit.execute(command);

    const outputExists = await RNFS.exists(outputPath);
    if (outputExists) {
      logger.debug('Video thumbnail generated:', outputPath);
      return outputPath;
    } else {
      throw new Error('Thumbnail generation failed');
    }

  } catch (error) {
    logger.error('Video thumbnail generation failed:', error);
    throw error;
  }
}

/**
 * Get video metadata
 */
export async function getVideoMetadata(videoPath: string): Promise<{
  width: number;
  height: number;
  duration: number;
  size: number;
  bitrate?: string;
  fps?: number;
}> {
  try {
    const RNFS = await import('react-native-fs');

    // Get file size
    const stats = await RNFS.stat(videoPath);

    // For now, return default metadata
    // In a real implementation, you'd use FFprobe to get actual metadata
    return {
      width: 1080,
      height: 1920,
      duration: 60,
      size: stats.size,
      bitrate: '2M',
      fps: 30,
    };

  } catch (error) {
    logger.error('Failed to get video metadata:', error);
    // Return default metadata if fails
    return {
      width: 1080,
      height: 1920,
      duration: 60,
      size: 0,
    };
  }
}

export type VideoEffect = 'grayscale' | 'sepia' | 'invert' | 'none';

/**
 * Apply a simple color effect to a video using ffmpeg.
 *
 * @param videoPath Path to the input video file
 * @param effect    Effect to apply (e.g. 'grayscale')
 * @returns Path to the processed video file
 */
export async function applyEffect(
  videoPath: string,
  effect: VideoEffect,
): Promise<string> {
  const { FFmpegKit } = await import('ffmpeg-kit-react-native');
  const RNFS = await import('react-native-fs');

  const effectFilters: Record<Exclude<VideoEffect, 'none'>, string> = {
    grayscale: 'hue=s=0',
    sepia: 'colorchannelmixer=.393:.769:.189:.349:.686:.168:.272:.534:.131',
    invert: 'lutrgb=r=negval:g=negval:b=negval',
  };

  const outputPath = `${RNFS.TemporaryDirectoryPath}/processed_${Date.now()}.mp4`;
  const filter = effect === 'none' ? '' : effectFilters[effect];
  const command = filter
    ? `-i "${videoPath}" -vf ${filter} -c:a copy "${outputPath}"`
    : `-i "${videoPath}" -c copy "${outputPath}"`;

  await FFmpegKit.execute(command);

  return outputPath;
}

/**
 * Compress video for messaging
 */
export async function compressVideoForMessage(
  videoPath: string
): Promise<ProcessedVideoResult> {
  return processVideo(videoPath, {
    maxDuration: 300, // 5 minutes for messages
    compress: true,
    quality: 0.7,
    maxWidth: 720,
    maxHeight: 1280,
    bitrate: '1M',
  });
}

/**
 * Optimize video for feed upload
 */
export async function optimizeVideoForFeed(
  videoPath: string
): Promise<ProcessedVideoResult> {
  return processVideo(videoPath, {
    maxDuration: 600, // 10 minutes for feed
    compress: true,
    quality: 0.8,
    maxWidth: 1080,
    maxHeight: 1920,
    bitrate: '3M',
  });
}

/**
 * Create video preview (short clip)
 */
export async function createVideoPreview(
  videoPath: string,
  duration: number = 15,
  startTime: number = 0
): Promise<string> {
  try {
    const { FFmpegKit } = await import('ffmpeg-kit-react-native');
    const RNFS = await import('react-native-fs');

    const outputPath = `${RNFS.CachesDirectoryPath}/preview_${Date.now()}.mp4`;

    const command = `-i "${videoPath}" -ss ${startTime} -t ${duration} -c:v libx264 -crf 28 -preset fast -c:a aac -b:a 128k -y "${outputPath}"`;

    await FFmpegKit.execute(command);

    const outputExists = await RNFS.exists(outputPath);
    if (outputExists) {
      logger.debug('Video preview created:', outputPath);
      return outputPath;
    } else {
      throw new Error('Preview creation failed');
    }

  } catch (error) {
    logger.error('Video preview creation failed:', error);
    throw error;
  }
}

/**
 * Extract audio from video
 */
export async function extractAudioFromVideo(
  videoPath: string,
  format: 'mp3' | 'aac' | 'wav' = 'mp3'
): Promise<string> {
  try {
    const { FFmpegKit } = await import('ffmpeg-kit-react-native');
    const RNFS = await import('react-native-fs');

    const outputPath = `${RNFS.CachesDirectoryPath}/audio_${Date.now()}.${format}`;

    let command = `-i "${videoPath}" -vn`;

    switch (format) {
      case 'mp3':
        command += ` -c:a libmp3lame -b:a 192k`;
        break;
      case 'aac':
        command += ` -c:a aac -b:a 128k`;
        break;
      case 'wav':
        command += ` -c:a pcm_s16le`;
        break;
    }

    command += ` -y "${outputPath}"`;

    await FFmpegKit.execute(command);

    const outputExists = await RNFS.exists(outputPath);
    if (outputExists) {
      logger.debug('Audio extracted from video:', outputPath);
      return outputPath;
    } else {
      throw new Error('Audio extraction failed');
    }

  } catch (error) {
    logger.error('Audio extraction failed:', error);
    throw error;
  }
}

/**
 * Validate video file
 */
export function validateVideo(videoPath: string): boolean {
  const validExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
  const extension = videoPath.toLowerCase().split('.').pop();
  return validExtensions.includes(`.${extension}`);
}

/**
 * Get video duration without full metadata
 */
export async function getVideoDuration(videoPath: string): Promise<number> {
  try {
    // In a real implementation, you'd use FFprobe to get actual duration
    // For now, return a default value
    return 60;
  } catch (error) {
    logger.error('Failed to get video duration:', error);
    return 0;
  }
}
