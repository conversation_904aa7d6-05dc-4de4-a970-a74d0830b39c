import AudioRecorderPlayer, {
  AudioSet,
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  OutputFormatAndroidType,
  AudioEncoderAndroidType,
} from 'react-native-audio-recorder-player';
import { Platform } from 'react-native';
import logger from '../../utils/logger';

const recorder = new AudioRecorderPlayer();

export interface AudioRecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  path: string | null;
}

let recordingState: AudioRecordingState = {
  isRecording: false,
  isPaused: false,
  duration: 0,
  path: null,
};

let recordingUpdateCallback: ((state: AudioRecordingState) => void) | null = null;

export function setRecordingUpdateCallback(callback: (state: AudioRecordingState) => void | null) {
  recordingUpdateCallback = callback;
}

export function getRecordingState(): AudioRecordingState {
  return { ...recordingState };
}

export async function startRecording(): Promise<string> {
  try {
    logger.debug('Starting audio recording...');

    const audioSet: AudioSet = {
      AVSampleRateKeyIOS: 44100,
      AVFormatIDKeyIOS: AVEncodingOption.aac,
      AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
      OutputFormatAndroid: OutputFormatAndroidType.MPEG_4,
      AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
    };

    const result = await recorder.startRecorder(undefined, audioSet);

    recorder.addRecordBackListener(e => {
      recordingState.duration = e.currentPosition;
      recordingUpdateCallback?.(recordingState);
    });

    recordingState = {
      isRecording: true,
      isPaused: false,
      duration: 0,
      path: Platform.OS === 'android' ? result : result.replace('file://', ''),
    };

    recordingUpdateCallback?.(recordingState);
    logger.debug('Audio recording started:', recordingState.path);

    return recordingState.path!;
  } catch (error) {
    logger.error('Failed to start recording:', error);
    throw error;
  }
}

export async function pauseRecording(): Promise<void> {
  try {
    if (!recordingState.isRecording || recordingState.isPaused) {
      throw new Error('No active recording to pause');
    }

    logger.debug('Pausing audio recording...');
    await recorder.pauseRecorder();

    recordingState.isPaused = true;
    recordingUpdateCallback?.(recordingState);
    logger.debug('Audio recording paused');
  } catch (error) {
    logger.error('Failed to pause recording:', error);
    throw error;
  }
}

export async function resumeRecording(): Promise<void> {
  try {
    if (!recordingState.isRecording || !recordingState.isPaused) {
      throw new Error('No paused recording to resume');
    }

    logger.debug('Resuming audio recording...');
    await recorder.resumeRecorder();

    recordingState.isPaused = false;
    recordingUpdateCallback?.(recordingState);
    logger.debug('Audio recording resumed');
  } catch (error) {
    logger.error('Failed to resume recording:', error);
    throw error;
  }
}

export async function stopRecording(): Promise<{ path: string; duration: number }> {
  try {
    if (!recordingState.isRecording) {
      throw new Error('No recording in progress');
    }

    logger.debug('Stopping audio recording...');
    await recorder.stopRecorder();
    recorder.removeRecordBackListener();

    const result = {
      path: recordingState.path!,
      duration: recordingState.duration
    };

    // Reset state
    recordingState = {
      isRecording: false,
      isPaused: false,
      duration: 0,
      path: null,
    };

    recordingUpdateCallback?.(recordingState);
    logger.debug('Audio recording stopped:', result);

    return result;
  } catch (error) {
    logger.error('Failed to stop recording:', error);
    throw error;
  }
}

export async function cancelRecording(): Promise<void> {
  try {
    if (!recordingState.isRecording) {
      return; // Nothing to cancel
    }

    logger.debug('Cancelling audio recording...');
    await recorder.stopRecorder();
    recorder.removeRecordBackListener();

    // Reset state
    recordingState = {
      isRecording: false,
      isPaused: false,
      duration: 0,
      path: null,
    };

    recordingUpdateCallback?.(recordingState);
    logger.debug('Audio recording cancelled');
  } catch (error) {
    logger.error('Failed to cancel recording:', error);
    throw error;
  }
}

// TikTok-style audio processing functions

export interface AudioProcessingOptions {
  maxDuration?: number;
  quality?: number;
  bitrate?: string;
  sampleRate?: number;
  channels?: number;
  format?: 'mp3' | 'aac' | 'wav' | 'm4a';
  generateWaveform?: boolean;
}

export interface ProcessedAudioResult {
  processedPath?: string;
  duration: number;
  size: number;
  bitrate?: string;
  sampleRate?: number;
  waveformData?: number[];
}

/**
 * Process audio with TikTok-style optimization
 */
export async function processAudio(
  audioPath: string,
  options: AudioProcessingOptions = {}
): Promise<ProcessedAudioResult> {
  try {
    const {
      maxDuration = 300, // 5 minutes max
      quality = 0.8,
      bitrate = '128k',
      sampleRate = 44100,
      channels = 2,
      format = 'aac',
      generateWaveform = true,
    } = options;

    logger.debug('Processing audio:', { audioPath, options });

    // Get audio metadata first
    const metadata = await getAudioMetadata(audioPath);

    let processedPath: string | undefined;
    let finalMetadata = metadata;
    let waveformData: number[] | undefined;

    // Check if processing is needed
    const needsProcessing =
      (metadata.duration > maxDuration) ||
      quality < 1.0;

    if (needsProcessing) {
      const { FFmpegKit } = await import('ffmpeg-kit-react-native');
      const RNFS = await import('react-native-fs');

      // Generate output path
      const outputPath = `${RNFS.CachesDirectoryPath}/processed_audio_${Date.now()}.${format}`;

      // Build FFmpeg command for audio processing
      let command = `-i "${audioPath}"`;

      // Trim audio if too long
      if (metadata.duration > maxDuration) {
        command += ` -t ${maxDuration}`;
      }

      // Set audio codec and quality
      switch (format) {
        case 'mp3':
          command += ` -c:a libmp3lame -b:a ${bitrate}`;
          break;
        case 'aac':
          command += ` -c:a aac -b:a ${bitrate}`;
          break;
        case 'm4a':
          command += ` -c:a aac -b:a ${bitrate}`;
          break;
        case 'wav':
          command += ` -c:a pcm_s16le`;
          break;
      }

      // Set sample rate and channels
      command += ` -ar ${sampleRate} -ac ${channels}`;

      // Output
      command += ` -y "${outputPath}"`;

      logger.debug('Audio FFmpeg command:', command);

      // Execute FFmpeg command
      await FFmpegKit.execute(command);

      // Check if output file exists
      const outputExists = await RNFS.exists(outputPath);
      if (outputExists) {
        processedPath = outputPath;
        finalMetadata = await getAudioMetadata(outputPath);
        logger.debug('Audio processed successfully:', { processedPath, metadata: finalMetadata });
      } else {
        logger.warn('Audio processing failed, using original');
      }
    }

    // Generate waveform data if requested
    if (generateWaveform) {
      waveformData = await generateWaveformData(processedPath || audioPath);
    }

    return {
      processedPath,
      duration: finalMetadata.duration,
      size: finalMetadata.size,
      bitrate: finalMetadata.bitrate,
      sampleRate: finalMetadata.sampleRate,
      waveformData,
    };

  } catch (error) {
    logger.error('Audio processing failed:', error);
    throw new Error(`Audio processing failed: ${error}`);
  }
}

/**
 * Get audio metadata
 */
export async function getAudioMetadata(audioPath: string): Promise<{
  duration: number;
  size: number;
  bitrate?: string;
  sampleRate?: number;
  channels?: number;
}> {
  try {
    const RNFS = await import('react-native-fs');

    // Get file size
    const stats = await RNFS.stat(audioPath);

    // For now, return default metadata
    // In a real implementation, you'd use FFprobe to get actual metadata
    return {
      duration: 60,
      size: stats.size,
      bitrate: '128k',
      sampleRate: 44100,
      channels: 2,
    };

  } catch (error) {
    logger.error('Failed to get audio metadata:', error);
    return {
      duration: 60,
      size: 0,
      bitrate: '128k',
      sampleRate: 44100,
      channels: 2,
    };
  }
}

/**
 * Get audio duration
 */
export async function getAudioDuration(audioPath: string): Promise<number> {
  try {
    const metadata = await getAudioMetadata(audioPath);
    return metadata.duration;
  } catch (error) {
    logger.error('Failed to get audio duration:', error);
    return 0;
  }
}

/**
 * Generate waveform data for TikTok-style audio visualization
 */
export async function generateWaveformData(
  audioPath: string,
  samples: number = 100
): Promise<number[]> {
  try {
    // In a real implementation, you'd use FFmpeg or a dedicated audio library
    // to extract amplitude data for waveform visualization
    // For now, generate mock waveform data
    const waveform: number[] = [];
    for (let i = 0; i < samples; i++) {
      waveform.push(Math.random() * 0.8 + 0.1); // Random values between 0.1 and 0.9
    }

    logger.debug('Waveform data generated:', { samples: waveform.length });
    return waveform;

  } catch (error) {
    logger.error('Waveform generation failed:', error);
    return [];
  }
}

/**
 * Compress audio for voice messages
 */
export async function compressAudioForVoiceMessage(
  audioPath: string
): Promise<ProcessedAudioResult> {
  return processAudio(audioPath, {
    maxDuration: 60, // 1 minute for voice messages
    quality: 0.7,
    bitrate: '64k', // Lower bitrate for voice
    sampleRate: 22050, // Lower sample rate for voice
    channels: 1, // Mono for voice
    format: 'aac',
    generateWaveform: true,
  });
}

/**
 * Process audio for music sharing
 */
export async function processAudioForMusic(
  audioPath: string
): Promise<ProcessedAudioResult> {
  return processAudio(audioPath, {
    maxDuration: 180, // 3 minutes for music
    quality: 0.9,
    bitrate: '192k', // Higher bitrate for music
    sampleRate: 44100,
    channels: 2, // Stereo for music
    format: 'aac',
    generateWaveform: true,
  });
}

/**
 * Validate audio file
 */
export function validateAudio(audioPath: string): boolean {
  const validExtensions = ['.mp3', '.aac', '.wav', '.m4a', '.ogg', '.flac'];
  const extension = audioPath.toLowerCase().split('.').pop();
  return validExtensions.includes(`.${extension}`);
}
