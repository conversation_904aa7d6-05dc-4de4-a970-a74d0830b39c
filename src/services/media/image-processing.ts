import { ImageResizer } from 'react-native-image-resizer';
import RNFS from 'react-native-fs';
import logger from '../../utils/logger';

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'JPEG' | 'PNG' | 'WEBP';
  rotation?: number;
  keepMeta?: boolean;
}

export interface ProcessedImageResult {
  path: string;
  width: number;
  height: number;
  size: number;
}

/**
 * Process image with TikTok-style optimization
 */
export async function processImage(
  imagePath: string,
  options: ImageProcessingOptions = {}
): Promise<ProcessedImageResult> {
  try {
    const {
      maxWidth = 1080,
      maxHeight = 1080,
      quality = 80,
      format = 'JPEG',
      rotation = 0,
      keepMeta = false,
    } = options;

    logger.debug('Processing image:', { imagePath, options });

    // Use react-native-image-resizer for processing
    const result = await ImageResizer.createResizedImage(
      imagePath,
      maxWidth,
      maxHeight,
      format,
      quality,
      rotation,
      undefined, // outputPath - let it generate
      keepMeta
    );

    // Get file size
    const stats = await RNFS.stat(result.path);

    const processedResult: ProcessedImageResult = {
      path: result.path,
      width: result.width,
      height: result.height,
      size: stats.size,
    };

    logger.debug('Image processed successfully:', processedResult);
    return processedResult;

  } catch (error) {
    logger.error('Image processing failed:', error);
    throw new Error(`Image processing failed: ${error}`);
  }
}

/**
 * Generate thumbnail from image
 */
export async function generateImageThumbnail(
  imagePath: string,
  size: number = 320
): Promise<string> {
  try {
    const result = await ImageResizer.createResizedImage(
      imagePath,
      size,
      size,
      'JPEG',
      70, // Lower quality for thumbnails
      0,
      undefined,
      false
    );

    logger.debug('Image thumbnail generated:', result.path);
    return result.path;

  } catch (error) {
    logger.error('Thumbnail generation failed:', error);
    throw new Error(`Thumbnail generation failed: ${error}`);
  }
}

/**
 * Compress image for TikTok-style messaging
 */
export async function compressImageForMessage(
  imagePath: string,
  quality: number = 75
): Promise<ProcessedImageResult> {
  return processImage(imagePath, {
    maxWidth: 1080,
    maxHeight: 1080,
    quality,
    format: 'JPEG',
  });
}

/**
 * Process image for profile picture with circular crop simulation
 */
export async function processProfileImage(
  imagePath: string,
  size: number = 400
): Promise<ProcessedImageResult> {
  return processImage(imagePath, {
    maxWidth: size,
    maxHeight: size,
    quality: 90,
    format: 'JPEG',
  });
}

/**
 * Apply TikTok-style image filters (basic implementation)
 */
export async function applyImageFilter(
  imagePath: string,
  filter: 'none' | 'vintage' | 'bright' | 'contrast' | 'sepia'
): Promise<string> {
  try {
    // This is a simplified implementation
    // In a real app, you'd use a proper image filtering library
    let quality = 80;

    switch (filter) {
      case 'vintage':
        quality = 75;
        break;
      case 'bright':
        quality = 85;
        break;
      case 'contrast':
        quality = 90;
        break;
      case 'sepia':
        quality = 70;
        break;
      default:
        quality = 80;
    }

    const result = await ImageResizer.createResizedImage(
      imagePath,
      1080,
      1080,
      'JPEG',
      quality,
      0,
      undefined,
      false
    );

    logger.debug('Image filter applied:', { filter, path: result.path });
    return result.path;

  } catch (error) {
    logger.error('Image filter application failed:', error);
    throw error;
  }
}
