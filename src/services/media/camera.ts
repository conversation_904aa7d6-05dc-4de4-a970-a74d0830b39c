import {
  launchCamera,
  CameraOptions,
  ImagePickerResponse,
} from 'react-native-image-picker';
import logger from '../../utils/logger';

export interface VideoRecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  path: string | null;
  thumbnail?: string;
}

let videoRecordingState: VideoRecordingState = {
  isRecording: false,
  isPaused: false,
  duration: 0,
  path: null,
};

let videoRecordingUpdateCallback: ((state: VideoRecordingState) => void) | null = null;

export function setVideoRecordingUpdateCallback(callback: (state: VideoRecordingState) => void | null) {
  videoRecordingUpdateCallback = callback;
}

export function getVideoRecordingState(): VideoRecordingState {
  return { ...videoRecordingState };
}

/**
 * Open the device camera and capture media.
 * Returns the ImagePicker response.
 */
export async function openCamera(
  options: CameraOptions = {
    mediaType: 'video',
    videoQuality: 'high',
    saveToPhotos: true,
  },
): Promise<ImagePickerResponse> {
  return new Promise((resolve, reject) => {
    launchCamera(options, response => {
      if (response.didCancel) {
        reject(new Error('User cancelled camera'));
      } else if (response.errorMessage) {
        reject(new Error(response.errorMessage));
      } else {
        resolve(response);
      }
    });
  });
}

/**
 * Start video recording with the camera
 */
export async function startVideoRecording(): Promise<ImagePickerResponse> {
  try {
    logger.debug('Starting video recording...');

    const options: CameraOptions = {
      mediaType: 'video',
      videoQuality: 'high',
      durationLimit: 60, // 60 seconds max
      saveToPhotos: false,
      includeBase64: false,
    };

    const response = await openCamera(options);

    if (response.assets && response.assets[0]) {
      const asset = response.assets[0];
      videoRecordingState = {
        isRecording: false, // Recording is complete when we get the response
        isPaused: false,
        duration: asset.duration || 0,
        path: asset.uri || null,
        thumbnail: asset.uri, // We'll use the video URI as thumbnail for now
      };

      videoRecordingUpdateCallback?.(videoRecordingState);
      logger.debug('Video recording completed:', videoRecordingState);
    }

    return response;
  } catch (error) {
    logger.error('Failed to start video recording:', error);
    videoRecordingState = {
      isRecording: false,
      isPaused: false,
      duration: 0,
      path: null,
    };
    videoRecordingUpdateCallback?.(videoRecordingState);
    throw error;
  }
}

/**
 * Take a photo with the camera
 */
export async function takePhoto(): Promise<ImagePickerResponse> {
  try {
    logger.debug('Taking photo...');

    const options: CameraOptions = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1920,
      saveToPhotos: false,
      includeBase64: false,
    };

    const response = await openCamera(options);
    logger.debug('Photo taken successfully');
    return response;
  } catch (error) {
    logger.error('Failed to take photo:', error);
    throw error;
  }
}
