import { uploadFileToSupabase, FileUploadResult } from '../../utils/fileUpload';
import {
  getBucketConfigFromFile,
  validateFileForBucket,
  getMediaTypeFromExtension,
  MediaContext
} from '../../utils/bucketConfig';
import logger from '../../utils/logger';
import RNFS from 'react-native-fs';
import { processImage } from './image-processing';
import { processVideo, generateVideoThumbnail } from './video-processing';
import { processAudio, getAudioDuration } from './audio';

export interface TikTokMediaUploadOptions {
  generateThumbnail?: boolean;
  compressVideo?: boolean;
  maxDuration?: number;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface EnhancedFileUploadResult extends FileUploadResult {
  thumbnailUrl?: string;
  duration?: number;
  width?: number;
  height?: number;
  compressedUrl?: string;
  waveformData?: number[];
}

/**
 * Upload a local media file to Supabase Storage with TikTok-style processing.
 *
 * @param filePath - Local file path
 * @param context - Upload context (message, feed, profile, etc.)
 * @param folderId - Folder identifier (conversationId for messages, userId for profile, etc.)
 * @param options - TikTok-style processing options
 */
export async function uploadMedia(
  filePath: string,
  context: MediaContext,
  folderId?: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  try {
    const name = filePath.split('/').pop() || `file-${Date.now()}`;
    const ext = name.includes('.') ? name.split('.').pop()!.toLowerCase() : '';

    logger.debug('Uploading media:', { filePath, name, ext, context, folderId });

    // Get bucket configuration based on context and file type
    const bucketConfig = getBucketConfigFromFile(filePath, context);
    const mediaType = getMediaTypeFromExtension(ext);

    logger.debug('Bucket config:', {
      bucket: bucketConfig.name,
      folder: bucketConfig.folder,
      mediaType,
      maxSize: bucketConfig.maxFileSize
    });

    // Validate file exists and get size
    const cleanUri = filePath.replace('file://', '');
    const fileExists = await RNFS.exists(cleanUri);
    if (!fileExists) {
      throw new Error(`File does not exist: ${cleanUri}`);
    }

    const fileStats = await RNFS.stat(cleanUri);

    // Validate file against bucket configuration
    const validation = validateFileForBucket(filePath, fileStats.size, bucketConfig);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Determine MIME type more accurately
    let mimeType = 'application/octet-stream';
    switch (mediaType) {
      case 'image':
        switch (ext) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'gif':
            mimeType = 'image/gif';
            break;
          case 'webp':
            mimeType = 'image/webp';
            break;
          default:
            mimeType = `image/${ext}`;
        }
        break;
      case 'video':
        switch (ext) {
          case 'mp4':
            mimeType = 'video/mp4';
            break;
          case 'mov':
            mimeType = 'video/quicktime';
            break;
          case 'm4v':
            mimeType = 'video/x-m4v';
            break;
          case 'avi':
            mimeType = 'video/x-msvideo';
            break;
          default:
            mimeType = `video/${ext}`;
        }
        break;
      case 'audio':
        switch (ext) {
          case 'mp3':
            mimeType = 'audio/mpeg';
            break;
          case 'aac':
            mimeType = 'audio/aac';
            break;
          case 'm4a':
            mimeType = 'audio/mp4';
            break;
          case 'wav':
            mimeType = 'audio/wav';
            break;
          case 'ogg':
            mimeType = 'audio/ogg';
            break;
          default:
            mimeType = `audio/${ext}`;
        }
        break;
      default:
        mimeType = 'application/octet-stream';
    }

    let processedFilePath = filePath;
    let thumbnailPath: string | null = null;
    let mediaMetadata: { width?: number; height?: number; duration?: number; waveformData?: number[] } = {};

    // TikTok-style media processing based on type
    if (mediaType === 'image' && (options.maxWidth || options.maxHeight || options.quality)) {
      try {
        const processed = await processImage(filePath, {
          maxWidth: options.maxWidth || 1080,
          maxHeight: options.maxHeight || 1080,
          quality: options.quality || 0.8,
        });
        processedFilePath = processed.path;
        mediaMetadata.width = processed.width;
        mediaMetadata.height = processed.height;
        logger.debug('Image processed:', { original: filePath, processed: processedFilePath });
      } catch (error) {
        logger.warn('Image processing failed, using original:', error);
      }
    }

    if (mediaType === 'video') {
      try {
        // Get video metadata
        const videoInfo = await processVideo(filePath, {
          maxDuration: options.maxDuration,
          compress: options.compressVideo,
          quality: options.quality,
        });

        if (videoInfo.processedPath) {
          processedFilePath = videoInfo.processedPath;
        }

        mediaMetadata.width = videoInfo.width;
        mediaMetadata.height = videoInfo.height;
        mediaMetadata.duration = videoInfo.duration;

        // Generate thumbnail for videos
        if (options.generateThumbnail !== false) {
          thumbnailPath = await generateVideoThumbnail(filePath, {
            timeOffset: Math.min(1, (videoInfo.duration || 0) / 2), // Middle of video or 1 second
            width: 320,
            height: 320,
          });
        }

        logger.debug('Video processed:', {
          original: filePath,
          processed: processedFilePath,
          thumbnail: thumbnailPath,
          metadata: mediaMetadata
        });
      } catch (error) {
        logger.warn('Video processing failed, using original:', error);
      }
    }

    if (mediaType === 'audio') {
      try {
        // Process audio and get metadata
        const audioInfo = await processAudio(filePath, {
          maxDuration: options.maxDuration,
          quality: options.quality,
        });

        if (audioInfo.processedPath) {
          processedFilePath = audioInfo.processedPath;
        }

        mediaMetadata.duration = audioInfo.duration;
        mediaMetadata.waveformData = audioInfo.waveformData;

        logger.debug('Audio processed:', {
          original: filePath,
          processed: processedFilePath,
          metadata: mediaMetadata
        });
      } catch (error) {
        logger.warn('Audio processing failed, using original:', error);
      }
    }

    const file = { uri: processedFilePath, type: mimeType, name };

    // Construct folder path based on new bucket convention
    // New Convention:
    // - Videos/Feeds → 'videos' bucket → {userId}/ folder → content
    // - Profile Pictures → 'profile' bucket → {userId}/ folder → content
    // - Messages Media → 'messages' bucket → {conversationId}/ folder → content
    let folderPath = '';
    if (folderId) {
      folderPath = folderId; // Use folderId directly as the main folder (userId or conversationId)
    }

    logger.debug('File details:', {
      file,
      bucket: bucketConfig.name,
      folderPath,
      context,
      mediaType,
      fileSize: `${(fileStats.size / 1024 / 1024).toFixed(2)}MB`
    });

    const result = await uploadFileToSupabase(file, bucketConfig.name, folderPath);
    if (!result.publicUrl) {
      throw new Error('Upload failed: no public URL returned');
    }

    // Enhanced result with TikTok-style metadata
    const enhancedResult: EnhancedFileUploadResult = {
      ...result,
      ...mediaMetadata,
    };

    // Upload thumbnail if generated
    if (thumbnailPath) {
      try {
        const thumbnailName = `thumb_${name}`;
        const thumbnailFile = { uri: thumbnailPath, type: 'image/jpeg', name: thumbnailName };
        const thumbnailResult = await uploadFileToSupabase(thumbnailFile, bucketConfig.name, folderPath);

        if (thumbnailResult.publicUrl) {
          enhancedResult.thumbnailUrl = thumbnailResult.publicUrl;
        }

        // Clean up temporary thumbnail file
        await RNFS.unlink(thumbnailPath.replace('file://', ''));
      } catch (error) {
        logger.warn('Thumbnail upload failed:', error);
      }
    }

    // Clean up temporary processed files if different from original
    if (processedFilePath !== filePath) {
      try {
        await RNFS.unlink(processedFilePath.replace('file://', ''));
      } catch (error) {
        logger.warn('Failed to clean up processed file:', error);
      }
    }

    logger.debug('Enhanced media upload successful:', {
      publicUrl: enhancedResult.publicUrl,
      thumbnailUrl: enhancedResult.thumbnailUrl,
      bucket: bucketConfig.name,
      context,
      mediaType,
      metadata: mediaMetadata
    });

    return enhancedResult;
  } catch (error) {
    logger.error('Media upload error:', error);
    throw error;
  }
}

/**
 * Upload media for chat messages with TikTok-style processing
 */
export async function uploadMessageMedia(
  filePath: string,
  conversationId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  // Default options for message media
  const messageOptions: TikTokMediaUploadOptions = {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 300, // 5 minutes max for messages
    quality: 0.8,
    maxWidth: 1080,
    maxHeight: 1080,
    ...options,
  };

  return uploadMedia(filePath, 'message', conversationId, messageOptions);
}

/**
 * Upload voice message with TikTok-style audio processing
 */
export async function uploadVoiceMessage(
  filePath: string,
  conversationId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const voiceOptions: TikTokMediaUploadOptions = {
    maxDuration: 60, // 1 minute max for voice messages
    quality: 0.7, // Compress for faster upload
    ...options,
  };

  return uploadMedia(filePath, 'message', conversationId, voiceOptions);
}

/**
 * Upload image with TikTok-style compression for messages
 */
export async function uploadMessageImage(
  filePath: string,
  conversationId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const imageOptions: TikTokMediaUploadOptions = {
    generateThumbnail: false, // Images don't need thumbnails
    quality: 0.85,
    maxWidth: 1080,
    maxHeight: 1080,
    ...options,
  };

  return uploadMedia(filePath, 'message', conversationId, imageOptions);
}

/**
 * Upload video with TikTok-style processing for messages
 */
export async function uploadMessageVideo(
  filePath: string,
  conversationId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const videoOptions: TikTokMediaUploadOptions = {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 300, // 5 minutes max
    quality: 0.8,
    maxWidth: 720, // Smaller for messages
    maxHeight: 1280,
    ...options,
  };

  return uploadMedia(filePath, 'message', conversationId, videoOptions);
}

/**
 * Upload media for main app feed with TikTok-style processing
 */
export async function uploadFeedMedia(
  filePath: string,
  userId?: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const feedOptions: TikTokMediaUploadOptions = {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 600, // 10 minutes max for feed videos
    quality: 0.9, // Higher quality for feed content
    maxWidth: 1080,
    maxHeight: 1920, // TikTok aspect ratio
    ...options,
  };

  return uploadMedia(filePath, 'feed', userId, feedOptions);
}

/**
 * Upload profile media (avatars, banners) with optimization
 */
export async function uploadProfileMedia(
  filePath: string,
  userId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const profileOptions: TikTokMediaUploadOptions = {
    generateThumbnail: false,
    quality: 0.9,
    maxWidth: 400, // Profile images are smaller
    maxHeight: 400,
    ...options,
  };

  return uploadMedia(filePath, 'profile', userId, profileOptions);
}

/**
 * Upload story media with TikTok-style processing
 */
export async function uploadStoryMedia(
  filePath: string,
  userId: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const storyOptions: TikTokMediaUploadOptions = {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 30, // Stories are short
    quality: 0.8,
    maxWidth: 1080,
    maxHeight: 1920,
    ...options,
  };

  return uploadMedia(filePath, 'story', userId, storyOptions);
}

/**
 * Upload music library media with audio processing
 */
export async function uploadMusicMedia(
  filePath: string,
  trackId?: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  const musicOptions: TikTokMediaUploadOptions = {
    maxDuration: 180, // 3 minutes max for music
    quality: 0.8,
    ...options,
  };

  return uploadMedia(filePath, 'music', trackId, musicOptions);
}

/**
 * Upload temporary media (for processing, previews, etc.)
 */
export async function uploadTempMedia(
  filePath: string,
  sessionId?: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  return uploadMedia(filePath, 'temp', sessionId, options);
}

/**
 * Upload video with metadata and TikTok-style processing (for video upload screen)
 */
export async function uploadVideo(
  file: { uri: string; type: string; name: string },
  userId: string,
  metadata: { title: string; description?: string },
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult> {
  // TikTok-style video processing options
  const videoOptions: TikTokMediaUploadOptions = {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 600, // 10 minutes max
    quality: 0.9,
    maxWidth: 1080,
    maxHeight: 1920, // TikTok aspect ratio
    ...options,
  };

  // Upload the video file to feed bucket with processing
  const result = await uploadFeedMedia(file.uri, userId, videoOptions);

  // TODO: Save video metadata to database
  // This would typically involve saving title, description, etc. to a videos table
  logger.debug('Video uploaded with metadata and processing:', {
    publicUrl: result.publicUrl,
    thumbnailUrl: result.thumbnailUrl,
    duration: result.duration,
    width: result.width,
    height: result.height,
    userId,
    metadata
  });

  return result;
}

/**
 * Batch upload multiple media files for TikTok-style carousel posts
 */
export async function uploadMediaBatch(
  files: Array<{ uri: string; type: string; name: string }>,
  context: MediaContext,
  folderId?: string,
  options: TikTokMediaUploadOptions = {}
): Promise<EnhancedFileUploadResult[]> {
  const results: EnhancedFileUploadResult[] = [];

  for (const file of files) {
    try {
      const result = await uploadMedia(file.uri, context, folderId, options);
      results.push(result);
    } catch (error) {
      logger.error('Batch upload failed for file:', file.uri, error);
      throw error;
    }
  }

  return results;
}

/**
 * Upload media with progress tracking for TikTok-style upload UI
 */
export async function uploadMediaWithProgress(
  filePath: string,
  context: MediaContext,
  folderId?: string,
  options: TikTokMediaUploadOptions = {},
  onProgress?: (progress: number) => void
): Promise<EnhancedFileUploadResult> {
  // Simulate progress for processing steps
  onProgress?.(10); // Processing started

  try {
    const result = await uploadMedia(filePath, context, folderId, options);
    onProgress?.(100); // Upload complete
    return result;
  } catch (error) {
    onProgress?.(0); // Reset on error
    throw error;
  }
}

