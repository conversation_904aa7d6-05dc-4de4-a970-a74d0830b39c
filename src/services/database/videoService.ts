import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export interface VideoRecord {
  id: string;
  user_id: string;
  title: string | null;
  description?: string | null;
  video_url: string;
  thumbnail_url?: string | null;
  duration?: number | null;
  original_audio_volume?: number | null;
  music_id?: string | null;
  music_volume?: number | null;
  privacy_setting: string;
  allow_comments: boolean | null;
  allow_downloads: boolean | null;
  views_count: number | null;
  likes_count: number | null;
  comments_count: number | null;
  shares_count: number | null;
  is_draft: boolean | null;
  published_at?: string | null;
  created_at: string | null;
  updated_at: string | null;
  author?: string | null;
  duration_sec?: number | null;
  likes?: number | null;
  sport?: string | null;
  tags?: string[] | null;
  team_mentioned?: string | null;
  type?: string | null;
  upload_date?: string | null;
  video_id: string;
  views?: number | null;
  custom_music_url?: string | null;
}

export interface CreateVideoData {
  user_id: string;
  title: string;
  description?: string;
  video_url: string;
  thumbnail_url?: string;
  duration?: number;
  original_audio_volume?: number;
  music_id?: string;
  music_volume?: number;
  privacy_setting: 'public' | 'friends' | 'private';
  allow_comments: boolean;
  allow_downloads: boolean;
  author?: string;
  sport?: string;
  tags?: string[];
  team_mentioned?: string;
  type?: 'live' | 'highlight' | 'analysis' | 'interview' | 'training' | 'challenge' | 'For fun' | 'esport';
  custom_music_url?: string;
  is_draft?: boolean;
}

export interface UpdateVideoData {
  title?: string;
  description?: string;
  privacy_setting?: 'public' | 'friends' | 'private';
  allow_comments?: boolean;
  allow_downloads?: boolean;
  author?: string;
  sport?: string;
  tags?: string[];
  team_mentioned?: string;
  type?: 'live' | 'highlight' | 'analysis' | 'interview' | 'training' | 'challenge' | 'For fun' | 'esport';
  is_draft?: boolean;
  published_at?: string;
}

/**
 * Generate a unique video ID
 */
const generateVideoId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `vid_${timestamp}_${randomStr}`;
};

/**
 * Safely get video title with fallback
 */
export const getVideoTitle = (video: VideoRecord): string => {
  return video.title || 'Untitled Video';
};

/**
 * Safely get video description with fallback
 */
export const getVideoDescription = (video: VideoRecord): string => {
  return video.description || '';
};

/**
 * Safely get video stats with fallbacks
 */
export const getVideoStats = (video: VideoRecord) => {
  return {
    views: video.views_count || video.views || 0,
    likes: video.likes_count || video.likes || 0,
    comments: video.comments_count || 0,
    shares: video.shares_count || 0,
  };
};

/**
 * Check if video allows interactions
 */
export const getVideoPermissions = (video: VideoRecord) => {
  return {
    allowComments: video.allow_comments ?? true,
    allowDownloads: video.allow_downloads ?? true,
  };
};

/**
 * Create a new video record
 */
export const createVideo = async (videoData: CreateVideoData): Promise<VideoRecord> => {
  try {
    logger.debug('Creating video record:', videoData);

    const videoId = generateVideoId();
    const now = new Date().toISOString();

    const insertData = {
      ...videoData,
      video_id: videoId,
      views_count: 0,
      likes_count: 0,
      comments_count: 0,
      shares_count: 0,
      views: 0,
      likes: 0,
      duration_sec: videoData.duration,
      upload_date: now,
      is_draft: videoData.is_draft ?? true,
      published_at: videoData.is_draft === false ? now : null,
    };

    const { data, error } = await supabase
      .from('videos')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      logger.error('Error creating video:', error);
      throw new Error(`Failed to create video: ${error.message}`);
    }

    logger.debug('Video created successfully:', data);
    return data;
  } catch (error) {
    logger.error('Video creation error:', error);
    throw error;
  }
};

/**
 * Update an existing video record
 */
export const updateVideo = async (videoId: string, updateData: UpdateVideoData): Promise<VideoRecord> => {
  try {
    logger.debug('Updating video:', { videoId, updateData });

    const { data, error } = await supabase
      .from('videos')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('video_id', videoId)
      .select()
      .single();

    if (error) {
      logger.error('Error updating video:', error);
      throw new Error(`Failed to update video: ${error.message}`);
    }

    logger.debug('Video updated successfully:', data);
    return data;
  } catch (error) {
    logger.error('Video update error:', error);
    throw error;
  }
};

/**
 * Publish a draft video
 */
export const publishVideo = async (videoId: string): Promise<VideoRecord> => {
  try {
    logger.debug('Publishing video:', videoId);

    const { data, error } = await supabase
      .from('videos')
      .update({
        is_draft: false,
        published_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('video_id', videoId)
      .select()
      .single();

    if (error) {
      logger.error('Error publishing video:', error);
      throw new Error(`Failed to publish video: ${error.message}`);
    }

    logger.debug('Video published successfully:', data);
    return data;
  } catch (error) {
    logger.error('Video publish error:', error);
    throw error;
  }
};

/**
 * Get video by video_id
 */
export const getVideoById = async (videoId: string): Promise<VideoRecord | null> => {
  try {
    logger.debug('Fetching video by ID:', videoId);

    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('video_id', videoId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      logger.error('Error fetching video:', error);
      throw new Error(`Failed to fetch video: ${error.message}`);
    }

    return data;
  } catch (error) {
    logger.error('Video fetch error:', error);
    throw error;
  }
};

/**
 * Get videos by user ID
 */
export const getVideosByUserId = async (
  userId: string,
  includeDrafts: boolean = false,
  limit: number = 20,
  offset: number = 0
): Promise<VideoRecord[]> => {
  try {
    logger.debug('Fetching videos by user ID:', { userId, includeDrafts, limit, offset });

    let query = supabase
      .from('videos')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (!includeDrafts) {
      query = query.eq('is_draft', false);
    }

    const { data, error } = await query;

    if (error) {
      logger.error('Error fetching user videos:', error);
      throw new Error(`Failed to fetch user videos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    logger.error('User videos fetch error:', error);
    throw error;
  }
};

/**
 * Get public videos for feed
 */
export const getPublicVideos = async (
  limit: number = 20,
  offset: number = 0,
  type?: string,
  sport?: string
): Promise<VideoRecord[]> => {
  try {
    logger.debug('Fetching public videos:', { limit, offset, type, sport });

    let query = supabase
      .from('videos')
      .select('*')
      .eq('privacy_setting', 'public')
      .eq('is_draft', false)
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (type) {
      query = query.eq('type', type);
    }

    if (sport) {
      query = query.eq('sport', sport);
    }

    const { data, error } = await query;

    if (error) {
      logger.error('Error fetching public videos:', error);
      throw new Error(`Failed to fetch public videos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    logger.error('Public videos fetch error:', error);
    throw error;
  }
};

/**
 * Delete a video
 */
export const deleteVideo = async (videoId: string, userId: string): Promise<void> => {
  try {
    logger.debug('Deleting video:', { videoId, userId });

    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('video_id', videoId)
      .eq('user_id', userId); // Ensure user can only delete their own videos

    if (error) {
      logger.error('Error deleting video:', error);
      throw new Error(`Failed to delete video: ${error.message}`);
    }

    logger.debug('Video deleted successfully');
  } catch (error) {
    logger.error('Video deletion error:', error);
    throw error;
  }
};

/**
 * Increment video views
 */
export const incrementVideoViews = async (videoId: string): Promise<void> => {
  try {
    logger.debug('Incrementing video views:', videoId);

    // First, get the current video data
    const { data: currentVideo, error: fetchError } = await supabase
      .from('videos')
      .select('views_count, views')
      .eq('video_id', videoId)
      .single();

    if (fetchError) {
      logger.error('Error fetching video for view increment:', fetchError);
      return;
    }

    // Increment the view counts
    const newViewsCount = (currentVideo?.views_count || 0) + 1;
    const newViews = (currentVideo?.views || 0) + 1;

    // Update the video with incremented views
    const { error } = await supabase
      .from('videos')
      .update({
        views_count: newViewsCount,
        views: newViews,
        updated_at: new Date().toISOString(),
      })
      .eq('video_id', videoId);

    if (error) {
      logger.error('Error incrementing video views:', error);
      // Don't throw error for view counting failures
    }
  } catch (error) {
    logger.error('Video views increment error:', error);
    // Don't throw error for view counting failures
  }
};

/**
 * Search videos by title, description, or tags
 */
export const searchVideos = async (
  query: string,
  limit: number = 20,
  offset: number = 0
): Promise<VideoRecord[]> => {
  try {
    logger.debug('Searching videos:', { query, limit, offset });

    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('privacy_setting', 'public')
      .eq('is_draft', false)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,tags.cs.{${query}}`)
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Error searching videos:', error);
      throw new Error(`Failed to search videos: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    logger.error('Video search error:', error);
    throw error;
  }
};
