import { AppDispatch } from '../store';
import { 
  executeQuery, 
  executeMutation, 
  executeWithOfflineSupport, 
  offlineQueue,
  getDatabaseInfo 
} from '../utils/databaseUtils';
import { 
  realtimeManager, 
  subscribeToVideoLikes, 
  subscribeToUserFollows, 
  subscribeToVideoComments,
  cleanupRealtimeSubscriptions 
} from '../utils/realtimeManager';
import { initializeDatabase } from '../utils/schemaValidation';
import logger from '../utils/logger';
import NetInfo from '@react-native-community/netinfo';

/**
 * Comprehensive API Integration Service
 * Manages all API interactions, real-time subscriptions, and offline support
 */

export interface ApiServiceConfig {
  enableRealtime?: boolean;
  enableOfflineSupport?: boolean;
  retryOptions?: {
    maxRetries?: number;
    baseDelay?: number;
  };
}

class ApiIntegrationService {
  private dispatch: AppDispatch | null = null;
  private config: ApiServiceConfig = {
    enableRealtime: true,
    enableOfflineSupport: true,
    retryOptions: {
      maxRetries: 3,
      baseDelay: 1000
    }
  };
  private isInitialized = false;
  private networkListener: any = null;

  /**
   * Initialize the API service
   */
  async initialize(dispatch: AppDispatch, config?: ApiServiceConfig): Promise<void> {
    if (this.isInitialized) {
      logger.warn('API service already initialized');
      return;
    }

    this.dispatch = dispatch;
    this.config = { ...this.config, ...config };

    logger.info('Initializing API Integration Service...');

    try {
      // Initialize database connection and validate schema
      const dbStatus = await initializeDatabase();
      
      if (!dbStatus.connected) {
        throw new Error('Failed to connect to database');
      }

      if (!dbStatus.schemaValid) {
        logger.warn('Database schema validation failed, but continuing...');
      }

      // Set up network monitoring
      this.setupNetworkMonitoring();

      // Set up offline queue processing
      if (this.config.enableOfflineSupport) {
        this.setupOfflineQueueProcessing();
      }

      this.isInitialized = true;
      logger.info('API Integration Service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize API service:', error);
      throw error;
    }
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    this.networkListener = NetInfo.addEventListener(state => {
      logger.info('Network state changed:', state);

      if (state.isConnected && state.isInternetReachable) {
        // Process offline queue when coming back online
        if (this.config.enableOfflineSupport) {
          this.processOfflineQueue();
        }

        // Reconnect real-time subscriptions if needed
        if (this.config.enableRealtime) {
          this.reconnectRealtimeSubscriptions();
        }
      }
    });
  }

  /**
   * Setup offline queue processing
   */
  private setupOfflineQueueProcessing(): void {
    // Process queue every 30 seconds when online
    setInterval(async () => {
      const dbInfo = await getDatabaseInfo();
      if (dbInfo.isOnline && dbInfo.isHealthy) {
        await this.processOfflineQueue();
      }
    }, 30000);
  }

  /**
   * Process offline queue
   */
  private async processOfflineQueue(): Promise<void> {
    try {
      await offlineQueue.processQueue();
      logger.info('Offline queue processed successfully');
    } catch (error) {
      logger.error('Error processing offline queue:', error);
    }
  }

  /**
   * Reconnect real-time subscriptions
   */
  private reconnectRealtimeSubscriptions(): void {
    // This would be implemented based on your app's specific needs
    // For now, we'll just log that we should reconnect
    logger.info('Reconnecting real-time subscriptions...');
  }

  /**
   * Subscribe to video interactions (likes, comments)
   */
  subscribeToVideoInteractions(videoId: string): void {
    if (!this.config.enableRealtime || !this.dispatch) {
      return;
    }

    // Subscribe to video likes
    subscribeToVideoLikes(videoId, this.dispatch, {
      onLikeAdded: (videoId, userId) => {
        logger.debug(`Like added to video ${videoId} by user ${userId}`);
        // Dispatch action to update UI
        // this.dispatch(updateVideoLikes({ videoId, increment: true }));
      },
      onLikeRemoved: (videoId, userId) => {
        logger.debug(`Like removed from video ${videoId} by user ${userId}`);
        // Dispatch action to update UI
        // this.dispatch(updateVideoLikes({ videoId, increment: false }));
      }
    });

    // Subscribe to video comments
    subscribeToVideoComments(videoId, this.dispatch, {
      onCommentAdded: (comment) => {
        logger.debug(`Comment added to video ${videoId}:`, comment);
        // Dispatch action to update UI
        // this.dispatch(addComment(comment));
      },
      onCommentUpdated: (comment) => {
        logger.debug(`Comment updated on video ${videoId}:`, comment);
        // Dispatch action to update UI
        // this.dispatch(updateComment(comment));
      },
      onCommentDeleted: (commentId) => {
        logger.debug(`Comment deleted from video ${videoId}: ${commentId}`);
        // Dispatch action to update UI
        // this.dispatch(removeComment(commentId));
      }
    });
  }

  /**
   * Subscribe to user interactions (follows)
   */
  subscribeToUserInteractions(userId: string): void {
    if (!this.config.enableRealtime || !this.dispatch) {
      return;
    }

    subscribeToUserFollows(userId, this.dispatch, {
      onFollowerAdded: (followerId, followingId) => {
        logger.debug(`User ${followerId} followed user ${followingId}`);
        // Dispatch action to update UI
        // this.dispatch(incrementFollowerCount({ userId: followingId }));
      },
      onFollowerRemoved: (followerId, followingId) => {
        logger.debug(`User ${followerId} unfollowed user ${followingId}`);
        // Dispatch action to update UI
        // this.dispatch(decrementFollowerCount({ userId: followingId }));
      }
    });
  }

  /**
   * Execute a query with full error handling and offline support
   */
  async executeQuery<T>(
    queryBuilder: any,
    fallbackData?: T
  ): Promise<{ data: T | null; error: any }> {
    if (this.config.enableOfflineSupport) {
      try {
        const result = await executeWithOfflineSupport(
          () => executeQuery<T>(queryBuilder, this.config.retryOptions),
          fallbackData
        );
        return result;
      } catch (error) {
        return { data: fallbackData || null, error };
      }
    } else {
      return executeQuery<T>(queryBuilder, this.config.retryOptions);
    }
  }

  /**
   * Execute a mutation with full error handling and offline support
   */
  async executeMutation<T>(
    mutationBuilder: any,
    fallbackData?: T
  ): Promise<{ data: T | null; error: any }> {
    if (this.config.enableOfflineSupport) {
      try {
        const result = await executeWithOfflineSupport(
          () => executeMutation<T>(mutationBuilder, this.config.retryOptions),
          fallbackData
        );
        return result;
      } catch (error) {
        return { data: fallbackData || null, error };
      }
    } else {
      return executeMutation<T>(mutationBuilder, this.config.retryOptions);
    }
  }

  /**
   * Get service status
   */
  async getStatus(): Promise<{
    initialized: boolean;
    database: {
      connected: boolean;
      healthy: boolean;
      latency?: number;
    };
    realtime: {
      enabled: boolean;
      activeChannels: string[];
    };
    offline: {
      enabled: boolean;
      queueSize: number;
    };
  }> {
    const dbInfo = await getDatabaseInfo();
    const realtimeStatus = realtimeManager.getActiveChannels();

    return {
      initialized: this.isInitialized,
      database: {
        connected: dbInfo.isOnline,
        healthy: dbInfo.isHealthy,
        latency: dbInfo.latency
      },
      realtime: {
        enabled: this.config.enableRealtime || false,
        activeChannels: realtimeStatus
      },
      offline: {
        enabled: this.config.enableOfflineSupport || false,
        queueSize: offlineQueue.getQueueSize()
      }
    };
  }

  /**
   * Cleanup service
   */
  cleanup(): void {
    logger.info('Cleaning up API Integration Service...');

    // Remove network listener
    if (this.networkListener) {
      this.networkListener();
      this.networkListener = null;
    }

    // Cleanup real-time subscriptions
    if (this.config.enableRealtime) {
      cleanupRealtimeSubscriptions();
    }

    // Clear offline queue
    if (this.config.enableOfflineSupport) {
      offlineQueue.clear();
    }

    this.isInitialized = false;
    this.dispatch = null;

    logger.info('API Integration Service cleaned up');
  }

  /**
   * Force sync with server (useful for pull-to-refresh)
   */
  async forceSync(): Promise<void> {
    logger.info('Forcing sync with server...');

    try {
      // Process offline queue
      if (this.config.enableOfflineSupport) {
        await this.processOfflineQueue();
      }

      // Refresh real-time connections
      if (this.config.enableRealtime) {
        this.reconnectRealtimeSubscriptions();
      }

      logger.info('Force sync completed');
    } catch (error) {
      logger.error('Force sync failed:', error);
      throw error;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ApiServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('API service configuration updated:', this.config);
  }
}

// Singleton instance
export const apiService = new ApiIntegrationService();

// Export convenience functions
export const initializeApiService = (dispatch: AppDispatch, config?: ApiServiceConfig) => 
  apiService.initialize(dispatch, config);

export const cleanupApiService = () => apiService.cleanup();

export const getApiServiceStatus = () => apiService.getStatus();

export const forceApiSync = () => apiService.forceSync();
