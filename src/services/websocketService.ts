import logger from '../utils/logger';

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
  conversationId?: string;
}

interface WebSocketServiceConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketServiceConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManuallyDisconnected = false;
  private messageQueue: WebSocketMessage[] = [];
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(config: Partial<WebSocketServiceConfig> = {}) {
    this.config = {
      url: process.env.REACT_APP_WS_URL || 'ws://localhost:3001',
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      ...config,
    };
  }

  // Connect to WebSocket server
  connect(userId: string, token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;
      this.isManuallyDisconnected = false;

      try {
        const wsUrl = `${this.config.url}?userId=${userId}&token=${token}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          logger.info('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.processMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          logger.warn('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          
          if (!this.isManuallyDisconnected) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          logger.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.stopHeartbeat();
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  // Send message through WebSocket
  sendMessage(type: string, payload: any, conversationId?: string): void {
    const message: WebSocketMessage = {
      type,
      payload,
      timestamp: Date.now(),
      conversationId,
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
      logger.debug('WebSocket message sent:', message);
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
      logger.debug('WebSocket message queued:', message);
    }
  }

  // Add event listener
  on(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  // Remove event listener
  off(event: string, callback: (data: any) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  // Emit event to listeners
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logger.error('Error in WebSocket event listener:', error);
        }
      });
    }
  }

  // Handle incoming WebSocket messages
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      logger.debug('WebSocket message received:', message);

      // Emit specific event
      this.emit(message.type, message.payload);
      
      // Emit general message event
      this.emit('message', message);

      // Handle built-in message types
      switch (message.type) {
        case 'ping':
          this.sendMessage('pong', { timestamp: Date.now() });
          break;
        
        case 'typing_start':
          this.emit('typing_start', message.payload);
          break;
        
        case 'typing_stop':
          this.emit('typing_stop', message.payload);
          break;
        
        case 'presence_update':
          this.emit('presence_update', message.payload);
          break;
        
        case 'message_delivered':
          this.emit('message_delivered', message.payload);
          break;
        
        case 'message_read':
          this.emit('message_read', message.payload);
          break;
        
        case 'reaction_added':
          this.emit('reaction_added', message.payload);
          break;
        
        case 'reaction_removed':
          this.emit('reaction_removed', message.payload);
          break;
        
        default:
          logger.debug('Unhandled WebSocket message type:', message.type);
      }

    } catch (error) {
      logger.error('Error parsing WebSocket message:', error);
    }
  }

  // Process queued messages
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift();
      if (message) {
        this.ws.send(JSON.stringify(message));
        logger.debug('Queued WebSocket message sent:', message);
      }
    }
  }

  // Schedule reconnection
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts_reached', {});
      return;
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    logger.info(`Scheduling WebSocket reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      if (!this.isManuallyDisconnected) {
        logger.info(`Attempting WebSocket reconnection ${this.reconnectAttempts}`);
        this.emit('reconnecting', { attempt: this.reconnectAttempts });
        // Note: You'd need to store and reuse the userId and token for reconnection
        // This is a simplified version
      }
    }, delay);
  }

  // Clear reconnection timer
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // Start heartbeat
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.sendMessage('ping', { timestamp: Date.now() });
      }
    }, this.config.heartbeatInterval);
  }

  // Stop heartbeat
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Get connection status
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // Get connection state
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Send typing indicator
  sendTypingIndicator(conversationId: string, isTyping: boolean, typingType: string = 'text'): void {
    this.sendMessage(isTyping ? 'typing_start' : 'typing_stop', {
      conversationId,
      typingType,
    }, conversationId);
  }

  // Send presence update
  sendPresenceUpdate(status: string, deviceInfo: any = {}): void {
    this.sendMessage('presence_update', {
      status,
      deviceInfo,
      timestamp: Date.now(),
    });
  }

  // Send message delivery confirmation
  sendMessageDelivered(messageId: string, conversationId: string): void {
    this.sendMessage('message_delivered', {
      messageId,
      conversationId,
    }, conversationId);
  }

  // Send message read confirmation
  sendMessageRead(messageId: string, conversationId: string): void {
    this.sendMessage('message_read', {
      messageId,
      conversationId,
    }, conversationId);
  }

  // Send reaction
  sendReaction(messageId: string, conversationId: string, reaction: string, action: 'add' | 'remove'): void {
    this.sendMessage(action === 'add' ? 'reaction_added' : 'reaction_removed', {
      messageId,
      conversationId,
      reaction,
    }, conversationId);
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();

export default websocketService;
