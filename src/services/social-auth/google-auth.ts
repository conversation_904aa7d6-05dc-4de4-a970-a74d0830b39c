import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

// Google Sign-In Configuration
export const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    webClientId: '87727666002-f58hh4blhvi9m6lpv9lc0vhjltfae5tq.apps.googleusercontent.com',    offlineAccess: true,
    hostedDomain: '',
    forceCodeForRefreshToken: true,
  });
};

// Google Sign-In Service
export class GoogleAuthService {
  static async initialize() {
    try {
      configureGoogleSignIn();
      logger.debug('Google Sign-In configured successfully');
    } catch (error) {
      logger.error('Google Sign-In configuration error:', error);
    }
  }

  static async signIn() {
    try {
      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();

      // Get user info from Google
      const signInResponse = await GoogleSignin.signIn();
      logger.debug('Google Sign-In successful:', signInResponse);

      if (signInResponse.type !== 'success') {
        throw new Error('Google Sign-In was cancelled');
      }

      const userInfo = signInResponse.data;

      if (!userInfo.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Sign in to Supabase with Google ID token
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: userInfo.idToken,
        access_token: userInfo.serverAuthCode ?? undefined,
      });

      if (error) {
        logger.error('Supabase Google auth error:', error);
        throw error;
      }

      logger.debug('Supabase Google auth successful:', data);
      return {
        user: data.user,
        session: data.session,
        userInfo: userInfo.user,
      };
    } catch (error: any) {
      logger.error('Google Sign-In error:', error);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        throw new Error('Google Sign-In was cancelled');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Google Sign-In is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      } else {
        throw new Error(error.message || 'Google Sign-In failed');
      }
    }
  }

  static async signOut() {
    try {
      await GoogleSignin.signOut();
      logger.debug('Google Sign-Out successful');
    } catch (error) {
      logger.error('Google Sign-Out error:', error);
    }
  }

  static async revokeAccess() {
    try {
      await GoogleSignin.revokeAccess();
      logger.debug('Google access revoked');
    } catch (error) {
      logger.error('Google revoke access error:', error);
    }
  }

  static async getCurrentUser() {
    try {
      const response = await GoogleSignin.signInSilently();
      if (response.type !== 'success') {
        return null;
      }
      return response.data;
    } catch (error) {
      logger.debug('No current Google user:', error);
      return null;
    }
  }

  static async isSignedIn() {
    try {
      const isSignedIn = await GoogleSignin.hasPreviousSignIn();
      return isSignedIn;
    } catch (error) {
      logger.error('Google isSignedIn check error:', error);
      return false;
    }
  }
}
