import logger from '../../utils/logger';

export interface LocalNotificationOptions {
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: boolean;
  badge?: number;
  categoryId?: string;
  trigger?: any; // TODO: Define proper trigger type for react-native-push-notification
}

/**
 * Schedule a local notification
 * TODO: Implement with react-native-push-notification
 */
export async function scheduleLocalNotification(
  options: LocalNotificationOptions
): Promise<string | null> {
  try {
    logger.warn('Local notifications not implemented - requires react-native-push-notification');

    // TODO: Implement with react-native-push-notification
    // import PushNotification from 'react-native-push-notification';
    // PushNotification.localNotification({
    //   title: options.title,
    //   message: options.body,
    //   userInfo: options.data,
    //   playSound: options.sound !== false,
    //   number: options.badge,
    // });

    return null;
  } catch (error) {
    logger.error('Error scheduling local notification:', error);
    return null;
  }
}

/**
 * Schedule a notification for a specific time
 */
export async function scheduleNotificationAt(
  options: Omit<LocalNotificationOptions, 'trigger'>,
  date: Date
): Promise<string | null> {
  return scheduleLocalNotification({
    ...options,
    trigger: { date },
  });
}

/**
 * Schedule a repeating notification
 */
export async function scheduleRepeatingNotification(
  options: Omit<LocalNotificationOptions, 'trigger'>,
  seconds: number
): Promise<string | null> {
  return scheduleLocalNotification({
    ...options,
    trigger: { seconds, repeats: true },
  });
}

/**
 * Cancel a scheduled notification
 */
export async function cancelNotification(identifier: string): Promise<void> {
  try {
    await Notifications.cancelScheduledNotificationAsync(identifier);
    logger.debug('Notification cancelled:', identifier);
  } catch (error) {
    logger.error('Error cancelling notification:', error);
  }
}

/**
 * Cancel all scheduled notifications
 */
export async function cancelAllNotifications(): Promise<void> {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
    logger.debug('All scheduled notifications cancelled');
  } catch (error) {
    logger.error('Error cancelling all notifications:', error);
  }
}

/**
 * Get all scheduled notifications
 */
export async function getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    return notifications;
  } catch (error) {
    logger.error('Error getting scheduled notifications:', error);
    return [];
  }
}

/**
 * Schedule a reminder notification
 */
export async function scheduleReminder(
  title: string,
  body: string,
  minutesFromNow: number,
  data?: Record<string, any>
): Promise<string | null> {
  const triggerDate = new Date();
  triggerDate.setMinutes(triggerDate.getMinutes() + minutesFromNow);

  return scheduleNotificationAt(
    {
      title,
      body,
      data,
      sound: true,
    },
    triggerDate
  );
}

/**
 * Schedule a daily notification
 */
export async function scheduleDailyNotification(
  title: string,
  body: string,
  hour: number,
  minute: number = 0,
  data?: Record<string, any>
): Promise<string | null> {
  return scheduleLocalNotification({
    title,
    body,
    data,
    sound: true,
    trigger: {
      hour,
      minute,
      repeats: true,
    },
  });
}

/**
 * Schedule a weekly notification
 */
export async function scheduleWeeklyNotification(
  title: string,
  body: string,
  weekday: number, // 1 = Sunday, 2 = Monday, etc.
  hour: number,
  minute: number = 0,
  data?: Record<string, any>
): Promise<string | null> {
  return scheduleLocalNotification({
    title,
    body,
    data,
    sound: true,
    trigger: {
      weekday,
      hour,
      minute,
      repeats: true,
    },
  });
}

/**
 * Show an immediate notification
 */
export async function showImmediateNotification(
  title: string,
  body: string,
  data?: Record<string, any>
): Promise<string | null> {
  return scheduleLocalNotification({
    title,
    body,
    data,
    sound: true,
  });
}

/**
 * Schedule a notification for when the app becomes inactive
 */
export async function scheduleInactivityReminder(
  title: string,
  body: string,
  hoursInactive: number = 24
): Promise<string | null> {
  const triggerDate = new Date();
  triggerDate.setHours(triggerDate.getHours() + hoursInactive);

  return scheduleNotificationAt(
    {
      title,
      body,
      data: { type: 'inactivity_reminder' },
      sound: true,
    },
    triggerDate
  );
}

/**
 * Setup notification categories for interactive notifications
 */
export async function setupNotificationCategories(): Promise<void> {
  try {
    await Notifications.setNotificationCategoryAsync('message', [
      {
        identifier: 'reply',
        buttonTitle: 'Reply',
        options: { opensAppToForeground: true },
      },
      {
        identifier: 'mark_read',
        buttonTitle: 'Mark as Read',
        options: { opensAppToForeground: false },
      },
    ]);

    await Notifications.setNotificationCategoryAsync('social', [
      {
        identifier: 'view',
        buttonTitle: 'View',
        options: { opensAppToForeground: true },
      },
      {
        identifier: 'dismiss',
        buttonTitle: 'Dismiss',
        options: { opensAppToForeground: false },
      },
    ]);

    logger.debug('Notification categories set up');
  } catch (error) {
    logger.error('Error setting up notification categories:', error);
  }
}
