import { AppState, AppStateStatus } from 'react-native';
import * as Notifications from 'expo-notifications';
import { supabase } from '../../integrations/supabase/client';
import {
  registerForPushNotifications,
  setupNotificationListeners,
  setBadgeCount,
  getBadgeCount,
  clearAllNotifications,
} from './push-notifications';
import {
  setupNotificationCategories,
  scheduleInactivityReminder,
  cancelAllNotifications as cancelAllLocalNotifications,
} from './local-notifications';
import logger from '../../utils/logger';

export interface NotificationSettings {
  push_notifications: boolean;
  email_notifications: boolean;
  like_notifications: boolean;
  comment_notifications: boolean;
  follow_notifications: boolean;
  live_notifications: boolean;
  message_notifications: boolean;
  mention_notifications: boolean;
  reaction_notifications: boolean;
  video_upload_notifications: boolean;
  security_notifications: boolean;
  marketing_notifications: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  notification_sound: string;
  vibration_enabled: boolean;
  badge_count_enabled: boolean;
  preview_enabled: boolean;
  group_similar: boolean;
}

class NotificationManager {
  private static instance: NotificationManager;
  private isInitialized = false;
  private appStateSubscription: any;
  private lastActiveTime: Date = new Date();
  private settings: NotificationSettings | null = null;

  private constructor() {}

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  /**
   * Initialize the notification manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.debug('NotificationManager already initialized');
      return;
    }

    try {
      logger.debug('Initializing NotificationManager...');

      // Setup notification categories
      await setupNotificationCategories();

      // Register for push notifications
      await registerForPushNotifications();

      // Setup notification listeners
      setupNotificationListeners();

      // Load user notification settings
      await this.loadNotificationSettings();

      // Setup app state monitoring
      this.setupAppStateMonitoring();

      // Update badge count
      await this.updateBadgeCount();

      this.isInitialized = true;
      logger.debug('NotificationManager initialized successfully');
    } catch (error) {
      logger.error('Error initializing NotificationManager:', error);
    }
  }

  /**
   * Load user notification settings from database
   */
  private async loadNotificationSettings(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('notification_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Error loading notification settings:', error);
        return;
      }

      this.settings = data || this.getDefaultSettings();
      logger.debug('Notification settings loaded:', this.settings);
    } catch (error) {
      logger.error('Exception loading notification settings:', error);
    }
  }

  /**
   * Get default notification settings
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      push_notifications: true,
      email_notifications: true,
      like_notifications: true,
      comment_notifications: true,
      follow_notifications: true,
      live_notifications: true,
      message_notifications: true,
      mention_notifications: true,
      reaction_notifications: true,
      video_upload_notifications: true,
      security_notifications: true,
      marketing_notifications: false,
      quiet_hours_enabled: false,
      quiet_hours_start: '22:00',
      quiet_hours_end: '08:00',
      notification_sound: 'default',
      vibration_enabled: true,
      badge_count_enabled: true,
      preview_enabled: true,
      group_similar: true,
    };
  }

  /**
   * Setup app state monitoring for inactivity tracking
   */
  private setupAppStateMonitoring(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (nextAppState === 'active') {
      this.lastActiveTime = new Date();
      // Clear notifications when app becomes active
      this.clearNotificationsOnAppActive();
    } else if (nextAppState === 'background') {
      // Schedule inactivity reminder
      this.scheduleInactivityReminder();
    }
  }

  /**
   * Clear notifications when app becomes active
   */
  private async clearNotificationsOnAppActive(): Promise<void> {
    try {
      await clearAllNotifications();
      await this.updateBadgeCount();
    } catch (error) {
      logger.error('Error clearing notifications on app active:', error);
    }
  }

  /**
   * Schedule inactivity reminder
   */
  private async scheduleInactivityReminder(): Promise<void> {
    if (!this.settings?.push_notifications) return;

    try {
      await scheduleInactivityReminder(
        "We miss you!",
        "Check out what's new on TikTok",
        24 // 24 hours
      );
    } catch (error) {
      logger.error('Error scheduling inactivity reminder:', error);
    }
  }

  /**
   * Update badge count based on unread notifications
   */
  async updateBadgeCount(): Promise<void> {
    if (!this.settings?.badge_count_enabled) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('notifications')
        .select('id')
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        logger.error('Error fetching unread notifications count:', error);
        return;
      }

      const unreadCount = data?.length || 0;
      await setBadgeCount(unreadCount);
    } catch (error) {
      logger.error('Error updating badge count:', error);
    }
  }

  /**
   * Check if notifications should be sent based on quiet hours
   */
  isInQuietHours(): boolean {
    if (!this.settings?.quiet_hours_enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMinute] = this.settings.quiet_hours_start.split(':').map(Number);
    const [endHour, endMinute] = this.settings.quiet_hours_end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;

    if (startTime <= endTime) {
      // Same day quiet hours (e.g., 22:00 to 08:00 next day)
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Overnight quiet hours (e.g., 22:00 to 08:00 next day)
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  /**
   * Check if a specific notification type is enabled
   */
  isNotificationTypeEnabled(type: keyof NotificationSettings): boolean {
    if (!this.settings) return true;
    return this.settings[type] as boolean;
  }

  /**
   * Update notification settings
   */
  async updateSettings(newSettings: Partial<NotificationSettings>): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('notification_settings')
        .upsert({
          user_id: user.id,
          ...newSettings,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        logger.error('Error updating notification settings:', error);
        return;
      }

      // Update local settings
      this.settings = { ...this.settings, ...newSettings } as NotificationSettings;
      logger.debug('Notification settings updated');
    } catch (error) {
      logger.error('Exception updating notification settings:', error);
    }
  }

  /**
   * Get current notification settings
   */
  getSettings(): NotificationSettings | null {
    return this.settings;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.isInitialized = false;
  }
}

export default NotificationManager;
