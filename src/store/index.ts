import {configureStore} from '@reduxjs/toolkit';
import {combineReducers} from 'redux';
import authReducer from './slices/authSlice';
//import cameraReducer from './slices/cameraSlice';
//import videoReducer from './slices/videoSlice';
import {authApi} from './api/authApi';
import {userApi} from './api/userApi';
import {profileApi} from './api/profileApi';
import {userManagementApi} from './api/userManagementApi';
import {settingsApi} from './api/settingsApi';
import {followersApi} from './api/followersApi';
import {accountApi} from './api/accountApi';
import {blockingApi} from './api/blockingApi';
import {discoveryApi} from './api/discoveryApi';
import {messagingApi} from './api/messagingApi';
import {videoLikesApi} from './api/videoLikesApi';
import {videoApi} from './api/videoApi';
import {securitySettingsApi} from './api/securitySettingsApi';
import {personalDataApi} from './api/personalDataApi';
import {loginSessionsApi} from './api/loginSessionsApi';
import { phoneVerificationApi } from './api/phoneVerificationApi';
import {musicLibraryApi} from './api/musicLibraryApi';
import {commentsApi} from './api/commentsApi';
import messagingReducer from './slices/messagingSlice';
import videoLikesReducer from './slices/videoLikesSlice';
import followersReducer from './slices/followersSlice';

const rootReducer = combineReducers({
  [authApi.reducerPath]: authApi.reducer,
  [userApi.reducerPath]: userApi.reducer,
  [profileApi.reducerPath]: profileApi.reducer,
  [userManagementApi.reducerPath]: userManagementApi.reducer,
  [settingsApi.reducerPath]: settingsApi.reducer,
  [followersApi.reducerPath]: followersApi.reducer,
  [accountApi.reducerPath]: accountApi.reducer,
  [blockingApi.reducerPath]: blockingApi.reducer,
  [discoveryApi.reducerPath]: discoveryApi.reducer,
  [securitySettingsApi.reducerPath]: securitySettingsApi.reducer,
  [messagingApi.reducerPath]: messagingApi.reducer,
  [loginSessionsApi.reducerPath]: loginSessionsApi.reducer,
  [videoLikesApi.reducerPath]: videoLikesApi.reducer,
  [videoApi.reducerPath]: videoApi.reducer,
  [personalDataApi.reducerPath]: personalDataApi.reducer,
  [phoneVerificationApi.reducerPath]: phoneVerificationApi.reducer,
  [musicLibraryApi.reducerPath]: musicLibraryApi.reducer,
  [commentsApi.reducerPath]: commentsApi.reducer,
  auth: authReducer,
  messaging: messagingReducer,
  videoLikes: videoLikesReducer,
  followers: followersReducer,
  //camera: cameraReducer,
  //video: videoReducer,
  // Add other reducers here
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(
      authApi.middleware,
      userApi.middleware,
      profileApi.middleware,
      userManagementApi.middleware,
      settingsApi.middleware,
      followersApi.middleware,
      accountApi.middleware,
      blockingApi.middleware,
      discoveryApi.middleware,
      securitySettingsApi.middleware,
      messagingApi.middleware,
      loginSessionsApi.middleware,
      videoLikesApi.middleware,
      videoApi.middleware,
      personalDataApi.middleware,
      phoneVerificationApi.middleware,
      musicLibraryApi.middleware,
      commentsApi.middleware,
    ),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export the typed hooks
export * from './hooks';
