import { createSlice, PayloadAction } from '@reduxjs/toolkit';


interface FollowData {
  isFollowing: boolean;
  count: number;
}

export interface FollowersState {
  [userId: string]: FollowData;
}

const initialState: FollowersState = {};

const followersSlice = createSlice({
  name: 'followers',
  initialState,
  reducers: {
    initializeFollowerState: (
      state,
      action: PayloadAction<{ userId: string; isFollowing: boolean; count: number }>
    ) => {
      state[action.payload.userId] = {
        isFollowing: action.payload.isFollowing,
        count: action.payload.count,
      };
    },
    setFollowing: (
      state,
      action: PayloadAction<{ userId: string; isFollowing: boolean }>
    ) => {
      if (state[action.payload.userId]) {
        state[action.payload.userId].isFollowing = action.payload.isFollowing;
      }
    },
    incrementFollowers: (state, action: PayloadAction<{ userId: string }>) => {
      const data = state[action.payload.userId];
      if (data) {
        data.count += 1;
      }
    },
    decrementFollowers: (state, action: PayloadAction<{ userId: string }>) => {
      const data = state[action.payload.userId];
      if (data) {
        data.count = Math.max(0, data.count - 1);
      }
    },
    setFollowerCount: (
      state,
      action: PayloadAction<{ userId: string; count: number }>
    ) => {
      if (state[action.payload.userId]) {
        state[action.payload.userId].count = action.payload.count;
      } else {
        state[action.payload.userId] = {
          isFollowing: false,
          count: action.payload.count,
        };
      }
    },
  },
});

export const {
  initializeFollowerState,
  setFollowing,
  setFollowerCount,
  incrementFollowers,
  decrementFollowers,
} = followersSlice.actions;

export const incrementFollowerCount = followersSlice.actions.incrementFollowers;
export const decrementFollowerCount = followersSlice.actions.decrementFollowers;

export const selectFollowerState =
  (userId: string) => (state: { followers: FollowersState }) =>
    state.followers[userId];

export const selectFollowerCount =
  (userId: string) => (state: { followers: FollowersState }) =>
    state.followers[userId]?.count ?? 0;

export default followersSlice.reducer;
