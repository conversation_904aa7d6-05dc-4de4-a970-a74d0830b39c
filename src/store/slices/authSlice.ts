import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { createSelector } from '@reduxjs/toolkit';

interface User {
  id: string;
  email?: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  profile_picture_url?: string;
  bio?: string;
  website?: string;
  pronouns?: string;
  followers_count?: number;
  following_count?: number;
  likes_count?: number;
  videos_count?: number;
  is_private?: boolean;
  is_verified?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<AuthState['user']>) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
      state.loading = false;
      state.error = null;
    },
    clearUser: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        // Ensure avatar_url is updated when profile_picture_url changes
        if (action.payload.profile_picture_url) {
          state.user.avatar_url = action.payload.profile_picture_url;
        }
      }
    },
    updateUserStats: (state, action: PayloadAction<{
      followers_count?: number;
      following_count?: number;
      likes_count?: number;
      videos_count?: number;
    }>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
});

export const { setUser, clearUser, setLoading, setError, updateUserProfile, updateUserStats } = authSlice.actions;

// Selectors
export const selectCurrentUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;

// Memoized selector for user stats to prevent unnecessary re-renders
export const selectUserStats = createSelector(
  [selectCurrentUser],
  (user) => ({
    followers: user?.followers_count || 0,
    following: user?.following_count || 0,
    likes: user?.likes_count || 0,
    videos: user?.videos_count || 0,
  })
);

// Export User type
export type { User };

export default authSlice.reducer;
