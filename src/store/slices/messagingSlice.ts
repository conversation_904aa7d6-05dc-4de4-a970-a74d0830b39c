import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Message, TypingStatus } from '../../types/messaging';

interface MessagingState {
  activeConversationId: string | null;
  typingUsers: Record<string, TypingStatus[]>; // conversationId -> typing users
  onlineUsers: Set<string>;
  unreadCount: number;
  isConnected: boolean;
}

const initialState: MessagingState = {
  activeConversationId: null,
  typingUsers: {},
  onlineUsers: new Set(),
  unreadCount: 0,
  isConnected: false,
};

const messagingSlice = createSlice({
  name: 'messaging',
  initialState,
  reducers: {
    setActiveConversation: (state, action: PayloadAction<string | null>) => {
      state.activeConversationId = action.payload;
    },
    
    setTypingUsers: (state, action: PayloadAction<{ conversationId: string; users: TypingStatus[] }>) => {
      const { conversationId, users } = action.payload;
      state.typingUsers[conversationId] = users;
    },
    
    addTypingUser: (state, action: PayloadAction<{ conversationId: string; user: TypingStatus }>) => {
      const { conversationId, user } = action.payload;
      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }
      const existingIndex = state.typingUsers[conversationId].findIndex(u => u.user_id === user.user_id);
      if (existingIndex >= 0) {
        state.typingUsers[conversationId][existingIndex] = user;
      } else {
        state.typingUsers[conversationId].push(user);
      }
    },
    
    removeTypingUser: (state, action: PayloadAction<{ conversationId: string; userId: string }>) => {
      const { conversationId, userId } = action.payload;
      if (state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = state.typingUsers[conversationId].filter(
          user => user.user_id !== userId
        );
      }
    },
    
    setOnlineUsers: (state, action: PayloadAction<string[]>) => {
      state.onlineUsers = new Set(action.payload);
    },
    
    addOnlineUser: (state, action: PayloadAction<string>) => {
      state.onlineUsers.add(action.payload);
    },
    
    removeOnlineUser: (state, action: PayloadAction<string>) => {
      state.onlineUsers.delete(action.payload);
    },
    
    setUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount = action.payload;
    },
    
    incrementUnreadCount: (state) => {
      state.unreadCount += 1;
    },
    
    decrementUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount = Math.max(0, state.unreadCount - action.payload);
    },
    
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    
    resetMessagingState: () => initialState,
  },
});

export const {
  setActiveConversation,
  setTypingUsers,
  addTypingUser,
  removeTypingUser,
  setOnlineUsers,
  addOnlineUser,
  removeOnlineUser,
  setUnreadCount,
  incrementUnreadCount,
  decrementUnreadCount,
  setConnectionStatus,
  resetMessagingState,
} = messagingSlice.actions;

export default messagingSlice.reducer;