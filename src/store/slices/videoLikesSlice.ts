import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface VideoLikeData {
  count: number;
  isLiked: boolean;
}

export interface VideoLikesState {
  [videoId: string]: VideoLikeData;
}

const initialState: VideoLikesState = {};

const videoLikesSlice = createSlice({
  name: 'videoLikes',
  initialState,
  reducers: {
    initializeLikeState: (
      state,
      action: PayloadAction<{videoId: string; count: number; isLiked: boolean}>,
    ) => {
      state[action.payload.videoId] = {
        count: action.payload.count,
        isLiked: action.payload.isLiked,
      };
    },
    incrementLike: (state, action: PayloadAction<{videoId: string}>) => {
      const data = state[action.payload.videoId];
      if (data) {
        data.count += 1;
      }
    },
    decrementLike: (state, action: PayloadAction<{videoId: string}>) => {
      const data = state[action.payload.videoId];
      if (data) {
        data.count = Math.max(0, data.count - 1);
      }
    },
    setLiked: (
      state,
      action: PayloadAction<{videoId: string; liked: boolean}>,
    ) => {
      const data = state[action.payload.videoId];
      if (data) {
        data.isLiked = action.payload.liked;
      }
    },
  },
});

export const {initializeLikeState, incrementLike, decrementLike, setLiked} =
  videoLikesSlice.actions;

export const selectVideoLikeState =
  (videoId: string) => (state: {videoLikes: VideoLikesState}) =>
    state.videoLikes[videoId];

export default videoLikesSlice.reducer;
