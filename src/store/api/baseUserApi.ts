import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base configuration for user-related APIs
export const createUserApi = (reducerPath: string, tagTypes: string[] = []) => {
  return createApi({
    reducerPath,
    baseQuery: fetchBaseQuery({ baseUrl: '/' }),
    tagTypes: ['User', 'Profile', 'Settings', 'Privacy', 'Notifications', 'Security', 'Followers', 'BlockedUsers', ...tagTypes],
    endpoints: () => ({}),
  });
};