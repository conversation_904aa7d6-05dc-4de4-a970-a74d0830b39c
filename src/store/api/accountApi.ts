import { supabase } from '../../integrations/supabase/client';
import { createUser<PERSON><PERSON> } from './baseUserApi';
import logger from '../../utils/logger';

export const accountApi = createUserApi('accountApi', ['AccountSettings']);

export const extendedAccountApi = accountApi.injectEndpoints({
  endpoints: (builder) => ({
    // Update username
    updateUsername: builder.mutation<{ success: boolean }, string>({
      queryFn: async (newUsername) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          // Check if username is already taken
          const { data: existingUser, error: checkError } = await supabase
            .from('users')
            .select('id')
            .eq('username', newUsername)
            .neq('auth_user_id', user.id)
            .single();

          if (checkError && checkError.code !== 'PGRST116') {
            throw checkError;
          }

          if (existingUser) {
            throw new Error('Username is already taken');
          }

          // Update username in users table
          const { error: updateError } = await supabase
            .from('users')
            .update({
              username: newUsername,
              updated_at: new Date().toISOString(),
            })
            .eq('auth_user_id', user.id);

          if (updateError) {throw updateError;}

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Update username error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update username',
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),

    // Update email
    updateEmail: builder.mutation<{ success: boolean }, { newEmail: string; currentPassword: string }>({
      queryFn: async ({ newEmail, currentPassword }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          // Verify current password by attempting to sign in
          const { error: passwordError } = await supabase.auth.signInWithPassword({
            email: user.email!,
            password: currentPassword,
          });

          if (passwordError) {
            throw new Error('Current password is incorrect');
          }

          // Update email in auth
          const { error: authError } = await supabase.auth.updateUser({
            email: newEmail,
          });

          if (authError) {throw authError;}

          // Update email in users table
          const { error: updateError } = await supabase
            .from('users')
            .update({
              email: newEmail,
              updated_at: new Date().toISOString(),
            })
            .eq('auth_user_id', user.id);

          if (updateError) {throw updateError;}

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Update email error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update email',
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),

    // Update phone number
    updatePhoneNumber: builder.mutation<{ success: boolean }, string>({
      queryFn: async (phoneNumber) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          // Update phone in users table
          const { error: updateError } = await supabase
            .from('users')
            .update({
              phone_number: phoneNumber,
              updated_at: new Date().toISOString(),
            })
            .eq('auth_user_id', user.id);

          if (updateError) {throw updateError;}

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Update phone number error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update phone number',
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),

    // Change password
    changePassword: builder.mutation<{ success: boolean }, { currentPassword: string; newPassword: string }>({
      queryFn: async ({ currentPassword, newPassword }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          // Verify current password
          const { error: passwordError } = await supabase.auth.signInWithPassword({
            email: user.email!,
            password: currentPassword,
          });

          if (passwordError) {
            throw new Error('Current password is incorrect');
          }

          // Update password
          const { error: updateError } = await supabase.auth.updateUser({
            password: newPassword,
          });

          if (updateError) {throw updateError;}

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Change password error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to change password',
            },
          };
        }
      },
      invalidatesTags: ['User'],
    }),
  }),
});

export const {
  useUpdateUsernameMutation,
  useUpdateEmailMutation,
  useUpdatePhoneNumberMutation,
  useChangePasswordMutation,
} = extendedAccountApi;