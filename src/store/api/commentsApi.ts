import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export interface Comment {
  id: string;
  text: string;
  video_id: string;
  user_id: string;
  parent_id?: string;
  likes: number;
  reply_count: number;
  created_at: string;
  updated_at: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  replies?: Comment[];
  isLiked: boolean;
  is_pinned?: boolean;
}

export interface CreateCommentRequest {
  text: string;
  video_id: string;
  parent_id?: string;
}

export interface CommentLikeRequest {
  comment_id: string;
}

export const commentsApi = createApi({
  reducerPath: 'commentsApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Comment', 'CommentLikes'],
  endpoints: (builder) => ({
    // Get comments for a video
    getVideoComments: builder.query<Comment[], { videoId: string }>({
      async queryFn({ videoId }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();

          // Fetch top-level comments using the optimized view
          const { data: comments, error } = await supabase
            .from('comments_with_details')
            .select('*')
            .eq('video_id', videoId)
            .is('parent_id', null)
            .order('created_at', { ascending: false });

          if (error) throw error;

          // Fetch replies for each comment
          const commentsWithReplies = await Promise.all(
            (comments || []).map(async (comment) => {
              const { data: replies, error: repliesError } = await supabase
                .from('comments_with_details')
                .select('*')
                .eq('parent_id', comment.id)
                .order('created_at', { ascending: true });

              if (repliesError) {
                logger.error('Error fetching replies:', repliesError);
              }

              // Check if user liked this comment
              let isLiked = false;
              if (user) {
                const { data: likeData } = await supabase
                  .rpc('user_liked_comment', {
                    comment_uuid: comment.id,
                    user_uuid: user.id
                  });
                isLiked = likeData || false;
              }

              // Check likes for replies
              const repliesWithLikes = await Promise.all(
                (replies || []).map(async (reply) => {
                  let replyIsLiked = false;
                  if (user) {
                    const { data: replyLikeData } = await supabase
                      .rpc('user_liked_comment', {
                        comment_uuid: reply.id,
                        user_uuid: user.id
                      });
                    replyIsLiked = replyLikeData || false;
                  }
                  return { ...reply, isLiked: replyIsLiked || false };
                })
              );

              return {
                ...comment,
                isLiked: isLiked || false,
                replies: repliesWithLikes,
              };
            })
          );

          return { data: commentsWithReplies };
        } catch (error: any) {
          logger.error('Get video comments error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch comments',
            },
          };
        }
      },
      providesTags: (_, __, { videoId }) => [
        { type: 'Comment', id: videoId },
        'Comment',
      ],
    }),

    // Create a new comment with optimistic updates
    createComment: builder.mutation<Comment, CreateCommentRequest>({
      async queryFn({ text, video_id, parent_id }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { data: comment, error } = await supabase
            .from('comments')
            .insert({
              text,
              video_id,
              parent_id,
              user_id: user.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (error) throw error;

          // Get user profile data
          const { data: profile } = await supabase
            .from('profiles')
            .select('profile_picture_url')
            .eq('user_id', user.id)
            .single();

          // Get username from auth metadata or email
          const username = user.user_metadata?.username ||
                          user.email?.split('@')[0] ||
                          'user';
          const fullName = user.user_metadata?.full_name ||
                          user.user_metadata?.name ||
                          'User';

          // Update video comments count
          if (!parent_id) {
            const { error: updateError } = await supabase.rpc('increment_video_comments', {
              video_id_param: video_id
            });

            if (updateError) {
              logger.error('Failed to update video comments count:', updateError);
            }
          } else {
            // Update parent comment reply count
            const { error: updateError } = await supabase.rpc('increment_comment_replies', {
              comment_uuid: parent_id
            });

            if (updateError) {
              logger.error('Failed to update parent comment reply count:', updateError);
            }
          }

          return {
            data: {
              ...comment,
              isLiked: false,
              likes: 0,
              reply_count: 0,
              username,
              full_name: fullName,
              avatar_url: profile?.profile_picture_url,
              replies: []
            }
          };
        } catch (error: any) {
          logger.error('Create comment error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to create comment',
            },
          };
        }
      },
      // Optimistic update
      async onQueryStarted({ text, video_id, parent_id }, { dispatch, queryFulfilled }) {
        // Create optimistic comment
        const tempId = `temp-${Date.now()}`;
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          const optimisticComment: Comment = {
            id: tempId,
            text,
            video_id,
            user_id: user.id,
            parent_id,
            likes: 0,
            reply_count: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            username: user.user_metadata?.username || user.email?.split('@')[0] || 'user',
            full_name: user.user_metadata?.full_name || 'User',
            avatar_url: undefined,
            isLiked: false,
            replies: []
          };

          // Update comments cache optimistically
          const patchResult = dispatch(
            commentsApi.util.updateQueryData('getVideoComments', { videoId: video_id }, (draft) => {
              if (parent_id) {
                // Add as reply
                const findAndAddReply = (comments: Comment[]): boolean => {
                  for (const comment of comments) {
                    if (comment.id === parent_id) {
                      comment.replies = comment.replies || [];
                      comment.replies.push(optimisticComment);
                      comment.reply_count = (comment.reply_count || 0) + 1;
                      return true;
                    }
                    if (comment.replies && findAndAddReply(comment.replies)) {
                      return true;
                    }
                  }
                  return false;
                };
                findAndAddReply(draft);
              } else {
                // Add as top-level comment
                draft.unshift(optimisticComment);
              }
            })
          );

          try {
            const result = await queryFulfilled;
            // Replace optimistic comment with real one
            dispatch(
              commentsApi.util.updateQueryData('getVideoComments', { videoId: video_id }, (draft) => {
                const replaceComment = (comments: Comment[]): boolean => {
                  for (let i = 0; i < comments.length; i++) {
                    if (comments[i].id === tempId) {
                      comments[i] = result.data;
                      return true;
                    }
                    if (comments[i].replies && replaceComment(comments[i].replies!)) {
                      return true;
                    }
                  }
                  return false;
                };
                replaceComment(draft);
              })
            );
          } catch {
            // Revert optimistic updates on error
            patchResult.undo();
          }
        }
      },
      invalidatesTags: (_, __, { video_id }) => [
        { type: 'Comment', id: video_id },
        'Comment',
      ],
    }),

    // Toggle comment like with optimistic updates
    toggleCommentLike: builder.mutation<
      { isLiked: boolean; likesCount: number },
      { commentId: string; isCurrentlyLiked: boolean; currentLikesCount: number; videoId: string }
    >({
      async queryFn({ commentId, isCurrentlyLiked, currentLikesCount }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          if (isCurrentlyLiked) {
            // Unlike the comment
            const { error } = await supabase
              .from('comment_likes')
              .delete()
              .eq('comment_id', commentId)
              .eq('user_id', user.id);

            if (error) throw error;

            // Update comment likes count
            const { error: updateError } = await supabase
              .from('comments')
              .update({
                likes: Math.max(0, currentLikesCount - 1),
                updated_at: new Date().toISOString()
              })
              .eq('id', commentId);

            if (updateError) throw updateError;

            return {
              data: {
                isLiked: false,
                likesCount: Math.max(0, currentLikesCount - 1)
              }
            };
          } else {
            // Like the comment
            const { error } = await supabase
              .from('comment_likes')
              .insert({
                comment_id: commentId,
                user_id: user.id,
                created_at: new Date().toISOString()
              });

            if (error) throw error;

            // Update comment likes count
            const { error: updateError } = await supabase
              .from('comments')
              .update({
                likes: currentLikesCount + 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', commentId);

            if (updateError) throw updateError;

            return {
              data: {
                isLiked: true,
                likesCount: currentLikesCount + 1
              }
            };
          }
        } catch (error: any) {
          logger.error('Toggle comment like error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to toggle comment like',
            },
          };
        }
      },
      // Optimistic update
      async onQueryStarted({ commentId, videoId, isCurrentlyLiked, currentLikesCount }, { dispatch, queryFulfilled }) {
        // Update the cache optimistically
        const optimisticUpdate = {
          isLiked: !isCurrentlyLiked,
          likesCount: isCurrentlyLiked ? currentLikesCount - 1 : currentLikesCount + 1
        };

        // Update comments cache
        const patchResult = dispatch(
          commentsApi.util.updateQueryData('getVideoComments', { videoId }, (draft) => {
            const updateCommentInList = (comments: Comment[]) => {
              for (const comment of comments) {
                if (comment.id === commentId) {
                  comment.likes = optimisticUpdate.likesCount;
                  comment.isLiked = optimisticUpdate.isLiked;
                  return true;
                }
                if (comment.replies && updateCommentInList(comment.replies)) {
                  return true;
                }
              }
              return false;
            };
            updateCommentInList(draft);
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic updates on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_, __, { commentId, videoId }) => [
        { type: 'CommentLikes', id: commentId },
        { type: 'Comment', id: videoId },
      ],
    }),

    // Edit a comment
    editComment: builder.mutation<Comment, { commentId: string; text: string; videoId: string }>({
      async queryFn({ commentId, text }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { data: comment, error } = await supabase
            .from('comments')
            .update({
              text,
              updated_at: new Date().toISOString()
            })
            .eq('id', commentId)
            .eq('user_id', user.id) // Only allow users to edit their own comments
            .select()
            .single();

          if (error) throw error;

          return { data: comment as Comment };
        } catch (error: any) {
          logger.error('Edit comment error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to edit comment',
            },
          };
        }
      },
      // Optimistic update
      async onQueryStarted({ commentId, text, videoId }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          commentsApi.util.updateQueryData('getVideoComments', { videoId }, (draft) => {
            const updateCommentInList = (comments: Comment[]): boolean => {
              for (const comment of comments) {
                if (comment.id === commentId) {
                  comment.text = text;
                  comment.updated_at = new Date().toISOString();
                  return true;
                }
                if (comment.replies && updateCommentInList(comment.replies)) {
                  return true;
                }
              }
              return false;
            };
            updateCommentInList(draft);
          })
        );

        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      invalidatesTags: (_, __, { videoId }) => [
        { type: 'Comment', id: videoId },
      ],
    }),

    // Pin/Unpin a comment (for video owners)
    toggleCommentPin: builder.mutation<{ isPinned: boolean }, { commentId: string; videoId: string; isPinned: boolean }>({
      async queryFn({ commentId, videoId, isPinned }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          // Check if user owns the video
          const { data: video, error: videoError } = await supabase
            .from('videos')
            .select('user_id')
            .eq('id', videoId)
            .single();

          if (videoError) throw videoError;
          if (video.user_id !== user.id) {
            throw new Error('Only video owner can pin comments');
          }

          // If pinning, unpin any existing pinned comment first
          if (!isPinned) {
            await supabase
              .from('comments')
              .update({ is_pinned: false })
              .eq('video_id', videoId)
              .eq('is_pinned', true);
          }

          // Toggle pin status
          const { error } = await supabase
            .from('comments')
            .update({
              is_pinned: !isPinned,
              updated_at: new Date().toISOString()
            })
            .eq('id', commentId);

          if (error) throw error;

          return { data: { isPinned: !isPinned } };
        } catch (error: any) {
          logger.error('Toggle comment pin error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to toggle comment pin',
            },
          };
        }
      },
      invalidatesTags: (_, __, { videoId }) => [
        { type: 'Comment', id: videoId },
      ],
    }),

    // Delete a comment with optimistic updates
    deleteComment: builder.mutation<{ success: boolean }, { commentId: string; videoId: string; parentId?: string }>({
      async queryFn({ commentId, videoId, parentId }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          // Get comment to check ownership
          const { data: comment, error: fetchError } = await supabase
            .from('comments')
            .select('user_id, reply_count')
            .eq('id', commentId)
            .single();

          if (fetchError) throw fetchError;
          if (comment.user_id !== user.id) {
            throw new Error('You can only delete your own comments');
          }

          // Delete the comment and all its replies
          const { error } = await supabase
            .from('comments')
            .delete()
            .eq('id', commentId);

          if (error) throw error;

          // Update counts
          if (!parentId) {
            // Update video comments count
            const { error: updateError } = await supabase.rpc('decrement_video_comments', {
              video_id_param: videoId,
              decrement_by: 1 + (comment.reply_count || 0)
            });

            if (updateError) {
              logger.error('Failed to update video comments count:', updateError);
            }
          } else {
            // Update parent comment reply count
            const { error: updateError } = await supabase.rpc('decrement_comment_replies', {
              comment_uuid: parentId
            });

            if (updateError) {
              logger.error('Failed to update parent comment reply count:', updateError);
            }
          }

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Delete comment error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to delete comment',
            },
          };
        }
      },
      // Optimistic update
      async onQueryStarted({ commentId, videoId }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          commentsApi.util.updateQueryData('getVideoComments', { videoId }, (draft) => {
            const removeCommentFromList = (comments: Comment[]): boolean => {
              for (let i = 0; i < comments.length; i++) {
                if (comments[i].id === commentId) {
                  comments.splice(i, 1);
                  return true;
                }
                if (comments[i].replies && removeCommentFromList(comments[i].replies!)) {
                  return true;
                }
              }
              return false;
            };
            removeCommentFromList(draft);
          })
        );

        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      invalidatesTags: (_, __, { videoId }) => [
        { type: 'Comment', id: videoId },
      ],
    }),
  }),
});

export const {
  useGetVideoCommentsQuery,
  useCreateCommentMutation,
  useToggleCommentLikeMutation,
  useEditCommentMutation,
  useToggleCommentPinMutation,
  useDeleteCommentMutation,
} = commentsApi;

// Legacy exports for backward compatibility
export const useLikeCommentMutation = useToggleCommentLikeMutation;
export const useUnlikeCommentMutation = useToggleCommentLikeMutation;
