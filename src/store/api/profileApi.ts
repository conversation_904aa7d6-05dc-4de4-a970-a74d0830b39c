import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { User, UpdateProfileData } from '../../types/user';
import { supabase } from '../../integrations/supabase/client';
import { createUserApi } from './baseUserApi';
import logger from '../../utils/logger';

export const profileApi = createApi({
  reducerPath: 'profileApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['Profile'],
  endpoints: (builder) => ({
    // Get current user profile
    getCurrentProfile: builder.query<User, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No user found');}

          logger.debug('Fetching profile for user:', user.id);

          // Try to get profile from profiles table
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', user.id)
            .maybeSingle();

          logger.debug('Profile query result:', { profile, error });

          // Get followers count
          const { count: followersCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('following_id', user.id);

          // Get following count
          const { count: followingCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('follower_id', user.id);

          logger.debug('Followers count:', followersCount);
          logger.debug('Following count:', followingCount);

          // Create user data from auth user and profile (if exists)
          const userData: User = {
            id: user.id,
            email: user.email || '',
            username: user.email?.split('@')[0] || 'user', // No username in profiles table
            full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
            profile_picture_url: profile?.profile_picture_url || user.user_metadata?.avatar_url || undefined,
            avatar_url: profile?.profile_picture_url || user.user_metadata?.avatar_url || undefined,
            bio: profile?.bio || undefined,
            user_tag: profile?.user_tag || 'Supporter',
            followers_count: followersCount || 0,
            following_count: followingCount || 0,
            likes_count: 0,
            videos_count: 0,
            is_private: false,
            is_verified: false,
            is_banned: false,
            auth_user_id: user.id,
            created_at: user.created_at || new Date().toISOString(),
            updated_at: user.updated_at || new Date().toISOString(),
          };

          logger.debug('Returning user data:', userData);
          return { data: userData };
        } catch (error: any) {
          logger.error('Get profile error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch profile',
            },
          };
        }
      },
      providesTags: ['Profile'],
    }),

    // Get user profile by ID
    getUserById: builder.query<User, string>({
      queryFn: async (userId) => {
        try {
          logger.debug('Fetching profile for user ID:', userId);

          // Get user profile from profiles table
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', userId)
            .maybeSingle();

          if (error) {
            logger.error('Error fetching user profile:', error);
            throw error;
          }

          // Get followers count
          const { count: followersCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('following_id', userId);

          // Get following count
          const { count: followingCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('follower_id', userId);

          // Get user auth data for email (if available)
          const { data: authUser } = await supabase.auth.admin.getUserById(userId);

          // Create user data from profile and auth data
          const userData: User = {
            id: userId,
            email: authUser?.user?.email || '',
            username: profile?.username || authUser?.user?.email?.split('@')[0] || 'user',
            full_name: profile?.full_name || authUser?.user?.user_metadata?.full_name || 'User',
            avatar_url: profile?.avatar_url || authUser?.user?.user_metadata?.avatar_url,
            bio: profile?.bio,
            followers_count: followersCount || 0,
            following_count: followingCount || 0,
            created_at: profile?.created_at || authUser?.user?.created_at || new Date().toISOString(),
            updated_at: profile?.updated_at || authUser?.user?.updated_at || new Date().toISOString(),
          };

          logger.debug('User profile fetched successfully:', userData);
          return { data: userData };
        } catch (error: any) {
          logger.error('Get user by ID error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch user profile'
            }
          };
        }
      },
      providesTags: (result, error, userId) => [
        { type: 'Profile', id: userId },
        'Profile',
      ],
    }),

    // Update user profile
    updateProfile: builder.mutation<User, UpdateProfileData>({
      queryFn: async (updateData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No user found');}

          logger.debug('Updating profile for user:', user.id);
          logger.debug('Update data:', updateData);

          // Check if profile exists
          const { data: existingProfile } = await supabase
            .from('profiles')
            .select('user_id')
            .eq('user_id', user.id)
            .maybeSingle();

          let result;
          if (existingProfile) {
            // Update existing profile
            logger.debug('Updating existing profile');
            const { data, error } = await supabase
              .from('profiles')
              .update({
                bio: updateData.bio || null,
                user_tag: updateData.user_tag || 'Supporter',
              })
              .eq('user_id', user.id)
              .select()
              .single();

            if (error) {throw error;}
            result = data;
          } else {
            // Create new profile
            logger.debug('Creating new profile');
            const { data, error } = await supabase
              .from('profiles')
              .insert({
                user_id: user.id,
                bio: updateData.bio || null,
                profile_picture_url: null,
                banner_image_url: null,
                user_tag: updateData.user_tag || 'Supporter',
              })
              .select()
              .single();

            if (error) {throw error;}
            result = data;
          }

          logger.debug('Profile operation successful:', result);

          // Get followers count
          const { count: followersCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('following_id', user.id);

          // Get following count
          const { count: followingCount } = await supabase
            .from('user_follows')
            .select('*', { count: 'exact', head: true })
            .eq('follower_id', user.id);

          // Return updated user data
          const userData: User = {
            id: user.id,
            email: user.email || '',
            username: user.email?.split('@')[0] || 'user',
            full_name: user.user_metadata?.full_name || 'User',
            profile_picture_url: result.profile_picture_url || undefined,
            avatar_url: result.profile_picture_url || undefined,
            bio: result.bio || undefined,
            user_tag: result.user_tag || 'Supporter',
            followers_count: followersCount || 0,
            following_count: followingCount || 0,
            likes_count: 0,
            videos_count: 0,
            is_private: false,
            is_verified: false,
            is_banned: false,
            auth_user_id: user.id,
            created_at: user.created_at || new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return { data: userData };
        } catch (error: any) {
          logger.error('Profile update error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update profile',
            },
          };
        }
      },
      invalidatesTags: ['Profile'],
    }),

    // Upload avatar
    uploadAvatar: builder.mutation<{ avatar_url: string }, { file: any; userId: string }>({
      queryFn: async ({ file, userId }) => {
        try {
          logger.debug('Uploading avatar for user:', userId);

          const fileExt = file.name?.split('.').pop() || 'jpg';
          const fileName = `${userId}-${Date.now()}.${fileExt}`;
          const filePath = `${userId}/${fileName}`; // Store in user-specific folder

          logger.debug('Uploading file to path:', filePath);

          // For React Native, we need to create a proper file object
          const response = await fetch(file.uri);
          const blob = await response.blob();

          // Upload file to Supabase Storage
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('profile.pictures.bucket')
            .upload(filePath, blob, {
              cacheControl: '3600',
              upsert: true,
              contentType: file.type || 'image/jpeg',
            });

          if (uploadError) {
            logger.error('Avatar upload error:', uploadError);
            throw uploadError;
          }

          // Get public URL
          const { data: { publicUrl } } = supabase.storage
            .from('profile.pictures.bucket')
            .getPublicUrl(filePath);

          logger.debug('Avatar uploaded successfully:', publicUrl);

          // Update profile with new avatar URL
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              profile_picture_url: publicUrl,
            })
            .eq('user_id', userId);

          if (updateError) {
            logger.error('Avatar URL update error:', updateError);
            // Don't throw error if profile doesn't exist yet
            logger.debug('Profile may not exist yet, avatar URL saved for later');
          }

          return { data: { avatar_url: publicUrl } };
        } catch (error: any) {
          logger.error('Avatar upload error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to upload avatar',
            },
          };
        }
      },
      invalidatesTags: ['Profile'],
    }),
  }),
});

export const {
  useGetCurrentProfileQuery,
  useGetUserByIdQuery,
  useUpdateProfileMutation: useUpdateProfileMutationNew,
  useUploadAvatarMutation: useUploadAvatarMutationNew,
} = profileApi;
