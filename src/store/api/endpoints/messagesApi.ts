import { EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../../integrations/supabase/client';
import {
  MessageWithSender,
  SendMessageRequest,
} from '../../../types/messaging';
import { 
  ensureArray, 
  handleAuthError, 
  validateConversationAccess, 
  formatMessageData 
} from '../utils/apiHelpers';
import logger from '../../../utils/logger';

export const messagesEndpoints = (builder: EndpointBuilder<any, any, any>) => ({
  // Get messages for a conversation
  getMessages: builder.query<MessageWithSender[], string>({
    async queryFn(conversationId, _api, _extraOptions, _baseQuery) {
      try {
        const {
          data: { user },
          error: authError,
        } = await supabase.auth.getUser();

        if (authError || !user) {
          logger.error('Auth error in getMessages:', authError);
          return handleAuthError(authError);
        }

        logger.debug('Fetching messages for conversation:', conversationId);

        // Validate conversation access
        const { hasAccess, error: accessError } = await validateConversationAccess(
          supabase,
          conversationId,
          user.id
        );

        if (!hasAccess) {
          logger.error('User does not have access to conversation:', conversationId);
          return { error: accessError || 'Access denied to conversation' };
        }

        // Fetch messages with sender info
        const { data: messagesData, error: messagesError } = await supabase
          .from('messages')
          .select(`
            id,
            content,
            type,
            created_at,
            updated_at,
            sender_id,
            conversation_id,
            media_url,
            file_name,
            file_type,
            file_size,
            thumbnail_url,
            is_deleted,
            is_edited,
            edited_at,
            reply_to_message_id,
            read_by,
            status,
            metadata
          `)
          .eq('conversation_id', conversationId)
          .eq('is_deleted', false)
          .order('created_at', { ascending: true });

        if (messagesError) {
          logger.error('Error fetching messages:', messagesError);
          return { error: messagesError.message };
        }

        const messages = ensureArray(messagesData);
        logger.debug(`Found ${messages.length} messages`);

        if (messages.length === 0) {
          return { data: [] };
        }

        // Get unique sender IDs
        const senderIds = [...new Set(messages.map(m => m.sender_id))];

        // Fetch sender details from users table
        const { data: sendersData, error: sendersError } = await supabase
          .from('users')
          .select('id, username, full_name, auth_user_id')
          .in('id', senderIds); // Use public.users.id

        if (sendersError) {
          logger.error('Error fetching senders:', sendersError);
          return { error: sendersError.message };
        }

        const senders = ensureArray(sendersData);

        // Get profile data separately (profiles use auth_user_id)
        const authUserIds = senders.map(s => s.auth_user_id).filter(Boolean);
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, profile_picture_url, bio, user_tag')
          .in('user_id', authUserIds);

        if (profilesError) {
          logger.error('Error fetching profiles:', profilesError);
          return { error: profilesError.message };
        }

        const profiles = ensureArray(profilesData);

        // Create profile map (profiles use auth_user_id as key)
        const profilesMap = new Map(profiles.map(p => [p.user_id, p]));

        // Map senders by public.users.id and combine with profile data
        const sendersMap = new Map(senders.map(s => {
          const profile = profilesMap.get(s.auth_user_id);
          return [s.id, { // Use public.users.id as key
            id: s.id, // Use public.users.id
            username: s.username,
            full_name: s.full_name,
            avatar_url: profile?.profile_picture_url || null,
            profile_picture_url: profile?.profile_picture_url || null,
            is_online: false // TODO: Add online status logic
          }];
        }));

        // Combine messages with sender info
        const result: MessageWithSender[] = messages.map(message => {
          const sender = sendersMap.get(message.sender_id);
          return formatMessageData(message, sender);
        });

        logger.debug(`Processed ${result.length} messages with sender info`);
        return { data: result };

      } catch (error) {
        logger.error('getMessages exception:', error);
        return { error: 'Failed to fetch messages' };
      }
    },
    providesTags: (result, error, conversationId) => [
      { type: 'Message', id: 'LIST' },
      { type: 'Message', id: conversationId },
    ],
  }),

  // Send message - TikTok style with enhanced features
  sendMessage: builder.mutation<string, SendMessageRequest>({
    async queryFn({
      conversation_id,
      content,
      media_url,
      message_type = 'text',
      file_url,
      file_name,
      file_type,
      file_size,
      thumbnail_url,
      duration,
      reply_to_message_id,
      forwarded_from_message_id,
      thread_id,
      expires_at,
      metadata,
    }) {
      try {
        // Get the auth user ID
        const authUser = (await supabase.auth.getUser()).data.user;
        if (!authUser) {
          return { error: 'User not authenticated' };
        }

        // Get the public user ID from the users table
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (userError || !publicUser) {
          logger.error('Error finding public user:', userError);
          return { error: 'User not found in public users table' };
        }

        // Use the enhanced send_message function with all TikTok-style parameters
        const { data, error } = await supabase.rpc('send_message', {
          conversation_id_param: conversation_id,
          sender_id_param: publicUser.id, // Use public.users.id
          content_param: content || null,
          message_type_param: message_type,
          file_url_param: file_url || media_url || null,
          file_type_param: file_type || null,
          file_name_param: file_name || null,
          file_size_param: file_size || null,
          thumbnail_url_param: thumbnail_url || null,
          duration_param: duration || null,
          reply_to_message_id_param: reply_to_message_id || null,
          forwarded_from_message_id_param: forwarded_from_message_id || null,
          thread_id_param: thread_id || null,
          expires_at_param: expires_at || null,
          metadata_param: metadata || null,
        });

        if (error) {
          logger.error('Error sending message:', error);
          return { error: error.message };
        }

        logger.debug('Message sent successfully:', data);
        return { data };
      } catch (error) {
        logger.error('sendMessage exception:', error);
        return { error: 'Failed to send message' };
      }
    },
    invalidatesTags: (result, error, { conversation_id }) => [
      { type: 'Message', id: 'LIST' },
      { type: 'Message', id: conversation_id },
      'Conversation',
    ],
  }),

  // Mark messages as read - TikTok style with enhanced tracking
  markMessagesAsRead: builder.mutation<boolean, { conversation_id: string; message_ids?: string[] }>({
    async queryFn({ conversation_id, message_ids }) {
      try {
        // Use the enhanced mark_messages_as_read function
        const { data, error } = await supabase.rpc('mark_messages_as_read', {
          conversation_id_param: conversation_id,
          message_ids: message_ids || null,
        });

        if (error) {
          logger.error('Error marking messages as read:', error);
          return { error: error.message };
        }

        logger.debug('Messages marked as read for conversation:', conversation_id);
        return { data: true };
      } catch (error) {
        logger.error('markMessagesAsRead exception:', error);
        return { error: 'Failed to mark messages as read' };
      }
    },
    invalidatesTags: (result, error, conversationId) => [
      { type: 'Message', id: conversationId },
      'Conversation',
    ],
  }),

  // Add message reaction - TikTok style with emoji support
  addMessageReaction: builder.mutation<boolean, { messageId: string; reaction: string; reactionType?: string }>({
    async queryFn({ messageId, reaction, reactionType = 'emoji' }) {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser) {
          return { error: 'No authenticated user found' };
        }

        // Get the public user ID
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (userError || !publicUser) {
          logger.error('Error finding public user:', userError);
          return { error: 'User not found in public users table' };
        }

        const { error } = await supabase
          .from('message_reactions')
          .upsert({
            message_id: messageId,
            user_id: publicUser.id, // Use public.users.id
            reaction: reaction,
            reaction_type: reactionType,
          });

        if (error) {
          logger.error('Error adding message reaction:', error);
          return { error: error.message };
        }

        logger.debug('Message reaction added:', { messageId, reaction, reactionType });
        return { data: true };
      } catch (error) {
        logger.error('addMessageReaction exception:', error);
        return { error: 'Failed to add reaction' };
      }
    },
    invalidatesTags: ['Message'],
  }),

  // Remove message reaction - TikTok style
  removeMessageReaction: builder.mutation<boolean, { messageId: string; reaction: string }>({
    async queryFn({ messageId, reaction }) {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser) {
          return { error: 'No authenticated user found' };
        }

        // Get the public user ID
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (userError || !publicUser) {
          logger.error('Error finding public user:', userError);
          return { error: 'User not found in public users table' };
        }

        const { error } = await supabase
          .from('message_reactions')
          .delete()
          .eq('message_id', messageId)
          .eq('user_id', publicUser.id) // Use public.users.id
          .eq('reaction', reaction);

        if (error) {
          logger.error('Error removing message reaction:', error);
          return { error: error.message };
        }

        logger.debug('Message reaction removed:', { messageId, reactionType });
        return { data: true };
      } catch (error) {
        logger.error('removeMessageReaction exception:', error);
        return { error: 'Failed to remove reaction' };
      }
    },
    invalidatesTags: ['Message'],
  }),

  // Forward message
  forwardMessage: builder.mutation<string, { messageId: string; conversationIds: string[] }>({
    async queryFn({ messageId, conversationIds }) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          return { error: 'No authenticated user found' };
        }

        // Get the original message
        const { data: originalMessage, error: fetchError } = await supabase
          .from('messages')
          .select('*')
          .eq('id', messageId)
          .single();

        if (fetchError || !originalMessage) {
          logger.error('Error fetching original message:', fetchError);
          return { error: 'Original message not found' };
        }

        // Forward to each conversation
        const forwardPromises = conversationIds.map(conversationId =>
          supabase.rpc('send_message', {
            conversation_id_param: conversationId,
            sender_id_param: user.id,
            content_param: originalMessage.content,
            message_type_param: originalMessage.type,
            file_url_param: originalMessage.media_url,
            file_type_param: originalMessage.file_type,
            file_name_param: originalMessage.file_name,
            file_size_param: originalMessage.file_size,
            thumbnail_url_param: originalMessage.thumbnail_url,
            forwarded_from_message_id_param: messageId,
          })
        );

        const results = await Promise.all(forwardPromises);

        // Check if any forwards failed
        const failedForwards = results.filter(result => result.error);
        if (failedForwards.length > 0) {
          logger.error('Some message forwards failed:', failedForwards);
          return { error: 'Some forwards failed' };
        }

        logger.debug('Message forwarded successfully:', { messageId, conversationIds });
        return { data: 'Message forwarded successfully' };
      } catch (error) {
        logger.error('forwardMessage exception:', error);
        return { error: 'Failed to forward message' };
      }
    },
    invalidatesTags: ['Message', 'Conversation'],
  }),

  // Delete message
  deleteMessage: builder.mutation<boolean, string>({
    async queryFn(messageId) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          return { error: 'No authenticated user found' };
        }

        const { error } = await supabase
          .from('messages')
          .update({
            is_deleted: true,
            deleted_at: new Date().toISOString(),
            content: 'This message was deleted',
          })
          .eq('id', messageId)
          .eq('sender_id', user.id);

        if (error) {
          logger.error('Error deleting message:', error);
          return { error: error.message };
        }

        logger.debug('Message deleted:', messageId);
        return { data: true };
      } catch (error) {
        logger.error('deleteMessage exception:', error);
        return { error: 'Failed to delete message' };
      }
    },
    invalidatesTags: ['Message'],
  }),

  // Edit message
  editMessage: builder.mutation<boolean, { messageId: string; content: string }>({
    async queryFn({ messageId, content }) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          return { error: 'No authenticated user found' };
        }

        // Get the public user ID
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', user.id)
          .single();

        if (userError || !publicUser) {
          logger.error('Error finding public user:', userError);
          return { error: 'User not found in public users table' };
        }

        // Get current message to save edit history
        const { data: currentMessage, error: fetchError } = await supabase
          .from('messages')
          .select('content, edit_history')
          .eq('id', messageId)
          .eq('sender_id', publicUser.id)
          .single();

        if (fetchError || !currentMessage) {
          logger.error('Error fetching current message:', fetchError);
          return { error: 'Message not found or not authorized' };
        }

        // Build edit history
        const editHistory = currentMessage.edit_history || [];
        editHistory.push({
          content: currentMessage.content,
          edited_at: new Date().toISOString(),
        });

        const { error } = await supabase
          .from('messages')
          .update({
            content,
            is_edited: true,
            edited_at: new Date().toISOString(),
            edit_history: editHistory,
          })
          .eq('id', messageId)
          .eq('sender_id', publicUser.id);

        if (error) {
          logger.error('Error editing message:', error);
          return { error: error.message };
        }

        logger.debug('Message edited with history:', messageId);
        return { data: true };
      } catch (error) {
        logger.error('editMessage exception:', error);
        return { error: 'Failed to edit message' };
      }
    },
    invalidatesTags: ['Message'],
  }),

  // Update typing indicator - TikTok style real-time feature
  updateTypingIndicator: builder.mutation<void, { conversation_id: string; is_typing: boolean; typing_type?: 'text' | 'voice' | 'media' }>({
    async queryFn({ conversation_id, is_typing, typing_type = 'text' }) {
      try {
        const { data, error } = await supabase.rpc('update_typing_indicator', {
          conversation_id_param: conversation_id,
          is_typing_param: is_typing,
          typing_type_param: typing_type,
        });

        if (error) {
          logger.error('Error updating typing indicator:', error);
          return { error: error.message };
        }

        logger.debug('Typing indicator updated:', { conversation_id, is_typing, typing_type });
        return { data: undefined };
      } catch (error) {
        logger.error('updateTypingIndicator exception:', error);
        return { error: 'Failed to update typing indicator' };
      }
    },
    invalidatesTags: [],
  }),

  // Update user presence - TikTok style online status
  updateUserPresence: builder.mutation<void, { status: 'online' | 'away' | 'busy' | 'offline'; device_info?: any }>({
    async queryFn({ status, device_info = {} }) {
      try {
        const { data, error } = await supabase.rpc('update_user_presence', {
          status_param: status,
          device_info_param: device_info,
        });

        if (error) {
          logger.error('Error updating user presence:', error);
          return { error: error.message };
        }

        logger.debug('User presence updated:', { status, device_info });
        return { data: undefined };
      } catch (error) {
        logger.error('updateUserPresence exception:', error);
        return { error: 'Failed to update user presence' };
      }
    },
    invalidatesTags: [],
  }),

  // Get typing indicators for a conversation
  getTypingIndicators: builder.query<any[], string>({
    async queryFn(conversationId) {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser) {
          return { error: 'No authenticated user found' };
        }

        // Get the public user ID
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (userError || !publicUser) {
          return { error: 'User not found' };
        }

        const { data, error } = await supabase
          .from('typing_indicators')
          .select(`
            user_id,
            is_typing,
            typing_type,
            last_activity_at,
            users!inner(username, full_name)
          `)
          .eq('conversation_id', conversationId)
          .eq('is_typing', true)
          .neq('user_id', publicUser.id) // Exclude current user
          .gte('last_activity_at', new Date(Date.now() - 30000).toISOString()); // Last 30 seconds

        if (error) {
          logger.error('Error fetching typing indicators:', error);
          return { error: error.message };
        }

        return { data: data || [] };
      } catch (error) {
        logger.error('getTypingIndicators exception:', error);
        return { error: 'Failed to fetch typing indicators' };
      }
    },
    providesTags: [],
  }),

  // Get user presence for conversation participants
  getUserPresence: builder.query<any[], string>({
    async queryFn(conversationId) {
      try {
        const { data, error } = await supabase
          .from('user_presence')
          .select(`
            user_id,
            status,
            last_seen_at,
            is_active,
            users!inner(username, full_name)
          `)
          .in('user_id',
            supabase
              .from('conversation_participants')
              .select('user_id')
              .eq('conversation_id', conversationId)
              .eq('is_active', true)
          );

        if (error) {
          logger.error('Error fetching user presence:', error);
          return { error: error.message };
        }

        return { data: data || [] };
      } catch (error) {
        logger.error('getUserPresence exception:', error);
        return { error: 'Failed to fetch user presence' };
      }
    },
    providesTags: [],
  }),
});
