import { EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../../integrations/supabase/client';
import {
  ConversationWithDetails,
  AddParticipantRequest,
} from '../../../types/messaging';
import { ensureArray, handleAuthError, formatUserForConversation } from '../utils/apiHelpers';
import logger from '../../../utils/logger';

export const conversationsEndpoints = (builder: EndpointBuilder<any, any, any>) => ({
  // Get conversations with last message and other participant
  getConversations: builder.query<ConversationWithDetails[], void>({
    async queryFn(_arg, api, _extraOptions, _baseQuery) {
      try {
        // 1. Auth
        const {
          data: { user: authUser },
          error: authError,
        } = await supabase.auth.getUser();

        if (authError || !authUser) {
          logger.error('Auth error in getConversations:', authError);
          return handleAuthError(authError);
        }

        // Get the public user ID
        const { data: publicUser, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (userError || !publicUser) {
          logger.error('Error finding public user:', userError);
          return { error: 'User not found in public users table' };
        }

        const userId = publicUser.id;
        logger.debug('Fetching conversations for user:', userId);

        // 2. Get conversations where user is a participant (new schema)
        const { data: conversationsData, error: conversationsError } = await supabase
          .from('conversations')
          .select(`
            id,
            type,
            name,
            description,
            avatar_url,
            created_by,
            last_message_id,
            last_message_at,
            last_activity_at,
            is_active,
            is_archived,
            group_settings,
            is_public,
            metadata,
            created_at,
            updated_at
          `)
          .eq('is_active', true)
          .in('id',
            supabase
              .from('conversation_participants')
              .select('conversation_id')
              .eq('user_id', userId)
              .eq('is_active', true)
              .eq('is_archived', false)
          )
          .order('last_activity_at', { ascending: false, nullsFirst: false })
          .order('created_at', { ascending: false });

        if (conversationsError) {
          logger.error('Error fetching conversations:', conversationsError);
          return { error: conversationsError.message };
        }

        const conversations = ensureArray(conversationsData);
        logger.debug(`Found ${conversations.length} conversations`);

        if (conversations.length === 0) {
          return { data: [] };
        }

        // 3. Get last messages for all conversations (updated schema)
        const conversationIds = conversations.map(c => c.id);
        const { data: messagesData, error: messagesError } = await supabase
          .from('messages')
          .select(`
            id,
            content,
            type,
            created_at,
            sender_id,
            conversation_id,
            media_url,
            thumbnail_url,
            file_name,
            file_type,
            file_size,
            duration,
            is_deleted,
            status,
            reaction_counts,
            has_reactions
          `)
          .in('conversation_id', conversationIds)
          .eq('is_deleted', false)
          .order('created_at', { ascending: false });

        if (messagesError) {
          logger.error('Error fetching last messages:', messagesError);
          return { error: messagesError.message };
        }

        const messages = ensureArray(messagesData);

        // 4. Get conversation participants for each conversation
        const { data: participantsData, error: participantsError } = await supabase
          .from('conversation_participants')
          .select(`
            conversation_id,
            user_id,
            role,
            is_active,
            last_read_message_id,
            last_read_at,
            last_seen_at,
            notification_settings,
            custom_nickname,
            is_pinned
          `)
          .in('conversation_id', conversationIds)
          .eq('is_active', true);

        if (participantsError) {
          logger.error('Error fetching participants:', participantsError);
          return { error: participantsError.message };
        }

        const participants = ensureArray(participantsData);

        // 5. Get user details for message senders and conversation participants
        const allUserIds = new Set([
          ...messages.map(m => m.sender_id),
          ...participants.map(p => p.user_id),
          ...conversations.map(c => c.created_by),
        ]);

        // Get user data from users table
        logger.debug('Fetching user data for IDs:', Array.from(allUserIds));
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('id, username, full_name, auth_user_id')
          .in('id', Array.from(allUserIds));

        logger.debug('Users data fetched:', usersData);
        logger.debug('Users error:', usersError);

        if (usersError) {
          logger.error('Error fetching user details:', usersError);
          return { error: usersError.message };
        }

        const users = ensureArray(usersData);
        logger.debug('Users array:', users);

        // Get profile data separately (profiles use auth_user_id)
        const authUserIds = users.map(u => u.auth_user_id).filter(Boolean);
        logger.debug('Auth user IDs for profiles:', authUserIds);

        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, profile_picture_url, bio, user_tag')
          .in('user_id', authUserIds);

        logger.debug('Profiles data:', profilesData);
        logger.debug('Profiles error:', profilesError);

        if (profilesError) {
          logger.error('Error fetching profiles:', profilesError);
          return { error: profilesError.message };
        }

        const profiles = ensureArray(profilesData);

        // Create profile map (profiles use auth_user_id as key)
        const profilesMap = new Map(profiles.map(p => [p.user_id, p]));

        // Map users by public.users.id and combine with profile data
        const usersMap = new Map(users.map(u => {
          const profile = profilesMap.get(u.auth_user_id);
          const userObj = {
            id: u.id, // Use public.users.id
            username: u.username,
            full_name: u.full_name,
            avatar_url: profile?.profile_picture_url || null,
            profile_picture_url: profile?.profile_picture_url || null,
            is_online: false // TODO: Add online status logic
          };
          logger.debug(`Mapping user ${u.id}:`, userObj);
          return [u.id, userObj]; // Use public.users.id as key
        }));

        logger.debug('Final users map:', Array.from(usersMap.entries()));

        // 6. Build final conversation objects with new schema
        const result: ConversationWithDetails[] = conversations.map(conversation => {
          // Find last message for this conversation
          const lastMessage = messages.find(m => m.conversation_id === conversation.id);

          // Get participants for this conversation (exclude current user)
          const conversationParticipants = participants.filter(p =>
            p.conversation_id === conversation.id && p.user_id !== userId
          );

          logger.debug(`Processing conversation ${conversation.id}:`);
          logger.debug(`- Participants:`, conversationParticipants);

          const otherParticipants = conversationParticipants
            .map(participant => {
              const user = usersMap.get(participant.user_id);
              if (!user) return null;

              return {
                ...formatUserForConversation(user),
                role: participant.role,
                custom_nickname: participant.custom_nickname,
                last_seen_at: participant.last_seen_at,
              };
            })
            .filter(Boolean);

          logger.debug(`- Final other participants:`, otherParticipants);

          // For direct conversations, use the other user's info
          let displayName = conversation.name;
          let displayAvatar = conversation.avatar_url;
          let otherParticipant = null;

          if (conversation.type === 'direct' && otherParticipants.length > 0) {
            otherParticipant = otherParticipants[0];
            displayName = otherParticipant.custom_nickname || otherParticipant.full_name || otherParticipant.username;
            displayAvatar = otherParticipant.avatar_url;
          }

          // Calculate unread count
          const userParticipant = participants.find(p =>
            p.conversation_id === conversation.id && p.user_id === userId
          );

          let unreadCount = 0;
          if (userParticipant?.last_read_message_id && lastMessage) {
            // Simple unread logic - if last message is different from last read, there's 1 unread
            // TODO: Implement proper unread count calculation
            unreadCount = userParticipant.last_read_message_id !== lastMessage.id ? 1 : 0;
          } else if (lastMessage && lastMessage.sender_id !== userId) {
            unreadCount = 1;
          }

          return {
            ...conversation,
            display_name: displayName,
            display_avatar: displayAvatar,
            other_participant: otherParticipant, // For direct chats
            other_participants: otherParticipants, // For group chats
            last_message: lastMessage ? {
              ...lastMessage,
              sender: usersMap.get(lastMessage.sender_id)
                ? formatUserForConversation(usersMap.get(lastMessage.sender_id))
                : null,
            } : null,
            unread_count: unreadCount,
            is_pinned: userParticipant?.is_pinned || false,
          };
        });

        logger.debug(`Processed ${result.length} conversations with details`);
        return { data: result };

      } catch (error) {
        logger.error('getConversations exception:', error);
        return { error: 'Failed to fetch conversations' };
      }
    },
    providesTags: ['Conversation'],
  }),

  // Create conversation
  createConversation: builder.mutation<string, { other_user_id: string }>({
    async queryFn({ other_user_id }) {
      try {
        // Convert auth user ID to public user ID for the other user
        const { data: otherPublicUser, error: otherUserError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', other_user_id)
          .single();

        if (otherUserError || !otherPublicUser) {
          logger.error('Error finding other user in public users:', otherUserError);
          return { error: 'User not found' };
        }

        // Get current user's public ID
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (!authUser) {
          return { error: 'User not authenticated' };
        }

        const { data: currentPublicUser, error: currentUserError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', authUser.id)
          .single();

        if (currentUserError || !currentPublicUser) {
          logger.error('Error finding current user in public users:', currentUserError);
          return { error: 'Current user not found' };
        }

        // Use the enhanced RPC function to create conversation with public user IDs
        const { data: conversationId, error } = await supabase.rpc('create_conversation', {
          participant_ids: [otherPublicUser.id], // Use public user ID
          conversation_type: 'direct',
          conversation_name: null,
          conversation_description: null,
        });

        if (error) {
          logger.error('Error creating conversation:', error);
          return { error: error.message };
        }

        logger.debug('Created conversation:', conversationId);
        return { data: conversationId };
      } catch (error) {
        logger.error('createConversation exception:', error);
        return { error: 'Failed to create conversation' };
      }
    },
    invalidatesTags: ['Conversation'],
  }),

  // Add participant to conversation
  addParticipantToConversation: builder.mutation<boolean, AddParticipantRequest>({
    async queryFn({ conversation_id, participant_id }) {
      try {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('id', participant_id)
          .single();

        if (userError || !userData) {
          logger.error('Error finding participant:', userError);
          return { error: 'User not found' };
        }

        // Add participant to conversation
        const { error } = await supabase
          .from('conversation_participants')
          .insert({
            conversation_id,
            user_id: participant_id,
            joined_at: new Date().toISOString(),
            is_active: true,
          });

        if (error) {
          logger.error('Error adding participant:', error);
          return { error: error.message };
        }

        logger.debug('Added participant to conversation:', { conversation_id, participant_id });
        return { data: true };
      } catch (error) {
        logger.error('addParticipantToConversation exception:', error);
        return { error: 'Failed to add participant' };
      }
    },
    invalidatesTags: ['Conversation'],
  }),
});
