import { EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../../integrations/supabase/client';
import { ensureArray, handleAuthError } from '../utils/apiHelpers';
import logger from '../../../utils/logger';

export const notificationsEndpoints = (builder: EndpointBuilder<any, any, any>) => ({
  // Get notifications
  getNotifications: builder.query<any[], void>({
    async queryFn(_arg, api, _extraOptions, _baseQuery) {
      try {
        // Use auth user (safer than relying on Redux state)
        const {
          data: { user },
          error: authError,
        } = await supabase.auth.getUser();

        if (authError || !user) {
          logger.error('Auth error in getNotifications:', authError);
          return handleAuthError(authError);
        }

        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          logger.error('Error fetching notifications:', error);
          return { error: error.message };
        }

        const notifications = ensureArray(data);
        logger.debug(`Found ${notifications.length} notifications`);
        return { data: notifications };
      } catch (error) {
        logger.error('getNotifications exception:', error);
        return { error: 'Failed to fetch notifications' };
      }
    },
    providesTags: ['Notification'],
  }),

  // Mark notification as read
  markNotificationAsRead: builder.mutation<boolean, string>({
    async queryFn(notificationId) {
      try {
        const { error } = await supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('id', notificationId);

        if (error) {
          logger.error('Error marking notification as read:', error);
          return { error: error.message };
        }

        logger.debug('Notification marked as read:', notificationId);
        return { data: true };
      } catch (error) {
        logger.error('markNotificationAsRead exception:', error);
        return { error: 'Failed to mark notification as read' };
      }
    },
    invalidatesTags: ['Notification'],
  }),
});
