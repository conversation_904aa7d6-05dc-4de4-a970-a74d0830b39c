import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { User, UpdateProfileData } from '../../types/user';
import { UserSettings, NotificationSettings } from '../../types/settings';
import { supabase } from '../../integrations/supabase/client';

// Import hooks from individual API files
import {
  useGetCurrentProfileQuery,
  useGetUserByIdQuery,
  useUpdateProfileMutationNew
} from './profileApi';

import {
  useUpdateUserSettingsMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation
} from './settingsApi';

import {
  useToggleFollowMutation,
  useGetFollowersQuery,
  useGetFollowingQuery
} from './followersApi';

import {
  useUpdateUsernameMutation,
  useUpdateEmailMutation,
  useUpdatePhoneNumberMutation,
  useChangePasswordMutation
} from './accountApi';

import {
  useGetBlockedUsersQuery,
  useBlockUserMutation,
  useUnblockUserMutation
} from './blockingApi';

import {
  useGetPersonalDataSettingsQuery,
  useUpdatePersonalDataSettingsMutation,
  useRequestDataExportMutation,
} from './personalDataApi';

import {
  useDiscoverUsersQuery
} from './discoveryApi';

// Create a minimal API for backward compatibility
export const userManagementApi = createApi({
  reducerPath: 'userManagementApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['User', 'Profile', 'Settings', 'Privacy', 'Notifications', 'Security', 'Followers', 'BlockedUsers'],
   endpoints: (builder) => ({}),
});

// Re-export all the hooks to maintain backward compatibility
export {
  useGetCurrentProfileQuery as useGetCurrentUserCompleteQuery,
  useGetUserByIdQuery,
  useUpdateProfileMutationNew as useUpdateUserProfileMutation,
  useUpdateUserSettingsMutation,
  useToggleFollowMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation,
  useDiscoverUsersQuery,
  useGetFollowersQuery,
  useGetFollowingQuery,
  useUpdateUsernameMutation,
  useUpdateEmailMutation,
  useUpdatePhoneNumberMutation,
  useChangePasswordMutation,
  useGetBlockedUsersQuery,
  useBlockUserMutation,
  useUnblockUserMutation,
  useGetPersonalDataSettingsQuery,
  useUpdatePersonalDataSettingsMutation,
  useRequestDataExportMutation,
};