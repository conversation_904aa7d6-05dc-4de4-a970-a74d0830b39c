import { PersonalDataSettings } from '../../types/settings';
import { supabase } from '../../integrations/supabase/client';
import { createUserApi } from './baseUserApi';
import logger from '../../utils/logger';

export const personalDataApi = createUserApi('personalDataApi', ['PersonalData']);

export const extendedPersonalDataApi = personalDataApi.injectEndpoints({
  endpoints: (builder) => ({
    // Fetch personal data settings
    getPersonalDataSettings: builder.query<PersonalDataSettings, void>({
      async queryFn() {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { data, error } = await supabase
            .from('personal_data_settings')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') throw error;

          const defaultSettings: PersonalDataSettings = {
            user_id: user.id,
            data_export_requested: false,
            data_export_date: undefined,
            data_deletion_requested: false,
            data_deletion_date: undefined,
            marketing_emails: true,
            analytics_tracking: true,
            third_party_sharing: false,
            data_retention_period: 365,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          const safeSettings: PersonalDataSettings = {
            user_id: data?.user_id ?? defaultSettings.user_id,
            data_export_requested: (data?.data_export_requested ?? defaultSettings.data_export_requested) === true,
            data_export_date: data?.data_export_date ?? defaultSettings.data_export_date,
            data_deletion_requested: (data?.data_deletion_requested ?? defaultSettings.data_deletion_requested) === true,
            data_deletion_date: data?.data_deletion_date ?? defaultSettings.data_deletion_date,
            marketing_emails: (data?.marketing_emails ?? defaultSettings.marketing_emails) === true,
            analytics_tracking: (data?.analytics_tracking ?? defaultSettings.analytics_tracking) === true,
            third_party_sharing: (data?.third_party_sharing ?? defaultSettings.third_party_sharing) === true,
            data_retention_period: data?.data_retention_period ?? defaultSettings.data_retention_period,
            created_at: data?.created_at ?? defaultSettings.created_at,
            updated_at: data?.updated_at ?? defaultSettings.updated_at,
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Get personal data settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch personal data settings',
            },
          };
        }
      },
      providesTags: ['PersonalData'],
    }),

    // Update personal data settings
    updatePersonalDataSettings: builder.mutation<PersonalDataSettings, Partial<PersonalDataSettings>>({
      async queryFn(updateData) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { data, error } = await supabase
            .from('personal_data_settings')
            .upsert({
              user_id: user.id,
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (error) throw error;

          const safeSettings: PersonalDataSettings = {
            user_id: data?.user_id ?? user.id,
            data_export_requested: (data?.data_export_requested ?? false) === true,
            data_export_date: data?.data_export_date ?? undefined,
            data_deletion_requested: (data?.data_deletion_requested ?? false) === true,
            data_deletion_date: data?.data_deletion_date ?? undefined,
            marketing_emails: (data?.marketing_emails ?? true) === true,
            analytics_tracking: (data?.analytics_tracking ?? true) === true,
            third_party_sharing: (data?.third_party_sharing ?? false) === true,
            data_retention_period: data?.data_retention_period ?? 365,
            created_at: data?.created_at ?? new Date().toISOString(),
            updated_at: data?.updated_at ?? new Date().toISOString(),
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Update personal data settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update personal data settings',
            },
          };
        }
      },
      invalidatesTags: ['PersonalData'],
    }),

    // Request data export
    requestDataExport: builder.mutation<{ success: boolean }, void>({
      async queryFn() {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { error } = await supabase.from('data_export_requests').insert({
            user_id: user.id,
            status: 'pending',
            request_type: 'export',
          });

          if (error) throw error;

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Request data export error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to request data export',
            },
          };
        }
      },
      invalidatesTags: ['PersonalData'],
    }),
  }),
});

export const {
  useGetPersonalDataSettingsQuery,
  useUpdatePersonalDataSettingsMutation,
  useRequestDataExportMutation,
} = extendedPersonalDataApi;
