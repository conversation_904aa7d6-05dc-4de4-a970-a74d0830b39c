import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export const phoneVerificationApi = createApi({
  reducerPath: 'phoneVerificationApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Security'],
  endpoints: (builder) => ({
    addPhoneNumber: builder.mutation<{ success: boolean }, string>({
      async queryFn(phoneNumber) {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { count } = await supabase
            .from('user_phone_numbers')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user.id);

          const { error } = await supabase.from('user_phone_numbers').insert({
            user_id: user.id,
            phone_number: phoneNumber,
            is_verified: false,
            is_primary: (count ?? 0) === 0,
          });

          if (error) throw error;

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Add phone number error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to add phone number',
            },
          };
        }
      },
      invalidatesTags: ['Security'],
    }),
    removePhoneNumber: builder.mutation<{ success: boolean }, string>({
      async queryFn(phoneId) {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { error } = await supabase
            .from('user_phone_numbers')
            .delete()
            .eq('id', phoneId)
            .eq('user_id', user.id);

          if (error) throw error;

          const { data: remaining, error: remainingError } = await supabase
            .from('user_phone_numbers')
            .select('id')
            .eq('user_id', user.id)
            .eq('is_verified', true);

          if (remainingError) throw remainingError;

          if (!remaining || remaining.length === 0) {
            await supabase
              .from('security_settings')
              .upsert(
                { user_id: user.id, two_factor_enabled: false },
                { onConflict: 'user_id' },
              );
          }

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Remove phone number error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to remove phone number',
            },
          };
        }
      },
      invalidatesTags: ['Security'],
    }),
    sendVerificationCode: builder.mutation<{ success: boolean }, string>({
      async queryFn(phoneNumber) {
        try {
          const { data, error } = await supabase.functions.invoke(
            'send-sms-verification',
            { body: { phoneNumber } },
          );

          if (error) throw error;
          if (!data.success) throw new Error(data.error || 'Failed to send verification code');

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Send verification code error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to send verification code',
            },
          };
        }
      },
    }),
    verifyCode: builder.mutation<{ success: boolean }, { phoneNumber: string; code: string }>({
      async queryFn({ phoneNumber, code }) {
        try {
          const { data, error } = await supabase.functions.invoke('verify-sms-code', {
            body: { phoneNumber, code },
          });

          if (error) throw error;
          if (!data.success || !data.valid) {
            throw new Error(data.error || 'Invalid or expired verification code');
          }

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const { count } = await supabase
            .from('user_phone_numbers')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user.id);

          const { error: upsertError } = await supabase
            .from('user_phone_numbers')
            .upsert(
              {
                user_id: user.id,
                phone_number: phoneNumber,
                is_verified: true,
                is_primary: (count ?? 0) === 0,
              },
              { onConflict: 'user_id,phone_number' },
            );

          if (upsertError) throw upsertError;

          const { error: securityError } = await supabase
            .from('security_settings')
            .upsert(
              { user_id: user.id, two_factor_enabled: true },
              { onConflict: 'user_id' },
            );

          if (securityError) throw securityError;

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Verify code error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to verify code',
            },
          };
        }
      },
      invalidatesTags: ['Security'],
    }),
  }),
});

export const {
  useAddPhoneNumberMutation,
  useRemovePhoneNumberMutation,
  useSendVerificationCodeMutation,
  useVerifyCodeMutation,
} = phoneVerificationApi;
