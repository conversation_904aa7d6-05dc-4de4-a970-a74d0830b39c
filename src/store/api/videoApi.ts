import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';
import { Video } from '../../types/video';

export const videoApi = createApi({
  reducerPath: 'videoApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Video'],
  endpoints: builder => ({
    getUserVideos: builder.query<Video[], { userId: string; privacy: string }>({
      async queryFn({ userId, privacy }) {
        try {
          const { data, error } = await supabase
            .from('videos')
            .select('*')
            .eq('user_id', userId)
            .eq('privacy_setting', privacy)
            .order('created_at', { ascending: false });
          if (error) throw error;
          const videos = (data ?? []).map(v => ({
            id: v.id,
            url: v.video_url,
            thumbnail: v.thumbnail_url ?? undefined,
            duration: v.duration_sec ?? 0,
            title: v.title ?? '',
            description: v.description ?? undefined,
            tags: v.tags ?? undefined,
            created_at: v.created_at ?? new Date().toISOString(),
            updated_at: v.updated_at ?? new Date().toISOString(),
            user_id: v.user_id,
            user: { id: v.user_id, username: '', full_name: '' },
            stats: {
              likes: v.likes_count ?? 0,
              comments: v.comments_count ?? 0,
              shares: v.shares_count ?? 0,
              views: v.views_count ?? 0,
            },
          })) as Video[];
          return { data: videos };
        } catch (error: any) {
          logger.error('Get user videos error:', error);
          return { error: { status: 'FETCH_ERROR', error: error.message || 'Failed to fetch videos' } };
        }
      },
      providesTags: result =>
        result ? result.map(({ id }) => ({ type: 'Video' as const, id })) : [],
    }),
    getLikedVideos: builder.query<Video[], void>({
      async queryFn() {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          // First get the video IDs that the user has liked
          const { data: likedVideoIds, error: likesError } = await supabase
            .from('video_likes')
            .select('video_id')
            .eq('user_id', user.id);

          if (likesError) throw likesError;

          if (!likedVideoIds || likedVideoIds.length === 0) {
            return { data: [] };
          }

          // Extract video IDs
          const videoIds = likedVideoIds.map(like => like.video_id);

          // Now get the actual video data
          const { data: videosData, error: videosError } = await supabase
            .from('videos')
            .select(`
              id,
              title,
              description,
              video_url,
              thumbnail_url,
              duration_sec,
              created_at,
              updated_at,
              user_id,
              privacy,
              tags,
              likes_count,
              comments_count,
              shares_count,
              views_count,
              users!videos_user_id_fkey (
                id,
                username,
                full_name,
                avatar_url
              )
            `)
            .in('id', videoIds)
            .eq('privacy', 'public') // Only show public videos in liked tab
            .order('created_at', { ascending: false });

          if (videosError) throw videosError;

          const videos = (videosData ?? []).map((v: any) => ({
            id: v.id,
            url: v.video_url,
            thumbnail: v.thumbnail_url ?? undefined,
            duration: v.duration_sec ?? 0,
            title: v.title ?? '',
            description: v.description ?? undefined,
            tags: v.tags ?? undefined,
            created_at: v.created_at ?? new Date().toISOString(),
            updated_at: v.updated_at ?? new Date().toISOString(),
            user_id: v.user_id,
            user: {
              id: v.users?.id || v.user_id,
              username: v.users?.username || 'user',
              full_name: v.users?.full_name || 'User',
              avatar_url: v.users?.avatar_url,
            },
            stats: {
              likes: v.likes_count ?? 0,
              comments: v.comments_count ?? 0,
              shares: v.shares_count ?? 0,
              views: v.views_count ?? 0,
            },
          })) as Video[];

          return { data: videos };
        } catch (error: any) {
          logger.error('Get liked videos error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch liked videos'
            }
          };
        }
      },
      providesTags: ['Video'],
    }),

    // Get feed videos for "For You" tab (all public videos)
    getForYouFeed: builder.query<Video[], void>({
      async queryFn() {
        try {
          // Get all videos first
          const { data: videosData, error } = await supabase
            .from('videos')
            .select('*')
            .eq('privacy_setting', 'public')
            .eq('is_draft', false)
            .order('created_at', { ascending: false })
            .limit(50);
          
          if (error) {
            logger.error('Supabase error:', error);
            throw error;
          }
          
          if (!videosData || videosData.length === 0) {
            return { data: [] };
          }
          
          // Get unique user IDs
          const userIds = [...new Set(videosData.map(v => v.user_id))];
          
          // Following the web app pattern: videos.user_id matches users.auth_user_id
          // and profiles.user_id also matches users.auth_user_id
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('auth_user_id, username, full_name, email')
            .in('auth_user_id', userIds);
          
          if (usersError) {
            logger.error('Error fetching users:', usersError);
          }
          
          // Get profiles using the same user_ids (which are auth_user_ids)
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('user_id, profile_picture_url, bio, user_tag')
            .in('user_id', userIds);
          
          if (profilesError) {
            logger.error('Error fetching profiles:', profilesError);
          }
          
          // Create lookup maps using auth_user_id as key
          const usersMap = new Map((usersData || []).map(user => [user.auth_user_id, user]));
          const profilesMap = new Map((profilesData || []).map(profile => [profile.user_id, profile]));
          
          const data = videosData;
          
          if (error) {
            logger.error('Supabase error:', error);
            throw error;
          }
          
          if (!data || data.length === 0) {
            return { data: [] };
          }
          
          const videos = data.map(v => {
            
            // Get user and profile data
            const userData = usersMap.get(v.user_id);
            const profileData = profilesMap.get(v.user_id);
            
            let videoUrl = v.video_url;
            
            
            return {
              id: v.id,
              url: videoUrl,
              thumbnail: v.thumbnail_url ?? undefined,
              duration: v.duration_sec ?? v.duration ?? 0,
              title: v.title ?? '',
              description: v.description ?? '',
              tags: v.tags ?? undefined,
              created_at: v.created_at ?? new Date().toISOString(),
              updated_at: v.updated_at ?? new Date().toISOString(),
              user_id: v.user_id,
              user: {
                id: v.user_id,
                username: userData?.username && userData.username !== v.user_id 
                  ? userData.username 
                  : userData?.email?.split('@')[0] || `user_${v.user_id.slice(0, 8)}`,
                full_name: userData?.full_name || userData?.email?.split('@')[0] || 'Unknown User',
                avatar_url: profileData?.profile_picture_url || undefined,
              },
              stats: {
                likes: v.likes_count ?? v.likes ?? 0,
                comments: v.comments_count ?? 0,
                shares: v.shares_count ?? 0,
                views: v.views_count ?? v.views ?? 0,
              },
            };
          }) as Video[];
          
          return { data: videos };
        } catch (error: any) {
          logger.error('Get for you feed error:', error);
          return { error: { status: 'FETCH_ERROR', error: error.message || 'Failed to fetch for you feed' } };
        }
      },
      providesTags: ['Video'],
    }),

    // Enhanced For You feed with pagination and like status
    getForYouFeedPaginated: builder.query<
      { videos: Video[]; hasMore: boolean; nextCursor?: string },
      { limit?: number; cursor?: string }
    >({
      async queryFn({ limit = 20, cursor }) {
        try {
          const { data: { user: currentUser } } = await supabase.auth.getUser();

          // Build query with pagination
          let query = supabase
            .from('videos')
            .select(`
              *,
              video_likes!left(user_id),
              comments(count)
            `)
            .eq('privacy_setting', 'public')
            .eq('is_draft', false)
            .order('created_at', { ascending: false })
            .limit(limit + 1); // Get one extra to check if there are more

          if (cursor) {
            query = query.lt('created_at', cursor);
          }

          const { data: videosData, error } = await query;

          if (error) throw error;

          if (!videosData || videosData.length === 0) {
            return { data: { videos: [], hasMore: false } };
          }

          // Check if there are more videos
          const hasMore = videosData.length > limit;
          const videos = hasMore ? videosData.slice(0, -1) : videosData;
          const nextCursor = hasMore ? videos[videos.length - 1].created_at : undefined;

          // Get user data for each video
          const userIds = [...new Set(videos.map(v => v.user_id))];

          // Get profiles data
          const { data: profiles, error: profilesError } = await supabase
            .from('profiles')
            .select('user_id, profile_picture_url, bio, user_tag')
            .in('user_id', userIds);

          if (profilesError) {
            logger.error('Error fetching profiles:', profilesError);
          }

          // Get users data
          const { data: users, error: usersError } = await supabase
            .from('users')
            .select('auth_user_id, username, full_name, email')
            .in('auth_user_id', userIds);

          if (usersError) {
            logger.error('Error fetching users:', usersError);
          }

          // Create maps for quick lookup
          const profileMap = new Map();
          profiles?.forEach(profile => {
            profileMap.set(profile.user_id, profile);
          });

          const userMap = new Map();
          users?.forEach(user => {
            userMap.set(user.auth_user_id, user);
          });

          const processedVideos = videos.map((v: any) => {
            const profileData = profileMap.get(v.user_id);
            const userData = userMap.get(v.user_id);

            // Check if current user liked this video
            const isLiked = currentUser ?
              v.video_likes?.some((like: any) => like.user_id === currentUser.id) : false;

            // Get comments count
            const commentsCount = Array.isArray(v.comments) ? v.comments.length : (v.comments_count || 0);

            return {
              id: v.id,
              url: v.video_url,
              thumbnail: v.thumbnail_url ?? undefined,
              duration: v.duration_sec ?? 0,
              title: v.title ?? '',
              description: v.description ?? undefined,
              tags: v.tags ?? undefined,
              created_at: v.created_at ?? new Date().toISOString(),
              updated_at: v.updated_at ?? new Date().toISOString(),
              user_id: v.user_id,
              user: {
                id: v.user_id,
                username: userData?.username
                  ? userData.username
                  : userData?.email?.split('@')[0] || `user_${v.user_id.slice(0, 8)}`,
                full_name: userData?.full_name || userData?.email?.split('@')[0] || 'Unknown User',
                avatar_url: profileData?.profile_picture_url || undefined,
              },
              stats: {
                likes: v.likes_count ?? 0,
                comments: commentsCount,
                shares: v.shares_count ?? 0,
                views: v.views_count ?? 0,
              },
              isLiked,
            };
          }) as Video[];

          return {
            data: {
              videos: processedVideos,
              hasMore,
              nextCursor
            }
          };
        } catch (error: any) {
          logger.error('Get For You feed paginated error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch For You feed'
            }
          };
        }
      },
      // Merge results for infinite scrolling
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      merge: (currentCache, newItems, { arg }) => {
        if (arg.cursor) {
          // Append new videos for pagination
          return {
            videos: [...currentCache.videos, ...newItems.videos],
            hasMore: newItems.hasMore,
            nextCursor: newItems.nextCursor
          };
        } else {
          // Replace cache for fresh load
          return newItems;
        }
      },
      forceRefetch({ currentArg, previousArg }) {
        return currentArg?.cursor !== previousArg?.cursor;
      },
      providesTags: (result) => [
        'Video',
        ...(result?.videos.map(({ id }) => ({ type: 'Video' as const, id })) || [])
      ],
    }),

    // Get feed videos for "Following" tab (videos from users you follow)
    getFollowingFeed: builder.query<Video[], void>({
      async queryFn() {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          // First, get the list of users the current user follows
          const { data: followingData, error: followingError } = await supabase
            .from('user_follows')
            .select('following_id')
            .eq('follower_id', user.id);

          if (followingError) throw followingError;

          const followingIds = (followingData ?? []).map(f => f.following_id);
          
          // If not following anyone, return empty array
          if (followingIds.length === 0) {
            return { data: [] };
          }

          // Get videos from followed users
          const { data: followingVideosData, error } = await supabase
            .from('videos')
            .select('*')
            .in('user_id', followingIds)
            .eq('privacy_setting', 'public')
            .eq('is_draft', false)
            .order('created_at', { ascending: false })
            .limit(50);
          
          if (error) {
            logger.error('Supabase error in following feed:', error);
            throw error;
          }
          
          if (!followingVideosData || followingVideosData.length === 0) {
            return { data: [] };
          }
          
          // Get user information for video creators
          const videoUserIds = [...new Set(followingVideosData.map(v => v.user_id))];
          
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('auth_user_id, username, full_name, email')
            .in('auth_user_id', videoUserIds);
          
          if (usersError) {
            logger.error('Error fetching users for following feed:', usersError);
          }
          
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('user_id, profile_picture_url, bio, user_tag')
            .in('user_id', videoUserIds);
          
          if (profilesError) {
            logger.error('Error fetching profiles for following feed:', profilesError);
          }
          
          // Create lookup maps using auth_user_id as key
          const usersMap = new Map((usersData || []).map(user => [user.auth_user_id, user]));
          const profilesMap = new Map((profilesData || []).map(profile => [profile.user_id, profile]));
          
          const videos = followingVideosData.map(v => {
            // Get user and profile data
            const userData = usersMap.get(v.user_id);
            const profileData = profilesMap.get(v.user_id);
            
            return {
              id: v.id,
              url: v.video_url,
              thumbnail: v.thumbnail_url ?? undefined,
              duration: v.duration_sec ?? v.duration ?? 0,
              title: v.title ?? '',
              description: v.description ?? '',
              tags: v.tags ?? undefined,
              created_at: v.created_at ?? new Date().toISOString(),
              updated_at: v.updated_at ?? new Date().toISOString(),
              user_id: v.user_id,
              user: {
                id: v.user_id,
                username: userData?.username && userData.username !== v.user_id 
                  ? userData.username 
                  : userData?.email?.split('@')[0] || `user_${v.user_id.slice(0, 8)}`,
                full_name: userData?.full_name || userData?.email?.split('@')[0] || 'Unknown User',
                avatar_url: profileData?.profile_picture_url || undefined,
              },
              stats: {
                likes: v.likes_count ?? v.likes ?? 0,
                comments: v.comments_count ?? 0,
                shares: v.shares_count ?? 0,
                views: v.views_count ?? v.views ?? 0,
              },
            };
          }) as Video[];
          
          return { data: videos };
        } catch (error: any) {
          logger.error('Get following feed error:', error);
          return { error: { status: 'FETCH_ERROR', error: error.message || 'Failed to fetch following feed' } };
        }
      },
      providesTags: ['Video'],
    }),
    // Like/Unlike video with optimistic updates
    toggleVideoLike: builder.mutation<
      { isLiked: boolean; likesCount: number },
      { videoId: string; isCurrentlyLiked: boolean; currentLikesCount: number }
    >({
      async queryFn({ videoId, isCurrentlyLiked, currentLikesCount }) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          if (isCurrentlyLiked) {
            // Unlike the video
            const { error: deleteError } = await supabase
              .from('video_likes')
              .delete()
              .eq('video_id', videoId)
              .eq('user_id', user.id);

            if (deleteError) throw deleteError;

            // Update video likes count
            const { error: updateError } = await supabase
              .from('videos')
              .update({
                likes_count: Math.max(0, currentLikesCount - 1),
                updated_at: new Date().toISOString()
              })
              .eq('id', videoId);

            if (updateError) throw updateError;

            return {
              data: {
                isLiked: false,
                likesCount: Math.max(0, currentLikesCount - 1)
              }
            };
          } else {
            // Like the video
            const { error: insertError } = await supabase
              .from('video_likes')
              .insert({
                video_id: videoId,
                user_id: user.id,
                created_at: new Date().toISOString()
              });

            if (insertError) throw insertError;

            // Update video likes count
            const { error: updateError } = await supabase
              .from('videos')
              .update({
                likes_count: currentLikesCount + 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', videoId);

            if (updateError) throw updateError;

            return {
              data: {
                isLiked: true,
                likesCount: currentLikesCount + 1
              }
            };
          }
        } catch (error: any) {
          logger.error('Toggle video like error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to toggle video like'
            }
          };
        }
      },
      // Optimistic update
      async onQueryStarted({ videoId, isCurrentlyLiked, currentLikesCount }, { dispatch, queryFulfilled }) {
        // Update the cache optimistically
        const optimisticUpdate = {
          isLiked: !isCurrentlyLiked,
          likesCount: isCurrentlyLiked ? currentLikesCount - 1 : currentLikesCount + 1
        };

        // Update For You feed cache
        const forYouPatchResult = dispatch(
          videoApi.util.updateQueryData('getForYouFeed', undefined, (draft) => {
            const video = draft.find(v => v.id === videoId);
            if (video) {
              video.stats.likes = optimisticUpdate.likesCount;
            }
          })
        );

        // Update Following feed cache
        const followingPatchResult = dispatch(
          videoApi.util.updateQueryData('getFollowingFeed', undefined, (draft) => {
            const video = draft.find(v => v.id === videoId);
            if (video) {
              video.stats.likes = optimisticUpdate.likesCount;
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic updates on error
          forYouPatchResult.undo();
          followingPatchResult.undo();
        }
      },
      invalidatesTags: (result, error, { videoId }) => [
        { type: 'Video', id: videoId },
        'Video'
      ],
    }),

    // Check if video is liked by current user
    checkVideoLiked: builder.query<boolean, string>({
      async queryFn(videoId) {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return { data: false };

          const { data, error } = await supabase
            .from('video_likes')
            .select('id')
            .eq('video_id', videoId)
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') throw error;

          return { data: !!data };
        } catch (error: any) {
          logger.error('Check video liked error:', error);
          return { data: false };
        }
      },
      providesTags: (result, error, videoId) => [{ type: 'Video', id: videoId }],
    }),
  }),
});

export const {
  useGetUserVideosQuery,
  useGetLikedVideosQuery,
  useGetForYouFeedQuery,
  useGetForYouFeedPaginatedQuery,
  useGetFollowingFeedQuery,
  useToggleVideoLikeMutation,
  useCheckVideoLikedQuery,
} = videoApi;
