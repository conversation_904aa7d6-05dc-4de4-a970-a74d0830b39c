import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';
import { Music } from '../../types/music';
import { MusicTrack } from '../../components/video/MusicSelector';
import logger from '../../utils/logger';

export const musicLibraryApi = createApi({
  reducerPath: 'musicLibraryApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['MusicLibrary'],
  endpoints: builder => ({
    getMusicLibrary: builder.query<Music[], void>({
      async queryFn() {
        try {
          const { data, error } = await supabase
            .from('music_library')
            .select('*')
            .eq('is_active', true)
            .order('title', { ascending: true });

          if (error) throw error;

          const items: Music[] = (data ?? []).map(row => ({
            id: row.id,
            title: row.title,
            artist: row.artist,
            genre: row.genre,
            duration: row.duration,
            file_url: row.file_url,
            thumbnail_url: row.thumbnail_url,
            is_active: row.is_active,
            created_at: row.created_at,
            updated_at: row.updated_at,
          }));

          return { data: items };
        } catch (error: any) {
          logger.error('Get music library error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch music library',
            },
          };
        }
      },
      providesTags: ['MusicLibrary'],
    }),

    // Get music library in MusicTrack format for video editor
    musicLibrary: builder.query<MusicTrack[], void>({
      async queryFn() {
        try {
          const { data, error } = await supabase
            .from('music_library')
            .select('*')
            .eq('is_active', true)
            .order('title', { ascending: true });

          if (error) throw error;

          // Convert to MusicTrack format
          const tracks: MusicTrack[] = (data ?? []).map(row => ({
            id: row.id,
            title: row.title,
            artist: row.artist,
            duration: row.duration,
            file_url: row.file_url,
            thumbnail_url: row.thumbnail_url,
            genre: row.genre,
          }));

          return { data: tracks };
        } catch (error: any) {
          logger.error('Get music library error:', error);

          // Return mock data as fallback
          const mockTracks: MusicTrack[] = [
            {
              id: '1',
              title: 'Summer Vibes',
              artist: 'Chill Beats',
              duration: 180,
              file_url: 'https://nllfhiuufnpqacalkmef.supabase.co/storage/v1/object/public/testvideos/sample-music-1.mp3',
              genre: 'Electronic',
            },
            {
              id: '2',
              title: 'Urban Flow',
              artist: 'Beat Master',
              duration: 165,
              file_url: 'https://nllfhiuufnpqacalkmef.supabase.co/storage/v1/object/public/testvideos/sample-music-2.mp3',
              genre: 'Hip Hop',
            },
            {
              id: '3',
              title: 'Acoustic Dreams',
              artist: 'Indie Folk',
              duration: 210,
              file_url: 'https://nllfhiuufnpqacalkmef.supabase.co/storage/v1/object/public/testvideos/sample-music-3.mp3',
              genre: 'Folk',
            },
          ];

          return { data: mockTracks };
        }
      },
      providesTags: ['MusicLibrary'],
    }),
  }),
});

export const { useGetMusicLibraryQuery, useMusicLibraryQuery } = musicLibraryApi;
