import {createApi, fakeBaseQuery} from '@reduxjs/toolkit/query/react';
import {supabase} from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export const videoLikesApi = createApi({
  reducerPath: 'videoLikesApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['VideoLikes'],
  endpoints: builder => ({
    likeVideo: builder.mutation<{success: boolean}, {videoId: string}>({
      async queryFn({videoId}) {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const {error} = await supabase
            .from('video_likes')
            .insert({video_id: videoId, user_id: user.id});

          if (error) throw error;
          return {data: {success: true}};
        } catch (error: any) {
          logger.error('Like video error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to like video',
            },
          };
        }
      },
      invalidatesTags: (result, error, {videoId}) => [
        {type: 'VideoLikes', id: videoId},
      ],
    }),
    unlikeVideo: builder.mutation<{success: boolean}, {videoId: string}>({
      async queryFn({videoId}) {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user found');

          const {error} = await supabase
            .from('video_likes')
            .delete()
            .match({video_id: videoId, user_id: user.id});

          if (error) throw error;
          return {data: {success: true}};
        } catch (error: any) {
          logger.error('Unlike video error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to unlike video',
            },
          };
        }
      },
      invalidatesTags: (result, error, {videoId}) => [
        {type: 'VideoLikes', id: videoId},
      ],
    }),
  }),
});

export const {useLikeVideoMutation, useUnlikeVideoMutation} = videoLikesApi;

export const subscribeToVideoLikes = (
  videoId: string,
  dispatch: any,
  handlers: {
    increment: (videoId: string) => any;
    decrement: (videoId: string) => any;
    setLiked: (videoId: string, liked: boolean) => any;
  },
  currentUserId: string,
) => {
  const channel = supabase
    .channel('public:video_likes')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'video_likes',
        filter: `video_id=eq.${videoId}`,
      },
      payload => {
        dispatch(handlers.increment(videoId));
        if ((payload as any).new?.user_id === currentUserId) {
          dispatch(handlers.setLiked(videoId, true));
        }
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'DELETE',
        schema: 'public',
        table: 'video_likes',
        filter: `video_id=eq.${videoId}`,
      },
      payload => {
        dispatch(handlers.decrement(videoId));
        if ((payload as any).old?.user_id === currentUserId) {
          dispatch(handlers.setLiked(videoId, false));
        }
      },
    )
    .subscribe();
  return channel;
};

export async function likeVideoRequest(videoId: string) {
  const {
    data: {user},
  } = await supabase.auth.getUser();
  if (!user) throw new Error('No authenticated user found');
  return supabase
    .from('video_likes')
    .insert({video_id: videoId, user_id: user.id});
}

export async function unlikeVideoRequest(videoId: string) {
  const {
    data: {user},
  } = await supabase.auth.getUser();
  if (!user) throw new Error('No authenticated user found');
  return supabase
    .from('video_likes')
    .delete()
    .match({video_id: videoId, user_id: user.id});
}
