import { createUser<PERSON><PERSON> } from './baseUserApi';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';
import type { Database } from '../../integrations/supabase/types';

export type LoginSession = Database['public']['Tables']['login_sessions']['Row'];

export const loginSessionsApi = createUserApi('loginSessionsApi', ['LoginSessions']);

export const extendedLoginSessionsApi = loginSessionsApi.injectEndpoints({
  endpoints: builder => ({
    getLoginSessions: builder.query<LoginSession[], void>({
      async queryFn() {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          const { data, error } = await supabase
            .from('login_sessions')
            .select('*')
            .eq('user_id', user.id)
            .order('login_time', { ascending: false });

          if (error) throw error;

          return { data: data ?? [] };
        } catch (error: any) {
          logger.error('Get login sessions error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch login sessions',
            },
          };
        }
      },
      providesTags: ['LoginSessions'],
    }),
    terminateSession: builder.mutation<{ success: boolean }, { id: string }>({
      async queryFn({ id }) {
        try {
          const { error } = await supabase
            .from('login_sessions')
            .update({
              is_active: false,
              logout_time: new Date().toISOString(),
            })
            .eq('id', id);

          if (error) throw error;

          return { data: { success: true } };
        } catch (error: any) {
          logger.error('Terminate session error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to terminate session',
            },
          };
        }
      },
      invalidatesTags: ['LoginSessions'],
    }),
  }),
});

export const {
  useGetLoginSessionsQuery,
  useTerminateSessionMutation,
} = extendedLoginSessionsApi;
