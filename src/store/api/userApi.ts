import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { User, UserProfile, UpdateProfileData } from '../../types/user';
import { supabase } from '../../integrations/supabase/client';
import { ProfileResponse } from '../../types/supabase';
import logger from '../../utils/logger';

export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/users',
    prepareHeaders: async (headers) => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.access_token) {
        headers.set('authorization', `Bearer ${session.access_token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['User', 'UserProfile', 'UserStats'],
  endpoints: (builder) => ({
    // Get current user profile
    getCurrentUser: builder.query<User, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No user found');}

          logger.debug('Fetching profile for user:', user.id);

          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', user.id)
            .single() as { data: ProfileResponse | null, error: any };

          if (error) {
            logger.error('Profile fetch error:', error);
            // If profile doesn't exist, create a basic one
            if (error.code === 'PGRST116') {
              const { data: newProfile, error: insertError } = await supabase
                .from('profiles')
                .insert({
                  user_id: user.id,
                  bio: null,
                  profile_picture_url: user.user_metadata?.avatar_url || null,
                  banner_image_url: null,
                  user_tag: 'Supporter',
                })
                .select()
                .single();

              if (insertError) {
                logger.error('Profile creation error:', insertError);
                throw insertError;
              }

              logger.debug('Created new profile:', newProfile);

              const userData: User = {
                id: user.id,
                email: user.email || '',
                username: user.email?.split('@')[0] || 'user',
                full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
                avatar_url: newProfile.profile_picture_url || undefined,
                bio: newProfile.bio || undefined,
                website: undefined,
                followers_count: 0,
                following_count: 0,
                likes_count: 0,
                videos_count: 0,
                is_private: false,
                is_verified: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              };

              return { data: userData };
            }
            throw error;
          }

          logger.debug('Profile fetched successfully:', profile);

          const userData: User = {
            id: user.id,
            email: user.email || '',
            username: user.email?.split('@')[0] || 'user',
            full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
            avatar_url: profile?.profile_picture_url || undefined,
            bio: profile?.bio || undefined,
            website: undefined,
            followers_count: 0,
            following_count: 0,
            likes_count: 0,
            videos_count: 0,
            is_private: false,
            is_verified: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return { data: userData };
        } catch (error: any) {
          logger.error('Get current user error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch user profile',
            },
          };
        }
      },
      providesTags: ['User'],
    }),

    // Update user profile
    updateProfile: builder.mutation<User, UpdateProfileData>({
      queryFn: async (updateData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No user found');}

          logger.debug('Updating profile for user:', user.id);
          logger.debug('Update data:', updateData);

          // First, try to get existing profile
          const { data: existingProfile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          let profile;
          if (existingProfile) {
            // Update existing profile
            const { data: updatedProfile, error: updateError } = await supabase
              .from('profiles')
              .update({
                username: updateData.username,
                full_name: updateData.full_name,
                bio: updateData.bio || null,
                website: updateData.website || null,
                is_private: updateData.is_private || false,
                updated_at: new Date().toISOString(),
              })
              .eq('id', user.id)
              .select()
              .single();

            if (updateError) {
              logger.error('Profile update error:', updateError);
              throw updateError;
            }
            profile = updatedProfile;
          } else {
            // Create new profile if it doesn't exist
            const { data: newProfile, error: insertError } = await supabase
              .from('profiles')
              .insert({
                user_id: user.id,
                username: updateData.username,
                full_name: updateData.full_name,
                bio: updateData.bio || null,
                website: updateData.website || null,
                is_private: updateData.is_private || false,
                followers_count: 0,
                following_count: 0,
                likes_count: 0,
                videos_count: 0,
                is_verified: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })
              .select()
              .single();

            if (insertError) {
              logger.error('Profile insert error:', insertError);
              throw insertError;
            }
            profile = newProfile;
          }

          logger.debug('Profile operation successful:', profile);

          const userData: User = {
            id: user.id,
            email: user.email || '',
            username: updateData.username || '',
            full_name: updateData.full_name || '',
            avatar_url: profile?.profile_picture_url ?? undefined,
            bio: profile?.bio ?? undefined,
            website: undefined,
            // pronouns: profile?.pronouns ?? undefined, // Removed because 'pronouns' does not exist on profile
            followers_count: 0,
            following_count: 0,
            likes_count: 0,
            videos_count: 0,
            is_private: updateData.is_private ?? false,
            is_verified: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return { data: userData };
        } catch (error: any) {
          logger.error('Profile update error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update profile',
            },
          };
        }
      },
      invalidatesTags: ['User', 'UserProfile'],
    }),

    // Upload avatar
    uploadAvatar: builder.mutation<{ avatar_url: string }, { file: any; userId: string }>({
      queryFn: async ({ file, userId }) => {
        try {
          logger.debug('Uploading avatar for user:', userId);

          const fileExt = file.name?.split('.').pop() || 'jpg';
          const fileName = `${userId}-${Date.now()}.${fileExt}`;
          const filePath = `avatars/${fileName}`;

          // Upload file to Supabase Storage
          const { error: uploadError } = await supabase.storage
            .from('avatars')
            .upload(filePath, file, {
              cacheControl: '3600',
              upsert: true,
            });

          if (uploadError) {
            logger.error('Avatar upload error:', uploadError);
            throw uploadError;
          }

          // Get public URL
          const { data: { publicUrl } } = supabase.storage
            .from('avatars')
            .getPublicUrl(filePath);

          logger.debug('Avatar uploaded successfully:', publicUrl);

          // Update profile with new avatar URL
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              profile_picture_url: publicUrl,
            })
            .eq('user_id', userId);

          if (updateError) {
            logger.error('Avatar URL update error:', updateError);
            throw updateError;
          }

          return { data: { avatar_url: publicUrl } };
        } catch (error: any) {
          logger.error('Avatar upload error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to upload avatar',
            },
          };
        }
      },
      invalidatesTags: ['User', 'UserProfile'],
    }),
  }),
});

export const {
  useGetCurrentUserQuery,
  useUpdateProfileMutation,
  useUploadAvatarMutation,
} = userApi;
