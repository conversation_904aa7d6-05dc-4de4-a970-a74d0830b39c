import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery(),
  endpoints: (builder) => ({
    login: builder.mutation({
      queryFn: async (credentials: { email: string; password: string }) => {
        try {
          const { data, error } = await supabase.auth.signInWithPassword(credentials);
          if (error) {
            return { error: { status: error.status || 500, data: error.message || error } };
          }
          return { data };
        } catch (error: any) {
          return { error: { status: error?.status || 500, data: error?.message || String(error) } };
        }
      },
    }),
    signup: builder.mutation({
      queryFn: async (
        payload: {
          email: string;
          password: string;
          username: string;
          full_name: string;
        }
      ) => {
        try {
          // Create auth user
          const { data: authData, error: authError } = await supabase.auth.signUp({
            email: payload.email,
            password: payload.password,
            options: {
              data: {
                username: payload.username,
                full_name: payload.full_name,
              },
            },
          });

          if (authError) {
            return {
              error: {
                status: authError.status || 500,
                data: authError.message || authError,
              },
            };
          }

          // Create profile
          if (authData.user) {
            const { error: profileError } = await supabase
              .from('profiles')
              .insert([
                {
                  user_id: authData.user.id,
                  username: payload.username,
                  full_name: payload.full_name,
                  email: payload.email,
                },
              ]);

            if (profileError) {
              return {
                error: {
                  status: 500,
                  data: profileError.message || profileError,
                },
              };
            }
          }

          return { data: authData.user };
        } catch (error: any) {
          return {
            error: {
              status: error?.status || 500,
              data: error?.message || String(error),
            },
          };
        }
      },
    }),
    logout: builder.mutation<boolean, void>({
      async queryFn(_arg, _api, _extraOptions, _baseQuery) {
        try {
          const { error } = await supabase.auth.signOut();
          if (error) {
            return {
              error: {
                status: error.status || 500,
                data: error.message || error,
              },
            };
          }
          return { data: true };
        } catch (error: any) {
          return {
            error: {
              status: error?.status || 500,
              data: error?.message || String(error),
            },
          };
        }
      },
    }),
  }),
});

export const { useLoginMutation, useSignupMutation, useLogoutMutation } = authApi;
