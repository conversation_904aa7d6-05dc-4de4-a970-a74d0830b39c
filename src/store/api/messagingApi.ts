import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { conversationsEndpoints } from './endpoints/conversationsApi';
import { messagesEndpoints } from './endpoints/messagesApi';
import { notificationsEndpoints } from './endpoints/notificationsApi';

/**
 * Messaging API - Handles all messaging-related operations
 * 
 * This API is split into logical endpoint groups:
 * - Conversations: Managing conversations and participants
 * - Messages: Sending, receiving, and managing messages
 * - Notifications: Handling user notifications
 */
export const messagingApi = createApi({
  reducerPath: 'messagingApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Conversation', 'Message', 'Notification'],
  endpoints: builder => ({
    // Conversations endpoints
    ...conversationsEndpoints(builder),
    
    // Messages endpoints
    ...messagesEndpoints(builder),
    
    // Notifications endpoints
    ...notificationsEndpoints(builder),
  }),
});

// Export hooks for usage in components - TikTok style messaging
export const {
  // Conversation hooks
  useGetConversationsQuery,
  useCreateConversationMutation,
  useAddParticipantToConversationMutation,

  // Message hooks
  useGetMessagesQuery,
  useSendMessageMutation,
  useMarkMessagesAsReadMutation,
  useAddMessageReactionMutation,
  useRemoveMessageReactionMutation,
  useForwardMessageMutation,
  useDeleteMessageMutation,
  useEditMessageMutation,

  // TikTok-style real-time features
  useUpdateTypingIndicatorMutation,
  useUpdateUserPresenceMutation,
  useGetTypingIndicatorsQuery,
  useGetUserPresenceQuery,

  // Notification hooks
  useGetNotificationsQuery,
  useMarkNotificationAsReadMutation,
} = messagingApi;
