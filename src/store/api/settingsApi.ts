import { UserSettings, NotificationSettings, PrivacySettings } from '../../types/settings';
import { supabase } from '../../integrations/supabase/client';
import { createUserApi } from './baseUserApi';
import logger from '../../utils/logger';

export const settingsApi = createUserApi('settingsApi', [
  'NotificationSettings',
  'PrivacySettings',
]);

export const extendedSettingsApi = settingsApi.injectEndpoints({
  endpoints: (builder) => ({
    // Update user settings (settings table)
    updateUserSettings: builder.mutation<UserSettings, Partial<UserSettings>>({
      queryFn: async (updateData, _api, _extraOptions, _baseQuery) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          logger.debug('Updating settings for user:', user.id, updateData);

          // Upsert settings
          const { data, error } = await supabase
            .from('settings')
            .upsert({
              user_id: user.id,
              ...updateData,
            })
            .select()
            .single();

          if (error) {throw error;}

          // Ensure all boolean fields are not null and match UserSettings type
          const safeSettings: UserSettings = {
            user_id: data?.user_id ?? user.id,
            allow_messages_from: (data?.allow_messages_from ?? 'Everyone') as 'Everyone' | 'Friends' | 'NoOne',
            dark_mode: data?.dark_mode === true,
            is_private_account: data?.is_private_account === true,
            show_activity_status: data?.show_activity_status !== false, // default true
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Settings update error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update settings',
            },
          };
        }
      },
      invalidatesTags: ['User', 'Settings'],
    }),

    // Get notification settings
    getNotificationSettings: builder.query<NotificationSettings, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          const { data, error } = await supabase
            .from('notification_settings')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') {throw error;}

          // Return default settings if none exist
          const defaultSettings: NotificationSettings = {
            user_id: user.id,
            push_notifications: true,
            email_notifications: true,
            like_notifications: true,
            comment_notifications: true,
            follow_notifications: true,
            live_notifications: true,
            message_notifications: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          // Ensure all boolean fields are not null
          const safeSettings: NotificationSettings = {
            user_id: (data?.user_id ?? defaultSettings.user_id),
            push_notifications: (data?.push_notifications ?? defaultSettings.push_notifications) as boolean,
            email_notifications: (data?.email_notifications ?? defaultSettings.email_notifications) as boolean,
            like_notifications: (data?.like_notifications ?? defaultSettings.like_notifications) as boolean,
            comment_notifications: (data?.comment_notifications ?? defaultSettings.comment_notifications) as boolean,
            follow_notifications: (data?.follow_notifications ?? defaultSettings.follow_notifications) as boolean,
            live_notifications: (data?.live_notifications ?? defaultSettings.live_notifications) as boolean,
            message_notifications: (data?.message_notifications ?? defaultSettings.message_notifications) as boolean,
            created_at: (data?.created_at ?? defaultSettings.created_at),
            updated_at: (data?.updated_at ?? defaultSettings.updated_at),
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Get notification settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch notification settings',
            },
          };
        }
      },
      providesTags: ['Notifications'],
    }),

    // Update notification settings
    updateNotificationSettings: builder.mutation<NotificationSettings, Partial<NotificationSettings>>({
      queryFn: async (updateData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          const { data, error } = await supabase
            .from('notification_settings')
            .upsert({
              user_id: user.id,
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (error) {throw error;}

          // Ensure all boolean fields are not null
          const safeSettings: NotificationSettings = {
            user_id: data?.user_id ?? user.id,
            push_notifications: (data?.push_notifications ?? true) === true,
            email_notifications: (data?.email_notifications ?? true) === true,
            like_notifications: (data?.like_notifications ?? true) === true,
            comment_notifications: (data?.comment_notifications ?? true) === true,
            follow_notifications: (data?.follow_notifications ?? true) === true,
            live_notifications: (data?.live_notifications ?? true) === true,
            message_notifications: (data?.message_notifications ?? true) === true,
            created_at: data?.created_at ?? new Date().toISOString(),
            updated_at: data?.updated_at ?? new Date().toISOString(),
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Update notification settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update notification settings',
            },
          };
        }
      },
      invalidatesTags: ['Notifications'],
    }),

    // Get privacy settings
    getPrivacySettings: builder.query<PrivacySettings, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          const { data, error } = await supabase
            .from('privacy_settings')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') {throw error;}

          const defaultSettings: PrivacySettings = {
            user_id: user.id,
            profile_visibility: true,
            allow_messages_from: 'everyone',
            show_online_status: true,
            allow_downloads: true,
            allow_duets: true,
            allow_comments: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          const safeSettings: PrivacySettings = {
            user_id: data?.user_id ?? defaultSettings.user_id,
            profile_visibility: (data?.profile_visibility ?? defaultSettings.profile_visibility) === true,
            allow_messages_from: (data?.allow_messages_from ?? defaultSettings.allow_messages_from) as 'everyone' | 'followers' | 'nobody',
            show_online_status: (data?.show_online_status ?? defaultSettings.show_online_status) === true,
            allow_downloads: (data?.allow_downloads ?? defaultSettings.allow_downloads) === true,
            allow_duets: (data?.allow_duets ?? defaultSettings.allow_duets) === true,
            allow_comments: (data?.allow_comments ?? defaultSettings.allow_comments) === true,
            created_at: data?.created_at ?? defaultSettings.created_at,
            updated_at: data?.updated_at ?? defaultSettings.updated_at,
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Get privacy settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch privacy settings',
            },
          };
        }
      },
      providesTags: ['Privacy'],
    }),

    // Update privacy settings
    updatePrivacySettings: builder.mutation<PrivacySettings, Partial<PrivacySettings>>({
      queryFn: async (updateData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {throw new Error('No authenticated user found');}

          const { data, error } = await supabase
            .from('privacy_settings')
            .upsert({
              user_id: user.id,
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (error) {throw error;}

          const safeSettings: PrivacySettings = {
            user_id: data?.user_id ?? user.id,
            profile_visibility: (data?.profile_visibility ?? true) === true,
            allow_messages_from: (data?.allow_messages_from ?? 'everyone') as 'everyone' | 'followers' | 'nobody',
            show_online_status: (data?.show_online_status ?? true) === true,
            allow_downloads: (data?.allow_downloads ?? true) === true,
            allow_duets: (data?.allow_duets ?? true) === true,
            allow_comments: (data?.allow_comments ?? true) === true,
            created_at: data?.created_at ?? new Date().toISOString(),
            updated_at: data?.updated_at ?? new Date().toISOString(),
          };

          return { data: safeSettings };
        } catch (error: any) {
          logger.error('Update privacy settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update privacy settings',
            },
          };
        }
      },
      invalidatesTags: ['Privacy'],
    }),
  }),
});

export const {
  useUpdateUserSettingsMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation,
} = extendedSettingsApi;