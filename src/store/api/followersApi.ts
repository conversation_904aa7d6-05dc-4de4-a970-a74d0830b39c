import {User} from '../../types/user';
import {supabase} from '../../integrations/supabase/client';
import {createUserApi} from './baseUserApi';
import logger from '../../utils/logger';

export const followersApi = createUserApi('followersApi', ['FollowersList']);

export const extendedFollowersApi = followersApi.injectEndpoints({
  endpoints: builder => ({
    // Follow/Unfollow user with optimistic updates
    toggleFollow: builder.mutation<
      { isFollowing: boolean; followersCount: number; followingCount: number },
      { targetUserId: string; isCurrentlyFollowing: boolean; currentFollowersCount: number; currentFollowingCount: number }
    >({
      queryFn: async ({ targetUserId, isCurrentlyFollowing, currentFollowersCount, currentFollowingCount }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          if (isCurrentlyFollowing) {
            // Unfollow
            const { error } = await supabase
              .from('user_follows')
              .delete()
              .eq('follower_id', user.id)
              .eq('following_id', targetUserId);

            if (error) throw error;

            return {
              data: {
                isFollowing: false,
                followersCount: Math.max(0, currentFollowersCount - 1),
                followingCount: currentFollowingCount
              }
            };
          } else {
            // Follow
            const { error } = await supabase
              .from('user_follows')
              .insert({
                follower_id: user.id,
                following_id: targetUserId,
                created_at: new Date().toISOString()
              });

            if (error) throw error;

            return {
              data: {
                isFollowing: true,
                followersCount: currentFollowersCount + 1,
                followingCount: currentFollowingCount
              }
            };
          }
        } catch (error: any) {
          logger.error('Toggle follow error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to toggle follow',
            },
          };
        }
      },
      // Optimistic update
      async onQueryStarted(
        { targetUserId, isCurrentlyFollowing, currentFollowersCount },
        { dispatch, queryFulfilled }
      ) {
        // Update followers list cache optimistically
        const followersPatchResult = dispatch(
          followersApi.util.updateQueryData('getFollowers', targetUserId, (draft) => {
            // This will be handled by the component that manages the follow state
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic updates on error
          followersPatchResult.undo();
        }
      },
      invalidatesTags: (result, error, { targetUserId }) => [
        'User',
        'Followers',
        { type: 'User', id: targetUserId }
      ],
    }),

    // Check if user is following another user
    checkIsFollowing: builder.query<boolean, string>({
      queryFn: async (targetUserId) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) return { data: false };

          const { data, error } = await supabase
            .from('user_follows')
            .select('id')
            .eq('follower_id', user.id)
            .eq('following_id', targetUserId)
            .single();

          if (error && error.code !== 'PGRST116') throw error;

          return { data: !!data };
        } catch (error: any) {
          logger.error('Check is following error:', error);
          return { data: false };
        }
      },
      providesTags: (result, error, targetUserId) => [
        { type: 'User', id: targetUserId },
        'Followers'
      ],
    }),

    // Get followers list
    getFollowers: builder.query<User[], string>({
      queryFn: async userId => {
        try {
          // Get follower relationships
          const {data: followers, error: followersError} = await supabase
            .from('user_follows')
            .select('follower_id')
            .eq('following_id', userId);

          if (followersError) {
            throw followersError;
          }

          if (!followers || followers.length === 0) {
            logger.debug('No followers found for user:', userId);
            return {data: []};
          }

          logger.debug(`Found ${followers.length} followers for user:`, userId);

          // Get user data for followers
          const followerIds = followers
            .map(f => f.follower_id)
            .filter(id => id != null);

          // Get user data from users table
          const {data: users, error: usersError} = await supabase
            .from('users')
            .select('id, username, full_name, auth_user_id')
            .in('auth_user_id', followerIds);

          if (usersError) {
            throw usersError;
          }

          // Get profiles for these users
          const {data: profiles, error: profilesError} = await supabase
            .from('profiles')
            .select('user_id, profile_picture_url, bio, user_tag')
            .in('user_id', followerIds);

          if (profilesError) {
            logger.error('Error fetching profiles:', profilesError);
          }

          // Create a map of profiles by user_id
          const profileMap = new Map();
          profiles?.forEach(profile => {
            profileMap.set(profile.user_id, profile);
          });

          const followerUsers = await Promise.all(
            (users || []).map(async userData => {
              const profile = profileMap.get(userData.auth_user_id);

              const {count: followersCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('following_id', userData.auth_user_id!);

              const {count: followingCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('follower_id', userData.auth_user_id!);

              return {
                id: userData.auth_user_id,
                username: userData.username,
                full_name: userData.full_name,
                bio: profile?.bio,
                profile_picture_url: profile?.profile_picture_url,
                banner_image_url: profile?.banner_image_url,
                user_tag: profile?.user_tag || 'Supporter',
                followers_count: followersCount || 0,
                following_count: followingCount || 0,
                likes_count: 0,
                videos_count: 0,
                auth_user_id: userData.auth_user_id,
              } as User;
            }),
          );

          return {data: followerUsers};
        } catch (error: any) {
          logger.error('Get followers error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to get followers',
            },
          };
        }
      },
      providesTags: ['Followers'],
    }),

    // Get following list
    getFollowing: builder.query<User[], string>({
      queryFn: async userId => {
        try {
          // Get following relationships
          const {data: following, error: followingError} = await supabase
            .from('user_follows')
            .select('following_id')
            .eq('follower_id', userId);

          if (followingError) {
            throw followingError;
          }

          if (!following || following.length === 0) {
            logger.debug('No following found for user:', userId);
            return {data: []};
          }

          logger.debug(`Found ${following.length} following for user:`, userId);

          // Get user data for following
          const followingIds = following
            .map(f => f.following_id)
            .filter(id => id != null);
          const {data: users, error: usersError} = await supabase
            .from('users')
            .select('id, username, full_name, auth_user_id')
            .in('auth_user_id', followingIds);

          if (usersError) {
            throw usersError;
          }

          // Get profiles for these users
          const {data: profiles, error: profilesError} = await supabase
            .from('profiles')
            .select('user_id, profile_picture_url, bio, user_tag')
            .in('user_id', followingIds);

          if (profilesError) {
            logger.error('Error fetching profiles:', profilesError);
          }

          // Create a map of profiles by user_id
          const profileMap = new Map();
          profiles?.forEach(profile => {
            profileMap.set(profile.user_id, profile);
          });

          const followingUsers = await Promise.all(
            (users || []).map(async userData => {
              const {count: followersCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('following_id', userData.auth_user_id!);

              const {count: followingCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('follower_id', userData.auth_user_id!);

              const profile = profileMap.get(userData.auth_user_id);

              return {
                id: userData.auth_user_id,
                username: userData.username,
                full_name: userData.full_name,
                bio: profile?.bio,
                profile_picture_url: profile?.profile_picture_url,
                banner_image_url: profile?.banner_image_url,
                user_tag: profile?.user_tag || 'Supporter',
                followers_count: followersCount || 0,
                following_count: followingCount || 0,
                likes_count: 0,
                videos_count: 0,
              } as User;
            }),
          );

          return {data: followingUsers};
        } catch (error: any) {
          logger.error('Get following error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to get following',
            },
          };
        }
      },
      providesTags: ['Followers'],
    }),
  }),
});

export const {
  useToggleFollowMutation,
  useCheckIsFollowingQuery,
  useGetFollowersQuery,
  useGetFollowingQuery,
} = extendedFollowersApi;

export const subscribeToFollowers = (

  followingUserId: string,
  dispatch: any,
  handlers: {
    increment: (userId: string) => any;
    decrement: (userId: string) => any;

    setFollowing: (userId: string, isFollowing: boolean) => any;
  },
  currentUserId: string,
) => {
  const channel = supabase
    .channel('public:followers')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'followers',

        filter: `following_user_id=eq.${followingUserId}`,
      },
      (payload) => {
        dispatch(handlers.increment(followingUserId));
        if ((payload as any).new?.follower_user_id === currentUserId) {
          dispatch(handlers.setFollowing(followingUserId, true));
        }
      },
    )
    .on(
      'postgres_changes',
      {
        event: 'DELETE',
        schema: 'public',
        table: 'followers',
        filter: `following_user_id=eq.${followingUserId}`,
      },
      (payload) => {
        dispatch(handlers.decrement(followingUserId));
        if ((payload as any).old?.follower_user_id === currentUserId) {
          dispatch(handlers.setFollowing(followingUserId, false));
        }
      },
    )
    .subscribe();
  return channel;
};
