import logger from '../../../utils/logger';

/**
 * Helper function to convert Supabase response to proper array
 * Supabase sometimes returns objects with numeric keys instead of arrays
 */
export const ensureArray = (data: any): any[] => {
  if (!data) return [];
  if (Array.isArray(data)) return data;
  
  // Handle object with numeric keys (Supabase sometimes returns this format)
  if (typeof data === 'object') {
    // Try to convert object with numeric keys and length property
    if (data.length !== undefined) {
      try {
        const result = [];
        for (let i = 0; i < data.length; i++) {
          if (data[i] != null) {
            result.push(data[i]);
          }
        }
        logger.debug('ensureArray: Converted object to array using indices');
        return result;
      } catch (e) {
        logger.debug('ensureArray: Error converting using indices:', e);
      }
    }
    
    // Fallback: try to convert using Object.values
    try {
      const result = Object.values(data).filter(item => 
        item !== null && 
        item !== undefined && 
        typeof item === 'object'
      );
      logger.debug('ensureArray: Converted object to array using Object.values');
      return result;
    } catch (e) {
      logger.debug('ensureArray: Error converting using Object.values:', e);
    }
  }
  
  return [];
};

/**
 * Helper function to handle Supabase auth errors
 */
export const handleAuthError = (error: any) => {
  if (error?.message?.includes('JWT')) {
    return { error: 'Authentication expired. Please log in again.' };
  }
  if (error?.message?.includes('not authenticated')) {
    return { error: 'You must be logged in to perform this action.' };
  }
  return { error: error?.message || 'An unexpected error occurred' };
};

/**
 * Helper function to validate conversation access
 */
export const validateConversationAccess = async (
  supabase: any,
  conversationId: string,
  userId: string
): Promise<{ hasAccess: boolean; error?: string }> => {
  try {
    const { data, error } = await supabase.rpc('is_conversation_participant', {
      conversation_id_param: conversationId,
      user_id_param: userId,
    });

    if (error) {
      logger.error('Conversation access validation error:', error);
      return { hasAccess: false, error: error.message };
    }

    return { hasAccess: !!data };
  } catch (error) {
    logger.error('Conversation access validation exception:', error);
    return { hasAccess: false, error: 'Failed to validate conversation access' };
  }
};

/**
 * Helper function to format user data for conversations
 */
export const formatUserForConversation = (user: any) => {
  return {
    id: user.id,
    username: user.username || 'Unknown',
    full_name: user.full_name || user.username || 'Unknown User',
    avatar_url: user.avatar_url || user.profile_picture_url,
    is_online: user.is_online || false,
  };
};

/**
 * Helper function to format message data
 */
export const formatMessageData = (message: any, sender: any) => {
  return {
    ...message,
    sender: formatUserForConversation(sender),
  };
};
