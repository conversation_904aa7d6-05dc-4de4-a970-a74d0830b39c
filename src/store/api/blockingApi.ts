import {User} from '../../types/user';
import {supabase} from '../../integrations/supabase/client';
import {createUserApi} from './baseUserApi';
import logger from '../../utils/logger';

export const blockingApi = createUserApi('blockingApi', ['BlockingSettings']);

export const extendedBlockingApi = blockingApi.injectEndpoints({
  endpoints: builder => ({
    // Get blocked users
    getBlockedUsers: builder.query<(User & {blocked_at: string})[], void>({
      queryFn: async () => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          const {data: blocks, error: blocksError} = await supabase
            .from('blocked_users')
            .select('*')
            .eq('blocker_user_id', user.id);

          if (blocksError) {
            throw blocksError;
          }

          if (!blocks || blocks.length === 0) {
            return {data: []};
          }

          const blockedIds = blocks
            .map(b => b.blocked_user_id)
            .filter((id): id is string => !!id);

          const {data: users, error: usersError} = await supabase
            .from('users')
            .select('*')
            .in('auth_user_id', blockedIds);

          if (usersError) {
            throw usersError;
          }

          const {data: profiles} = await supabase
            .from('profiles')
            .select('*')
            .in('user_id', blockedIds);

          const profileMap = new Map();
          profiles?.forEach(p => {
            profileMap.set(p.user_id, p);
          });

          const blockedUsers = blocks
            .map(block => {
              const u = users?.find(
                us => us.auth_user_id === block.blocked_user_id,
              );
              if (!u) {
                return null;
              }
              const profile = profileMap.get(block.blocked_user_id);

              return {
                id: u.auth_user_id,
                email: u.email,
                username: u.username,
                full_name: u.full_name,
                phone_number: u.phone_number,
                date_of_birth: u.date_of_birth,
                gender: u.gender,
                is_verified: u.is_verified ?? false,
                is_banned: u.is_banned ?? false,
                created_at: u.created_at ?? new Date().toISOString(),
                updated_at: u.updated_at ?? new Date().toISOString(),
                auth_user_id: u.auth_user_id ?? undefined,
                bio: profile?.bio ?? undefined,
                profile_picture_url: profile?.profile_picture_url ?? undefined,
                banner_image_url: profile?.banner_image_url ?? undefined,
                user_tag: profile?.user_tag || 'Supporter',
                followers_count: 0,
                following_count: 0,
                likes_count: 0,
                videos_count: 0,
                blocked_at: block.created_at,
              } as User & {blocked_at: string};
            })
            .filter((u): u is User & {blocked_at: string} => u !== null);

          return {data: blockedUsers};
        } catch (error: any) {
          logger.error('Get blocked users error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to get blocked users',
            },
          };
        }
      },
      providesTags: ['BlockedUsers'],
    }),

    // Block user
    blockUser: builder.mutation<{success: boolean}, string>({
      queryFn: async userIdToBlock => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          const {error} = await supabase.from('blocked_users').insert({
            blocker_user_id: user.id,
            blocked_user_id: userIdToBlock,
          });

          if (error) {
            throw error;
          }

          return {data: {success: true}};
        } catch (error: any) {
          logger.error('Block user error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to block user',
            },
          };
        }
      },
      invalidatesTags: ['BlockedUsers'],
    }),

    // Unblock user
    unblockUser: builder.mutation<{success: boolean}, string>({
      queryFn: async userIdToUnblock => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          const {error} = await supabase
            .from('blocked_users')
            .delete()
            .eq('blocker_user_id', user.id)
            .eq('blocked_user_id', userIdToUnblock);

          if (error) {
            throw error;
          }

          return {data: {success: true}};
        } catch (error: any) {
          logger.error('Unblock user error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to unblock user',
            },
          };
        }
      },
      invalidatesTags: ['BlockedUsers'],
    }),
  }),
});

export const {
  useGetBlockedUsersQuery,
  useBlockUserMutation,
  useUnblockUserMutation,
} = extendedBlockingApi;
