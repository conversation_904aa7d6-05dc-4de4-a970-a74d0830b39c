import {SecuritySettings} from '../../types/settings';
import {supabase} from '../../integrations/supabase/client';
import {createUserApi} from './baseUserApi';
import logger from '../../utils/logger';

export const securitySettingsApi = createUserApi('securitySettingsApi', [
  'Security',
]);

export const extendedSecuritySettingsApi = securitySettingsApi.injectEndpoints({
  endpoints: builder => ({
    getSecuritySettings: builder.query<SecuritySettings, void>({
      queryFn: async () => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user');
          }

          const {data, error} = await supabase
            .from('security_settings')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') {
            throw error;
          }

          let settings: SecuritySettings;
          if (!data) {
            const defaultSettings = {
              user_id: user.id,
              two_factor_enabled: false,
              login_notifications: true,
              device_management: true,
              session_timeout: 30,
              security_questions_set: false,
              backup_codes_generated: false,
            };

            const {data: newSettings, error: insertError} = await supabase
              .from('security_settings')
              .insert(defaultSettings)
              .select()
              .single();

            if (insertError) {
              throw insertError;
            }

            settings = {
              ...defaultSettings,
              password_last_changed:
                newSettings?.password_last_changed ?? undefined,
              created_at: newSettings?.created_at ?? new Date().toISOString(),
              updated_at: newSettings?.updated_at ?? new Date().toISOString(),
            };
          } else {
            settings = {
              user_id: data.user_id,
              two_factor_enabled: data.two_factor_enabled === true,
              login_notifications: data.login_notifications === true,
              device_management: data.device_management === true,
              session_timeout: data.session_timeout ?? 30,
              password_last_changed: data.password_last_changed ?? undefined,
              security_questions_set:
                'security_questions_set' in data
                  ? (data as any).security_questions_set === true
                  : false,
              backup_codes_generated:
                'backup_codes_generated' in data
                  ? (data as any).backup_codes_generated === true
                  : false,
              created_at: data.created_at ?? new Date().toISOString(),
              updated_at: data.updated_at ?? new Date().toISOString(),
            };
          }

          return {data: settings};
        } catch (error: any) {
          logger.error('Get security settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to fetch security settings',
            },
          };
        }
      },
      providesTags: ['Security'],
    }),

    updateSecuritySettings: builder.mutation<
      SecuritySettings,
      Partial<SecuritySettings>
    >({
      queryFn: async updateData => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user');
          }

          const {data, error} = await supabase
            .from('security_settings')
            .upsert({
              user_id: user.id,
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (error) {
            throw error;
          }

          const settings: SecuritySettings = {
            user_id: data?.user_id ?? user.id,
            two_factor_enabled: data?.two_factor_enabled === true,
            login_notifications: data?.login_notifications === true,
            device_management: data?.device_management === true,
            session_timeout: data?.session_timeout ?? 30,
            password_last_changed: data?.password_last_changed ?? undefined,
            security_questions_set:
              'security_questions_set' in (data ?? {})
                ? (data as any).security_questions_set === true
                : false,
            backup_codes_generated:
              'backup_codes_generated' in (data ?? {})
                ? (data as any).backup_codes_generated === true
                : false,
            created_at: data?.created_at ?? new Date().toISOString(),
            updated_at: data?.updated_at ?? new Date().toISOString(),
          };

          return {data: settings};
        } catch (error: any) {
          logger.error('Update security settings error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to update security settings',
            },
          };
        }
      },
      invalidatesTags: ['Security'],
    }),
  }),
});

export const {useGetSecuritySettingsQuery, useUpdateSecuritySettingsMutation} =
  extendedSecuritySettingsApi;
