import {User} from '../../types/user';
import {supabase} from '../../integrations/supabase/client';
import {createUserApi} from './baseUserApi';
import logger from '../../utils/logger';

export const discoveryApi = createUserApi('discoveryApi', ['UserDiscovery']);

export const extendedDiscoveryApi = discoveryApi.injectEndpoints({
  endpoints: builder => ({
    // Discover users (suggested friends)
    discoverUsers: builder.query<User[], {limit?: number; search?: string}>({
      queryFn: async ({limit = 20, search = ''}) => {
        try {
          const {
            data: {user},
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No authenticated user found');
          }

          logger.debug('🔍 Discover users called with:', {
            limit,
            search,
            currentUserId: user.id,
          });

          // First, let's check if there are ANY users in the database
          const {data: allUsersCheck, error: allUsersError} = await supabase
            .from('users')
            .select('*')
            .limit(5);

          logger.debug('🔍 ALL USERS CHECK:', {
            count: allUsersCheck?.length || 0,
            users: allUsersCheck,
            error: allUsersError,
          });

          // Check current user data
          const {data: currentUserCheck, error: currentUserError} =
            await supabase
              .from('users')
              .select('*')
              .eq('auth_user_id', user.id);

          logger.debug('🔍 CURRENT USER CHECK:', {
            found: currentUserCheck?.length || 0,
            currentUser: currentUserCheck,
            error: currentUserError,
          });

          // First get users, then get their profiles separately
          let userQuery = supabase
            .from('users')
            .select('*')
            // Temporarily comment out current user filter for testing
            // .neq('auth_user_id', user.id)
            .eq('is_banned', false)
            .limit(limit);

          logger.debug(
            '🔍 Query without current user filter - should show all users including yourself',
          );

          // Add search filter if provided
          if (search.trim()) {
            const searchTerm = search.trim();
            logger.debug('🔍 Adding search filter for:', searchTerm);
            // Try multiple search approaches
            userQuery = userQuery.or(
              `username.ilike.%${searchTerm}%,full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`,
            );
          }

          const {data: users, error: userError} = await userQuery;

          logger.debug('📊 Users query result:', {
            users: users?.length || 0,
            error: userError,
            firstUser: users?.[0],
          });

          if (userError) {
            logger.error('❌ User query error:', userError);
            throw userError;
          }

          if (!users || users.length === 0) {
            logger.debug('📭 No users found');
            return {data: []};
          }

          // Get profiles for these users
          const userIds = users
            .map(u => u.auth_user_id)
            .filter((id): id is string => id != null); // Type guard to ensure non-null strings

          const {data: profiles} = await supabase
            .from('profiles')
            .select('*')
            .in('user_id', userIds);

          // Create a map of profiles by user_id
          const profileMap = new Map();
          profiles?.forEach(profile => {
            profileMap.set(profile.user_id, profile);
          });

          // Get follow status for each user
          const {data: followData} = await supabase
            .from('user_follows')
            .select('following_id')
            .eq('follower_id', user.id)
            .in('following_id', userIds);

          const followingIds = new Set(
            followData?.map(f => f.following_id) || [],
          );

          // Get follower counts for each user
          const usersWithCounts = await Promise.all(
            users.map(async userData => {
              const {count: followersCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('following_id', userData.auth_user_id || '');

              const {count: followingCount} = await supabase
                .from('user_follows')
                .select('*', {count: 'exact', head: true})
                .eq('follower_id', userData.auth_user_id || '');

              const profile = profileMap.get(userData.auth_user_id);

              return {
                id: userData.auth_user_id,
                email: userData.email,
                username: userData.username,
                full_name: userData.full_name,
                phone_number: userData.phone_number,
                date_of_birth: userData.date_of_birth,
                gender: userData.gender,
                is_verified: userData.is_verified,
                is_banned: userData.is_banned,
                created_at: userData.created_at,
                updated_at: userData.updated_at,
                auth_user_id: userData.auth_user_id,
                bio: profile?.bio,
                profile_picture_url: profile?.profile_picture_url,
                banner_image_url: profile?.banner_image_url,
                user_tag: profile?.user_tag || 'Supporter',
                followers_count: followersCount || 0,
                following_count: followingCount || 0,
                likes_count: 0,
                videos_count: 0,
                is_following: followingIds.has(userData.auth_user_id!),
              } as User & {is_following: boolean};
            }),
          );

          return {data: usersWithCounts};
        } catch (error: any) {
          logger.error('Discover users error:', error);
          return {
            error: {
              status: 'FETCH_ERROR',
              error: error.message || 'Failed to discover users',
            },
          };
        }
      },
      providesTags: ['User', 'Followers'],
    }),
  }),
});

export const {useDiscoverUsersQuery} = extendedDiscoveryApi;
