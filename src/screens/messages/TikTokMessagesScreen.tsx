import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import { useRealtimeMessagingContext } from '../../contexts/RealtimeMessagingContext';
import TikTokChatList from '../../components/messages/TikTokChatList';
import Header from '../../components/common/Header';
import Text from '../../components/common/Text';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { useGetConversationsQuery, useCreateConversationMutation } from '../../store/api/messagingApi';
import { ConversationWithDetails } from '../../types/messaging';
import { normalize } from '../../utils/responsive';
import logger from '../../utils/logger';

type RootStackParamList = {
  TikTokMessages: undefined;
  TikTokChat: { conversationId: string; otherUser?: any };
  NewChat: undefined;
  SearchUsers: undefined;
};

type TikTokMessagesScreenNavigationProp = StackNavigationProp<RootStackParamList, 'TikTokMessages'>;

const TikTokMessagesScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<TikTokMessagesScreenNavigationProp>();
  
  // Real-time messaging context
  const { isConnected, onlineUsersCount } = useRealtimeMessagingContext();

  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // API hooks
  const { data: conversations = [], isLoading, error, refetch } = useGetConversationsQuery();
  const [createConversation] = useCreateConversationMutation();

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      logger.error('Failed to refresh conversations:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  // Handle chat press
  const handleChatPress = useCallback((conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    navigation.navigate('TikTokChat', {
      conversationId,
      otherUser: conversation?.other_participant,
    });
  }, [conversations, navigation]);

  // Handle new chat
  const handleNewChat = useCallback(() => {
    navigation.navigate('SearchUsers');
  }, [navigation]);

  // Handle search toggle
  const handleSearchToggle = useCallback(() => {
    setShowSearch(!showSearch);
    if (showSearch) {
      setSearchQuery('');
    }
  }, [showSearch]);

  // Handle archive chat
  const handleArchiveChat = useCallback((conversationId: string) => {
    Alert.alert(
      'Archive Chat',
      'Are you sure you want to archive this conversation?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Archive',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement archive functionality
            logger.debug('Archive conversation:', conversationId);
          },
        },
      ]
    );
  }, []);

  // Handle delete chat
  const handleDeleteChat = useCallback((conversationId: string) => {
    Alert.alert(
      'Delete Chat',
      'Are you sure you want to delete this conversation? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement delete functionality
            logger.debug('Delete conversation:', conversationId);
          },
        },
      ]
    );
  }, []);

  // Handle pin chat
  const handlePinChat = useCallback((conversationId: string) => {
    // TODO: Implement pin functionality
    logger.debug('Pin conversation:', conversationId);
  }, []);

  // Connection status indicator
  const renderConnectionStatus = () => {
    if (!isConnected) {
      return (
        <View style={[styles.connectionStatus, { backgroundColor: theme.colors.error }]}>
          <Ionicons name="cloud-offline" size={normalize(16)} color="#FFFFFF" />
          <Text style={styles.connectionStatusText}>Connecting...</Text>
        </View>
      );
    }
    return null;
  };

  // Search bar
  const renderSearchBar = () => {
    if (!showSearch) return null;

    return (
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="search" size={normalize(20)} color={theme.colors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search conversations..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoFocus
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={normalize(20)} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Header right component
  const renderHeaderRight = () => (
    <View style={styles.headerRight}>
      <TouchableOpacity
        style={styles.headerButton}
        onPress={handleSearchToggle}
      >
        <Ionicons 
          name={showSearch ? "close" : "search"} 
          size={normalize(24)} 
          color={theme.colors.text} 
        />
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.headerButton}
        onPress={handleNewChat}
      >
        <Ionicons name="add" size={normalize(24)} color={theme.colors.text} />
      </TouchableOpacity>
    </View>
  );

  // Empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="chatbubbles-outline" size={normalize(48)} color={theme.colors.textSecondary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]} weight="600">
        No messages yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Start a conversation with your friends and followers
      </Text>
      <TouchableOpacity
        style={[styles.startChatButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleNewChat}
      >
        <Ionicons name="add" size={normalize(20)} color="#FFFFFF" />
        <Text style={styles.startChatButtonText} weight="600">
          Start Chatting
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header
          title="Messages"
          rightComponent={renderHeaderRight()}
        />
        {renderConnectionStatus()}
        {renderSearchBar()}
        <LoadingSpinner />
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header
          title="Messages"
          rightComponent={renderHeaderRight()}
        />
        {renderConnectionStatus()}
        {renderSearchBar()}
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={normalize(48)} color={theme.colors.error} />
          <Text style={[styles.errorTitle, { color: theme.colors.error }]} weight="600">
            Failed to load messages
          </Text>
          <Text style={[styles.errorSubtitle, { color: theme.colors.textSecondary }]}>
            Please check your connection and try again
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleRefresh}
          >
            <Text style={styles.retryButtonText} weight="600">
              Try Again
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Header
        title="Messages"
        subtitle={isConnected ? `${onlineUsersCount} online` : 'Offline'}
        rightComponent={renderHeaderRight()}
      />

      {/* Connection Status */}
      {renderConnectionStatus()}

      {/* Search Bar */}
      {renderSearchBar()}

      {/* Messages List */}
      {conversations.length === 0 ? (
        renderEmptyState()
      ) : (
        <TikTokChatList
          conversations={conversations}
          onChatPress={handleChatPress}
          onRefresh={handleRefresh}
          isRefreshing={refreshing}
          onNewChat={handleNewChat}
          searchQuery={searchQuery}
          onArchiveChat={handleArchiveChat}
          onDeleteChat={handleDeleteChat}
          onPinChat={handlePinChat}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: normalize(8),
    marginLeft: normalize(8),
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(16),
  },
  connectionStatusText: {
    color: '#FFFFFF',
    fontSize: normalize(12),
    fontWeight: '500',
    marginLeft: normalize(8),
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: normalize(16),
    marginVertical: normalize(8),
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderRadius: normalize(25),
  },
  searchInput: {
    flex: 1,
    fontSize: normalize(16),
    marginLeft: normalize(12),
    marginRight: normalize(8),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  emptyIconContainer: {
    width: normalize(100),
    height: normalize(100),
    borderRadius: normalize(50),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: normalize(24),
  },
  emptyTitle: {
    fontSize: normalize(20),
    textAlign: 'center',
    marginBottom: normalize(8),
  },
  emptySubtitle: {
    fontSize: normalize(16),
    textAlign: 'center',
    lineHeight: normalize(24),
    marginBottom: normalize(32),
  },
  startChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(24),
    paddingVertical: normalize(12),
    borderRadius: normalize(25),
  },
  startChatButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(16),
    marginLeft: normalize(8),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  errorTitle: {
    fontSize: normalize(18),
    textAlign: 'center',
    marginTop: normalize(16),
    marginBottom: normalize(8),
  },
  errorSubtitle: {
    fontSize: normalize(14),
    textAlign: 'center',
    lineHeight: normalize(20),
    marginBottom: normalize(24),
  },
  retryButton: {
    paddingHorizontal: normalize(24),
    paddingVertical: normalize(12),
    borderRadius: normalize(25),
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(16),
  },
});

export default TikTokMessagesScreen;
