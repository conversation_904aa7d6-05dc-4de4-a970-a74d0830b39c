import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTheme } from '../../contexts/ThemeContext';
import { useRealtimeMessagingContext } from '../../contexts/RealtimeMessagingContext';
import TikTokChatInput from '../../components/messages/TikTokChatInput';
import TikTokMessageBubble from '../../components/messages/TikTokMessageBubble';
import TikTokTypingIndicator from '../../components/messages/TikTokTypingIndicator';
import Header from '../../components/common/Header';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { useGetMessagesQuery, useSendMessageMutation, useMarkMessagesAsReadMutation } from '../../store/api/messagingApi';
import { MessageWithSender } from '../../types/messaging';
import { normalize } from '../../utils/responsive';
import { shouldGroupMessages, shouldShowTimestamp, getDateSeparatorText } from '../../utils/messagingHelpers';
import logger from '../../utils/logger';

const { height: screenHeight } = Dimensions.get('window');

type RootStackParamList = {
  TikTokChat: { conversationId: string; otherUser?: any };
};

type TikTokChatScreenRouteProp = RouteProp<RootStackParamList, 'TikTokChat'>;
type TikTokChatScreenNavigationProp = StackNavigationProp<RootStackParamList, 'TikTokChat'>;

const TikTokChatScreen: React.FC = () => {
  const { theme } = useTheme();
  const route = useRoute<TikTokChatScreenRouteProp>();
  const navigation = useNavigation<TikTokChatScreenNavigationProp>();
  const { conversationId, otherUser } = route.params;

  // Real-time messaging context
  const {
    typingUsers,
    handleTextChange,
    handleVoiceRecording,
    handleMediaSharing,
    setCurrentConversationId,
  } = useRealtimeMessagingContext();

  // State
  const [messageText, setMessageText] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<MessageWithSender | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // Refs
  const flatListRef = useRef<FlatList>(null);
  const scrollOffsetY = useRef(new Animated.Value(0)).current;

  // API hooks
  const { data: messages = [], isLoading, error, refetch } = useGetMessagesQuery(conversationId);
  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();
  const [markAsRead] = useMarkMessagesAsReadMutation();

  // Set current conversation for real-time features
  useEffect(() => {
    setCurrentConversationId(conversationId);
    return () => setCurrentConversationId(undefined);
  }, [conversationId, setCurrentConversationId]);

  // Mark messages as read when screen is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (messages.length > 0) {
        markAsRead({ conversation_id: conversationId });
      }
    });
    return unsubscribe;
  }, [navigation, conversationId, messages.length, markAsRead]);

  // Handle text input changes with typing indicators
  const handleMessageTextChange = useCallback((text: string) => {
    setMessageText(text);
    handleTextChange(text);
  }, [handleTextChange]);

  // Send message
  const handleSendMessage = useCallback(async () => {
    if (!messageText.trim() && !isUploading) return;

    try {
      const messageData = {
        conversation_id: conversationId,
        content: messageText.trim(),
        message_type: 'text' as const,
        reply_to_message_id: replyingTo?.id,
      };

      await sendMessage(messageData).unwrap();
      setMessageText('');
      setReplyingTo(null);
      
      // Scroll to bottom after sending
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

    } catch (error) {
      logger.error('Failed to send message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  }, [messageText, conversationId, replyingTo, sendMessage, isUploading]);

  // Handle media selection
  const handleSelectAttachment = useCallback(() => {
    // TODO: Implement media picker
    Alert.alert('Media Selection', 'Media picker will be implemented here');
  }, []);

  // Handle emoji toggle
  const handleToggleEmoji = useCallback(() => {
    // TODO: Implement emoji picker
    Alert.alert('Emoji Picker', 'Emoji picker will be implemented here');
  }, []);

  // Handle voice recording
  const handleVoiceRecord = useCallback(() => {
    // TODO: Implement voice recording
    handleVoiceRecording(true);
    Alert.alert('Voice Recording', 'Voice recording will be implemented here');
  }, [handleVoiceRecording]);

  // Handle message actions
  const handleImagePress = useCallback((imageUrl: string) => {
    // TODO: Implement image viewer
    Alert.alert('Image Viewer', `View image: ${imageUrl}`);
  }, []);

  const handleReaction = useCallback((messageId: string, reaction: string) => {
    // TODO: Implement reaction handling
    logger.debug('Add reaction:', { messageId, reaction });
  }, []);

  const handleReply = useCallback((message: MessageWithSender) => {
    setReplyingTo(message);
  }, []);

  const handleForward = useCallback((message: MessageWithSender) => {
    // TODO: Implement message forwarding
    Alert.alert('Forward Message', 'Message forwarding will be implemented here');
  }, []);

  const handleLongPress = useCallback((message: MessageWithSender) => {
    // TODO: Implement message selection/context menu
    Alert.alert('Message Options', 'Message options will be implemented here');
  }, []);

  const handleDoublePress = useCallback((message: MessageWithSender) => {
    // TikTok-style double tap to like
    handleReaction(message.id, '❤️');
  }, [handleReaction]);

  // Cancel reply
  const handleCancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  // Scroll handling
  const handleScroll = useCallback((event: any) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const isNearBottom = contentOffset.y + layoutMeasurement.height >= contentSize.height - 100;
    setShowScrollToBottom(!isNearBottom);
    
    scrollOffsetY.setValue(contentOffset.y);
  }, [scrollOffsetY]);

  const scrollToBottom = useCallback(() => {
    flatListRef.current?.scrollToEnd({ animated: true });
  }, []);

  // Render message item
  const renderMessage = useCallback(({ item: message, index }: { item: MessageWithSender; index: number }) => {
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    
    const isMe = message.sender_id === 'current_user_id'; // TODO: Get actual current user ID
    const showAvatar = !shouldGroupMessages(message, previousMessage);
    const showTimestamp = shouldShowTimestamp(message, nextMessage);
    
    return (
      <TikTokMessageBubble
        message={message}
        isMe={isMe}
        showAvatar={showAvatar}
        showTimestamp={showTimestamp}
        onImagePress={handleImagePress}
        onReaction={handleReaction}
        onReply={handleReply}
        onForward={handleForward}
        onLongPress={handleLongPress}
        onDoublePress={handleDoublePress}
      />
    );
  }, [messages, handleImagePress, handleReaction, handleReply, handleForward, handleLongPress, handleDoublePress]);

  // Render date separator
  const renderDateSeparator = useCallback((date: string) => (
    <View style={[styles.dateSeparator, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.dateSeparatorText, { color: theme.colors.textSecondary }]}>
        {getDateSeparatorText(date)}
      </Text>
    </View>
  ), [theme]);

  // Group messages by date and add separators
  const messagesWithSeparators = React.useMemo(() => {
    const grouped: Array<MessageWithSender | { type: 'date'; date: string; id: string }> = [];
    let currentDate = '';

    messages.forEach((message, index) => {
      const messageDate = new Date(message.created_at).toDateString();
      
      if (messageDate !== currentDate) {
        currentDate = messageDate;
        grouped.push({
          type: 'date',
          date: message.created_at,
          id: `date-${messageDate}`,
        });
      }
      
      grouped.push(message);
    });

    return grouped;
  }, [messages]);

  const renderItem = useCallback(({ item, index }: { item: any; index: number }) => {
    if (item.type === 'date') {
      return renderDateSeparator(item.date);
    }
    
    // Adjust index for actual message
    const messageIndex = messages.findIndex(m => m.id === item.id);
    return renderMessage({ item, index: messageIndex });
  }, [messages, renderMessage, renderDateSeparator]);

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Loading..." />
        <LoadingSpinner />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Error" />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load messages
          </Text>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Header */}
      <Header
        title={otherUser?.full_name || otherUser?.username || 'Chat'}
        subtitle={typingUsers.length > 0 ? 'typing...' : 'online'} // TODO: Get actual online status
        onBack={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={() => {/* TODO: Chat settings */}}>
            <Ionicons name="ellipsis-vertical" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        }
      />

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messagesWithSeparators}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        inverted={false}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 100,
        }}
      />

      {/* Typing Indicator */}
      <TikTokTypingIndicator typingUsers={typingUsers} />

      {/* Scroll to Bottom Button */}
      {showScrollToBottom && (
        <Animated.View style={[styles.scrollToBottomButton, { backgroundColor: theme.colors.primary }]}>
          <TouchableOpacity onPress={scrollToBottom}>
            <Ionicons name="chevron-down" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Chat Input */}
      <TikTokChatInput
        messageText={messageText}
        onTextChange={handleMessageTextChange}
        onSendMessage={handleSendMessage}
        onSelectAttachment={handleSelectAttachment}
        onToggleEmoji={handleToggleEmoji}
        onVoiceRecord={handleVoiceRecord}
        isSending={isSending}
        isUploading={isUploading}
        replyingTo={replyingTo}
        onCancelReply={handleCancelReply}
        placeholder="Message..."
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: normalize(8),
  },
  dateSeparator: {
    alignItems: 'center',
    marginVertical: normalize(16),
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(16),
    alignSelf: 'center',
  },
  dateSeparatorText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  errorText: {
    fontSize: normalize(16),
    textAlign: 'center',
  },
  scrollToBottomButton: {
    position: 'absolute',
    bottom: normalize(100),
    right: normalize(20),
    width: normalize(40),
    height: normalize(40),
    borderRadius: normalize(20),
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default TikTokChatScreen;
