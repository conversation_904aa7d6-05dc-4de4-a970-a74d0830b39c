import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useHeaderHeight } from '@react-navigation/elements';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppSelector } from '../../store/hooks';

import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Loading from '../../components/common/Loading';
import Text from '../../components/common/Text';
import EmojiPicker from '../../components/common/EmojiPicker';
import AttachmentModal from '../../components/messages/AttachmentModal';
import AudioRecordingModal from '../../components/messages/AudioRecordingModal';
import VideoRecordingModal from '../../components/messages/VideoRecordingModal';
import CameraModal from '../../components/messages/CameraModal';
import ImagePreviewModal from '../../components/messages/ImagePreviewModal';
import MessageItem from '../../components/messages/MessageItem';
import ChatInput from '../../components/messages/ChatInput';
import ChatHeader from '../../components/messages/ChatHeader';
import { MessageWithSender } from '../../types/messaging';
import { MainStackParamList } from '../../navigation/types';
import { useMediaHandlers } from '../../hooks/useMediaHandlers';
import logger from '../../utils/logger';

// Custom API hooks
import { useConversationMessages, useMessageSender } from '../../hooks/api';

type ChatDetailRouteProp = RouteProp<MainStackParamList, 'Chat'>;

// Placeholder for typing indicator logic
type TypingUser = { username: string };

const TypingIndicator: React.FC<{ typingUsers?: TypingUser[] }> = ({
  typingUsers = [],
}) => {
  const { theme } = useTheme();
  if (!typingUsers.length) return null;
  return (
    <View style={[styles.typingContainer, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.typingText, { color: theme.colors.textSecondary }]}>
        {typingUsers.map(u => u.username).join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
      </Text>
    </View>
  );
};

const ChatDetailScreen: React.FC = () => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const headerHeight = useHeaderHeight();
  const route = useRoute<ChatDetailRouteProp>();
  const { conversationId } = route.params || { conversationId: '' };

  // State management
  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const [showAudioRecordingModal, setShowAudioRecordingModal] = useState(false);
  const [showVideoRecordingModal, setShowVideoRecordingModal] = useState(false);
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  
  // Refs
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | number | null>(null);
  
  // Constants
  const keyboardVerticalOffset = Platform.OS === 'ios' ? headerHeight + insets.bottom : 0;

  // Get current user from Redux
  const user = useAppSelector((state: any) => state.auth?.user);

  // Custom API hooks
  const {
    messages,
    isLoadingMessages: isLoading,
    messagesError: isError,
    refetchMessages: refetch,
    markMessagesAsRead,
  } = useConversationMessages(conversationId);

  const { sendTextMessage, sendMediaMessage, isSendingMessage: isSending } = useMessageSender(conversationId);

  // Media handlers hook
  const {
    isUploading,
    handleSelectImage,
    handleSelectVideo,
    handleTakePhoto,
    handleVideoRecordingComplete,
    handleAudioRecordingComplete,
    handleAudioRecord,
  } = useMediaHandlers({
    conversationId,
    sendMessage: sendMediaMessage,
    onCloseAttachmentModal: () => setShowAttachmentModal(false),
  });

  // Message handling
  const handleTextChange = useCallback((text: string) => {
    setMessageText(text);
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      // Stop typing indicator logic would go here
    }, 1000);
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!messageText.trim() || isSending) return;
    const textToSend = messageText.trim();
    setMessageText('');

    const success = await sendTextMessage(textToSend);
    if (!success) {
      setMessageText(textToSend);
    }
  }, [messageText, sendTextMessage, isSending]);

  // Modal handlers
  const handleSelectAttachment = useCallback(() => {
    setShowAttachmentModal(true);
  }, []);

  const handleOpenCamera = useCallback(() => {
    setShowAttachmentModal(false);
    setShowCameraModal(true);
  }, []);

  const handleRecordVideo = useCallback(() => {
    setShowCameraModal(false);
    setShowVideoRecordingModal(true);
  }, []);

  const handleStartAudioRecording = useCallback(async () => {
    const canRecord = await handleAudioRecord();
    if (canRecord) {
      setShowAudioRecordingModal(true);
    }
  }, [handleAudioRecord]);

  const handleImagePress = useCallback((imageUrl: string) => {
    if (imageUrl) {
      setPreviewImageUrl(imageUrl);
      setShowImagePreview(true);
    }
  }, []);

  const handleToggleEmoji = useCallback(() => {
    setShowEmojiPicker(prev => !prev);
  }, []);

  const handleEmojiSelected = useCallback((emoji: string) => {
    setMessageText(prev => prev + emoji);
    setShowEmojiPicker(false);
  }, []);

  // Effects
  useEffect(() => {
    if (messages.length > 0) {
      flatListRef.current?.scrollToEnd({ animated: true });
    }
  }, [messages.length]);

  useEffect(() => {
    if (conversationId && messages.length > 0) {
      markMessagesAsRead();
    }
  }, [conversationId, messages.length, markMessagesAsRead]);

  // Render functions
  const renderMessage = ({ item, index }: { item: MessageWithSender; index: number }) => {
    const isMe = item.sender_id === user?.id;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const showAvatar = !isMe && (!previousMessage || previousMessage.sender_id !== item.sender_id);

    return (
      <MessageItem
        message={item}
        isMe={isMe}
        showAvatar={showAvatar}
        onImagePress={handleImagePress}
      />
    );
  };

  const getItemLayout = (_: any, index: number) => ({
    length: 80, // Approximate message height
    offset: 80 * index,
    index,
  });

  // Loading state
  if (isLoading && messages.length === 0) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  // Error state
  if (isError) {
    return (
      <SafeAreaWrapper>
        <View style={styles.errorContainer}>
          <Text style={{ color: theme.colors.error }}>
            Failed to load messages. Please try again.
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        {/* Chat Header */}
        <ChatHeader
          conversationName="Chat" // TODO: Get from conversation data
          isOnline={false} // TODO: Get from user status
        />

        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          getItemLayout={getItemLayout}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={20}
        />

        {/* Typing Indicator */}
        <TypingIndicator typingUsers={[]} />

        {/* Chat Input */}
        <ChatInput
          messageText={messageText}
          onTextChange={handleTextChange}
          onSendMessage={handleSendMessage}
          onSelectAttachment={handleSelectAttachment}
          onToggleEmoji={handleToggleEmoji}
          isSending={isSending}
          isUploading={isUploading}
        />

        {/* Emoji Picker */}
        {showEmojiPicker && (
          <EmojiPicker
            onEmojiSelected={handleEmojiSelected}
            onClose={() => setShowEmojiPicker(false)}
          />
        )}
      </KeyboardAvoidingView>

      {/* Modals */}
      <AttachmentModal
        visible={showAttachmentModal}
        onClose={() => setShowAttachmentModal(false)}
        onSelectImage={handleSelectImage}
        onSelectVideo={handleSelectVideo}
        onOpenCamera={handleOpenCamera}
        onRecordAudio={handleStartAudioRecording}
      />

      <AudioRecordingModal
        visible={showAudioRecordingModal}
        onClose={() => setShowAudioRecordingModal(false)}
        onRecordingComplete={handleAudioRecordingComplete}
        onRecordingError={(error) => {
          setShowAudioRecordingModal(false);
          Alert.alert('Recording Error', error);
        }}
      />

      <VideoRecordingModal
        visible={showVideoRecordingModal}
        onClose={() => setShowVideoRecordingModal(false)}
        onRecordingComplete={handleVideoRecordingComplete}
        onRecordingError={(error) => {
          setShowVideoRecordingModal(false);
          Alert.alert('Recording Error', error);
        }}
      />

      <CameraModal
        visible={showCameraModal}
        onClose={() => setShowCameraModal(false)}
        onTakePhoto={handleTakePhoto}
        onRecordVideo={handleRecordVideo}
      />

      <ImagePreviewModal
        visible={showImagePreview}
        imageUrl={previewImageUrl}
        onClose={() => {
          setShowImagePreview(false);
          setPreviewImageUrl('');
        }}
      />
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 12,
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  typingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});

export default ChatDetailScreen;
