import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, StatusBar, Animated, TextInput } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../../navigation/types';
import { useGetConversationsQuery } from '../../store/api/messagingApi';
import ChatList from '../../components/messages/ChatList';
import Loading from '../../components/common/Loading';
import Text from '../../components/common/Text';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

const MessagesScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NativeStackNavigationProp<MainStackParamList>>();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  const {
    data: conversations,
    isLoading,
    isError,
    refetch,
    isFetching,
    error
  } = useGetConversationsQuery();
  
  // Log conversation data for debugging
  logger.debug('MessagesScreen: conversations data:', conversations);
  logger.debug('MessagesScreen: loading state:', { isLoading, isError, isFetching });
  logger.debug('MessagesScreen: error:', error);

  // Log key debugging information
  logger.debug('MessagesScreen: Loading states:', { isLoading, isFetching, isError });
  if (conversations) {
    logger.debug(`MessagesScreen: Received ${conversations.length} conversations from API`);
  }

  const handleChatPress = useCallback((conversationId: string) => {
    navigation.navigate('Chat', { conversationId });
  }, [navigation]);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleNewMessage = useCallback(() => {
    // Log that we're attempting to navigate to the new conversation screen
    logger.debug('MessagesScreen: Navigating to NewConversation screen');
    navigation.navigate('NewConversation');
  }, [navigation]);
  
  // Debug function to check if we can create a test conversation
  const checkConversationCreation = useCallback(async () => {
    try {
      logger.debug('MessagesScreen: Checking conversation creation capability');
      const { supabase } = await import('../../integrations/supabase/client');
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        logger.debug('MessagesScreen: No authenticated user found');
        return;
      }
      
      logger.debug('MessagesScreen: Current user ID:', user.id);
      
      // Check if the conversations table exists and is accessible
      const { data: tableCheck, error: tableError } = await supabase
        .from('conversations')
        .select('count(*)')
        .limit(1);
        
      if (tableError) {
        logger.debug('MessagesScreen: Error accessing conversations table:', tableError);
      } else {
        logger.debug('MessagesScreen: Successfully accessed conversations table');
      }
      
      // Check user profiles
      const { data: profiles, error: profilesError } = await supabase
        .from('users')
        .select('id, username, full_name, auth_user_id')
        .limit(5);
        
      if (profilesError) {
        logger.debug('MessagesScreen: Error accessing user profiles:', profilesError);
      } else {
        logger.debug('MessagesScreen: Available user profiles:', profiles);
      }
      
    } catch (error) {
      logger.debug('MessagesScreen: Error in checkConversationCreation:', error);
    }
  }, []);

  // First, check for null conversations in the raw data
  if (conversations) {
    // Ensure we have a proper array before iterating
    const rawArray = Array.isArray(conversations) ? conversations : [];
    logger.debug('MessagesScreen: Raw conversations array check:', Array.isArray(rawArray));
    
    rawArray.forEach((conv, index) => {
      if (conv == null) {
        logger.debug(`MessagesScreen: Found null conversation at index ${index} in raw data`);
      } else if (!conv.id) {
        logger.debug(`MessagesScreen: Found conversation without ID at index ${index}:`, conv);
      }
    });
  }

  const validConversations = useMemo(() => {
    if (!conversations) return [];
    
    // Ensure we have a proper array
    const conversationsArray = Array.isArray(conversations) ? conversations : [];
    logger.debug('MessagesScreen: validConversations - array check:', Array.isArray(conversationsArray));
    
    return conversationsArray.filter(conv => conv && conv.id);
  }, [conversations]);

  const filteredConversations = useMemo(() => {
    return validConversations.filter(conv => {
        // Then apply search filter
        if (!searchQuery) return true;
        
        const query = searchQuery.toLowerCase();
        if (conv.type === 'group') {
          return (conv.name || '').toLowerCase().includes(query);
        }
        
        if (conv.other_participant) {
          const username = conv.other_participant.username || '';
          const fullName = conv.other_participant.full_name || '';
          return username.toLowerCase().includes(query) ||
                 fullName.toLowerCase().includes(query);
        }

        return false;
      });
  }, [validConversations, searchQuery]);
    
  logger.debug('MessagesScreen: Filtered conversations count:', filteredConversations.length);
  
  // Final check on filtered conversations
  filteredConversations.forEach((conv, index) => {
    if (!conv || !conv.id) {
      logger.debug(`MessagesScreen: Invalid conversation in filtered results at index ${index}:`, conv);
    }
  });

  // Log search filtering results
  if (searchQuery) {
    logger.debug(`MessagesScreen: Search query "${searchQuery}" returned ${filteredConversations.length} results`);
  }

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
      <StatusBar
        barStyle={theme.colors.background === '#0D0D0D' ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />

      {/* Top Bar */}
      <View style={styles.topBar}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Messages
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={handleNewMessage}
            style={[styles.actionButton, { backgroundColor: theme.colors.surface }]}
          >
            <Ionicons name="add" size={22} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons
          name="search"
          size={20}
          color={theme.colors.textSecondary}
          style={styles.searchIcon}
        />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search"
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={handleSearch}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={() => setSearchQuery('')}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={18} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <TouchableOpacity style={[styles.filterTab, styles.activeFilterTab, { backgroundColor: theme.colors.primary }]}>
          <Text style={[styles.filterTabText, { color: '#FFFFFF' }]}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={[styles.filterTabText, { color: theme.colors.textSecondary }]}>
            Unread
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={[styles.filterTabText, { color: theme.colors.textSecondary }]}>
            Groups
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (isLoading && !conversations) {
    return <SafeAreaWrapper><Loading /></SafeAreaWrapper>;
  }

  if (isError) {
    return (
      <SafeAreaWrapper>
        <View style={styles.centeredContainer}>
          <Text style={{ color: theme.colors.error }}>Failed to load conversations.</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  // Log the final state before rendering
  logger.debug('MessagesScreen: About to render with conversations count:', conversations?.length);
  
  // Show loading state if data is still loading
  if (isLoading) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          {renderHeader()}
          <View style={styles.centeredContainer}>
            <Loading />
          </View>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {renderHeader()}
        
        {/* Show filtered results when searching, or all conversations otherwise */}
        {(searchQuery && filteredConversations.length === 0 && (conversations?.length ?? 0) > 0) ? (
          <View style={styles.centeredContainer}>
            <View style={styles.emptyState}>
              <Ionicons name="search" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]} weight="600">
                No results found
              </Text>
              <Text style={[styles.emptyStateSubtitle, { color: theme.colors.textSecondary }]}>
                Try searching for a different name
              </Text>
            </View>
          </View>
        ) : validConversations.length > 0 ? (
          <ChatList
            conversations={Array.isArray(filteredConversations) ? filteredConversations : []}
            onChatPress={handleChatPress}
            onRefresh={refetch}
            isRefreshing={isFetching}
          />
        ) : (
          <View style={styles.centeredContainer}>
            <View style={styles.emptyState}>
              <Ionicons name="chatbubbles-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]} weight="600">
                No messages yet
              </Text>
              <Text style={[styles.emptyStateSubtitle, { color: theme.colors.textSecondary }]}>
                Start a conversation with your friends
              </Text>
              <TouchableOpacity 
                style={[styles.startChatButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleNewMessage}
              >
                <Text style={styles.startChatButtonText} weight="600">
                  Start chatting
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '800',
    letterSpacing: -0.5,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderRadius: 28,
    marginBottom: 20,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
  },
  clearButton: {
    padding: 4,
  },
  filterTabs: {
    flexDirection: 'row',
    gap: 12,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 24,
  },
  activeFilterTab: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '700',
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyState: {
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontSize: 20,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  startChatButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  startChatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
});

export default MessagesScreen;