import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, TextInput, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../contexts/ThemeContext';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import UserListItem from '../../components/common/UserListItem';
import Loading from '../../components/common/Loading';
import { useAppSelector } from '../../store/hooks';
import { selectCurrentUser, selectIsAuthenticated } from '../../store/slices/authSlice';
import {
  useGetFollowersQuery,
  useGetFollowingQuery,
} from '../../store/api/userManagementApi';
import { MainStackParamList } from '../../navigation/types';
import { startConversationWithUser } from '../../utils/messagingHelpers';
import logger from '../../utils/logger';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useDebounce } from '../../hooks/useDebounce';
import { supabase } from '../../integrations/supabase/client';

const NewConversationScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NativeStackNavigationProp<MainStackParamList>>();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>('followers');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const currentUser = useAppSelector(selectCurrentUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [supabaseUserId, setSupabaseUserId] = useState<string | null>(null);

  // Fallback to get user ID directly from Supabase if Redux state is not ready
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        console.log('[NewConversation] Supabase user:', user);
        setSupabaseUserId(user?.id || null);
      } catch (error) {
        console.error('[NewConversation] Error getting Supabase user:', error);
      }
    };
    getCurrentUser();
  }, []);

  const currentUserId = currentUser?.id || supabaseUserId;

  console.log('[NewConversation] Auth state:', {
    isAuthenticated,
    currentUser,
    currentUserId,
    supabaseUserId
  });

  const {
    data: followers = [],
    isLoading: loadingFollowers,
    refetch: refetchFollowers,
  } = useGetFollowersQuery(currentUserId || '', { skip: !currentUserId });

  const {
    data: following = [],
    isLoading: loadingFollowing,
    refetch: refetchFollowing,
  } = useGetFollowingQuery(currentUserId || '', { skip: !currentUserId });

  const isLoading = loadingFollowers || loadingFollowing;

  // Select users for active tab and filter out current user
  const activeUsers = useMemo(() => {
    const list = activeTab === 'followers' ? followers : following;
    console.log(`[NewConversation] ${activeTab} list:`, list);
    console.log(`[NewConversation] Current user ID:`, currentUserId);
    if (!currentUserId) return list;
    const filtered = list.filter(u => u.id !== currentUserId);
    console.log(`[NewConversation] Filtered ${activeTab}:`, filtered);
    return filtered;
  }, [activeTab, followers, following, currentUserId]);

  // Filter users based on search query
  const filteredUsers = useMemo(() => {
    if (!debouncedSearchQuery) return activeUsers;

    return activeUsers.filter(user => {
      const fullName = user.full_name?.toLowerCase() || '';
      const username = user.username?.toLowerCase() || '';
      const query = debouncedSearchQuery.toLowerCase();

      return fullName.includes(query) || username.includes(query);
    });
  }, [activeUsers, debouncedSearchQuery]);

  const handleRefresh = useCallback(() => {
    refetchFollowers();
    refetchFollowing();
  }, [refetchFollowers, refetchFollowing]);

  const handlePress = useCallback(async (userId: string) => {
    try {
      logger.debug('Starting conversation with user ID:', userId);

      // The userId here is the auth_user_id, which is what our updated startConversationWithUser expects
      await startConversationWithUser(userId, navigation as any);
    } catch (error) {
      logger.error('Failed to start conversation:', error);
      // Show error toast or alert
      alert('Failed to start conversation. Please try again.');
    }
  }, [navigation]);

  const renderItem = useCallback(({ item }: { item: any }) => (
    <UserListItem
      user={item}
      showFollowButton={false}
      onPress={() => handlePress(item.id)}
    />
  ), [handlePress]);

  const renderEmptyComponent = useCallback(() => (
    <View style={styles.emptyContainer}>
      {searchQuery ? (
        <>
          <Ionicons name="search" size={48} color={theme.colors.textSecondary} />
          <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>No users found matching "{searchQuery}"</Text>
        </>
      ) : (
        <>
          <Ionicons name="people" size={48} color={theme.colors.textSecondary} />
          <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>No users available to message</Text>
        </>
      )}
    </View>
  ), [searchQuery, theme.colors.textSecondary]);

  if (isLoading) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  if (activeUsers.length === 0) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.emptyContainer, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>No users available</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]} weight="600">
            New Message
          </Text>
          <View style={styles.placeholder} />
        </View>
        
        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search users..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Tabs for Followers / Following */}
        <View style={styles.filterTabs}>
          <TouchableOpacity
            style={[
              styles.filterTab,
              activeTab === 'followers' && [styles.activeFilterTab, { backgroundColor: theme.colors.primary }],
            ]}
            onPress={() => setActiveTab('followers')}
          >
            <Text
              style={[
                styles.filterTabText,
                { color: activeTab === 'followers' ? '#FFFFFF' : theme.colors.textSecondary },
              ]}
              weight="600"
            >
              Followers
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              activeTab === 'following' && [styles.activeFilterTab, { backgroundColor: theme.colors.primary }],
            ]}
            onPress={() => setActiveTab('following')}
          >
            <Text
              style={[
                styles.filterTabText,
                { color: activeTab === 'following' ? '#FFFFFF' : theme.colors.textSecondary },
              ]}
              weight="600"
            >
              Following
            </Text>
          </TouchableOpacity>
        </View>

        {/* User List */}
        <FlatList
          data={filteredUsers}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          initialNumToRender={20}
          maxToRenderPerBatch={20}
          windowSize={10}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
  },
  placeholder: {
    width: 32,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 14,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 30,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
  },
  filterTabs: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginHorizontal: 16,
    marginBottom: 12,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
  },
  activeFilterTab: {},
  filterTabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  messageButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NewConversationScreen;
