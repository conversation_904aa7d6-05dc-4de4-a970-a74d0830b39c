import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../contexts/ThemeContext';

// Mock gift data
const gifts = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  name: i === 49 ? "Gift VIP" : `Gift ${i + 1}`,
  // Using placeholder images - in a real app, you'd use actual image assets
  image: `https://via.placeholder.com/150?text=Gift${i+1}`,
}));

const { width } = Dimensions.get('window');
const numColumns = Math.floor(width / 100); // Adjust based on screen width

const GiftGridTestScreen = () => {
  const { theme } = useTheme();
  const [selectedGift, setSelectedGift] = useState<any>(null);
  
  // Animation values
  const scaleAnim = useState(new Animated.Value(0))[0];
  const opacityAnim = useState(new Animated.Value(0))[0];
  const textOpacityAnim = useState(new Animated.Value(0))[0];

  const handleSendGift = (gift: any) => {
    setSelectedGift(gift);
    
    // Reset animations
    scaleAnim.setValue(0);
    opacityAnim.setValue(0);
    textOpacityAnim.setValue(0);
    
    // Play animations
    Animated.parallel([
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.sequence([
        Animated.delay(200),
        Animated.timing(textOpacityAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
    
    // Clear the selected gift after animation
    setTimeout(() => {
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }).start(() => setSelectedGift(null));
    }, 3000);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        Catalogue des Cadeaux TS1
      </Text>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.grid}>
          {gifts.map((gift) => (
            <TouchableOpacity
              key={gift.id}
              style={[styles.giftItem, { borderColor: theme.colors.border }]}
              onPress={() => handleSendGift(gift)}
              activeOpacity={0.7}
            >
              <Image
                source={{ uri: gift.image }}
                style={styles.giftImage}
                resizeMode="contain"
              />
              <Text style={[styles.giftName, { color: theme.colors.text }]}>
                {gift.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {selectedGift && (
        <Animated.View
          style={[
            styles.animationOverlay,
            {
              opacity: opacityAnim,
              backgroundColor: 'rgba(0,0,0,0.7)',
            },
          ]}
        >
          {/* VIP effect for special gift */}
          {selectedGift.name === "Gift VIP" && (
            <View style={styles.vipEffectContainer}>
              {/* This would be a real animation/effect in production */}
              <Text style={styles.vipText}>✨ VIP ✨</Text>
            </View>
          )}

          <Animated.View
            style={[
              styles.animatedGiftContainer,
              {
                transform: [
                  {
                    scale: scaleAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 1.5],
                    }),
                  },
                ],
              },
            ]}
          >
            <Image
              source={{ uri: selectedGift.image }}
              style={styles.animatedGiftImage}
              resizeMode="contain"
            />
            
            <Animated.View
              style={[
                styles.giftSentContainer,
                {
                  opacity: textOpacityAnim,
                },
              ]}
            >
              <Text style={styles.giftSentText}>
                {selectedGift.name} envoyé !
              </Text>
            </Animated.View>
          </Animated.View>
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    padding: 16,
    textAlign: 'center',
  },
  scrollContent: {
    padding: 12,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  giftItem: {
    width: (width - 48) / numColumns,
    aspectRatio: 0.8,
    marginBottom: 16,
    borderRadius: 16,
    borderWidth: 1,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  giftImage: {
    width: '80%',
    height: '70%',
    marginBottom: 8,
  },
  giftName: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  animationOverlay: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  animatedGiftContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  animatedGiftImage: {
    width: 150,
    height: 150,
  },
  giftSentContainer: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 20,
  },
  giftSentText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  vipEffectContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  vipText: {
    fontSize: 40,
    color: 'gold',
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 10,
  },
});

export default GiftGridTestScreen;