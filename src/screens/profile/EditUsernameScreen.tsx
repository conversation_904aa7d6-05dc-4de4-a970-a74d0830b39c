import React, { useState } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { useGetCurrentUserCompleteQuery, useUpdateUsernameMutation } from '../../store/api/userManagementApi';
import { spacing } from '../../styles/spacing';

/**
 * Edit Username Screen
 * Allows users to change their username with validation
 */
const EditUsernameScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const { data: user, refetch } = useGetCurrentUserCompleteQuery();
  const [updateUsername, { isLoading }] = useUpdateUsernameMutation();
  const [username, setUsername] = useState(user?.username || '');
  const [error, setError] = useState('');

  const validateUsername = (value: string): string => {
    if (!value.trim()) {
      return 'Username is required';
    }
    if (value.length < 3) {
      return 'Username must be at least 3 characters';
    }
    if (value.length > 20) {
      return 'Username must be less than 20 characters';
    }
    if (!/^[a-zA-Z0-9._]+$/.test(value)) {
      return 'Username can only contain letters, numbers, dots, and underscores';
    }
    if (value.startsWith('.') || value.endsWith('.')) {
      return 'Username cannot start or end with a dot';
    }
    return '';
  };

  const handleSave = async () => {
    const validationError = validateUsername(username);
    if (validationError) {
      setError(validationError);
      return;
    }

    if (username === user?.username) {
      navigation.goBack();
      return;
    }

    setError('');

    try {
      await updateUsername(username).unwrap();

      Alert.alert(
        'Username Updated',
        'Your username has been successfully updated.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );

      refetch();
    } catch (error: any) {
      setError(error.message || 'Failed to update username');
    }
  };

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Change Username
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Your username is how other people can find and mention you on TikTok.
          </Text>

          <View style={styles.inputContainer}>
            <Input
              label="Username"
              value={username}
              onChangeText={(text) => {
                setUsername(text.toLowerCase().replace(/[^a-zA-Z0-9._]/g, ''));
                setError('');
              }}
              placeholder="Enter your username"
              autoCapitalize="none"
              autoCorrect={false}
              maxLength={20}
              error={error}
              leftIcon={<Text style={{ color: theme.colors.textSecondary }}>@</Text>}
            />
          </View>

          <View style={styles.infoContainer}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
              Username Guidelines:
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              • 3-20 characters long{'\n'}
              • Letters, numbers, dots, and underscores only{'\n'}
              • Cannot start or end with a dot{'\n'}
              • Must be unique
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Button
            title="Save Changes"
            onPress={handleSave}
            loading={isLoading}
            disabled={!username.trim() || username === user?.username}
            style={styles.saveButton}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  infoContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(254, 44, 85, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(254, 44, 85, 0.2)',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: spacing.base,
    paddingBottom: spacing.xl,
  },
  saveButton: {
    marginTop: spacing.sm,
  },
});

export default EditUsernameScreen;
