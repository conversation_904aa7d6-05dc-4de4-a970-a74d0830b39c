import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { useAuth } from '../../hooks/useAuth';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import { useGetCurrentUserCompleteQuery } from '../../store/api/userManagementApi';
import logger from '../../utils/logger';
import { spacing } from '../../styles/spacing';

type SettingsScreenNavigationProp = StackNavigationProp<MainStackParamList>;

/**
 * Main Settings Screen
 * Central hub for all app settings with organized sections
 */
const SettingsScreen = () => {
  const { theme, toggleTheme, themeName } = useTheme();
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { logout } = useAuth();

  const { data: user } = useGetCurrentUserCompleteQuery();

  const handleThemeToggle = () => {
    toggleTheme();
  };

  const handleLogout = () => {
    Alert.alert(
      'Log Out',
      'Are you sure you want to log out of your account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Log Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              logger.error('Logout error:', error);
            }
          },
        },
      ]
    );
  };


  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Account Management */}
          <SettingsSection
            title="Account"
            description="Manage your account and profile settings"
          >
            <SettingsItem
              title="Account Settings"
              description="Username, email, phone, and personal info"
              type="navigation"
              onPress={() => navigation.navigate('AccountSettings')}
              icon="person-outline"
            />

            <SettingsItem
              title="Privacy Settings"
              description="Control who can see your content"
              type="navigation"
              onPress={() => navigation.navigate('PrivacySettings')}
              icon="shield-outline"
            />

            <SettingsItem
              title="Security Settings"
              description="2FA, login activities, and security"
              type="navigation"
              onPress={() => navigation.navigate('SecuritySettings')}
              icon="lock-closed-outline"
            />

            <SettingsItem
              title="Personal Data"
              description="Manage your data preferences"
              type="navigation"
              onPress={() => navigation.navigate('PersonalDataSettings')}
              icon="download-outline"
            />

            <SettingsItem
              title="Notifications"
              description="Manage push and email notifications"
              type="navigation"
              onPress={() => navigation.navigate('NotificationSettings')}
              icon="notifications-outline"
              showChevron={false}
            />
          </SettingsSection>

          {/* Content & Activity */}
          <SettingsSection
            title="Content & Activity"
            description="Manage your content preferences"
          >
            <SettingsItem
              title="Liked Videos"
              description="Manage videos you've liked"
              type="navigation"
              onPress={() => {
                // Navigate to liked videos
                logger.debug('Navigate to liked videos');
              }}
              icon="heart-outline"
            />

            <SettingsItem
              title="Watch History"
              description="See videos you've watched"
              type="navigation"
              onPress={() => {
                // Navigate to watch history
                logger.debug('Navigate to watch history');
              }}
              icon="time-outline"
            />

            <SettingsItem
              title="Downloads"
              description="Manage downloaded videos"
              type="navigation"
              onPress={() => {
                // Navigate to downloads
                logger.debug('Navigate to downloads');
              }}
              icon="download-outline"
            />

            <SettingsItem
              title="Blocked Accounts"
              description="Manage blocked users"
              type="navigation"
              onPress={() => navigation.navigate('BlockedAccounts')}
              icon="ban-outline"
              showChevron={false}
            />
          </SettingsSection>

          {/* App Preferences */}
          <SettingsSection
            title="App Preferences"
            description="Customize your app experience"
          >
            <SettingsItem
              title="Dark Mode"
              description={themeName === 'dark' ? 'Dark theme enabled' : 'Light theme enabled'}
              type="toggle"
              value={themeName === 'dark'}
              onToggle={handleThemeToggle}
              icon={themeName === 'dark' ? 'moon' : 'sunny'}
            />

            <SettingsItem
              title="Language"
              description="Choose your preferred language"
              type="selection"
              value="English"
              onPress={() => {
                // Navigate to language selection
                logger.debug('Navigate to language selection');
              }}
              icon="language-outline"
            />

            <SettingsItem
              title="Data Saver"
              description="Reduce data usage"
              type="navigation"
              onPress={() => {
                // Navigate to data saver settings
                logger.debug('Navigate to data saver');
              }}
              icon="cellular-outline"
              showChevron={false}
            />
          </SettingsSection>

          {/* Support & About */}
          <SettingsSection
            title="Support & About"
            description="Get help and learn more about TS1"
          >
            <SettingsItem
              title="Help Center"
              description="Get help with TS1"
              type="navigation"
              onPress={() => {
                // Open help center
                logger.debug('Open help center');
              }}
              icon="help-circle-outline"
            />

            <SettingsItem
              title="Report a Problem"
              description="Let us know about issues"
              type="navigation"
              onPress={() => {
                // Navigate to report problem
                logger.debug('Report a problem');
              }}
              icon="flag-outline"
            />

            <SettingsItem
              title="About TS1"
              description="Version, terms, and policies"
              type="navigation"
              onPress={() => {
                // Navigate to about screen
                logger.debug('Navigate to about');
              }}
              icon="information-circle-outline"
            />

            <SettingsItem
              title="Terms of Service"
              description="Review our terms of service"
              type="navigation"
              onPress={() => {
                // Navigate to terms
                logger.debug('Navigate to terms');
              }}
              icon="document-text-outline"
            />

            <SettingsItem
              title="Privacy Policy"
              description="Review our privacy policy"
              type="navigation"
              onPress={() => {
                // Navigate to privacy policy
                logger.debug('Navigate to privacy policy');
              }}
              icon="shield-checkmark-outline"
              showChevron={false}
            />
          </SettingsSection>

          {/* Developer Tools */}
          <SettingsSection
            title="Developer Tools"
            description="Testing and development features"
          >
            <SettingsItem
              title="Gift Grid Test"
              description="Test the gift grid animation feature"
              type="navigation"
              onPress={() => navigation.navigate('GiftGridTest')}
              icon="gift-outline"
            />
          </SettingsSection>

          {/* Account Actions */}
          <SettingsSection
            title="Account Actions"
            description="Manage your account"
          >
            <SettingsItem
              title="Log Out"
              description="Sign out of your account"
              type="action"
              onPress={handleLogout}
              icon="log-out-outline"
              destructive={true}
              showChevron={false}
            />
          </SettingsSection>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
              TS1 v1.0.0{'\n'}
              {user && `Signed in as @${user.username}`}
            </Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  footer: {
    padding: spacing.base,
    marginTop: spacing.lg,
    marginBottom: spacing['2xl'],
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default SettingsScreen;
