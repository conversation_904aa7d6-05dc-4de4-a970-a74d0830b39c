import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Button from '../../components/common/Button';
import PhoneNumberInput from '../../components/phone/PhoneNumberInput';
import VerificationCodeInput from '../../components/phone/VerificationCodeInput';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useGetCurrentUserCompleteQuery } from '../../store/api/userManagementApi';
import { useCountryDetection } from '../../hooks/useCountryDetection';
import { usePhoneVerification } from '../../hooks/usePhoneVerification';
import { parsePhoneNumber, getFullPhoneNumber } from '../../utils/phoneUtils';
import { spacing } from '../../styles/spacing';
import { normalize } from '../../utils/responsive';

/**
 * Edit Phone Number Screen
 * Allows users to add or change their phone number with SMS verification
 */
const EditPhoneScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const { data: user, refetch } = useGetCurrentUserCompleteQuery();
  
  // Country detection hook
  const {
    selectedCountry,
    setSelectedCountry,
    isDetectingLocation,
  } = useCountryDetection();

  // Phone verification hook
  const {
    phoneNumber,
    setPhoneNumber,
    verificationCode,
    setVerificationCode,
    error,
    setError,
    step,
    isLoading,
    handleSendCode,
    handleVerifyCode,
    handleResendCode,
    goBackToPhoneStep,
  } = usePhoneVerification({
    selectedCountry,
    currentPhoneNumber: user?.phone_number,
    onSuccess: () => {
      refetch();
      navigation.goBack();
    },
    onError: (errorMessage) => {
      console.error('Phone verification error:', errorMessage);
    },
  });

  // Initialize phone number from user data
  useEffect(() => {
    if (user?.phone_number) {
      const parsed = parsePhoneNumber(user.phone_number);
      if (parsed.country) {
        setSelectedCountry(parsed.country);
        setPhoneNumber(parsed.phoneNumber);
      } else {
        setPhoneNumber(user.phone_number);
      }
    }
  }, [user, setSelectedCountry, setPhoneNumber]);

  const handleBack = () => {
    if (step === 'verify') {
      goBackToPhoneStep();
    } else {
      navigation.goBack();
    }
  };

  const getHeaderTitle = () => {
    switch (step) {
      case 'verify':
        return 'Verify Phone';
      default:
        return user?.phone_number ? 'Change Phone Number' : 'Add Phone Number';
    }
  };

  const getFullPhoneNumberDisplay = () => {
    return getFullPhoneNumber(phoneNumber, selectedCountry);
  };

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            {getHeaderTitle()}
          </Text>
          <View style={styles.headerRight} />
        </View>

        {/* Content */}
        <View style={styles.content}>
          {step === 'phone' ? (
            <>
              {/* Phone Number Input Step */}
              <View style={styles.stepContainer}>
                <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
                  {user?.phone_number ? 'Update your phone number' : 'Add your phone number'}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
                  We'll send you a verification code to confirm your number.
                </Text>

                <PhoneNumberInput
                  selectedCountry={selectedCountry}
                  phoneNumber={phoneNumber}
                  error={error}
                  isDetectingLocation={isDetectingLocation}
                  onCountryChange={setSelectedCountry}
                  onPhoneNumberChange={setPhoneNumber}
                  onErrorChange={setError}
                />

                {/* Current Phone Number Display */}
                {user?.phone_number && (
                  <View style={styles.currentPhoneContainer}>
                    <Text style={[styles.currentPhoneLabel, { color: theme.colors.textSecondary }]}>
                      Current phone number:
                    </Text>
                    <Text style={[styles.currentPhoneNumber, { color: theme.colors.text }]}>
                      {user.phone_number}
                    </Text>
                  </View>
                )}
              </View>

              {/* Continue Button */}
              <View style={styles.buttonContainer}>
                <Button
                  title="Send Verification Code"
                  onPress={handleSendCode}
                  loading={isLoading}
                  disabled={!phoneNumber.trim() || isLoading}
                />
              </View>
            </>
          ) : (
            <>
              {/* Verification Code Input Step */}
              <VerificationCodeInput
                verificationCode={verificationCode}
                phoneNumber={getFullPhoneNumberDisplay()}
                error={error}
                isLoading={isLoading}
                onCodeChange={setVerificationCode}
                onVerify={handleVerifyCode}
                onResend={handleResendCode}
                onGoBack={goBackToPhoneStep}
              />
            </>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  headerRight: {
    width: normalize(32),
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  stepContainer: {
    padding: spacing.lg,
  },
  stepTitle: {
    fontSize: normalize(24),
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  stepDescription: {
    fontSize: normalize(16),
    lineHeight: normalize(24),
    marginBottom: spacing.xl,
  },
  currentPhoneContainer: {
    marginTop: spacing.md,
    padding: spacing.md,
    borderRadius: normalize(8),
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  currentPhoneLabel: {
    fontSize: normalize(14),
    marginBottom: spacing.xs,
  },
  currentPhoneNumber: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  buttonContainer: {
    padding: spacing.lg,
    paddingTop: 0,
  },
});

export default EditPhoneScreen;
