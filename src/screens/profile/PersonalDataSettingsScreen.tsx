import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import Loading from '../../components/common/Loading';
import {
  useGetPersonalDataSettingsQuery,
  useUpdatePersonalDataSettingsMutation,
  useRequestDataExportMutation,
} from '../../store/api/userManagementApi';
import { PersonalDataSettings } from '../../types/settings';
import { spacing } from '../../styles/spacing';

/**
 * Personal Data Settings Screen
 * Manage data preferences and export requests
 */
const PersonalDataSettingsScreen = () => {
  const { theme } = useTheme();

  const { data, isLoading, error } = useGetPersonalDataSettingsQuery();
  const [updateSettings] = useUpdatePersonalDataSettingsMutation();
  const [requestExport, { isLoading: requesting }] = useRequestDataExportMutation();

  const [localSettings, setLocalSettings] = useState<PersonalDataSettings | null>(null);

  useEffect(() => { if (data) setLocalSettings(data); }, [data]);

  const handleToggle = async (key: keyof PersonalDataSettings, value: boolean) => {
    if (!localSettings) return;
    const updated = { ...localSettings, [key]: value } as PersonalDataSettings;
    setLocalSettings(updated);
    try {
      await updateSettings({ [key]: value }).unwrap();
    } catch (e) {
      setLocalSettings(localSettings);
      Alert.alert('Error', 'Failed to update setting');
    }
  };

  const handleRequestExport = async () => {
    try {
      await requestExport().unwrap();
      Alert.alert('Requested', 'Your data export request has been submitted.');
    } catch (e) {
      Alert.alert('Error', 'Failed to request data export');
    }
  };

  if (isLoading || !localSettings) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  if (error) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>Failed to load settings</Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <SettingsSection title="Data Preferences" description="Control how we use your data">
            <SettingsItem
              title="Marketing Emails"
              description="Receive product updates"
              type="toggle"
              value={localSettings.marketing_emails}
              onToggle={(v) => handleToggle('marketing_emails', v)}
            />
            <SettingsItem
              title="Analytics Tracking"
              description="Allow anonymous analytics"
              type="toggle"
              value={localSettings.analytics_tracking}
              onToggle={(v) => handleToggle('analytics_tracking', v)}
            />
            <SettingsItem
              title="Third-party Sharing"
              description="Allow sharing data with partners"
              type="toggle"
              value={localSettings.third_party_sharing}
              onToggle={(v) => handleToggle('third_party_sharing', v)}
            />
          </SettingsSection>

          <SettingsSection title="Data Requests">
            <SettingsItem
              title="Request Data Export"
              type="action"
              onPress={handleRequestExport}
              icon="download-outline"
              showChevron={false}
              disabled={requesting}
            />
          </SettingsSection>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>Manage your personal data preferences and request a copy of your data.</Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  errorText: { fontSize: 16, textAlign: 'center', marginTop: spacing['4xl'] },
  footer: { padding: spacing.base, marginTop: spacing.lg },
  footerText: { fontSize: 14, lineHeight: 20, textAlign: 'center' },
});

export default PersonalDataSettingsScreen;
