import React from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import UserListItem from '../../components/common/UserListItem';
import Text from '../../components/common/Text';
import { useGetFollowersQuery } from '../../store/api/userManagementApi';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

type FollowersScreenNavigationProp = StackNavigationProp<MainStackParamList>;

const FollowersScreen = ({ route }: { route: any }) => {
  const { theme } = useTheme();
  const navigation = useNavigation<FollowersScreenNavigationProp>();
  const { userId } = route.params;

  const {
    data: followers = [],
    isLoading,
    refetch,
    error,
  } = useGetFollowersQuery(userId);

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
        No followers yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        When people follow this account, they'll appear here
      </Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorState}>
      <Ionicons name="alert-circle-outline" size={64} color={theme.colors.error} />
      <Text style={[styles.errorTitle, { color: theme.colors.error }]}>
        Unable to load followers
      </Text>
      <Text style={[styles.errorSubtitle, { color: theme.colors.textSecondary }]}>
        Please try again later
      </Text>
    </View>
  );

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {renderErrorState()}
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={followers}
        renderItem={({ item }) => (
          <UserListItem
            user={item}
            showFollowButton={true}
            onPress={() => {
              logger.debug('Navigate to user profile:', item.id);
              navigation.navigate('UserProfile', { userId: item.id });
            }}
          />
        )}
        keyExtractor={item => item.id}
        ListEmptyComponent={!isLoading ? renderEmptyState : null}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={followers.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default FollowersScreen;
