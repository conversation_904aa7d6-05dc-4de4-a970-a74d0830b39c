import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import Loading from '../../components/common/Loading';
import {
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
} from '../../store/api/userManagementApi';
import { NotificationSettings } from '../../types/settings';
import { spacing } from '../../styles/spacing';

/**
 * Notification Settings Screen
 * Allows users to manage all notification preferences
 */
const NotificationSettingsScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const {
    data: settings,
    isLoading,
    error,
    refetch,
  } = useGetNotificationSettingsQuery();

  const [updateSettings, { isLoading: isUpdating }] = useUpdateNotificationSettingsMutation();

  const [localSettings, setLocalSettings] = useState<NotificationSettings | null>(null);

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleToggle = async (key: keyof NotificationSettings, value: boolean) => {
    if (!localSettings) {return;}

    // Update local state immediately for responsive UI
    const updatedSettings = { ...localSettings, [key]: value };
    setLocalSettings(updatedSettings);

    try {
      await updateSettings({ [key]: value }).unwrap();
    } catch (error) {
      // Revert local state on error
      setLocalSettings(localSettings);
      Alert.alert(
        'Error',
        'Failed to update notification settings. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  if (isLoading || !localSettings) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  if (error) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load notification settings
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <SettingsSection
            title="Push Notifications"
            description="Manage notifications sent to your device"
          >
            <SettingsItem
              title="Push Notifications"
              description="Allow TS1 to send push notifications"
              type="toggle"
              value={localSettings.push_notifications}
              onToggle={(value) => handleToggle('push_notifications', value)}
            />

            <SettingsItem
              title="Likes"
              description="When someone likes your videos"
              type="toggle"
              value={localSettings.like_notifications}
              onToggle={(value) => handleToggle('like_notifications', value)}
              disabled={!localSettings.push_notifications}
            />

            <SettingsItem
              title="Comments"
              description="When someone comments on your videos"
              type="toggle"
              value={localSettings.comment_notifications}
              onToggle={(value) => handleToggle('comment_notifications', value)}
              disabled={!localSettings.push_notifications}
            />

            <SettingsItem
              title="New Followers"
              description="When someone follows you"
              type="toggle"
              value={localSettings.follow_notifications}
              onToggle={(value) => handleToggle('follow_notifications', value)}
              disabled={!localSettings.push_notifications}
            />

            <SettingsItem
              title="Messages"
              description="When you receive direct messages"
              type="toggle"
              value={localSettings.message_notifications}
              onToggle={(value) => handleToggle('message_notifications', value)}
              disabled={!localSettings.push_notifications}
            />

            <SettingsItem
              title="Live Videos"
              description="When people you follow go live"
              type="toggle"
              value={localSettings.live_notifications}
              onToggle={(value) => handleToggle('live_notifications', value)}
              disabled={!localSettings.push_notifications}
            />

            <SettingsItem
              title="Video Updates"
              description="When people you follow post new videos"
              type="toggle"
              value={true} // TODO: Add to settings
              onToggle={() => {}}
              disabled={!localSettings.push_notifications}
              showChevron={false}
            />
          </SettingsSection>

          <SettingsSection
            title="Email Notifications"
            description="Manage email notifications from TS1"
          >
            <SettingsItem
              title="Email Notifications"
              description="Receive notifications via email"
              type="toggle"
              value={localSettings.email_notifications}
              onToggle={(value) => handleToggle('email_notifications', value)}
              showChevron={false}
            />
          </SettingsSection>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
              You can change these settings anytime. Some notifications may still be sent for security and account purposes.
            </Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: spacing['4xl'],
  },
  footer: {
    padding: spacing.base,
    marginTop: spacing.lg,
  },
  footerText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default NotificationSettingsScreen;
