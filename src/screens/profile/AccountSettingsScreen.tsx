import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import Loading from '../../components/common/Loading';
import { useGetCurrentUserCompleteQuery } from '../../store/api/userManagementApi';
import logger from '../../utils/logger';
import { spacing } from '../../styles/spacing';

/**
 * Account Settings Screen
 * Manage basic account details
 */
type AccountSettingsNavigationProp = StackNavigationProp<MainStackParamList>;

const AccountSettingsScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<AccountSettingsNavigationProp>();

  const {
    data: user,
    isLoading,
    error,
  } = useGetCurrentUserCompleteQuery();


  if (isLoading || !user) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  if (error) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load account settings
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}> 
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Profile Picture */}
          <SettingsSection
            title="Profile Picture"
            description="Manage your profile photo"
          >
            <SettingsItem
              title="Change Profile Picture"
              description="Update your photo"
              type="navigation"
              onPress={() => navigation.navigate('EditProfile')}
              icon="camera-outline"
            />
          </SettingsSection>

          {/* Personal Information */}
          <SettingsSection
            title="Personal Information"
            description="Edit your personal information"
          >
            <SettingsItem
              title="Full Name"
              description={user.full_name || 'Not set'}
              type="navigation"
              onPress={() => navigation.navigate('EditProfile')}
              icon="person-outline"
            />

            <SettingsItem
              title="Username"
              description={`@${user.username}`}
              type="navigation"
              onPress={() => navigation.navigate('EditUsername')}
              icon="at-outline"
            />

            <SettingsItem
              title="Email"
              description={user.email}
              type="navigation"
              onPress={() => navigation.navigate('EditEmail')}
              icon="mail-outline"
            />

            <SettingsItem
              title="Phone"
              description={user.phone || 'Not set'}
              type="navigation"
              onPress={() => navigation.navigate('EditPhone')}
              icon="call-outline"
            />

            <SettingsItem
              title="Date of Birth"
              description={user.date_of_birth || 'Not set'}
              type="navigation"
              onPress={() => navigation.navigate('EditDateOfBirth')}
              icon="calendar-outline"
            />
          </SettingsSection>

          {/* Blocked Users */}
          <SettingsSection
            title="Blocked Users"
            description="Manage blocked accounts"
          >
            <SettingsItem
              title="Blocked Users"
              description="Manage blocked accounts"
              type="navigation"
              onPress={() => navigation.navigate('BlockedAccounts')}
              icon="ban-outline"
            />
          </SettingsSection>

          {/* Account Status */}
          <SettingsSection
            title="Account Status"
            description="Information about your account"
          >
            <SettingsItem
              title="Verified Account"
              description={user.is_verified ? 'Verified' : 'Pending'}
              showChevron={false}
              icon={user.is_verified ? "checkmark-circle" : "time-outline"}
            />

            <SettingsItem
              title="Account Type"
              description={user.user_tag || 'Standard'}
              showChevron={false}
              icon="pricetag-outline"
            />

            <SettingsItem
              title="Followers"
              description={user.followers_count?.toLocaleString() || '0'}
              showChevron={false}
              icon="people-outline"
            />
          </SettingsSection>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: spacing['4xl'],
  },
  footer: {
    padding: spacing.base,
    marginTop: spacing.lg,
    marginBottom: spacing['2xl'],
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default AccountSettingsScreen;
