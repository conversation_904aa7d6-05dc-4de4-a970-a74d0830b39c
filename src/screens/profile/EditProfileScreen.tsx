import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Clipboard,
  ToastAndroid,
  Platform,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectCurrentUser, updateUserProfile } from '../../store/slices/authSlice';
import { useUpdateUserProfileMutation, useGetCurrentUserCompleteQuery } from '../../store/api/userManagementApi';
import Text from '../../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';
import logger from '../../utils/logger';

// Components
import ProfilePhotoSection from '../../components/profile/ProfilePhotoSection';
import ProfileInfoItem from '../../components/profile/ProfileInfoItem';
import EditModal from '../../components/profile/EditModal';

// Hooks
import { useProfilePhoto } from '../../hooks/useProfilePhoto';

interface EditProfileForm {
  full_name: string;
  username: string;
  bio: string;
}

const EditProfileScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const currentUserData = useAppSelector(selectCurrentUser);

  const [updateProfile, { isLoading: isUpdating }] = useUpdateUserProfileMutation();
  const { data: userData } = useGetCurrentUserCompleteQuery();

  // Form state
  const [form, setForm] = useState<EditProfileForm>({
    full_name: '',
    username: '',
    bio: '',
  });

  // Modal states
  const [nameModalVisible, setNameModalVisible] = useState(false);
  const [bioModalVisible, setBioModalVisible] = useState(false);

  // Profile photo hook
  const { isUploading: isUploadingAvatar, showImagePicker } = useProfilePhoto({
    userId: currentUserData?.id || '',
    onSuccess: (avatarUrl) => {
      dispatch(updateUserProfile({ avatar_url: avatarUrl }));
      Alert.alert('Success', 'Profile photo updated successfully');
    },
    onError: (error) => {
      Alert.alert('Error', error);
    },
  });

  // Initialize form data
  useEffect(() => {
    if (userData) {
      setForm({
        full_name: userData.full_name || '',
        username: userData.username || '',
        bio: userData.bio || '',
      });
    }
  }, [userData]);

  // Update handlers
  const handleUpdateField = async (field: keyof EditProfileForm, value: string) => {
    try {
      const updatedData = { [field]: value };
      await updateProfile(updatedData).unwrap();
      
      setForm(prev => ({ ...prev, [field]: value }));
      dispatch(updateUserProfile(updatedData));
      
      Alert.alert('Success', `${field.replace('_', ' ')} updated successfully`);
    } catch (error) {
      logger.error(`Update ${field} error:`, error);
      Alert.alert('Error', `Failed to update ${field.replace('_', ' ')}`);
    }
  };

  const handleSaveName = (name: string) => {
    setNameModalVisible(false);
    handleUpdateField('full_name', name);
  };

  const handleSaveBio = (bio: string) => {
    setBioModalVisible(false);
    handleUpdateField('bio', bio);
  };

  const handleCopyUsername = () => {
    const username = `ts1.fr/@${form.username}`;
    Clipboard.setString(username);
    
    if (Platform.OS === 'android') {
      ToastAndroid.show('Username copied to clipboard', ToastAndroid.SHORT);
    } else {
      Alert.alert('Copied', 'Username copied to clipboard');
    }
  };

  const handleCreateAISelf = () => {
    Alert.alert('Coming Soon', 'AI Self feature will be available soon');
  };

  const handleWatchHistory = () => {
    Alert.alert('Coming Soon', 'Watch history will be available soon');
  };

  const handleLinks = () => {
    Alert.alert('Coming Soon', 'Links editor will be available soon');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Edit profile
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Photo Section */}
        <ProfilePhotoSection
          avatarUrl={currentUserData?.avatar_url}
          onChangePhoto={showImagePicker}
          isUploading={isUploadingAvatar}
        />

        {/* User Info Fields */}
        <View style={styles.infoSection}>
          <ProfileInfoItem
            label="Name"
            value={form.full_name}
            placeholder="Add your name"
            onPress={() => setNameModalVisible(true)}
          />

          <ProfileInfoItem
            label="Username"
            value=""
            isEditable={false}
            showChevron={false}
            customContent={
              <View style={styles.usernameContainer}>
                <Text style={[styles.usernameText, { color: theme.colors.text }]}>
                  @{form.username}
                </Text>
                <TouchableOpacity onPress={handleCopyUsername} style={styles.copyButton}>
                  <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
                <Text style={[styles.usernameUrl, { color: theme.colors.textSecondary }]}>
                  ts1.fr/@{form.username}
                </Text>
              </View>
            }
          />

          <ProfileInfoItem
            label="Bio"
            value={form.bio}
            placeholder="Add a bio"
            onPress={() => setBioModalVisible(true)}
          />

          <ProfileInfoItem
            label="Links"
            value=""
            placeholder="Add links"
            onPress={handleLinks}
          />

          <ProfileInfoItem
            label="AI Self"
            value=""
            placeholder="Create your AI"
            onPress={handleCreateAISelf}
          />
        </View>

        {/* Other Settings Section */}
        <View style={styles.otherSection}>
          <Text style={[styles.sectionLabel, { color: theme.colors.textSecondary }]}>
            Change display order
          </Text>

          <ProfileInfoItem
            label="Watch History"
            value=""
            placeholder="Manage history"
            onPress={handleWatchHistory}
          />
        </View>

        <View style={styles.bottomSpace} />
      </ScrollView>

      {/* Modals */}
      <EditModal
        visible={nameModalVisible}
        title="Name"
        value={form.full_name}
        placeholder="Enter your name"
        maxLength={50}
        onClose={() => setNameModalVisible(false)}
        onSave={handleSaveName}
        isLoading={isUpdating}
      />

      <EditModal
        visible={bioModalVisible}
        title="Bio"
        value={form.bio}
        placeholder="Tell people about yourself"
        maxLength={150}
        multiline={true}
        onClose={() => setBioModalVisible(false)}
        onSave={handleSaveBio}
        isLoading={isUpdating}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderBottomWidth: 1,
  },
  backButton: {
    padding: normalize(4),
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  headerRight: {
    width: normalize(32),
  },
  infoSection: {
    backgroundColor: 'transparent',
  },
  usernameContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  usernameText: {
    fontSize: normalize(16),
    marginRight: normalize(8),
  },
  copyButton: {
    padding: normalize(4),
    marginRight: normalize(8),
  },
  usernameUrl: {
    fontSize: normalize(12),
    flex: 1,
    textAlign: 'right',
  },
  otherSection: {
    marginTop: normalize(32),
  },
  sectionLabel: {
    fontSize: normalize(14),
    fontWeight: '600',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    textTransform: 'uppercase',
  },
  bottomSpace: {
    height: normalize(40),
  },
});

export default EditProfileScreen;
