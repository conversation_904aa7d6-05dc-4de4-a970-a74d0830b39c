import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import Loading from '../../components/common/Loading';
import {
  useGetCurrentUserCompleteQuery,
  useUpdateUserSettingsMutation,
  useGetPrivacySettingsQuery,
  useUpdatePrivacySettingsMutation,
} from '../../store/api/userManagementApi';
import { UserSettings, PrivacySettings } from '../../types/settings';
import { spacing } from '../../styles/spacing';

/**
 * Privacy Settings Screen
 * Manage privacy and interaction preferences
 */
const PrivacySettingsScreen = () => {
  const { theme } = useTheme();

  const {
    data: user,
    isLoading,
    error,
    refetch,
  } = useGetCurrentUserCompleteQuery();

  const [updateSettings, { isLoading: isUpdating }] = useUpdateUserSettingsMutation();

  const { data: privacyData, isLoading: privacyLoading, error: privacyError } = useGetPrivacySettingsQuery();
  const [updatePrivacy] = useUpdatePrivacySettingsMutation();
  const [localSettings, setLocalSettings] = useState<Partial<UserSettings>>({});
  const [localPrivacy, setLocalPrivacy] = useState<PrivacySettings | null>(null);

  useEffect(() => {
    if (user) {
      setLocalSettings({
        is_private_account: (user as any).is_private_account || false,
        allow_messages_from: (user as any).allow_messages_from || 'Everyone',
        show_activity_status: (user as any).show_activity_status !== false,
      });
    }
  }, [user]);

  useEffect(() => {
    if (privacyData) {
      setLocalPrivacy(privacyData);
    }
  }, [privacyData]);

  const handleUserToggle = async (key: keyof UserSettings, value: boolean | string) => {
    // Update local state immediately for responsive UI
    const updatedSettings = { ...localSettings, [key]: value };
    setLocalSettings(updatedSettings);

    try {
      await updateSettings({ [key]: value } as Partial<UserSettings>).unwrap();
      refetch(); // Refresh user data
    } catch (error) {
      // Revert local state on error
      setLocalSettings(localSettings);
      Alert.alert(
        'Error',
        'Failed to update privacy settings. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handlePrivacyToggle = async (key: keyof PrivacySettings, value: boolean) => {
    if (!localPrivacy) {return;}

    const updated = { ...localPrivacy, [key]: value } as PrivacySettings;
    setLocalPrivacy(updated);

    try {
      await updatePrivacy({ [key]: value }).unwrap();
    } catch (error) {
      setLocalPrivacy(localPrivacy);
      Alert.alert('Error', 'Failed to update privacy settings. Please try again.', [{ text: 'OK' }]);
    }
  };

  const showMessageOptions = () => {
    Alert.alert(
      'Who can message you',
      'Choose who can send you direct messages',
      [
        {
          text: 'Everyone',
          onPress: () => handleUserToggle('allow_messages_from', 'Everyone'),
        },
        {
          text: 'Friends only',
          onPress: () => handleUserToggle('allow_messages_from', 'Friends'),
        },
        {
          text: 'No one',
          onPress: () => handleUserToggle('allow_messages_from', 'NoOne'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  if (isLoading || privacyLoading || !user || !localPrivacy) {
    return (
      <SafeAreaWrapper>
        <Loading />
      </SafeAreaWrapper>
    );
  }

  if (error || privacyError) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load privacy settings
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <SettingsSection
            title="Account Privacy"
            description="Control who can see your content and interact with you"
          >
            <SettingsItem
              title="Private Account"
              description={
                localSettings.is_private_account
                  ? 'Only approved followers can see your videos'
                  : 'Anyone can see your videos'
              }
              type="toggle"
              value={localSettings.is_private_account}
              onToggle={(value) => handleUserToggle('is_private_account', value)}
              icon="lock-closed-outline"
            />
          </SettingsSection>

          <SettingsSection
            title="Interactions"
            description="Manage how others can interact with you"
          >
            <SettingsItem
              title="Who can message you"
              description="Control who can send you direct messages"
              type="selection"
              value={localSettings.allow_messages_from}
              onPress={showMessageOptions}
              icon="chatbubble-outline"
            />

            <SettingsItem
              title="Show activity status"
              description="Let others see when you're active"
              type="toggle"
              value={localSettings.show_activity_status}
              onToggle={(value) => handleUserToggle('show_activity_status', value)}
              icon="radio-outline"
              showChevron={false}
            />
          </SettingsSection>


          <SettingsSection
            title="Content & Discovery"
            description="Control how your content appears to others"
          >
            <SettingsItem
              title="Suggest your account to others"
              description="Allow TikTok to suggest your account to other users"
              type="toggle"
              value={localPrivacy?.profile_visibility === true}
              onToggle={(value) => handlePrivacyToggle('profile_visibility', value)}
              icon="people-outline"
            />

            <SettingsItem
              title="Allow comments"
              description="Let others comment on your videos"
              type="toggle"

              value={localPrivacy?.allow_downloads}
              onToggle={(value) => handlePrivacyToggle('allow_downloads', value)}
              icon="download-outline"
            />
            <SettingsItem
              title="Allow challenges"
              description="Let others challenge your videos"
              type="toggle"

              value={localPrivacy?.allow_duets}
              onToggle={(value) => handlePrivacyToggle('allow_duets', value)}
              icon="copy-outline"
            />
            <SettingsItem
              title="Allow downloads"
              description="Let others download your videos"
              type="toggle"

              value={localPrivacy?.allow_comments}
              onToggle={(value) => handlePrivacyToggle('allow_comments', value)}
              icon="chatbubbles-outline"
              showChevron={false}
            />
          </SettingsSection>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
              Privacy settings help you control your TikTok experience. Some features may be limited when privacy settings are enabled.
            </Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: spacing['4xl'],
  },
  footer: {
    padding: spacing.base,
    marginTop: spacing.lg,
  },
  footerText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default PrivacySettingsScreen;
