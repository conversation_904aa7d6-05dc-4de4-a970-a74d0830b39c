import React, { useState } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { useGetCurrentUserCompleteQuery, useUpdateEmailMutation } from '../../store/api/userManagementApi';
import { spacing } from '../../styles/spacing';

/**
 * Edit Email Screen
 * Allows users to change their email address with verification
 */
const EditEmailScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const { data: user, refetch } = useGetCurrentUserCompleteQuery();
  const [updateEmail, { isLoading }] = useUpdateEmailMutation();
  const [email, setEmail] = useState(user?.email || '');
  const [currentPassword, setCurrentPassword] = useState('');
  const [error, setError] = useState('');
  const [step, setStep] = useState<'email' | 'verify'>('email');

  const validateEmail = (value: string): string => {
    if (!value.trim()) {
      return 'Email is required';
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  };

  const handleSave = async () => {
    const emailError = validateEmail(email);
    if (emailError) {
      setError(emailError);
      return;
    }

    if (!currentPassword.trim()) {
      setError('Current password is required to change email');
      return;
    }

    if (email === user?.email) {
      navigation.goBack();
      return;
    }

    setError('');

    try {
      await updateEmail({ newEmail: email, currentPassword }).unwrap();

      setStep('verify');
    } catch (error: any) {
      setError(error.message || 'Failed to update email');
    }
  };

  const handleVerification = () => {
    Alert.alert(
      'Email Updated',
      'Your email has been successfully updated and verified.',
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
    refetch();
  };

  if (step === 'verify') {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <View style={styles.verifyContent}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Verify Your Email
            </Text>

            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              We've sent a verification link to {email}. Please check your email and click the link to verify your new email address.
            </Text>

            <Button
              title="I've Verified My Email"
              onPress={handleVerification}
              style={styles.verifyButton}
            />

            <Button
              title="Resend Verification Email"
              variant="outline"
              onPress={() => {
                // TODO: Resend verification email
                Alert.alert('Verification Email Sent', 'Please check your email for the verification link.');
              }}
              style={styles.resendButton}
            />
          </View>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Change Email Address
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Your email is used for account recovery and important notifications.
          </Text>

          <View style={styles.inputContainer}>
            <Input
              label="New Email Address"
              value={email}
              onChangeText={(text) => {
                setEmail(text.trim());
                setError('');
              }}
              placeholder="Enter your new email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              error={error && !currentPassword ? error : ''}
            />
          </View>

          <View style={styles.inputContainer}>
            <Input
              label="Current Password"
              value={currentPassword}
              onChangeText={(text) => {
                setCurrentPassword(text);
                setError('');
              }}
              placeholder="Enter your current password"
              secureTextEntry
              error={error && currentPassword ? error : ''}
            />
          </View>

          <View style={styles.warningContainer}>
            <Text style={[styles.warningTitle, { color: theme.colors.warning }]}>
              Important:
            </Text>
            <Text style={[styles.warningText, { color: theme.colors.textSecondary }]}>
              • You'll need to verify your new email address{'\n'}
              • Your current email will remain active until verification{'\n'}
              • Make sure you have access to the new email
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Button
            title="Update Email"
            onPress={handleSave}
            loading={isLoading}
            disabled={!email.trim() || !currentPassword.trim() || email === user?.email}
            style={styles.saveButton}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.base,
  },
  verifyContent: {
    flex: 1,
    justifyContent: 'center',
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  warningContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(255, 184, 0, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 184, 0, 0.2)',
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  warningText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: spacing.base,
    paddingBottom: spacing.xl,
  },
  saveButton: {
    marginTop: spacing.sm,
  },
  verifyButton: {
    marginBottom: spacing.base,
  },
  resendButton: {
    marginTop: spacing.sm,
  },
});

export default EditEmailScreen;
