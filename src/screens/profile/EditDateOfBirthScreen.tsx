import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Button from '../../components/common/Button';
import { useGetCurrentUserCompleteQuery, useUpdateUserProfileMutation } from '../../store/api/userManagementApi';
import { spacing } from '../../styles/spacing';
import logger from '../../utils/logger';

/**
 * Edit Date of Birth Screen
 * Allows users to set or change their date of birth
 */
const EditDateOfBirthScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const { data: user, refetch } = useGetCurrentUserCompleteQuery();
  const [updateUserProfile, { isLoading }] = useUpdateUserProfileMutation();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [error, setError] = useState('');

  // Initialize date from user data
  useEffect(() => {
    if (user?.date_of_birth) {
      setSelectedDate(new Date(user.date_of_birth));
    } else {
      // Set default to 18 years ago
      const eighteenYearsAgo = new Date();
      eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);
      setSelectedDate(eighteenYearsAgo);
    }
  }, [user]);

  const validateAge = (date: Date): string => {
    const today = new Date();
    let age = today.getFullYear() - date.getFullYear();
    const monthDiff = today.getMonth() - date.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
      age--;
    }

    if (calculateAge(date) < 13) {
      return 'You must be at least 13 years old to use this app';
    }

    if (date > today) {
      return 'Date of birth cannot be in the future';
    }

    return '';
  };

  const handleDateChange = (event: any, date?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');

    if (date) {
      setSelectedDate(date);
      setError('');
    }
  };

  const handleSave = async () => {
    const validationError = validateAge(selectedDate);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Check if date has changed
    const currentDateString = user?.date_of_birth;
    const newDateString = selectedDate.toISOString().split('T')[0];

    if (currentDateString === newDateString) {
      navigation.goBack();
      return;
    }

    setError('');

    try {
      await updateUserProfile({
        date_of_birth: newDateString,
      }).unwrap();

      Alert.alert(
        'Date of Birth Updated',
        'Your date of birth has been successfully updated.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );

      refetch();
    } catch (error: any) {
      logger.error('Update date of birth error:', error);
      setError(error.message || 'Failed to update date of birth');
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const calculateAge = (date: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - date.getFullYear();
    const monthDiff = today.getMonth() - date.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
      age--;
    }

    return age;
  };

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {user?.date_of_birth ? 'Change Date of Birth' : 'Set Date of Birth'}
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Your date of birth helps us provide age-appropriate content and comply with legal requirements.
          </Text>

          <View style={styles.dateContainer}>
            <Text style={[styles.label, { color: theme.colors.text }]}>
              Date of Birth
            </Text>

            <Button
              title={formatDate(selectedDate)}
              variant="outline"
              onPress={() => setShowDatePicker(true)}
              style={[styles.dateButton, { borderColor: error ? theme.colors.error : theme.colors.border }]}
            />

            {selectedDate && (
              <Text style={[styles.ageText, { color: theme.colors.textSecondary }]}>
                Age: {calculateAge(selectedDate)} years old
              </Text>
            )}

            {error ? (
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {error}
              </Text>
            ) : null}
          </View>

          <View style={styles.infoContainer}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
              Why we need your date of birth:
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              • Age verification and compliance{'\n'}
              • Age-appropriate content filtering{'\n'}
              • Legal requirements and safety{'\n'}
              • Better user experience
            </Text>
          </View>

          {showDatePicker && (
            <DateTimePicker
              value={selectedDate}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleDateChange}
              maximumDate={new Date()}
              minimumDate={new Date(1900, 0, 1)}
            />
          )}
        </View>

        <View style={styles.footer}>
          <Button
            title="Save Date of Birth"
            onPress={handleSave}
            loading={isLoading}
            style={styles.saveButton}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  dateContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.sm,
  },
  dateButton: {
    marginBottom: spacing.sm,
    justifyContent: 'flex-start',
  },
  ageText: {
    fontSize: 14,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: 14,
    marginTop: spacing.xs,
  },
  infoContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(37, 244, 238, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(37, 244, 238, 0.2)',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: spacing.base,
    paddingBottom: spacing.xl,
  },
  saveButton: {
    marginTop: spacing.sm,
  },
});

export default EditDateOfBirthScreen;
