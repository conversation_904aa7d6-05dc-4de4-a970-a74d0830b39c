import React, {useState} from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import {useTheme} from '../../contexts/ThemeContext';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '../../navigation/types';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import SettingsSection from '../../components/settings/SettingsSection';
import SettingsItem from '../../components/settings/SettingsItem';
import Loading from '../../components/common/Loading';
import {spacing} from '../../styles/spacing';
import logger from '../../utils/logger';
import {supabase} from '../../integrations/supabase/client';
import {SecuritySettings} from '../../types/settings';
import {
  useGetSecuritySettingsQuery,
  useUpdateSecuritySettingsMutation,
} from '../../store/api/securitySettingsApi';

type SecuritySettingsNavigationProp = StackNavigationProp<MainStackParamList>;

/**
 * Security Settings Screen
 * Manage 2FA, login notifications, and security preferences
 */
const SecuritySettingsScreen = () => {
  const {theme} = useTheme();
  const navigation = useNavigation<SecuritySettingsNavigationProp>();

  const {data: settings, isLoading} = useGetSecuritySettingsQuery();
  const [updateSecuritySettings] = useUpdateSecuritySettingsMutation();
  const [updating, setUpdating] = useState<string | null>(null);

  const updateSetting = async (key: keyof SecuritySettings, value: any) => {
    if (!settings) {
      return;
    }

    setUpdating(key);
    try {
      await updateSecuritySettings({[key]: value}).unwrap();

      // Log security activity
      await logSecurityActivity(`${key}_changed`);
    } catch (error: any) {
      logger.error(`Error updating ${key}:`, error);
      Alert.alert('Error', `Failed to update ${key.replace('_', ' ')}`);
    } finally {
      setUpdating(null);
    }
  };

  const handleToggle2FA = async () => {
    if (!settings) {
      return;
    }

    if (settings.two_factor_enabled) {
      // Disable 2FA
      Alert.alert(
        'Disable Two-Factor Authentication',
        'Are you sure you want to disable 2FA? This will make your account less secure.',
        [
          {text: 'Cancel', style: 'cancel'},
          {
            text: 'Disable',
            style: 'destructive',
            onPress: () => updateSetting('two_factor_enabled', false),
          },
        ],
      );
    } else {
      // Enable 2FA - navigate to setup screen
      navigation.navigate('Setup2FAScreen' as any);
    }
  };

  const logSecurityActivity = async (activityType: string) => {
    try {
      const {
        data: {user},
      } = await supabase.auth.getUser();
      if (!user) {
        return;
      }

      await supabase.from('security_activities').insert({
        user_id: user.id,
        activity_type: activityType,
        ip_address: '0.0.0.0', // Would get real IP in production
        device_info: 'Mobile App',
        location: 'Unknown',
      });
    } catch (error) {
      logger.error('Failed to log security activity:', error);
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (!settings) {
    return (
      <SafeAreaWrapper>
        <View
          style={[
            styles.container,
            {backgroundColor: theme.colors.background},
          ]}>
          <Text style={[styles.errorText, {color: theme.colors.error}]}>
            Failed to load security settings
          </Text>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <ScrollView
        style={[styles.container, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={[styles.title, {color: theme.colors.text}]}>
            Security Settings
          </Text>

          <Text
            style={[styles.description, {color: theme.colors.textSecondary}]}>
            Manage your account security and authentication preferences.
          </Text>

          <SettingsSection title="Two-Factor Authentication">
            <SettingsItem
              title="Two-Factor Authentication"
              description={settings.two_factor_enabled ? 'Enabled' : 'Disabled'}
              type="toggle"
              value={settings.two_factor_enabled}
              onToggle={handleToggle2FA}
              loading={updating === 'two_factor_enabled'}
              icon="shield-checkmark-outline"
            />

            {settings.two_factor_enabled && (
              <>
                <SettingsItem
                  title="Backup Codes"
                  description={
                    settings.backup_codes_generated
                      ? 'Generated'
                      : 'Not generated'
                  }
                  type="navigation"
                  onPress={() =>
                    navigation.navigate('BackupCodesScreen' as any)
                  }
                  icon="key-outline"
                />

                <SettingsItem
                  title="Recovery Options"
                  description="Manage account recovery methods"
                  type="navigation"
                  onPress={() =>
                    navigation.navigate('RecoveryOptionsScreen' as any)
                  }
                  icon="medical-outline"
                />
              </>
            )}
          </SettingsSection>

          <SettingsSection title="Login Security">
            <SettingsItem
              title="Login Notifications"
              description="Get notified of new login attempts"
              type="toggle"
              value={settings.login_notifications}
              onToggle={value => updateSetting('login_notifications', value)}
              loading={updating === 'login_notifications'}
              icon="notifications-outline"
            />

            <SettingsItem
              title="Device Management"
              description="Manage trusted devices"
              type="toggle"
              value={settings.device_management}
              onToggle={value => updateSetting('device_management', value)}
              loading={updating === 'device_management'}
              icon="phone-portrait-outline"
            />

            <SettingsItem
              title="Login Activities"
              description="View recent login attempts"
              type="navigation"
              onPress={() =>
                navigation.navigate('LoginActivitiesScreen' as any)
              }
              icon="time-outline"
            />
          </SettingsSection>

          <SettingsSection title="Session Management">
            <SettingsItem
              title="Session Timeout"
              description={`${settings.session_timeout} minutes`}
              type="navigation"
              onPress={() => {
                // Show session timeout picker
                Alert.alert(
                  'Session Timeout',
                  'Choose session timeout duration',
                  [
                    {
                      text: '15 minutes',
                      onPress: () => updateSetting('session_timeout', 15),
                    },
                    {
                      text: '30 minutes',
                      onPress: () => updateSetting('session_timeout', 30),
                    },
                    {
                      text: '1 hour',
                      onPress: () => updateSetting('session_timeout', 60),
                    },
                    {
                      text: '2 hours',
                      onPress: () => updateSetting('session_timeout', 120),
                    },
                    {text: 'Cancel', style: 'cancel'},
                  ],
                );
              }}
              icon="timer-outline"
            />
          </SettingsSection>

          <SettingsSection title="Password Security">
            <SettingsItem
              title="Change Password"
              description={
                settings.password_last_changed
                  ? `Last changed: ${new Date(
                      settings.password_last_changed,
                    ).toLocaleDateString()}`
                  : 'Never changed'
              }
              type="navigation"
              onPress={() => navigation.navigate('ChangePassword')}
              icon="lock-closed-outline"
            />
          </SettingsSection>
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: spacing.xl,
  },
});

export default SecuritySettingsScreen;
