import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Loading from '../../components/common/Loading';
import Button from '../../components/common/Button';
import { spacing } from '../../styles/spacing';
import { supabase } from '../../integrations/supabase/client';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

interface SecurityActivity {
  id: string;
  activity_type: string;
  ip_address: string;
  device_info: string;
  location: string;
  created_at: string;
}

/**
 * Login Activities Screen
 * Shows recent login attempts and security activities
 */
const LoginActivitiesScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activities, setActivities] = useState<SecurityActivity[]>([]);

  useEffect(() => {
    fetchActivities();
  }, []);

  const fetchActivities = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {throw new Error('No authenticated user');}

      const { data, error } = await supabase
        .from('security_activities')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {throw error;}

      setActivities(
        (data || []).map((item) => ({
          ...item,
          location: item.location ?? '',
          created_at: item.created_at ?? '',
        }))
      );
    } catch (error) {
      logger.error('Error fetching activities:', error);
      Alert.alert('Error', 'Failed to load login activities');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    fetchActivities(true);
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'login_success':
        return { name: 'checkmark-circle', color: theme.colors.success || '#4CAF50' };
      case 'login_failed':
        return { name: 'close-circle', color: theme.colors.error };
      case '2fa_login_success':
        return { name: 'shield-checkmark', color: theme.colors.success || '#4CAF50' };
      case '2fa_login_failed':
        return { name: 'shield-outline', color: theme.colors.error };
      case '2fa_sms_sent':
        return { name: 'chatbubble', color: theme.colors.primary };
      case '2fa_sms_failed':
        return { name: 'chatbubble-outline', color: theme.colors.error };
      case 'phone_verification_sent':
        return { name: 'phone-portrait', color: theme.colors.primary };
      case 'phone_verification_success':
        return { name: 'phone-portrait', color: theme.colors.success || '#4CAF50' };
      case 'phone_verification_failed':
        return { name: 'phone-portrait-outline', color: theme.colors.error };
      case 'password_changed':
        return { name: 'key', color: theme.colors.warning || '#FF9800' };
      case 'two_factor_enabled':
        return { name: 'shield', color: theme.colors.primary };
      case 'two_factor_disabled':
        return { name: 'shield-outline', color: theme.colors.warning || '#FF9800' };
      case 'logout':
        return { name: 'log-out', color: theme.colors.textSecondary };
      case 'account_locked':
        return { name: 'lock-closed', color: theme.colors.error };
      case 'suspicious_activity':
        return { name: 'warning', color: theme.colors.warning || '#FF9800' };
      default:
        return { name: 'information-circle', color: theme.colors.textSecondary };
    }
  };

  const getActivityTitle = (activityType: string) => {
    switch (activityType) {
      case 'login_success':
        return 'Successful Login';
      case 'login_failed':
        return 'Failed Login Attempt';
      case '2fa_login_success':
        return 'Successful 2FA Login';
      case '2fa_login_failed':
        return 'Failed 2FA Attempt';
      case '2fa_sms_sent':
        return '2FA SMS Code Sent';
      case '2fa_sms_failed':
        return '2FA SMS Failed';
      case 'phone_verification_sent':
        return 'Phone Verification Sent';
      case 'phone_verification_success':
        return 'Phone Verified Successfully';
      case 'phone_verification_failed':
        return 'Phone Verification Failed';
      case 'password_changed':
        return 'Password Changed';
      case 'two_factor_enabled':
        return '2FA Enabled';
      case 'two_factor_disabled':
        return '2FA Disabled';
      case 'logout':
        return 'Logout';
      case 'account_locked':
        return 'Account Locked';
      case 'suspicious_activity':
        return 'Suspicious Activity Detected';
      default:
        return activityType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getActivityDescription = (activity: SecurityActivity) => {
    const date = new Date(activity.created_at).toLocaleString();
    return `${activity.device_info} • ${activity.location} • ${date}`;
  };

  const clearOldActivities = async () => {
    Alert.alert(
      'Clear Old Activities',
      'This will remove login activities older than 30 days. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              const { data: { user } } = await supabase.auth.getUser();
              if (!user) {return;}

              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

              const { error } = await supabase
                .from('security_activities')
                .delete()
                .eq('user_id', user.id)
                .lt('created_at', thirtyDaysAgo.toISOString());

              if (error) {throw error;}

              fetchActivities();
              Alert.alert('Success', 'Old activities have been cleared');
            } catch (error) {
              logger.error('Error clearing activities:', error);
              Alert.alert('Error', 'Failed to clear activities');
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <SafeAreaWrapper>
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Login Activities
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Review recent login attempts and security activities on your account.
          </Text>

          {activities.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons
                name="time-outline"
                size={64}
                color={theme.colors.textSecondary}
              />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                No Activities Yet
              </Text>
              <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
                Your login activities will appear here once you start using the app.
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.activitiesList}>
                {activities.map((activity) => {
                  const icon = getActivityIcon(activity.activity_type);
                  return (
                    <View
                      key={activity.id}
                      style={[styles.activityItem, { borderBottomColor: theme.colors.border }]}
                    >
                      <View style={styles.activityIcon}>
                        <Ionicons
                          name={icon.name as any}
                          size={24}
                          color={icon.color}
                        />
                      </View>

                      <View style={styles.activityContent}>
                        <Text style={[styles.activityTitle, { color: theme.colors.text }]}>
                          {getActivityTitle(activity.activity_type)}
                        </Text>
                        <Text style={[styles.activityDescription, { color: theme.colors.textSecondary }]}>
                          {getActivityDescription(activity)}
                        </Text>
                        {activity.ip_address !== '0.0.0.0' && (
                          <Text style={[styles.activityIP, { color: theme.colors.textSecondary }]}>
                            IP: {activity.ip_address}
                          </Text>
                        )}
                      </View>
                    </View>
                  );
                })}
              </View>

              <View style={styles.actions}>
                <Button
                  title="Clear Old Activities"
                  variant="outline"
                  onPress={clearOldActivities}
                  style={styles.clearButton}
                />
              </View>
            </>
          )}

          <View style={styles.infoContainer}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
              Security Tips
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              • Review your login activities regularly{'\n'}
              • Report any suspicious activities immediately{'\n'}
              • Enable 2FA for additional security{'\n'}
              • Use strong, unique passwords
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: spacing.base,
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  activitiesList: {
    marginBottom: spacing.xl,
  },
  activityItem: {
    flexDirection: 'row',
    paddingVertical: spacing.base,
    borderBottomWidth: 1,
  },
  activityIcon: {
    marginRight: spacing.base,
    paddingTop: spacing.xs,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  activityDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: spacing.xs,
  },
  activityIP: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  actions: {
    marginBottom: spacing.xl,
  },
  clearButton: {
    marginBottom: spacing.base,
  },
  infoContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(37, 244, 238, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(37, 244, 238, 0.2)',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default LoginActivitiesScreen;
