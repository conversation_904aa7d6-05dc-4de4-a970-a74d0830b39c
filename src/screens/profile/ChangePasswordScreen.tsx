import React, { useState } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { useChangePasswordMutation } from '../../store/api/userManagementApi';
import { spacing } from '../../styles/spacing';

/**
 * Change Password Screen
 * Allows users to change their password with current password verification
 */
const ChangePasswordScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const [changePassword, { isLoading }] = useChangePasswordMutation();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validatePassword = (password: string): string => {
    if (!password) {
      return 'Password is required';
    }
    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number';
    }
    if (!/(?=.*[@$!%*?&])/.test(password)) {
      return 'Password must contain at least one special character';
    }
    return '';
  };

  const handleChangePassword = async () => {
    const newErrors: {[key: string]: string} = {};

    // Validate current password
    if (!currentPassword.trim()) {
      newErrors.currentPassword = 'Current password is required';
    }

    // Validate new password
    const passwordError = validatePassword(newPassword);
    if (passwordError) {
      newErrors.newPassword = passwordError;
    }

    // Validate password confirmation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Check if new password is different from current
    if (currentPassword === newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      return;
    }

    try {
      await changePassword({ currentPassword, newPassword }).unwrap();

      Alert.alert(
        'Password Changed',
        'Your password has been successfully updated.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error: any) {
      if (error.message.includes('current password') || error.message.includes('incorrect')) {
        setErrors({ currentPassword: 'Current password is incorrect' });
      } else {
        setErrors({ general: error.message || 'Failed to change password' });
      }
    }
  };

  const getPasswordStrength = (password: string): { strength: number; label: string; color: string } => {
    let strength = 0;

    if (password.length >= 8) {strength++;}
    if (/(?=.*[a-z])/.test(password)) {strength++;}
    if (/(?=.*[A-Z])/.test(password)) {strength++;}
    if (/(?=.*\d)/.test(password)) {strength++;}
    if (/(?=.*[@$!%*?&])/.test(password)) {strength++;}

    if (strength <= 2) {return { strength, label: 'Weak', color: '#FF4757' };}
    if (strength <= 3) {return { strength, label: 'Fair', color: '#FFA502' };}
    if (strength <= 4) {return { strength, label: 'Good', color: '#2ED573' };}
    return { strength, label: 'Strong', color: '#2ED573' };
  };

  const passwordStrength = getPasswordStrength(newPassword);

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Change Password
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Choose a strong password to keep your account secure.
          </Text>

          {errors.general && (
            <View style={styles.errorContainer}>
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {errors.general}
              </Text>
            </View>
          )}

          <View style={styles.inputContainer}>
            <Input
              label="Current Password"
              value={currentPassword}
              onChangeText={(text) => {
                setCurrentPassword(text);
                setErrors(prev => ({ ...prev, currentPassword: '' }));
              }}
              placeholder="Enter your current password"
              secureTextEntry
              error={errors.currentPassword}
            />
          </View>

          <View style={styles.inputContainer}>
            <Input
              label="New Password"
              value={newPassword}
              onChangeText={(text) => {
                setNewPassword(text);
                setErrors(prev => ({ ...prev, newPassword: '' }));
              }}
              placeholder="Enter your new password"
              secureTextEntry
              error={errors.newPassword}
            />

            {newPassword.length > 0 && (
              <View style={styles.strengthContainer}>
                <View style={styles.strengthBar}>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <View
                      key={level}
                      style={[
                        styles.strengthSegment,
                        {
                          backgroundColor: level <= passwordStrength.strength
                            ? passwordStrength.color
                            : theme.colors.border,
                        },
                      ]}
                    />
                  ))}
                </View>
                <Text style={[styles.strengthLabel, { color: passwordStrength.color }]}>
                  {passwordStrength.label}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.inputContainer}>
            <Input
              label="Confirm New Password"
              value={confirmPassword}
              onChangeText={(text) => {
                setConfirmPassword(text);
                setErrors(prev => ({ ...prev, confirmPassword: '' }));
              }}
              placeholder="Confirm your new password"
              secureTextEntry
              error={errors.confirmPassword}
            />
          </View>

          <View style={styles.requirementsContainer}>
            <Text style={[styles.requirementsTitle, { color: theme.colors.text }]}>
              Password Requirements:
            </Text>
            <Text style={[styles.requirementsText, { color: theme.colors.textSecondary }]}>
              • At least 8 characters long{'\n'}
              • One uppercase letter (A-Z){'\n'}
              • One lowercase letter (a-z){'\n'}
              • One number (0-9){'\n'}
              • One special character (@$!%*?&)
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Button
            title="Change Password"
            onPress={handleChangePassword}
            loading={isLoading}
            disabled={!currentPassword || !newPassword || !confirmPassword}
            style={styles.saveButton}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.base,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: spacing.xl,
  },
  errorContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
    borderRadius: 8,
    marginBottom: spacing.base,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  strengthContainer: {
    marginTop: spacing.sm,
  },
  strengthBar: {
    flexDirection: 'row',
    gap: 4,
    marginBottom: spacing.xs,
  },
  strengthSegment: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  strengthLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  requirementsContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(37, 244, 238, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(37, 244, 238, 0.2)',
  },
  requirementsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  requirementsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: spacing.base,
    paddingBottom: spacing.xl,
  },
  saveButton: {
    marginTop: spacing.sm,
  },
});

export default ChangePasswordScreen;
