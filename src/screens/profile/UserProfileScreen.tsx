import React, { useState, useEffect } from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';
import {
  setFollowerCount,
  incrementFollowerCount,
  decrementFollowerCount,
  setFollowing,
  selectFollowerCount,
} from '../../store/slices/followersSlice';
import { subscribeToFollowers } from '../../store/api/followersApi';
import { useGetUserByIdQuery, useGetUserVideosQuery, useToggleFollowMutation, useCheckIsFollowingQuery } from '../../store/api/userManagementApi';
import ProfileHeader from '../../components/profile/ProfileHeader';
import ProfileStats from '../../components/profile/ProfileStats';
import ProfileTabs from '../../components/profile/ProfileTabs';
import VideoGrid from '../../components/profile/VideoGrid';
import Loading from '../../components/common/Loading';
import logger from '../../utils/logger';

const UserProfileScreen = ({ route }: { route: any }) => {
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectCurrentUser);
  const { userId } = route.params;
  const [activeTab, setActiveTab] = useState<'videos' | 'liked' | 'private'>('videos');

  // Get follower count from Redux store (for real-time updates)
  const reduxFollowerCount = useAppSelector(selectFollowerCount(userId));

  // Fetch the user data for the profile we're viewing
  const { data: profileUser, isLoading: isLoadingUser } = useGetUserByIdQuery(userId);

  // Check if this is the current user's own profile
  const isOwnProfile = currentUser?.id === userId;

  // Fetch user's videos
  const { data: publicVideos } = useGetUserVideosQuery(
    { userId: userId, privacy: 'public' },
    { skip: !userId }
  );

  // Only fetch private videos if viewing own profile
  const { data: privateVideos } = useGetUserVideosQuery(
    { userId: userId, privacy: 'private' },
    { skip: !userId || !isOwnProfile }
  );

  // Check if current user is following this profile
  const { data: isFollowing = false } = useCheckIsFollowingQuery(userId, {
    skip: !userId || !currentUser || isOwnProfile
  });

  // Follow/unfollow mutation
  const [toggleFollow, { isLoading: isToggleFollowLoading }] = useToggleFollowMutation();

  useEffect(() => {
    if (!profileUser || !currentUser) return;
    dispatch(
      setFollowerCount({ userId: profileUser.id, count: profileUser.followers_count || 0 })
    );
    const channel = subscribeToFollowers(
      profileUser.id,
      dispatch,
      {
        increment: id => incrementFollowerCount({ userId: id }),
        decrement: id => decrementFollowerCount({ userId: id }),
        setFollowing: (userId, isFollowing) => setFollowing({ userId, isFollowing }),
      },
      currentUser.id,
    );
    return () => {
      channel.unsubscribe();
    };
  }, [dispatch, profileUser, currentUser]);

  logger.debug('[UserProfileScreen] profileUser:', profileUser);
  logger.debug('[UserProfileScreen] currentUser:', currentUser);

  if (isLoadingUser || !profileUser) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Loading />
      </SafeAreaView>
    );
  }

  // Example handlers (replace with real logic as needed)
  const handleEditProfile = () => {
    // Navigate to edit profile or open modal
    logger.debug('Edit profile pressed');
  };

  const handleFollow = async () => {
    if (!currentUser || !profileUser || isToggleFollowLoading) return;

    try {
      await toggleFollow({
        targetUserId: profileUser.id,
        isCurrentlyFollowing: isFollowing,
        currentFollowersCount: displayFollowerCount,
        currentFollowingCount: displayFollowingCount
      }).unwrap();

      logger.debug('Follow/unfollow successful');
    } catch (error) {
      logger.error('Follow/unfollow failed:', error);
    }
  };

  const handleFollowersPress = () => {
    // Navigate to followers list
    logger.debug('Followers pressed');
  };

  const handleFollowingPress = () => {
    // Navigate to following list
    logger.debug('Following pressed');
  };

  // Convert API videos to the format expected by VideoGrid
  const formatVideosForGrid = (videos: any[] | undefined) => {
    if (!videos) return [];
    return videos.map(video => ({
      id: video.id,
      thumbnail: video.thumbnail,
      uri: video.url,
      views: video.stats?.views?.toString() || '0',
      likes: video.stats?.likes || 0,
    }));
  };

  const publicVideosForGrid = formatVideosForGrid(publicVideos);
  const privateVideosForGrid = formatVideosForGrid(privateVideos);
  // Don't show liked videos for other users' profiles
  const likedVideosForGrid = isOwnProfile ? [] : [];

  // Calculate total likes from all user's videos
  const totalLikes = (publicVideos || []).reduce((total, video) => {
    return total + (video.stats?.likes || 0);
  }, 0) + (isOwnProfile ? (privateVideos || []).reduce((total, video) => {
    return total + (video.stats?.likes || 0);
  }, 0) : 0);

  // Use Redux follower count if available (for real-time updates), otherwise fall back to API data
  const displayFollowerCount = reduxFollowerCount > 0 ? reduxFollowerCount : (profileUser?.followers_count || 0);
  const displayFollowingCount = profileUser?.following_count || 0;

  const getVideosForTab = () => {
    switch (activeTab) {
      case 'videos':
        return publicVideosForGrid;
      case 'liked':
        return likedVideosForGrid;
      case 'private':
        return privateVideosForGrid;
      default:
        return [];
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ProfileHeader
        user={{
          id: profileUser.id,
          username: profileUser.username || 'user',
          full_name: profileUser.full_name || 'User',
          avatar_url: profileUser.avatar_url,
          bio: profileUser.bio,
          followers: displayFollowerCount,
          following: displayFollowingCount,
          likes: totalLikes,
          isOwnProfile: isOwnProfile,
          user_tag: (profileUser as any).user_tag,
          is_verified: (profileUser as any).is_verified,
          banner_image_url: (profileUser as any).banner_image_url,
          profile_picture_url: (profileUser as any).profile_picture_url,
          isFollowing: isFollowing,
        }}
        onEditProfile={handleEditProfile}
        onFollow={handleFollow}
      />
      <ProfileStats
        followers={displayFollowerCount}
        following={displayFollowingCount}
        likes={totalLikes}
        videos={publicVideos?.length || 0}
        onPressFollowers={handleFollowersPress}
        onPressFollowing={handleFollowingPress}
      />
      <ProfileTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        videosCount={publicVideosForGrid.length}
        likedCount={isOwnProfile ? likedVideosForGrid.length : 0}
        privateCount={isOwnProfile ? privateVideosForGrid.length : 0}
      />
      <VideoGrid
        videos={getVideosForTab()}
        onVideoPress={(videoId) => {
          logger.debug('Open video:', videoId);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default UserProfileScreen;
