import React from 'react';
import {View, StyleSheet, FlatList, RefreshControl, Alert} from 'react-native';
import {useTheme} from '../../contexts/ThemeContext';
import {useNavigation} from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import UserListItem from '../../components/common/UserListItem';
import Button from '../../components/common/Button';
import {spacing} from '../../styles/spacing';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';
import {
  useGetBlockedUsersQuery,
  useUnblockUserMutation,
} from '../../store/api/userManagementApi';

/**
 * Blocked Accounts Screen
 * Manage users that have been blocked
 */
const BlockedAccountsScreen = () => {
  const {theme} = useTheme();
  const navigation = useNavigation();

  const {
    data: blockedUsers = [],
    isLoading,
    refetch,
  } = useGetBlockedUsersQuery();
  const [unblockUser] = useUnblockUserMutation();

  const handleUnblockUser = (userId: string, username: string) => {
    Alert.alert(
      'Unblock User',
      `Are you sure you want to unblock @${username}? They will be able to see your content and interact with you again.`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Unblock',
          style: 'destructive',
          onPress: async () => {
            try {
              await unblockUser(userId).unwrap();
              Alert.alert(
                'User Unblocked',
                `@${username} has been unblocked successfully.`,
                [{text: 'OK'}],
              );
              refetch();
            } catch (error) {
              Alert.alert(
                'Error',
                'Failed to unblock user. Please try again.',
                [{text: 'OK'}],
              );
            }
          },
        },
      ],
    );
  };

  const handleRefresh = async () => {
    try {
      await refetch();
    } catch (error) {
      logger.error('Failed to refresh blocked users:', error);
    }
  };

  const renderBlockedUser = ({item}: {item: any}) => (
    <View
      style={[styles.userContainer, {borderBottomColor: theme.colors.divider}]}>
      <UserListItem
        user={item}
        showFollowButton={false}
        onPress={() => {
          // Navigate to user profile or show options
          logger.debug('View blocked user profile:', item.id);
        }}
      />
      <View style={styles.actionContainer}>
        <Text style={[styles.blockedDate, {color: theme.colors.textSecondary}]}>
          Blocked {new Date(item.blocked_at).toLocaleDateString()}
        </Text>
        <Button
          title="Unblock"
          variant="outline"
          size="sm"
          onPress={() => handleUnblockUser(item.id, item.username)}
          style={styles.unblockButton}
        />
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="shield-checkmark-outline"
        size={64}
        color={theme.colors.textSecondary}
      />
      <Text style={[styles.emptyTitle, {color: theme.colors.text}]}>
        No Blocked Accounts
      </Text>
      <Text style={[styles.emptySubtitle, {color: theme.colors.textSecondary}]}>
        When you block someone, they won't be able to see your profile, videos,
        or send you messages.
      </Text>
    </View>
  );

  return (
    <SafeAreaWrapper>
      <View
        style={[styles.container, {backgroundColor: theme.colors.background}]}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, {color: theme.colors.text}]}>
            Blocked Accounts
          </Text>
          <Text
            style={[
              styles.headerSubtitle,
              {color: theme.colors.textSecondary},
            ]}>
            {blockedUsers.length} blocked account
            {blockedUsers.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <FlatList
          data={blockedUsers}
          renderItem={renderBlockedUser}
          keyExtractor={item => item.id}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={
            blockedUsers.length === 0 ? styles.emptyContainer : undefined
          }
        />

        <View style={styles.footer}>
          <Text
            style={[styles.footerText, {color: theme.colors.textSecondary}]}>
            Blocked users cannot see your profile, videos, or send you messages.
            You can unblock them anytime.
          </Text>
        </View>
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: spacing.base,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  userContainer: {
    paddingVertical: spacing.sm,
    borderBottomWidth: 0.5,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.base,
    paddingTop: spacing.sm,
  },
  blockedDate: {
    fontSize: 12,
  },
  unblockButton: {
    minWidth: 80,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing['2xl'],
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: spacing.base,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  footer: {
    padding: spacing.base,
    borderTopWidth: 0.5,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default BlockedAccountsScreen;
