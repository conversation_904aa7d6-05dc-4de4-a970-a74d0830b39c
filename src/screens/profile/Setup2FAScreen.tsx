import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Clipboard } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { spacing } from '../../styles/spacing';
import { supabase } from '../../integrations/supabase/client';
import Ionicons from 'react-native-vector-icons/Ionicons';
import QRCode from 'react-native-qrcode-svg';
import logger from '../../utils/logger';

/**
 * Setup Two-Factor Authentication Screen
 * Guides users through enabling 2FA with TOTP
 */
const Setup2FAScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const [step, setStep] = useState<'setup' | 'verify'>('setup');
  const [qrCodeUri, setQrCodeUri] = useState('');
  const [secret, setSecret] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  useEffect(() => {
    setupTOTP();
  }, []);

  const setupTOTP = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {throw new Error('No authenticated user');}

      // Enroll for TOTP
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
        friendlyName: 'TS1 App',
      });

      if (error) {throw error;}

      setQrCodeUri(data.totp.qr_code);
      setSecret(data.totp.secret);
    } catch (error: any) {
      logger.error('TOTP setup error:', error);
      Alert.alert('Setup Error', error.message || 'Failed to setup 2FA');
      navigation.goBack();
    }
  };

  const verifyAndEnable2FA = async () => {
    if (!verificationCode.trim() || verificationCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {throw new Error('No authenticated user');}

      // Verify the TOTP code
      const { data, error } = await supabase.auth.mfa.challengeAndVerify({
        factorId: user.factors?.[0]?.id || '',
        code: verificationCode,
      });

      if (error) {throw error;}

      // Update security settings
      await supabase
        .from('security_settings')
        .upsert({
          user_id: user.id,
          two_factor_enabled: true,
          backup_codes_generated: true,
          updated_at: new Date().toISOString(),
        });

      // Generate backup codes
      const codes = generateBackupCodes();
      setBackupCodes(codes);

      // Log security activity
      await logSecurityActivity('two_factor_enabled');

      Alert.alert(
        '2FA Enabled Successfully!',
        'Two-factor authentication has been enabled for your account. Please save your backup codes.',
        [{ text: 'OK' }]
      );

    } catch (error: any) {
      logger.error('2FA verification error:', error);
      setError(error.message || 'Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateBackupCodes = (): string[] => {
    const codes = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  };

  const copySecret = () => {
    Clipboard.setString(secret);
    Alert.alert('Copied', 'Secret key copied to clipboard');
  };

  const copyBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    Clipboard.setString(codesText);
    Alert.alert('Copied', 'Backup codes copied to clipboard');
  };

  const logSecurityActivity = async (activityType: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {return;}

      await supabase
        .from('security_activities')
        .insert({
          user_id: user.id,
          activity_type: activityType,
          ip_address: '0.0.0.0',
          device_info: 'Mobile App',
          location: 'Unknown',
        });
    } catch (error) {
      logger.error('Failed to log security activity:', error);
    }
  };

  const handleComplete = () => {
    navigation.goBack();
  };

  if (backupCodes.length > 0) {
    return (
      <SafeAreaWrapper>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.colors.background }]}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons
                name="checkmark-circle"
                size={64}
                color={theme.colors.success || '#4CAF50'}
              />
            </View>

            <Text style={[styles.title, { color: theme.colors.text }]}>
              2FA Setup Complete!
            </Text>

            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              Your backup codes are listed below. Save them in a secure location - you'll need them if you lose access to your authenticator app.
            </Text>

            <View style={[styles.backupCodesContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
              <View style={styles.backupCodesHeader}>
                <Text style={[styles.backupCodesTitle, { color: theme.colors.text }]}>
                  Backup Codes
                </Text>
                <Button
                  title="Copy"
                  variant="text"
                  onPress={copyBackupCodes}
                  style={styles.copyButton}
                />
              </View>

              <View style={styles.codesList}>
                {backupCodes.map((code, index) => (
                  <Text key={index} style={[styles.backupCode, { color: theme.colors.text }]}>
                    {code}
                  </Text>
                ))}
              </View>
            </View>

            <View style={styles.warningContainer}>
              <Ionicons name="warning" size={20} color={theme.colors.warning || '#FF9800'} />
              <Text style={[styles.warningText, { color: theme.colors.textSecondary }]}>
                Store these codes safely. Each code can only be used once.
              </Text>
            </View>

            <Button
              title="Complete Setup"
              onPress={handleComplete}
              style={styles.completeButton}
            />
          </View>
        </ScrollView>
      </SafeAreaWrapper>
    );
  }

  if (step === 'verify') {
    return (
      <SafeAreaWrapper>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.colors.background }]}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons
                name="shield-checkmark"
                size={64}
                color={theme.colors.primary}
              />
            </View>

            <Text style={[styles.title, { color: theme.colors.text }]}>
              Verify Setup
            </Text>

            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              Enter the 6-digit code from your authenticator app to complete the setup.
            </Text>

            <View style={styles.inputContainer}>
              <Input
                label="Verification Code"
                value={verificationCode}
                onChangeText={(text) => {
                  setVerificationCode(text.replace(/\D/g, '').slice(0, 6));
                  setError('');
                }}
                placeholder="000000"
                keyboardType="number-pad"
                maxLength={6}
                error={error}
                textAlign="center"
                style={styles.codeInput}
              />
            </View>

            <Button
              title="Verify & Enable 2FA"
              onPress={verifyAndEnable2FA}
              loading={isLoading}
              disabled={verificationCode.length !== 6}
              style={styles.verifyButton}
            />

            <Button
              title="Back to Setup"
              variant="outline"
              onPress={() => setStep('setup')}
              style={styles.backButton}
            />
          </View>
        </ScrollView>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <Ionicons
              name="shield-outline"
              size={64}
              color={theme.colors.primary}
            />
          </View>

          <Text style={[styles.title, { color: theme.colors.text }]}>
            Setup Two-Factor Authentication
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Scan the QR code below with your authenticator app (Google Authenticator, Authy, etc.) or manually enter the secret key.
          </Text>

          {qrCodeUri && (
            <View style={styles.qrContainer}>
              <QRCode
                value={qrCodeUri}
                size={200}
                backgroundColor="white"
                color="black"
              />
            </View>
          )}

          <View style={[styles.secretContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
            <Text style={[styles.secretLabel, { color: theme.colors.text }]}>
              Manual Entry Key:
            </Text>
            <View style={styles.secretRow}>
              <Text style={[styles.secretText, { color: theme.colors.text }]}>
                {secret}
              </Text>
              <Button
                title="Copy"
                variant="text"
                onPress={copySecret}
                style={styles.copySecretButton}
              />
            </View>
          </View>

          <View style={styles.instructionsContainer}>
            <Text style={[styles.instructionsTitle, { color: theme.colors.text }]}>
              Setup Instructions:
            </Text>
            <Text style={[styles.instructionsText, { color: theme.colors.textSecondary }]}>
              1. Install an authenticator app on your device{'\n'}
              2. Scan the QR code or enter the secret key manually{'\n'}
              3. Enter the 6-digit code from your app to verify{'\n'}
              4. Save your backup codes in a secure location
            </Text>
          </View>

          <Button
            title="I've Added the Account"
            onPress={() => setStep('verify')}
            style={styles.nextButton}
          />

          <Button
            title="Cancel"
            variant="outline"
            onPress={() => navigation.goBack()}
            style={styles.cancelButton}
          />
        </View>
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: spacing.base,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    padding: spacing.base,
  },
  secretContainer: {
    padding: spacing.base,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: spacing.xl,
  },
  secretLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: spacing.sm,
  },
  secretRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  secretText: {
    fontSize: 12,
    fontFamily: 'monospace',
    flex: 1,
    marginRight: spacing.sm,
  },
  copySecretButton: {
    paddingHorizontal: spacing.sm,
  },
  instructionsContainer: {
    marginBottom: spacing.xl,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  codeInput: {
    fontSize: 24,
    fontWeight: '600',
    letterSpacing: 8,
  },
  nextButton: {
    marginBottom: spacing.base,
  },
  verifyButton: {
    marginBottom: spacing.base,
  },
  backButton: {
    marginBottom: spacing.base,
  },
  cancelButton: {
    marginBottom: spacing.base,
  },
  backupCodesContainer: {
    padding: spacing.base,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: spacing.xl,
  },
  backupCodesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  backupCodesTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  copyButton: {
    paddingHorizontal: spacing.sm,
  },
  codesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  backupCode: {
    fontSize: 12,
    fontFamily: 'monospace',
    width: '48%',
    marginBottom: spacing.xs,
    padding: spacing.xs,
    textAlign: 'center',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xl,
    padding: spacing.sm,
  },
  warningText: {
    fontSize: 14,
    marginLeft: spacing.sm,
    flex: 1,
  },
  completeButton: {
    marginBottom: spacing.base,
  },
});

export default Setup2FAScreen;
