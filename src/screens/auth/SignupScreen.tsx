import React, { useState } from 'react';
import { View, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Text} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { normalize } from '../../utils/responsive';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthStackParamList } from '../../navigation/types';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

type SignupScreenProps = {
  navigation: StackNavigationProp<AuthStackParamList, 'Signup'>;
};

const SignupScreen: React.FC<SignupScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  logger.debug('SignupScreen mounted');
  const [form, setForm] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [secureTextEntry, setSecureTextEntry] = useState(true);

  const handleChange = (name: string, value: string) => {
    setForm({ ...form, [name]: value });
    // Clear error when typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!form.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (form.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    if (!form.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^\S+@\S+\.\S+$/.test(form.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!form.password) {
      newErrors.password = 'Password is required';
    } else if (form.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      setLoading(true);
      // Here you would typically call your signup API
      logger.debug('Form submitted:', form);
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        navigation.navigate('Welcome');
      }, 1500);
    }
  };

  const toggleSecureEntry = () => {
    setSecureTextEntry(!secureTextEntry);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      keyboardVerticalOffset={Platform.OS === 'ios' ? normalize(60) : 0}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Create Account
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Join the TS1 community
          </Text>
        </View>

        <View style={styles.form}>
          <Input
            label="Username"
            placeholder="Enter your username"
            value={form.username}
            onChangeText={(text) => handleChange('username', text)}
            error={errors.username}
            autoCapitalize="none"
            leftIcon={<Ionicons name="person-outline" size={normalize(20)} color={theme.colors.textSecondary} />}
          />

          <Input
            label="Email"
            placeholder="Enter your email"
            value={form.email}
            onChangeText={(text) => handleChange('email', text)}
            error={errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon={<Ionicons name="mail-outline" size={normalize(20)} color={theme.colors.textSecondary} />}
          />

          <Input
            label="Password"
            placeholder="Enter your password"
            value={form.password}
            onChangeText={(text) => handleChange('password', text)}
            error={errors.password}
            secureTextEntry={secureTextEntry}
            leftIcon={<Ionicons name="lock-closed-outline" size={normalize(20)} color={theme.colors.textSecondary} />}
            rightIcon={
              <Ionicons
                name={secureTextEntry ? 'eye-off-outline' : 'eye-outline'}
                size={normalize(20)}
                color={theme.colors.textSecondary}
                onPress={toggleSecureEntry}
              />
            }
          />

          <Input
            label="Confirm Password"
            placeholder="Confirm your password"
            value={form.confirmPassword}
            onChangeText={(text) => handleChange('confirmPassword', text)}
            error={errors.confirmPassword}
            secureTextEntry={secureTextEntry}
            leftIcon={<Ionicons name="lock-closed-outline" size={normalize(20)} color={theme.colors.textSecondary} />}
          />
        </View>

        <Button
          title="Sign Up"
          onPress={handleSubmit}
          loading={loading}
          disabled={loading}
          fullWidth
          variant="primary"
        />

        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
            Already have an account?
          </Text>
          <View style={styles.loginButton}>
            <Button
              title="Log In"
              onPress={() => navigation.navigate('Login')}
              variant="text"
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: normalize(24),
    paddingBottom: normalize(40),
  },
  header: {
    marginBottom: normalize(32),
    alignItems: 'center',
  },
  title: {
    fontSize: normalize(28),
    fontWeight: 'bold',
    marginBottom: normalize(8),
  },
  subtitle: {
    fontSize: normalize(16),
  },
  form: {
    marginBottom: normalize(24),
  },
  footer: {
    marginTop: normalize(24),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: normalize(14),
  },
  loginButton: {
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
  },
});

export default SignupScreen;
