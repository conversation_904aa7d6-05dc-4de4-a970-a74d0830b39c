import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../hooks/useAuth';
import { setLoading, setError, selectAuthLoading } from '../../store/slices/authSlice';
import { supabase } from '../../integrations/supabase/client';
import { Platform } from 'react-native';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import Text from '../../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import logger from '../../utils/logger';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthStackParamList } from '../../navigation/types';

const LoginScreen = () => {
  const { theme } = useTheme();
  const { login, signInWithGoogle } = useAuth();
  const dispatch = useAppDispatch();
  const navigation = useNavigation<StackNavigationProp<AuthStackParamList>>();
  const isLoading = useAppSelector(selectAuthLoading);

  const [form, setForm] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (name: string, value: string) => {
    setForm(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {setErrors(prev => ({ ...prev, [name]: '' }));}
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!form.email.trim()) {newErrors.email = 'Email is required';}
    else if (!/^\S+@\S+\.\S+$/.test(form.email)) {newErrors.email = 'Please enter a valid email';}
    if (!form.password) {newErrors.password = 'Password is required';}
    else if (form.password.length < 6) {newErrors.password = 'Password must be at least 6 characters';}

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {return;}

    try {
      dispatch(setLoading(true));

      // Attempt login
      const { data, error } = await supabase.auth.signInWithPassword({
        email: form.email,
        password: form.password,
      });

      if (error) {
        throw error;
      }

      // Check if user has 2FA enabled
      if (data.user) {
        const { data: securitySettings } = await supabase
          .from('security_settings')
          .select('two_factor_enabled')
          .eq('user_id', data.user.id)
          .single();

        if (securitySettings?.two_factor_enabled) {
          // Send 2FA code via SMS if user has phone number
          const { data: userData } = await supabase
            .from('users')
            .select('phone_number')
            .eq('auth_user_id', data.user.id)
            .single();

          if (userData?.phone_number) {
            // Send SMS 2FA code
            const { send2FAVerification } = await import('../../services/twilio/verification');
            const result = await send2FAVerification(userData.phone_number, 'sms');

            if (!result.success) {
              logger.error('Failed to send 2FA SMS:', result.error);
              // Continue to 2FA screen anyway, user can try resend
            }
          }

          // Navigate to 2FA screen
          navigation.navigate('TwoFactor', {
            email: form.email,
            password: form.password,
            session: data.session,
          });
          return;
        }

        // Log successful login
        await logSecurityActivity(data.user.id, 'login_success');
      }

      // Regular login success - navigation handled by auth hook
      await login(form.email, form.password);

    } catch (error) {
      let errorMessage = 'Login failed. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
        // Handle specific auth errors
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password';
        }
      }
      dispatch(setError(errorMessage));
      Alert.alert('Login Error', errorMessage);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      dispatch(setLoading(true));
      await signInWithGoogle();
      // Navigation is handled automatically by the auth listener
    } catch (error) {
      logger.error('Google Sign-In error:', error);
      // Error is already handled in the signInWithGoogle function
    } finally {
      dispatch(setLoading(false));
    }
  };

  const logSecurityActivity = async (userId: string, activityType: string) => {
    try {
      const deviceInfo = Platform.OS === 'ios' ? 'iOS Device' : 'Android Device';

      await supabase
        .from('security_activities')
        .insert({
          user_id: userId,
          activity_type: activityType,
          ip_address: '0.0.0.0', // Would get real IP in production
          device_info: deviceInfo,
          location: 'Unknown', // Would use geolocation in production
        });
    } catch (error) {
      logger.error('Failed to log security activity:', error);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text variant="h2" style={[styles.title, { color: theme.colors.text }]}>
        Welcome Back
      </Text>

      <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
        Log in to your TS1 account
      </Text>

      <Input
        label="Email"
        placeholder="Enter your email"
        value={form.email}
        onChangeText={(text) => handleChange('email', text)}
        error={errors.email}
        keyboardType="email-address"
        autoCapitalize="none"
        autoComplete="email"
        textContentType="emailAddress"
        leftIcon={<Ionicons name="mail-outline" size={20} color={theme.colors.textSecondary} />}
      />

      <Input
        label="Password"
        placeholder="Enter your password"
        value={form.password}
        onChangeText={(text) => handleChange('password', text)}
        error={errors.password}
        secureTextEntry
        autoComplete="password"
        textContentType="password"
        leftIcon={<Ionicons name="lock-closed-outline" size={20} color={theme.colors.textSecondary} />}
      />

      <View style={styles.loginButton}>
        <Button
          title="Log In"
          onPress={handleSubmit}
          loading={isLoading}
          disabled={isLoading}
          fullWidth
          variant="primary"
        />
      </View>

      <Button
        title="Forgot Password?"
        onPress={() => navigation.navigate('ForgotPassword')}
        variant="text"
      />

      {/* Divider */}
      <View style={styles.dividerContainer}>
        <View style={[styles.divider, { backgroundColor: theme.colors.border }]} />
        <Text style={[styles.dividerText, { color: theme.colors.textSecondary }]}>
          or continue with
        </Text>
        <View style={[styles.divider, { backgroundColor: theme.colors.border }]} />
      </View>

      {/* Google Sign-In Button */}
      <Button
        title="Continue with Google"
        onPress={handleGoogleSignIn}
        loading={isLoading}
        disabled={isLoading}
        fullWidth
        variant="outline"
        icon={
          <Ionicons
            name="logo-google"
            size={20}
            color={theme.colors.text}
            style={{ marginRight: 8 }}
          />
        }
      />

      {/* Sign Up Link */}
      <View style={styles.signupContainer}>
        <Text style={[styles.signupText, { color: theme.colors.textSecondary }]}>
          Don't have an account?{' '}
        </Text>
        <Button
          title="Sign Up"
          onPress={() => navigation.navigate('Signup')}
          variant="text"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 24,
  },
  forgotPasswordButton: {
    marginTop: 16,
    alignSelf: 'center',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  signupText: {
    fontSize: 14,
  },
});

export default LoginScreen;
