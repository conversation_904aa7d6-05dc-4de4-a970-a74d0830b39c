import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthStackParamList } from '../../navigation/types';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { spacing } from '../../styles/spacing';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';
import { send2FAVerification, verify2FACode } from '../../services/twilio/verification';
import Ionicons from 'react-native-vector-icons/Ionicons';

type TwoFactorScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'TwoFactor'>;

interface RouteParams {
  email: string;
  password: string;
  session?: any;
}

/**
 * Two-Factor Authentication Screen
 * Handles TOTP verification for users with 2FA enabled
 */
const TwoFactorScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<TwoFactorScreenNavigationProp>();
  const route = useRoute();
  const { email, password, session } = route.params as RouteParams;

  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);

  const handleVerifyCode = async () => {
    if (!verificationCode.trim() || verificationCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      // Get user's phone number for SMS 2FA verification
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {throw new Error('No authenticated user found');}

      // Get user's phone number from database
      const { data: userData } = await supabase
        .from('users')
        .select('phone_number')
        .eq('auth_user_id', user.id)
        .single();

      if (userData?.phone_number) {
        // Use Twilio Verify for SMS-based 2FA
        const result = await verify2FACode(userData.phone_number, verificationCode);

        if (!result.success || !result.valid) {
          throw new Error(result.error || 'Invalid verification code');
        }
      } else {
        // Fallback to TOTP verification for authenticator apps
        // If you intend to use TOTP, use the appropriate method or type supported by Supabase.
        // For email OTP:
        const { data, error: verifyError } = await supabase.auth.verifyOtp({
          email: email,
          token: verificationCode,
          type: 'email',
        });
        // If you need TOTP, check Supabase docs for the correct method or type.

        if (verifyError) {
          throw verifyError;
        }
      }

      // Log security activity
      await logSecurityActivity('2fa_login_success');

      // Navigate to main app
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' as any }],
      });

    } catch (error: any) {
      logger.error('2FA verification error:', error);
      await logSecurityActivity('2fa_login_failed');
      setError(error.message || 'Invalid verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);
    setError('');

    try {
      // Get user's phone number for SMS 2FA
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {throw new Error('No authenticated user found');}

      const { data: userData } = await supabase
        .from('users')
        .select('phone_number')
        .eq('auth_user_id', user.id)
        .single();

      if (userData?.phone_number) {
        // Send new SMS code via Twilio Verify
        const result = await send2FAVerification(userData.phone_number, 'sms');

        if (result.success) {
          Alert.alert(
            'Code Sent',
            'A new verification code has been sent to your phone.',
            [{ text: 'OK' }]
          );
        } else {
          throw new Error(result.error || 'Failed to send verification code');
        }
      } else {
        // For TOTP (authenticator app), codes are generated locally
        Alert.alert(
          'Code Information',
          'If you\'re using an authenticator app, the code refreshes automatically every 30 seconds.',
          [{ text: 'OK' }]
        );
      }
    } catch (error: any) {
      logger.error('Resend code error:', error);
      setError(error.message || 'Failed to resend code. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const logSecurityActivity = async (activityType: string) => {
    try {
      // Get device info (simplified for demo)
      const deviceInfo = Platform.OS === 'ios' ? 'iOS Device' : 'Android Device';

      const { error } = await supabase
        .from('security_activities')
        .insert({
          user_id: session?.user?.id,
          activity_type: activityType,
          ip_address: '0.0.0.0', // Would get real IP in production
          device_info: deviceInfo,
          location: 'Unknown', // Would use geolocation in production
        });

      if (error) {
        logger.error('Failed to log security activity:', error);
      }
    } catch (error) {
      logger.error('Security logging error:', error);
    }
  };

  return (
    <SafeAreaWrapper>
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <Ionicons
              name="shield-checkmark"
              size={64}
              color={theme.colors.primary}
            />
          </View>

          <Text style={[styles.title, { color: theme.colors.text }]}>
            Two-Factor Authentication
          </Text>

          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            Enter the 6-digit code from your authenticator app to complete your login.
          </Text>

          <View style={styles.inputContainer}>
            <Input
              label="Verification Code"
              value={verificationCode}
              onChangeText={(text) => {
                setVerificationCode(text.replace(/\D/g, '').slice(0, 6));
                setError('');
              }}
              placeholder="000000"
              keyboardType="number-pad"
              maxLength={6}
              error={error}
              textAlign="center"
              style={styles.codeInput}
              leftIcon={<Ionicons name="key-outline" size={20} color={theme.colors.textSecondary} />}
            />
          </View>

          <Button
            title="Verify Code"
            onPress={handleVerifyCode}
            loading={isLoading}
            disabled={verificationCode.length !== 6}
            style={styles.verifyButton}
          />

          <Button
            title="Resend Code"
            variant="outline"
            onPress={handleResendCode}
            loading={resendLoading}
            style={styles.resendButton}
          />

          <View style={styles.helpContainer}>
            <Text style={[styles.helpTitle, { color: theme.colors.text }]}>
              Having trouble?
            </Text>
            <Text style={[styles.helpText, { color: theme.colors.textSecondary }]}>
              • Make sure your device time is correct{'\n'}
              • Check your authenticator app for the latest code{'\n'}
              • Contact support if you've lost access to your authenticator
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Button
            title="Back to Login"
            variant="text"
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.base,
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  codeInput: {
    fontSize: 24,
    fontWeight: '600',
    letterSpacing: 8,
  },
  verifyButton: {
    marginBottom: spacing.base,
  },
  resendButton: {
    marginBottom: spacing.xl,
  },
  helpContainer: {
    padding: spacing.base,
    backgroundColor: 'rgba(37, 244, 238, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(37, 244, 238, 0.2)',
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: spacing.base,
  },
  backButton: {
    marginTop: spacing.sm,
  },
});

export default TwoFactorScreen;
