import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import SafeAreaWrapper  from '../../components/common/SafeAreaWrapper';
import  Button  from '../../components/common/Button';
import { useTheme } from '../../contexts/ThemeContext';
import  { AuthStackParamList}  from '../../navigation/types';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import logger from '../../utils/logger';

type WelcomeScreenProps = NativeStackScreenProps<AuthStackParamList, 'Welcome'>;

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  logger.debug('WelcomeScreen mounted');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    title: {
      fontSize: theme.fontSizes?.['3xl'] ?? 32,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.fontSizes?.lg ?? 18,
      marginBottom: theme.spacing['2xl'],
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  return (
    <SafeAreaWrapper>
      <View style={styles.container}>
        <Text style={styles.title}>Welcome to TS1</Text>
        <Text style={styles.subtitle}>
          Create, share, and discover amazing short videos
        </Text>
        <View style={styles.buttonContainer}>
          <Button
            title="Get Started"
            onPress={() => navigation.navigate('Signup')}
            fullWidth
          />
          <Button
            title="Sign In"
            onPress={() => navigation.navigate('Login')}
            variant="outline"
            fullWidth
          />
        </View>
      </View>
    </SafeAreaWrapper>
  );
};

export default WelcomeScreen;
