import React, { useState } from 'react';
import { View, StyleSheet, FlatList, Dimensions, Image, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../../components/common/Button';
import { normalize } from '../../utils/responsive';
import { StackNavigationProp } from '@react-navigation/stack';
import { AuthStackParamList } from '../../navigation/types';

type OnboardingScreenProps = {
  navigation: StackNavigationProp<AuthStackParamList, 'Onboarding'>;
};

const { width } = Dimensions.get('window');

const onboardingSlides = [
  {
    id: '1',
    title: 'Discover Amazing Content',
    description: 'Explore a world of short videos tailored just for you',
    image: require('../../assets/images/onboarding_1.png'),
  },
  {
    id: '2',
    title: 'Connect With Creators',
    description: 'Follow your favorite creators and never miss their content',
    image: require('../../assets/images/onboarding_1.png'),
  },
  {
    id: '3',
    title: 'Create Your Own Videos',
    description: 'Express yourself with our easy-to-use creation tools',
    image: require('../../assets/images/onboarding_1.png'),
  },
];

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / width);
    setCurrentIndex(newIndex);
  };

  const handleContinue = () => {
    if (currentIndex < onboardingSlides.length - 1) {
      // Scroll to next slide
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
    } else {
      // Navigate to signup screen
      navigation.navigate('Signup');
    }
  };

  const flatListRef = React.useRef<FlatList>(null);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        ref={flatListRef}
        data={onboardingSlides}
        renderItem={({ item }) => (
          <View style={[styles.slide, { width }]}>
            <View style={styles.imageContainer}>
              <Image source={item.image} style={styles.image} resizeMode="contain" />
            </View>
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>{item.title}</Text>
              <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
                {item.description}
              </Text>
            </View>
          </View>
        )}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id}
      />

      <View style={styles.footer}>
        <View style={styles.pagination}>
          {onboardingSlides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  backgroundColor:
                    index === currentIndex ? theme.colors.primary : theme.colors.border,
                  width: index === currentIndex ? normalize(20) : normalize(8),
                },
              ]}
            />
          ))}
        </View>

        <Button
          title={currentIndex === onboardingSlides.length - 1 ? 'Get Started' : 'Continue'}
          onPress={handleContinue}
          fullWidth
          variant="primary"
        />

        {currentIndex === onboardingSlides.length - 1 && (
          <View style={styles.loginButton}>
            <Button
              title="Already have an account? Login"
              onPress={() => navigation.navigate('Login')}
              variant="text"
            />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  slide: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: normalize(24),
  },
  imageContainer: {
    flex: 0.6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    flex: 0.4,
    alignItems: 'center',
    paddingHorizontal: normalize(16),
  },
  title: {
    fontSize: normalize(24),
    fontWeight: 'bold',
    marginBottom: normalize(12),
    textAlign: 'center',
  },
  description: {
    fontSize: normalize(16),
    textAlign: 'center',
    lineHeight: normalize(24),
  },
  footer: {
    padding: normalize(24),
    paddingBottom: normalize(40),
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalize(24),
  },
  dot: {
    height: normalize(8),
    borderRadius: normalize(4),
    marginHorizontal: normalize(4),
  },
  loginButton: {
    marginTop: normalize(12),
  },
});

export default OnboardingScreen;
