import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

// Components
import VideoPreviewArea from '../../components/video/VideoPreviewArea';
import VideoControlsPanel from '../../components/video/VideoControlsPanel';
import VideoPreviewModal from '../../components/video/VideoPreviewModal';
import { MusicSelector, MusicTrack } from '../../components/video/MusicSelector';
import VideoUploadModal from '../../components/video/VideoUploadModal';

// Hooks
import { useVideoGeneration } from '../../hooks/useVideoGeneration';
import { useTextOverlays } from '../../hooks/useTextOverlays';
import { useAppSelector } from '../../store';
import { selectCurrentUser } from '../../store/slices/authSlice';

import logger from '../../utils/logger';

interface PhotoToVideoEditorProps {
  route: RouteProp<{ params: { photoUri: string } }, 'params'>;
}

export const PhotoToVideoEditor: React.FC<PhotoToVideoEditorProps> = ({ route }) => {
  const { photoUri } = route.params;
  const { theme } = useTheme();
  const navigation = useNavigation();
  const user = useAppSelector(selectCurrentUser);

  // State
  const [selectedMusic, setSelectedMusic] = useState<MusicTrack | null>(null);
  const [showMusicSelector, setShowMusicSelector] = useState(false);
  const [videoDuration, setVideoDuration] = useState(15);
  const [videoQuality, setVideoQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Custom hooks
  const {
    textOverlays,
    selectedOverlayId,
    addTextOverlay,
    updateTextOverlay,
    deleteTextOverlay,
    selectOverlay,
  } = useTextOverlays();

  const {
    isGenerating,
    generationProgress,
    generatedVideoUri,
    ffmpegAvailable,
    generateVideo,
    resetGeneration,
  } = useVideoGeneration({
    photoUri,
    userId: user?.id || '',
  });

  // Handlers
  const handleGeneratePreview = async () => {
    const videoUri = await generateVideo(
      textOverlays,
      selectedMusic,
      videoDuration,
      videoQuality
    );
    
    if (videoUri) {
      setShowPreview(true);
    }
  };

  const handleUploadVideo = () => {
    if (!generatedVideoUri || !user) {
      Alert.alert('Error', 'No video to upload');
      return;
    }
    setShowUploadModal(true);
  };

  const handleUploadComplete = (videoId: string) => {
    logger.debug('Video upload completed:', videoId);
    navigation.goBack();
  };

  const handleBack = () => {
    if (generatedVideoUri) {
      Alert.alert(
        'Discard Changes',
        'Are you sure you want to go back? Your video will be lost.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Discard', 
            style: 'destructive',
            onPress: () => {
              resetGeneration();
              navigation.goBack();
            }
          },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const canGenerate = ffmpegAvailable && !isGenerating;
  const hasContent = textOverlays.length > 0 || selectedMusic;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Create Video
        </Text>
        <TouchableOpacity
          style={[
            styles.generateButton,
            {
              backgroundColor: canGenerate && hasContent
                ? theme.colors.primary
                : theme.colors.border,
            },
          ]}
          onPress={handleGeneratePreview}
          disabled={!canGenerate}
        >
          <Text
            style={[
              styles.generateButtonText,
              {
                color: canGenerate && hasContent
                  ? '#FFFFFF'
                  : theme.colors.textSecondary,
              },
            ]}
          >
            {isGenerating ? 'Generating...' : 'Preview'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Generation Progress */}
      {isGenerating && generationProgress && (
        <View style={[styles.progressContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.progressText, { color: theme.colors.text }]}>
            {generationProgress}
          </Text>
        </View>
      )}

      {/* Preview Area */}
      <VideoPreviewArea
        photoUri={photoUri}
        textOverlays={textOverlays}
        selectedOverlayId={selectedOverlayId}
        onUpdateOverlay={updateTextOverlay}
        onSelectOverlay={selectOverlay}
        onDeleteOverlay={deleteTextOverlay}
      />

      {/* Controls Panel */}
      <VideoControlsPanel
        selectedMusic={selectedMusic}
        videoDuration={videoDuration}
        videoQuality={videoQuality}
        onAddText={addTextOverlay}
        onSelectMusic={() => setShowMusicSelector(true)}
        onDurationChange={setVideoDuration}
        onQualityChange={setVideoQuality}
      />

      {/* Music Selector Modal */}
      <MusicSelector
        visible={showMusicSelector}
        onClose={() => setShowMusicSelector(false)}
        onSelectMusic={setSelectedMusic}
        selectedTrack={selectedMusic}
      />

      {/* Video Preview Modal */}
      <VideoPreviewModal
        visible={showPreview}
        videoUri={generatedVideoUri}
        onClose={() => setShowPreview(false)}
        onPublish={handleUploadVideo}
      />

      {/* Video Upload Modal */}
      <VideoUploadModal
        visible={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        videoUri={generatedVideoUri || ''}
        duration={videoDuration}
        userId={user?.id || ''}
        onUploadComplete={handleUploadComplete}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  generateButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
  },
  generateButtonText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  progressContainer: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  progressText: {
    fontSize: normalize(14),
    textAlign: 'center',
  },
});

export default PhotoToVideoEditor;
