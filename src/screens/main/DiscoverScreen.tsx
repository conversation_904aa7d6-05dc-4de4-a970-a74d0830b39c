import React, { useState, useEffect } from 'react';
import { View, StyleSheet, StatusBar, SafeAreaView, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import SearchBar from '../../components/search/SearchBar';
import TrendingTopics from '../../components/search/TrendingTopics';
import UserSuggestions from '../../components/search/UserSuggestions';
import SearchResults from '../../components/search/SearchResults';
import logger from '../../utils/logger';
import Text from '../../components/common/Text';
import { mockVideos } from '../../utils/mockData';
import { normalize } from '../../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');

type DiscoverScreenNavigationProp = StackNavigationProp<MainStackParamList>;

const DiscoverScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<DiscoverScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [trendingTopics] = useState([
    '#fyp', '#viral', '#trending', '#dance', '#comedy', '#music', '#art', '#food', '#travel', '#fitness',
  ]);
  const [suggestedUsers] = useState([
    { id: '1', username: 'creator1', full_name: 'Amazing Creator', avatar_url: 'https://via.placeholder.com/50', followers: '1.2M' },
    { id: '2', username: 'artist_pro', full_name: 'Pro Artist', avatar_url: 'https://via.placeholder.com/50', followers: '850K' },
    { id: '3', username: 'dancer_queen', full_name: 'Dance Queen', avatar_url: 'https://via.placeholder.com/50', followers: '2.1M' },
    { id: '4', username: 'chef_master', full_name: 'Master Chef', avatar_url: 'https://via.placeholder.com/50', followers: '650K' },
  ]);

  useEffect(() => {
    StatusBar.setBarStyle('light-content', true);
    StatusBar.setBackgroundColor('#000000', true);
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setIsSearching(query.length > 0);

    if (query.length > 0) {
      // Simulate search results
      const filteredVideos = mockVideos.filter(video =>
        video.description.toLowerCase().includes(query.toLowerCase()) ||
        video.user.username.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(filteredVideos);
    } else {
      setSearchResults([]);
    }
  };

  const handleTrendingTopicPress = (topic: string) => {
    handleSearch(topic.replace('#', ''));
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const handleVideoPress = (videoId: string) => {
    logger.debug('Video pressed:', videoId);
    // navigation.navigate('VideoView', { videoId });
  };

  const renderContent = () => {
    if (isSearching) {
      return (
        <SearchResults
          results={searchResults}
          onUserPress={handleUserPress}
          onVideoPress={handleVideoPress}
        />
      );
    }

    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Trending Topics Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trending</Text>
          <TrendingTopics
            topics={trendingTopics}
            onTopicPress={handleTrendingTopicPress}
          />
        </View>

        {/* Suggested Users Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Suggested for you</Text>
          <UserSuggestions
            users={suggestedUsers}
            onUserPress={handleUserPress}
          />
        </View>

        {/* Popular Videos Grid */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular videos</Text>
          <View style={styles.videoGrid}>
            {mockVideos.slice(0, 6).map((video, index) => (
              <TouchableOpacity
                key={video.id}
                style={styles.videoGridItem}
                onPress={() => handleVideoPress(video.id)}
              >
                <View style={styles.videoThumbnail}>
                  <Text style={styles.videoViews}>{video.views}</Text>
                </View>
                <Text style={styles.videoDescription} numberOfLines={2}>
                  {video.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Discover</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={handleSearch}
          placeholder="Search videos, users, sounds..."
        />
      </View>

      {/* Content */}
      {renderContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    backgroundColor: '#000000',
    paddingTop: normalize(50),
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: normalize(24),
    fontWeight: 'bold',
    textAlign: 'center',
  },
  searchContainer: {
    paddingHorizontal: normalize(16),
    paddingBottom: normalize(16),
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
    backgroundColor: '#000000',
  },
  section: {
    marginBottom: normalize(24),
    paddingHorizontal: normalize(16),
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontSize: normalize(18),
    fontWeight: '600',
    marginBottom: normalize(12),
  },
  videoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  videoGridItem: {
    width: (screenWidth - normalize(48)) / 2,
    marginBottom: normalize(16),
  },
  videoThumbnail: {
    width: '100%',
    height: normalize(200),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: normalize(8),
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    padding: normalize(8),
    marginBottom: normalize(8),
  },
  videoViews: {
    color: '#FFFFFF',
    fontSize: normalize(12),
    fontWeight: '600',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: normalize(6),
    paddingVertical: normalize(2),
    borderRadius: normalize(4),
  },
  videoDescription: {
    color: '#FFFFFF',
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
});

export default DiscoverScreen;
