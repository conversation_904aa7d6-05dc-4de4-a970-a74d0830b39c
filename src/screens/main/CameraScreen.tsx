import { useRef } from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { Camera, useCameraDevices } from 'react-native-vision-camera';

// Custom Hooks
import { useCameraPermissions } from '../../hooks/camera/useCameraPermissions';
import { useCameraSettings } from '../../hooks/camera/useCameraSettings';
import { useCameraRecording } from '../../hooks/camera/useCameraRecording';
import { useMediaGallery } from '../../hooks/camera/useMediaGallery';
import { useCameraUI } from '../../hooks/camera/useCameraUI';

// Components
import { PermissionScreen } from '../../components/camera/PermissionScreen';
import { TopControls } from '../../components/camera/TopControls';
import { SideControls } from '../../components/camera/SideControls';
import { RecordingIndicator } from '../../components/camera/RecordingIndicator';
import { BottomControls } from '../../components/camera/BottomControls';
import { FilterPanel } from '../../components/camera/FilterPanel';
import { EffectsPanel } from '../../components/camera/EffectsPanel';
import { GalleryModal } from '../../components/camera/GalleryModal';

// Constants and Services
import { CAMERA_FILTERS, CAMERA_EFFECTS } from '../../constants/camera';
import { createZoomGestureHandler } from '../../services/camera/gestureHandler';

// Utils
import Text from '../../components/common/Text';

type CameraScreenNavigationProp = StackNavigationProp<MainStackParamList>;

const CameraScreen = () => {
  const navigation = useNavigation<CameraScreenNavigationProp>();
  const camera = useRef<Camera>(null);
  const devices = useCameraDevices();

  // Custom Hooks
  const { permissionsGranted, requestCameraPermission, requestMicPermission } = useCameraPermissions();
  const cameraSettings = useCameraSettings();
  const cameraUI = useCameraUI();
  const mediaGallery = useMediaGallery();

  // Get the appropriate camera device
  const device = devices.find(d => d.position === cameraSettings.cameraType);

  // Camera recording hook
  const cameraRecording = useCameraRecording({
    camera,
    flashMode: cameraSettings.flashMode,
    recordingMode: cameraSettings.recordingMode,
    onVideoRecorded: (videoUri: string) => {
      mediaGallery.handleVideoRecorded(videoUri, cameraRecording.recordingTime);
    },
  });

  // Gesture handlers
  const zoomGesture = createZoomGestureHandler(cameraSettings.zoom, cameraSettings.handleZoomChange);

  // Handlers
  const handleTakePhoto = async () => {
    const photoUri = await cameraRecording.takePhoto();
    if (photoUri) {
      // Navigate to PhotoToVideoEditor instead of just uploading
      navigation.navigate('PhotoToVideoEditor', { photoUri });
    }
  };

  const handleSpeedChange = () => {
    const speeds = [0.5, 1, 2];
    const currentIndex = speeds.indexOf(cameraSettings.speed);
    const nextIndex = (currentIndex + 1) % speeds.length;
    cameraSettings.handleSpeedChange(speeds[nextIndex]);
  };

  const handleRequestPermissions = async () => {
    await requestCameraPermission();
    await requestMicPermission();
  };

  // Show permission screen if not granted
  if (!permissionsGranted) {
    return <PermissionScreen onRequestPermissions={handleRequestPermissions} />;
  }

  // Show loading if no device
  if (!device) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <Text style={styles.loadingText}>Loading Camera...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Camera View */}
      <Camera
        ref={camera}
        style={styles.camera}
        device={device}
        isActive={true}
        video={true}
        photo={true}
        audio={true}
        zoom={cameraSettings.zoom}
        {...zoomGesture.panHandlers}
      />

      {/* Top Controls */}
      <TopControls
        flashMode={cameraSettings.flashMode}
        timerEnabled={cameraSettings.timerEnabled}
        speed={cameraSettings.speed}
        onClose={() => navigation.goBack()}
        onFlashToggle={cameraSettings.handleFlashToggle}
        onTimerToggle={cameraSettings.handleTimerToggle}
        onSpeedChange={handleSpeedChange}
        onSelectFromLibrary={mediaGallery.selectFromLibrary}
      />

      {/* Recording Indicator */}
      <RecordingIndicator
        isRecording={cameraRecording.isRecording}
        recordingTime={cameraRecording.recordingTime}
        recordingProgress={cameraRecording.recordingProgress}
      />

      {/* Side Controls */}
      <SideControls
        showEffects={cameraUI.showEffects}
        showFilters={cameraUI.showFilters}
        onToggleEffects={cameraUI.toggleEffects}
        onToggleFilters={cameraUI.toggleFilters}
        onFlipCamera={cameraSettings.handleFlipCamera}
        onOpenGallery={mediaGallery.openGallery}
      />

      {/* Bottom Controls */}
      <BottomControls
        captureMode={cameraSettings.captureMode}
        isRecording={cameraRecording.isRecording}
        galleryItems={mediaGallery.galleryItems}
        recordButtonScale={cameraRecording.recordButtonScale}
        onCaptureModeChange={cameraSettings.setCaptureMode}
        onStartVideoRecording={cameraRecording.startVideoRecording}
        onStopVideoRecording={cameraRecording.stopVideoRecording}
        onTakePhoto={handleTakePhoto}
        onOpenGallery={mediaGallery.openGallery}
        onFlipCamera={cameraSettings.handleFlipCamera}
      />

      {/* Filter Panel */}
      <FilterPanel
        visible={cameraUI.showFilters}
        filters={CAMERA_FILTERS}
        selectedFilter={cameraSettings.selectedFilter}
        onFilterSelect={cameraSettings.handleFilterSelect}
      />

      {/* Effects Panel */}
      <EffectsPanel
        visible={cameraUI.showEffects}
        effects={CAMERA_EFFECTS}
        selectedEffect={cameraSettings.selectedEffect}
        onEffectSelect={cameraSettings.handleEffectSelect}
      />

      {/* Gallery Modal */}
      <GalleryModal
        visible={mediaGallery.showGallery}
        galleryItems={mediaGallery.galleryItems}
        gallerySlideAnim={mediaGallery.gallerySlideAnim}
        onClose={mediaGallery.closeGallery}
        onSelectItem={(item) => mediaGallery.setSelectedMedia(item)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  camera: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 18,
  },
});

export default CameraScreen;
