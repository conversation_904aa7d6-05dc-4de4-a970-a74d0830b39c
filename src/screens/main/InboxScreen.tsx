import React from 'react';
import { View, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import { useTheme } from '../../contexts/ThemeContext';
import ChatItem from '../../components/messages/ChatItem';
import Loading from '../../components/common/Loading';
import { MainStackParamList } from '../../navigation/types';
import { NavigationProp } from '@react-navigation/native';

import { useGetConversationsQuery } from '../../store/api/messagingApi';
import { ConversationWithDetails } from '../../types/messaging';

const InboxScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();

  // Fetch conversations
  const {
    data: conversations,
    isLoading: conversationsLoading,
    error: conversationsError,
  } = useGetConversationsQuery();

  const handleChatPress = (conversationId: string) => {
    navigation.navigate('Chat', { conversationId });
  };

  const handleViewAllMessages = () => {
    navigation.navigate('Messages');
  };

  const renderMessageItem = ({ item }: { item: ConversationWithDetails }) => (
    <ChatItem
      conversation={item}
      onPress={() => handleChatPress(item.id)}
    />
  );



  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.title, { color: theme.colors.text }]}>Messages</Text>
          <TouchableOpacity onPress={() => navigation.navigate('NewConversation')}>
            <Ionicons name="create-outline" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {conversationsLoading ? (
            <Loading />
          ) : conversationsError ? (
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: theme.colors.primary }]}>Failed to load conversations</Text>
            </View>
          ) : conversations && conversations.length > 0 ? (
            <>
              <FlatList
                data={conversations.slice(0, 5)}
                renderItem={renderMessageItem}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
              />
              {conversations.length > 5 && (
                <TouchableOpacity
                  style={[styles.viewAllButton, { borderColor: theme.colors.border }]}
                  onPress={handleViewAllMessages}
                >
                  <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>View All Messages ({
                    conversations.length
                  })</Text>
                </TouchableOpacity>
              )}
            </>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>No messages yet</Text>
            </View>
          )}
        </View>
      </View>
    </SafeAreaWrapper>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  viewAllButton: {
    margin: 16,
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default InboxScreen;
