import React, { useState, useEffect } from 'react';
import { View, StyleSheet, StatusBar, SafeAreaView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import FeedList from '../../components/feed/FeedList';
import Text from '../../components/common/Text';
import Loading from '../../components/common/Loading';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { normalize } from '../../utils/responsive';
import logger from '../../utils/logger';
import {
  useGetForYouFeedQuery,
  useGetFollowingFeedQuery,
} from '../../store/api/videoApi';

type HomeScreenNavigationProp = StackNavigationProp<MainStackParamList>;

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'foryou' | 'following'>('foryou');

  // Fetch feed data based on active tab
  const { data: forYouVideos, isLoading: forYouLoading, refetch: refetchForYou, error: forYouError } = useGetForYouFeedQuery();
  const { data: followingVideos, isLoading: followingLoading, refetch: refetchFollowing, error: followingError } = useGetFollowingFeedQuery();


  useEffect(() => {
    // Set status bar to light content for video viewing with transparent background
    StatusBar.setBarStyle('light-content', true);
    StatusBar.setBackgroundColor('transparent', true);
    StatusBar.setTranslucent(true);
  }, []);

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const handleVideoPress = (videoId: string) => {
    // Could navigate to video details or handle video interactions
    logger.debug('Video pressed:', videoId);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (activeTab === 'foryou') {
        await refetchForYou();
      } else {
        await refetchFollowing();
      }
    } catch (error) {
      logger.error('Error refreshing feed:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = () => {
    // Simulate loading more videos
    logger.debug('Loading more videos...');
    // In a real app, you'd fetch more videos and append to the list
  };

  const handleLivePress = () => {
    // Navigate to live streaming or live videos
    logger.debug('Live button pressed');
    // navigation.navigate('Live');
  };

  // Format videos for FeedList component
  const formatVideosForFeed = (videos: any[] | undefined) => {
    if (!videos) return [];
    
    // Filter out videos without URLs first
    const validVideos = videos.filter(video => !!video.url);
    
    const formattedVideos = validVideos.map(video => ({
      id: video.id,
      uri: video.url, // FeedItem expects 'uri' but our API returns 'url'
      thumbnail: video.thumbnail,
      description: video.description || video.title || '',
      song: 'Original Sound', // Could be enhanced to use actual audio data
      user: {
        id: video.user.id,
        username: video.user.username || 'user',
        full_name: video.user.full_name || 'User',
        avatar_url: video.user.avatar_url,
      },
      likes: video.stats?.likes || 0,
      comments: video.stats?.comments || 0,
      shares: video.stats?.shares || 0,
      views: video.stats?.views?.toString() || '0',
    }));
    
    return formattedVideos;
  };

  const getVideosForTab = () => {
    switch (activeTab) {
      case 'foryou':
        return formatVideosForFeed(forYouVideos);
      case 'following':
        return formatVideosForFeed(followingVideos);
      default:
        return [];
    }
  };

  const isLoading = activeTab === 'foryou' ? forYouLoading : followingLoading;
  const videos = getVideosForTab();

  const renderContent = () => {
    if (isLoading && videos.length === 0) {
      return <Loading />;
    }

    if (videos.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="videocam-outline" size={normalize(64)} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.emptyTitle}>
            {activeTab === 'following' ? 'No videos from people you follow' : 'No videos available'}
          </Text>
          <Text style={styles.emptySubtitle}>
            {activeTab === 'following' 
              ? 'Follow some users to see their videos here' 
              : 'Check back later for new content'}
          </Text>
        </View>
      );
    }

    return (
      <FeedList
        videos={videos}
        onUserPress={handleUserPress}
        onVideoPress={handleVideoPress}
        onRefresh={handleRefresh}
        onLoadMore={handleLoadMore}
        refreshing={refreshing}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} hidden={false} />

      {/* TikTok-style Header */}
      <View style={styles.header}>
        {/* Live Button */}
        <TouchableOpacity style={styles.liveButton} onPress={handleLivePress}>
          <MaterialIcons
            name="live-tv"
            size={normalize(12)}
            color="rgba(255, 255, 255, 0.9)"
            style={{
              textShadowColor: 'rgba(0, 0, 0, 0.7)',
              textShadowOffset: { width: 0, height: 1 },
              textShadowRadius: 3,
            }}
          />
          <Text style={styles.liveText}>LIVE</Text>
        </TouchableOpacity>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={styles.tab}
            onPress={() => setActiveTab('following')}
          >
            <Text style={[
              styles.tabText,
              activeTab === 'following' && styles.activeTabText,
            ]}>
              Following
            </Text>
            {activeTab === 'following' && <View style={styles.activeIndicator} />}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.tab}
            onPress={() => setActiveTab('foryou')}
          >
            <Text style={[
              styles.tabText,
              activeTab === 'foryou' && styles.activeTabText,
            ]}>
              For You
            </Text>
            {activeTab === 'foryou' && <View style={styles.activeIndicator} />}
          </TouchableOpacity>
        </View>

        {/* Search Button */}
        <TouchableOpacity style={styles.searchButton} onPress={() => logger.debug('Search pressed')}>
          <Ionicons
            name="search"
            size={normalize(18)}
            color="rgba(255, 255, 255, 0.9)"
            style={{
              textShadowColor: 'rgba(0, 0, 0, 0.7)',
              textShadowOffset: { width: 0, height: 1 },
              textShadowRadius: 3,
            }}
          />
        </TouchableOpacity>
      </View>

      {/* Content */}
      {renderContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: 0,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    paddingTop: normalize(44), // Reduced for status bar
    height: normalize(70), // Much smaller
  },
  liveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingHorizontal: normalize(4),
    paddingVertical: normalize(2),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: normalize(4),
  },
  liveText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '600',
    marginLeft: normalize(2),
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: normalize(16),
  },
  tab: {
    alignItems: 'center',
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    marginHorizontal: normalize(4),
  },
  tabText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: normalize(14),
    fontWeight: '400',
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  activeTabText: {
    color: '#FFFFFF',
    fontSize: normalize(15),
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  activeIndicator: {
    width: normalize(20),
    height: normalize(1),
    backgroundColor: '#FFFFFF',
    marginTop: normalize(4),
    borderRadius: normalize(0.5),
  },
  searchButton: {
    padding: normalize(4),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  emptyTitle: {
    color: '#FFFFFF',
    fontSize: normalize(18),
    fontWeight: '600',
    textAlign: 'center',
    marginTop: normalize(16),
    marginBottom: normalize(8),
  },
  emptySubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: normalize(14),
    textAlign: 'center',
    lineHeight: normalize(20),
  },
});

export default HomeScreen;
