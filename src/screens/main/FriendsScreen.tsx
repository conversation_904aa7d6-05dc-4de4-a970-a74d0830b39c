import React, { useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, TextInput, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import SafeAreaWrapper from '../../components/common/SafeAreaWrapper';
import Text from '../../components/common/Text';
import UserListItem from '../../components/common/UserListItem';
import { useTheme } from '../../contexts/ThemeContext';
import { useDiscoverUsersQuery } from '../../store/api/userManagementApi';
import { useDebounce } from '../../hooks/useDebounce';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

type FriendsScreenNavigationProp = StackNavigationProp<MainStackParamList>;

const FriendsScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<FriendsScreenNavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearch = useDebounce(searchQuery, 300);

  const {
    data: users = [],
    isLoading,
    refetch,
    error,
  } = useDiscoverUsersQuery({
    limit: 50,
    search: debouncedSearch,
  });

  // Debug logging
  logger.debug('🔍 FriendsScreen state:', {
    searchQuery,
    debouncedSearch,
    usersCount: users.length,
    isLoading,
    error,
  });

  const handleShowAllUsers = () => {
    setSearchQuery('');
    refetch();
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        Discover Friends
      </Text>
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
        <Ionicons name="search" size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search users..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
      <TouchableOpacity
        style={[styles.showAllButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleShowAllUsers}
      >
        <Text style={[styles.showAllButtonText, { color: 'white' }]}>
          Show All Users ({users.length}) - Testing Mode
        </Text>
      </TouchableOpacity>
      <Text style={[styles.debugText, { color: theme.colors.textSecondary }]}>
        Debug: {searchQuery ? `Searching "${searchQuery}"` : 'Showing all users (including yourself for testing)'}
      </Text>
    </View>
  );

  const renderUser = ({ item }: { item: any }) => (
    <UserListItem
      user={item}
      showFollowButton={true}
      onPress={() => {
        logger.debug('Navigate to user profile:', item.id);
        navigation.navigate('UserProfile', { userId: item.id });
      }}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
        {searchQuery ? 'No users found' : 'Discover new friends'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        {searchQuery
          ? 'Try searching with different keywords'
          : 'Search for users or explore suggested friends'
        }
      </Text>
    </View>
  );

  if (error) {
    return (
      <SafeAreaWrapper>
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          {renderHeader()}
          <View style={styles.errorState}>
            <Ionicons name="alert-circle-outline" size={64} color={theme.colors.error} />
            <Text style={[styles.errorTitle, { color: theme.colors.error }]}>
              Something went wrong
            </Text>
            <Text style={[styles.errorSubtitle, { color: theme.colors.textSecondary }]}>
              Unable to load users. Please try again.
            </Text>
          </View>
        </View>
      </SafeAreaWrapper>
    );
  }

  return (
    <SafeAreaWrapper>
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <FlatList
          data={users}
          renderItem={renderUser}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={refetch}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={users.length === 0 ? styles.emptyContainer : undefined}
        />
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  clearButton: {
    padding: 4,
  },
  showAllButton: {
    marginTop: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  showAllButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  debugText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default FriendsScreen;
