import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, StyleSheet, ScrollView, SafeAreaView, StatusBar, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectCurrentUser, updateUserProfile } from '../../store/slices/authSlice';
import Text from '../../components/common/Text';
import {
  setFollowerCount,
  incrementFollowerCount,
  decrementFollowerCount,
  setFollowing,
} from '../../store/slices/followersSlice';
import { subscribeToFollowers } from '../../store/api/followersApi';
import { useGetCurrentUserCompleteQuery } from '../../store/api/userManagementApi';
import ProfileHeader from '../../components/profile/ProfileHeader';
import ProfileStats from '../../components/profile/ProfileStats';
import ProfileTabs from '../../components/profile/ProfileTabs';
import VideoGrid from '../../components/profile/VideoGrid';
import Loading from '../../components/common/Loading';
import logger from '../../utils/logger';
import {
  useGetUserVideosQuery,
} from '../../store/api/videoApi';
import { useLikedVideos } from '../../hooks/api/useLikedVideos';
import Ionicons from 'react-native-vector-icons/Ionicons';

type ProfileScreenNavigationProp = StackNavigationProp<MainStackParamList>;

interface ProfileScreenProps {
  user?: {
    username: string;
    displayName: string;
  };
  onEditProfile?: () => void;
  onFollow?: () => void;
}

const ProfileScreen: React.FC<ProfileScreenProps> = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const currentUserData = useAppSelector(selectCurrentUser);
  const [activeTab, setActiveTab] = useState<'videos' | 'liked' | 'private'>('videos');

  // Fetch current user data from API
  const { data: apiUserData, isLoading, refetch } = useGetCurrentUserCompleteQuery();

  // Update Redux store when API data is fetched (only if different)
  useEffect(() => {
    if (apiUserData && (!currentUserData || currentUserData.id !== apiUserData.id)) {
      dispatch(updateUserProfile(apiUserData));
    }
  }, [apiUserData, dispatch]);

  // Use current user data from store, fallback to API data
  const profileUser = currentUserData || apiUserData;

  // Memoize user ID to prevent unnecessary re-queries
  const userId = useMemo(() => profileUser?.id, [profileUser?.id]);

  const { data: publicVideos } = useGetUserVideosQuery(
    { userId: userId || '', privacy: 'public' },
    { skip: !userId }
  );
  const { data: privateVideos } = useGetUserVideosQuery(
    { userId: userId || '', privacy: 'private' },
    { skip: !userId }
  );
  // Use the liked videos hook
  const {
    likedVideosForGrid: likedVideosFromHook,
    likedVideosCount,
    isLoading: isLoadingLikedVideos,
    refreshLikedVideos
  } = useLikedVideos({
    enabled: !!profileUser,
  });


  useEffect(() => {
    if (!profileUser || !currentUserData) return;
    dispatch(
      setFollowerCount({
        userId: profileUser.id,
        count: profileUser.followers_count || 0,
      })
    );
    const channel = subscribeToFollowers(
      profileUser.id,
      dispatch,
      {
        increment: id => incrementFollowerCount({ userId: id }),
        decrement: id => decrementFollowerCount({ userId: id }),
        setFollowing: (userId, isFollowing) => setFollowing({ userId, isFollowing }),
      },
      currentUserData.id,
    );
    logger.debug([ProfileScreen.name, 'subscribeToFollowers', channel]);
    logger.debug('Profile user:', profileUser);
    return () => {
      channel.unsubscribe();
    };
  }, [dispatch, profileUser?.id, currentUserData?.id]);

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handleFollowers = () => {
    if (profileUser) {
      navigation.navigate('Followers', { userId: profileUser.id });
    }
  };

  const handleFollowing = () => {
    if (profileUser) {
      navigation.navigate('Following', { userId: profileUser.id });
    }
  };

  const handleShare = () => {
    // Implement share profile functionality
    logger.debug('Share profile');
  };

  const handleNotifications = () => {
    // Navigate to notifications
    logger.debug('Open notifications');
  };

  // Memoize video formatting function to prevent unnecessary re-renders
  const formatVideosForGrid = useCallback((videos: any[] | undefined) => {
    if (!videos || !Array.isArray(videos)) return [];

    return videos.map(video => {
      // Ensure video is an object
      if (!video || typeof video !== 'object') {
        return {
          id: '',
          thumbnail: '',
          uri: '',
          views: '0',
          likes: 0,
        };
      }

      // Safely handle views conversion
      const views = video.stats?.views;
      let viewsString = '0';
      if (views !== null && views !== undefined) {
        const viewsNum = Number(views);
        if (!isNaN(viewsNum) && viewsNum >= 0) {
          viewsString = String(viewsNum);
        }
      }

      // Safely handle likes
      const likes = video.stats?.likes;
      let likesNumber = 0;
      if (likes !== null && likes !== undefined) {
        const likesNum = Number(likes);
        if (!isNaN(likesNum) && likesNum >= 0) {
          likesNumber = likesNum;
        }
      }

      return {
        id: String(video.id || ''),
        thumbnail: String(video.thumbnail || ''),
        uri: String(video.url || ''),
        views: viewsString,
        likes: likesNumber,
      };
    }).filter(video => video.id !== ''); // Remove any videos without valid IDs
  }, []);

  // Memoize formatted video arrays
  const publicVideosForGrid = useMemo(() => formatVideosForGrid(publicVideos), [formatVideosForGrid, publicVideos]);
  const privateVideosForGrid = useMemo(() => formatVideosForGrid(privateVideos), [formatVideosForGrid, privateVideos]);
  // Use the liked videos from the hook instead of formatting here
  const likedVideosForGrid = likedVideosFromHook;

  // Memoize total likes calculation
  const totalLikes = useMemo(() => {
    const publicLikes = (publicVideos || []).reduce((total, video) => {
      return total + (video.stats?.likes || 0);
    }, 0);
    const privateLikes = (privateVideos || []).reduce((total, video) => {
      return total + (video.stats?.likes || 0);
    }, 0);
    return publicLikes + privateLikes;
  }, [publicVideos, privateVideos]);

  // Memoize videos for current tab
  const getVideosForTab = useCallback(() => {
    switch (activeTab) {
      case 'videos':
        return publicVideosForGrid;
      case 'liked':
        return (likedVideosForGrid || []).filter((video): video is NonNullable<typeof video> => Boolean(video)); // Remove any null entries
      case 'private':
        return privateVideosForGrid;
      default:
        return [];
    }
  }, [activeTab, publicVideosForGrid, likedVideosForGrid, privateVideosForGrid]);

  const handleRefreshProfile = () => {
    refetch();
  };

  // Show loading state
  if (isLoading && !profileUser) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Loading />
      </SafeAreaView>
    );
  }

  // Show error state or fallback to mock data
  if (!profileUser) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>
            Unable to load profile
          </Text>
          <TouchableOpacity onPress={handleRefreshProfile} style={styles.retryButton}>
            <Ionicons name="refresh" size={24} color={theme.colors.primary} />
            <Text style={[styles.retryText, { color: theme.colors.primary }]}>
              Retry
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle={theme.colors.background === '#000000' ? 'light-content' : 'dark-content'} />

      {/* Header with navigation */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity onPress={handleNotifications} style={styles.headerButton}>
          <Ionicons name="notifications-outline" size={26} color={theme.colors.text} />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
            <Ionicons name="share-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity onPress={handleSettings} style={[styles.settingsButton, { backgroundColor: theme.colors.primary }]}>
          <Ionicons name="settings" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <ProfileHeader
          user={{
            id: profileUser.id,
            username: profileUser.username || 'user',
            full_name: profileUser.full_name || 'User',
            avatar_url: profileUser.profile_picture_url || profileUser.avatar_url,
            bio: profileUser.bio || '',
            followers: profileUser.followers_count || 0,
            following: profileUser.following_count || 0,
            likes: totalLikes,
            isOwnProfile: true,
            user_tag: (profileUser as any).user_tag,
            is_verified: (profileUser as any).is_verified,
            banner_image_url: (profileUser as any).banner_image_url,
            profile_picture_url: (profileUser as any).profile_picture_url,
          }}
          onEditProfile={handleEditProfile}
          onFollow={() => {}}
        />

        {/* Profile Stats */}
        <ProfileStats
          following={profileUser.following_count || 0}
          followers={profileUser.followers_count || 0}
          likes={totalLikes}
          videos={publicVideos?.length || 0}
          onPressFollowing={handleFollowing}
          onPressFollowers={handleFollowers}
        />

        {/* Profile Tabs */}
        <ProfileTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          videosCount={publicVideosForGrid.length}
          likedCount={likedVideosCount}
          privateCount={privateVideosForGrid.length}
        />

        {/* Video Grid */}
        <VideoGrid
          videos={getVideosForTab()}
          onVideoPress={(videoId) => {
            // Navigate to video view
            logger.debug('Open video:', videoId);
          }}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
  },
  shareButton: {
    padding: 8,
  },
  settingsButton: {
    padding: 10,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  retryText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileScreen;
