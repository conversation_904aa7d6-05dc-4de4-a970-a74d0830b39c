import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Image, StatusBar, ActivityIndicator } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/types';
import { normalize } from '../../utils/responsive';
import Video from 'react-native-video';
import { supabase } from '../../integrations/supabase/client';
import { store } from '../../store';
import { setUser, clearUser } from '../../store/slices/authSlice';
import { User } from '../../store/slices/authSlice';
import { authStorage } from '../../utils/authStorage';
import Text from '../../components/common/Text';
import logger from '../../utils/logger';

const SplashScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Function to fetch user profile data
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Error fetching profile:', error);
        return null;
      }

      return profile;
    } catch (error) {
      logger.error('Profile fetch error:', error);
      return null;
    }
  };

  useEffect(() => {
    const checkAuthState = async () => {
      try {
        logger.debug('Checking authentication state...');

        // First, check stored auth state for faster UX
        const storedAuthState = await authStorage.getAuthState();
        const storedUserData = await authStorage.getUserData();

        // If we have valid stored auth state, restore it immediately
        if (storedAuthState && authStorage.isSessionValid(storedAuthState) && storedUserData) {
          logger.debug('Valid stored auth state found, restoring immediately...');
          store.dispatch(setUser(storedUserData));
          navigation.replace('Main');
          setIsCheckingAuth(false);
          return;
        }

        // If no valid stored state, check with Supabase
        logger.debug('Checking with Supabase for current session...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          logger.error('Error getting session:', error);
          await authStorage.clearAuthData();
          store.dispatch(clearUser());
          navigation.replace('Auth');
          return;
        }

        if (session?.user) {
          logger.debug('User session found, restoring user state...');

          // Fetch the user's profile data
          const profile = await fetchUserProfile(session.user.id);

          // Create user data from session and profile
          const userData: User = {
            id: session.user.id,
            email: session.user.email || '',
            username: session.user.email?.split('@')[0] || 'user',
            full_name: session.user.user_metadata?.full_name || profile?.full_name || session.user.email?.split('@')[0] || 'User',
            avatar_url: profile?.profile_picture_url || session.user.user_metadata?.avatar_url || undefined,
            bio: profile?.bio || undefined,
            website: undefined,
            pronouns: undefined,
            followers_count: 0,
            following_count: 0,
            likes_count: 0,
            videos_count: 0,
            is_private: false,
            is_verified: false,
            created_at: session.user.created_at || new Date().toISOString(),
            updated_at: session.user.updated_at || new Date().toISOString(),
          };

          // Update Redux store and local storage
          store.dispatch(setUser(userData));
          await authStorage.storeAuthState(true, session.user.id);
          await authStorage.storeUserData(userData);

          logger.debug('User authenticated, navigating to Main...');
          navigation.replace('Main');
        } else {
          logger.debug('No user session found, navigating to Auth...');
          // Clear any existing user data
          await authStorage.clearAuthData();
          store.dispatch(clearUser());
          navigation.replace('Auth');
        }
      } catch (error) {
        logger.error('Auth check error:', error);
        // Clear user data and navigate to auth on error
        await authStorage.clearAuthData();
        store.dispatch(clearUser());
        navigation.replace('Auth');
      } finally {
        setIsCheckingAuth(false);
      }
    };

    // Add a minimum splash screen time for better UX
    const minSplashTime = 1500; // 1.5 seconds
    const startTime = Date.now();

    checkAuthState().then(() => {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minSplashTime - elapsedTime);

      if (remainingTime > 0) {
        setTimeout(() => {
          setIsCheckingAuth(false);
        }, remainingTime);
      }
    });
  }, [navigation]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.primary }]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={theme.colors.primary}
      />

      {/* Logo/Brand Video */}
      <Video
        source={{ uri: 'https://nllfhiuufnpqacalkmef.supabase.co/storage/v1/object/public/testvideos//Chiliz_Globe_MQ.webm' }}
        style={styles.video}
        resizeMode="cover"
        repeat
        muted
        onLoad={() => logger.debug('Video loaded')}
        onError={(error) => logger.error('Video error:', error)}
      />

      {/* Loading Indicator */}
      {isCheckingAuth && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FFFFFF" />
          <Text style={styles.loadingText}>Checking authentication...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: theme.colors.primary, // already set in the component
  },
  video: {
    width: 200,   // Set your desired width
    height: 200,  // Set your desired height
    borderRadius: 16, // Optional: rounded corners
    overflow: 'hidden',
  },
  logo: {
    width: normalize(120),
    height: normalize(120),
    tintColor: '#FFFFFF',
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 100,
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 12,
    fontWeight: '500',
  },
});

export default SplashScreen;
