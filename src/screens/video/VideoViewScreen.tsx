import React, { useRef, useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Video from  'react-native-video';
import { useTheme } from '../../contexts/ThemeContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

import type { RouteProp } from '@react-navigation/native';

type VideoViewScreenRouteProp = RouteProp<{ params: { videoUri: string } }, 'params'>;

interface VideoViewScreenProps {
  route: VideoViewScreenRouteProp;
}

const VideoViewScreen = ({ route }: VideoViewScreenProps) => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const videoRef = useRef(null);
  const [paused, setPaused] = useState(false);
  const { videoUri } = route.params;

  return (
    <View style={styles.container}>
      <Video
        ref={videoRef}
        source={{ uri: videoUri }}
        style={styles.video}
        resizeMode="cover"
        repeat
        paused={paused}
      />

      <TouchableOpacity
        style={styles.closeButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="close" size={30} color="white" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.playButton}
        onPress={() => setPaused(!paused)}
      >
        <Ionicons
          name={paused ? 'play' : 'pause'}
          size={50}
          color="white"
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  video: {
    width,
    height,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -25,
    marginTop: -25,
    zIndex: 10,
  },
});

export default VideoViewScreen;
