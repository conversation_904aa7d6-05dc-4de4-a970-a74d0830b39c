/*import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import VideoPlayer from '../../components/video/VideoPlayer';
import VideoInfo from '../../components/feed/VideoInfo';
import InteractionPanel from '../../components/feed/InteractionPanel';

const VideoDetailsScreen = ({ route }) => {
  const { theme } = useTheme();
  const { video } = route.params;

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <VideoPlayer videoUri={video.uri} />
      <VideoInfo
        username={video.user.username}
        description={video.description}
        song={video.song}
      />
      <InteractionPanel
        likes={video.likes}
        comments={video.comments}
        shares={video.shares}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default VideoDetailsScreen;*/
