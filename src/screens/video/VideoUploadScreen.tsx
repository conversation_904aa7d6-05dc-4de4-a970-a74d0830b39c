
import { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { useTheme } from '../../contexts/ThemeContext';
import { useAppSelector } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';
import VideoPlayer from '../../components/video/VideoPlayer';
import VideoUploadModal from '../../components/video/VideoUploadModal';
import Text from '../../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';
import logger from '../../utils/logger';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type VideoUploadScreenRouteProp = RouteProp<
  MainStackParamList,
  'VideoUpload'
>;

type VideoUploadScreenNavigationProp = StackNavigationProp<MainStackParamList>;

interface Props {
  route: VideoUploadScreenRouteProp;
}

const VideoUploadScreen = ({ route }: Props) => {
  const { theme } = useTheme();
  const navigation = useNavigation<VideoUploadScreenNavigationProp>();
  const currentUser = useAppSelector(selectCurrentUser);
  const { videoUri, duration = 0 } = route.params;

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);

  const handleNext = () => {
    if (!currentUser) {
      Alert.alert('Error', 'You must be logged in to upload');
      return;
    }
    setShowUploadModal(true);
  };

  const handleUploadComplete = (videoId: string) => {
    logger.debug('Video uploaded successfully:', videoId);
    // Navigate back to home or feed
    navigation.navigate('Tabs');
  };

  const handleClose = () => {
    Alert.alert(
      'Discard Video?',
      'Are you sure you want to discard this video? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Discard',
          style: 'destructive',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
        <TouchableOpacity onPress={handleClose} style={styles.headerButton}>
          <Ionicons name="close" size={normalize(24)} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Post
        </Text>
        <TouchableOpacity onPress={handleNext} style={styles.headerButton}>
          <Text style={[styles.nextText, { color: theme.colors.primary }]}>
            Next
          </Text>
        </TouchableOpacity>
      </View>

      {/* Video Preview */}
      <View style={styles.videoContainer}>
        <VideoPlayer
          uri={videoUri}
          style={styles.video}
          paused={!isPlaying}

          muted={false}
        />

        {/* Play/Pause Overlay */}
        <TouchableOpacity
          style={styles.playOverlay}
          onPress={() => setIsPlaying(!isPlaying)}
        >
          {!isPlaying && (
            <View style={styles.playButton}>
              <Ionicons name="play" size={normalize(40)} color="#FFFFFF" />
            </View>
          )}
        </TouchableOpacity>

        {/* Video Info */}
        <View style={styles.videoInfo}>
          <Text style={styles.videoDuration}>
            {duration ? `${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}` : '0:00'}
          </Text>
        </View>
      </View>

      {/* Upload Modal */}
      <VideoUploadModal
        visible={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        videoUri={videoUri}
        duration={duration}
        userId={currentUser?.id || ''}
        onUploadComplete={handleUploadComplete}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerButton: {
    padding: normalize(8),
    minWidth: normalize(60),
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  nextText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  videoContainer: {
    flex: 1,
    backgroundColor: '#000000',
    position: 'relative',
  },
  video: {
    width: screenWidth,
    height: screenHeight - normalize(100), // Account for header
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: normalize(80),
    height: normalize(80),
    borderRadius: normalize(40),
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoInfo: {
    position: 'absolute',
    bottom: normalize(20),
    left: normalize(20),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
    borderRadius: normalize(20),
  },
  videoDuration: {
    color: '#FFFFFF',
    fontSize: normalize(14),
    fontWeight: '500',
  },
});

export default VideoUploadScreen;
