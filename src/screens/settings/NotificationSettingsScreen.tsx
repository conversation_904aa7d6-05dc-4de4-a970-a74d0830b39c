import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../../components/common/Text';
import NotificationManager from '../../services/notifications/NotificationManager';
import { normalize } from '../../utils/responsive';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  type: 'toggle' | 'select' | 'time';
  value?: boolean | string;
  options?: string[];
}

const NotificationSettingsScreen: React.FC = () => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const notificationManager = NotificationManager.getInstance();
      const currentSettings = notificationManager.getSettings();
      setSettings(currentSettings || getDefaultSettings());
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDefaultSettings = () => ({
    push_notifications: true,
    email_notifications: true,
    like_notifications: true,
    comment_notifications: true,
    follow_notifications: true,
    live_notifications: true,
    message_notifications: true,
    mention_notifications: true,
    reaction_notifications: true,
    video_upload_notifications: true,
    security_notifications: true,
    marketing_notifications: false,
    quiet_hours_enabled: false,
    quiet_hours_start: '22:00',
    quiet_hours_end: '08:00',
    notification_sound: 'default',
    vibration_enabled: true,
    badge_count_enabled: true,
    preview_enabled: true,
    group_similar: true,
  });

  const updateSetting = async (key: string, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);

      const notificationManager = NotificationManager.getInstance();
      await notificationManager.updateSettings({ [key]: value });
    } catch (error) {
      console.error('Error updating setting:', error);
      Alert.alert('Error', 'Failed to update notification setting');
    }
  };

  const settingSections = [
    {
      title: 'General',
      items: [
        {
          id: 'push_notifications',
          title: 'Push Notifications',
          subtitle: 'Receive notifications on this device',
          icon: 'notifications',
          type: 'toggle',
        },
        {
          id: 'email_notifications',
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          icon: 'mail',
          type: 'toggle',
        },
        {
          id: 'badge_count_enabled',
          title: 'Badge Count',
          subtitle: 'Show unread count on app icon',
          icon: 'radio-button-on',
          type: 'toggle',
        },
        {
          id: 'preview_enabled',
          title: 'Show Previews',
          subtitle: 'Display message content in notifications',
          icon: 'eye',
          type: 'toggle',
        },
      ],
    },
    {
      title: 'Social',
      items: [
        {
          id: 'message_notifications',
          title: 'Messages',
          subtitle: 'New messages and replies',
          icon: 'chatbubble',
          type: 'toggle',
        },
        {
          id: 'like_notifications',
          title: 'Likes',
          subtitle: 'When someone likes your content',
          icon: 'heart',
          type: 'toggle',
        },
        {
          id: 'comment_notifications',
          title: 'Comments',
          subtitle: 'New comments on your videos',
          icon: 'chatbubble-ellipses',
          type: 'toggle',
        },
        {
          id: 'follow_notifications',
          title: 'Followers',
          subtitle: 'When someone follows you',
          icon: 'person-add',
          type: 'toggle',
        },
        {
          id: 'mention_notifications',
          title: 'Mentions',
          subtitle: 'When you are mentioned',
          icon: 'at',
          type: 'toggle',
        },
        {
          id: 'reaction_notifications',
          title: 'Reactions',
          subtitle: 'Reactions to your messages',
          icon: 'happy',
          type: 'toggle',
        },
      ],
    },
    {
      title: 'Content',
      items: [
        {
          id: 'live_notifications',
          title: 'Live Streams',
          subtitle: 'When people you follow go live',
          icon: 'videocam',
          type: 'toggle',
        },
        {
          id: 'video_upload_notifications',
          title: 'New Videos',
          subtitle: 'New videos from people you follow',
          icon: 'cloud-upload',
          type: 'toggle',
        },
      ],
    },
    {
      title: 'System',
      items: [
        {
          id: 'security_notifications',
          title: 'Security',
          subtitle: 'Account security alerts',
          icon: 'shield-checkmark',
          type: 'toggle',
        },
        {
          id: 'marketing_notifications',
          title: 'Marketing',
          subtitle: 'Promotional content and updates',
          icon: 'megaphone',
          type: 'toggle',
        },
      ],
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'vibration_enabled',
          title: 'Vibration',
          subtitle: 'Vibrate for notifications',
          icon: 'phone-portrait',
          type: 'toggle',
        },
        {
          id: 'group_similar',
          title: 'Group Similar',
          subtitle: 'Group similar notifications together',
          icon: 'layers',
          type: 'toggle',
        },
        {
          id: 'notification_sound',
          title: 'Notification Sound',
          subtitle: settings?.notification_sound || 'Default',
          icon: 'volume-high',
          type: 'select',
          options: ['default', 'chime', 'bell', 'pop', 'none'],
        },
      ],
    },
    {
      title: 'Quiet Hours',
      items: [
        {
          id: 'quiet_hours_enabled',
          title: 'Enable Quiet Hours',
          subtitle: 'Silence notifications during specific hours',
          icon: 'moon',
          type: 'toggle',
        },
        {
          id: 'quiet_hours_start',
          title: 'Start Time',
          subtitle: settings?.quiet_hours_start || '22:00',
          icon: 'time',
          type: 'time',
        },
        {
          id: 'quiet_hours_end',
          title: 'End Time',
          subtitle: settings?.quiet_hours_end || '08:00',
          icon: 'time',
          type: 'time',
        },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    const value = settings?.[item.id];

    return (
      <TouchableOpacity
        key={item.id}
        style={[
          styles.settingItem,
          { borderBottomColor: theme.colors.border }
        ]}
        onPress={() => {
          if (item.type === 'toggle') {
            updateSetting(item.id, !value);
          } else if (item.type === 'select') {
            // Handle select options
            showSelectOptions(item);
          } else if (item.type === 'time') {
            // Handle time picker
            showTimePicker(item);
          }
        }}
        disabled={item.id.includes('quiet_hours') && item.id !== 'quiet_hours_enabled' && !settings?.quiet_hours_enabled}
      >
        <View style={styles.settingLeft}>
          <View style={[
            styles.iconContainer,
            { backgroundColor: theme.colors.primary + '20' }
          ]}>
            <Ionicons
              name={item.icon as any}
              size={normalize(20)}
              color={theme.colors.primary}
            />
          </View>
          <View style={styles.settingText}>
            <Text style={[
              styles.settingTitle,
              { 
                color: (item.id.includes('quiet_hours') && item.id !== 'quiet_hours_enabled' && !settings?.quiet_hours_enabled)
                  ? theme.colors.textSecondary
                  : theme.colors.text
              }
            ]}>
              {item.title}
            </Text>
            {item.subtitle && (
              <Text style={[
                styles.settingSubtitle,
                { color: theme.colors.textSecondary }
              ]}>
                {item.subtitle}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.settingRight}>
          {item.type === 'toggle' && (
            <Switch
              value={value}
              onValueChange={(newValue) => updateSetting(item.id, newValue)}
              trackColor={{
                false: theme.colors.border,
                true: theme.colors.primary + '40',
              }}
              thumbColor={value ? theme.colors.primary : theme.colors.surface}
              disabled={item.id.includes('quiet_hours') && item.id !== 'quiet_hours_enabled' && !settings?.quiet_hours_enabled}
            />
          )}
          {(item.type === 'select' || item.type === 'time') && (
            <Ionicons
              name="chevron-forward"
              size={normalize(16)}
              color={theme.colors.textSecondary}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const showSelectOptions = (item: any) => {
    // Implementation for showing select options
    Alert.alert(
      item.title,
      'Select an option',
      item.options?.map((option: string) => ({
        text: option.charAt(0).toUpperCase() + option.slice(1),
        onPress: () => updateSetting(item.id, option),
      })) || []
    );
  };

  const showTimePicker = (item: any) => {
    // Implementation for showing time picker
    Alert.alert('Time Picker', 'Time picker would be implemented here');
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={{ color: theme.colors.text }}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {settingSections.map((section) => (
        <View key={section.title} style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.textSecondary }]}>
            {section.title}
          </Text>
          <View style={[styles.sectionContent, { backgroundColor: theme.colors.surface }]}>
            {section.items.map(renderSettingItem)}
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: normalize(24),
  },
  sectionTitle: {
    fontSize: normalize(14),
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(12),
  },
  sectionContent: {
    marginHorizontal: normalize(20),
    borderRadius: normalize(12),
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  settingLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: normalize(36),
    height: normalize(36),
    borderRadius: normalize(18),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: normalize(12),
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: normalize(16),
    fontWeight: '500',
    lineHeight: normalize(20),
  },
  settingSubtitle: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
    marginTop: normalize(2),
  },
  settingRight: {
    marginLeft: normalize(12),
  },
});

export default NotificationSettingsScreen;
