import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import {
  TextOverlay,
  VideoGenerationOptions,
  generateVideoFromPhoto,
  validateFFmpegAvailability,
  cleanupTempFiles,
} from '../utils/videoGenerator';
import { MusicTrack } from '../components/video/MusicSelector';
import logger from '../utils/logger';

interface UseVideoGenerationProps {
  photoUri: string;
  userId: string;
}

export const useVideoGeneration = ({ photoUri, userId }: UseVideoGenerationProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [generatedVideoUri, setGeneratedVideoUri] = useState<string | null>(null);
  const [ffmpegAvailable, setFFmpegAvailable] = useState<boolean | null>(null);

  // Check FFmpeg availability on mount
  useEffect(() => {
    const checkFFmpeg = async () => {
      try {
        const available = await validateFFmpegAvailability();
        setFFmpegAvailable(available);
        if (!available) {
          Alert.alert(
            'FFmpeg Not Available',
            'Video generation requires FFmpeg. Some features may not work properly.',
            [{ text: 'OK' }]
          );
        }
      } catch (error) {
        logger.error('FFmpeg validation error:', error);
        setFFmpegAvailable(false);
      }
    };

    checkFFmpeg();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (generatedVideoUri) {
        cleanupTempFiles([generatedVideoUri]);
      }
    };
  }, [generatedVideoUri]);

  const generateVideo = useCallback(async (
    textOverlays: TextOverlay[],
    selectedMusic: MusicTrack | null,
    videoDuration: number,
    videoQuality: 'low' | 'medium' | 'high'
  ): Promise<string | null> => {
    if (!userId) {
      Alert.alert('Error', 'You must be logged in to create videos');
      return null;
    }

    if (!ffmpegAvailable) {
      Alert.alert('Error', 'Video generation is not available on this device');
      return null;
    }

    try {
      setIsGenerating(true);
      setGenerationProgress('Initializing...');

      // Validate inputs
      if (textOverlays.length === 0 && !selectedMusic) {
        const proceed = await new Promise<boolean>((resolve) => {
          Alert.alert(
            'No Content Added',
            'You haven\'t added any text or music. Do you want to create a basic video?',
            [
              { text: 'Cancel', onPress: () => resolve(false) },
              { text: 'Continue', onPress: () => resolve(true) },
            ]
          );
        });

        if (!proceed) {
          setIsGenerating(false);
          setGenerationProgress('');
          return null;
        }
      }

      // Prepare generation options
      const options: VideoGenerationOptions = {
        photoPath: photoUri,
        outputPath: '', // Will be generated automatically
        duration: videoDuration,
        quality: videoQuality,
        textOverlays,
        musicPath: selectedMusic?.file_url,
        musicVolume: selectedMusic ? 0.7 : 0,
        originalAudioVolume: 0.3,
      };

      logger.debug('Generating video with options:', options);
      setGenerationProgress('Preparing video generation...');

      const result = await generateVideoFromPhoto(options);

      setGenerationProgress('Video generation completed!');
      logger.debug('Video generated successfully:', result);

      setGeneratedVideoUri(result.videoPath);
      return result.videoPath;

    } catch (error) {
      logger.error('Video generation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      Alert.alert(
        'Generation Failed',
        `Failed to generate video: ${errorMessage}`,
        [{ text: 'OK' }]
      );
      
      return null;
    } finally {
      setIsGenerating(false);
      setGenerationProgress('');
    }
  }, [photoUri, userId, ffmpegAvailable]);

  const resetGeneration = useCallback(() => {
    if (generatedVideoUri) {
      cleanupTempFiles([generatedVideoUri]);
    }
    setGeneratedVideoUri(null);
    setGenerationProgress('');
    setIsGenerating(false);
  }, [generatedVideoUri]);

  return {
    isGenerating,
    generationProgress,
    generatedVideoUri,
    ffmpegAvailable,
    generateVideo,
    resetGeneration,
  };
};
