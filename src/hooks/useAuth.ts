import { useState, useEffect, useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../integrations/supabase/client';
import { Session } from '@supabase/supabase-js';
import { store } from '../store';
import { setUser, clearUser } from '../store/slices/authSlice';
import { Alert } from 'react-native';

import { User } from '../store/slices/authSlice';
import { GoogleAuthService } from '../services/social-auth/google-auth';
import { authStorage } from '../utils/authStorage';
import logger from '../utils/logger';

export const useAuth = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUserState] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const navigation = useNavigation<any>();

  // Initialize Google Sign-In on mount
  useEffect(() => {
    GoogleAuthService.initialize();
  }, []);

  // Fetch user profile from `profiles` table
  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Error fetching user profile:', error);
        // Return a default profile if none exists
        return {
          user_id: userId,
          username: 'user',
          full_name: 'User',
          profile_picture_url: null,
          bio: null,
          followers_count: 0,
          following_count: 0,
          likes_count: 0,
          videos_count: 0,
          is_private: false,
          is_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      }

      return data;
    } catch (error) {
      logger.error('Error in fetchUserProfile:', error);
      // Return a default profile on error
      return {
        user_id: userId,
        username: 'user',
        full_name: 'User',
        profile_picture_url: null,
        bio: null,
        followers_count: 0,
        following_count: 0,
        likes_count: 0,
        videos_count: 0,
        is_private: false,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    }
  }, []);

  // Auth state listener
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          logger.error('Error getting session in useAuth:', error);
          setLoading(false);
          return;
        }

        setSession(session);
        if (session?.user) {
          logger.debug('Session found in useAuth, fetching profile...');
          // Fetch profile data to get the latest avatar
          try {
            const profile = await fetchUserProfile(session.user.id);
            const userData: User = {
              id: session.user.id,
              email: session.user.email || '',
              username: session.user.email?.split('@')[0] || 'user',
              full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
              avatar_url: profile?.profile_picture_url || session.user.user_metadata?.avatar_url || undefined,
              bio: profile?.bio || undefined,
              website: undefined,
              pronouns: undefined,
              followers_count: 0,
              following_count: 0,
              likes_count: 0,
              videos_count: 0,
              is_private: false,
              is_verified: false,
              created_at: session.user.created_at || new Date().toISOString(),
              updated_at: session.user.updated_at || new Date().toISOString(),
            };
            setUserState(userData);
            store.dispatch(setUser(userData));
            logger.debug('User state restored in useAuth');
          } catch (profileError) {
            logger.error('Error fetching profile in useAuth:', profileError);
          }
        } else {
          logger.debug('No session found in useAuth');
        }
        setLoading(false);
      } catch (error) {
        logger.error('Error in initializeAuth:', error);
        setLoading(false);
      }
    };

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      logger.debug('Auth state changed:', event, !!session);
      setSession(session);

      if (session?.user) {
        try {
          // Fetch profile data for complete user information
          const profile = await fetchUserProfile(session.user.id);

          // Create user data from auth session with profile data
          const userData: User = {
            id: session.user.id,
            email: session.user.email || '',
            username: session.user.email?.split('@')[0] || 'user',
            full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            avatar_url: profile?.profile_picture_url || session.user.user_metadata?.avatar_url || undefined,
            bio: profile?.bio || undefined,
            website: undefined,
            pronouns: undefined,
            followers_count: 0,
            following_count: 0,
            likes_count: 0,
            videos_count: 0,
            is_private: false,
            is_verified: false,
            created_at: session.user.created_at || new Date().toISOString(),
            updated_at: session.user.updated_at || new Date().toISOString(),
          };
          setUserState(userData);
          store.dispatch(setUser(userData));

          // Store auth state and user data for persistence
          await authStorage.storeAuthState(true, session.user.id);
          await authStorage.storeUserData(userData);

          // Only navigate on explicit sign in, not on session restoration
          if (event === 'SIGNED_IN') {
            logger.debug('User signed in, navigating to Main');
            navigation.navigate('Main');
          }
        } catch (error) {
          logger.error('Error handling auth state change:', error);
        }
      } else {
        logger.debug('No session, clearing user state');
        setUserState(null);
        store.dispatch(clearUser());

        // Clear stored auth data
        await authStorage.clearAuthData();

        // Only navigate to auth on explicit sign out, not on app startup
        if (event === 'SIGNED_OUT') {
          logger.debug('User signed out, navigating to Auth');
          navigation.navigate('Auth', { screen: 'Login' });
        }
      }

      setLoading(false);
    });

    initializeAuth();

    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [fetchUserProfile, navigation]);

  // Login
  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { error, data } = await supabase.auth.signInWithPassword({ email, password });
      logger.debug('Login response:', data);

      if (error) {
        Alert.alert('Login Error', error.message);
        throw error;
      }
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Signup
  const signup = async (email: string, password: string, username: string, fullName: string) => {
    setLoading(true);
    try {
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { username, full_name: fullName },
        },
      });

      if (authError) {
        Alert.alert('Signup Error', authError.message);
        throw authError;
      }

      if (authData.user) {
        const { error: profileError } = await supabase
          .from('profiles')
          .insert([
            {
              user_id: authData.user.id,
              username,
              full_name: fullName,
              profile_picture_url: '', // default empty, you can update later
              banner_image_url: '',
              bio: '',
              user_tag: 'Supporter',
            },
          ]);

        if (profileError) {throw profileError;}

        return authData.user;
      }
    } catch (error) {
      logger.error('Signup error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };



  // Reset Password
  const resetPassword = async (email: string) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) {throw error;}
      Alert.alert('Password Reset', 'Check your email for a password reset link');
    } catch (error) {
      logger.error('Password reset error:', error);
      Alert.alert('Error', 'Failed to send password reset email');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update Profile
  const updateProfile = async (updates: { bio?: string; user_tag?: string }) => {
    if (!user?.id) {return;}

    setLoading(true);
    try {
      // Update auth metadata
      const { error: authError } = await supabase.auth.updateUser({
        data: updates,
      });
      if (authError) {throw authError;}

      // Prepare updates for the profiles table with correct user_tag type
      const profileUpdates: {
        username?: string;
        full_name?: string;
        profile_picture_url?: string | null;
        banner_image_url?: string | null;
        bio?: string | null;
        user_tag?: 'Célébrité' | 'Partenaire' | 'Club/Pro.' | 'Créateur' | 'Supporter' | null;
      } = {
        ...updates,
        bio: updates.bio,
        user_tag: updates.user_tag as 'Célébrité' | 'Partenaire' | 'Club/Pro.' | 'Créateur' | 'Supporter' | undefined,
      };

      // Update `profiles` table
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('user_id', user.id);

      if (profileError) {throw profileError;}

      const updatedUser = { ...user, ...updates };
      setUserState(updatedUser);
      store.dispatch(setUser(updatedUser));

      return true;
    } catch (error) {
      logger.error('Profile update error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Google Sign-In
  const signInWithGoogle = async () => {
    setLoading(true);
    try {
      const result = await GoogleAuthService.signIn();

      // Save Google avatar to profile if user doesn't have one
      if (result.userInfo?.photo) {
        const { supabase } = await import('../integrations/supabase/client');
        await supabase
          .from('profiles')
          .upsert({
            user_id: result.user.id,
            profile_picture_url: result.userInfo.photo,
            bio: null,
            user_tag: 'Supporter',
          });
      }

      // Create user data from Google auth result
      const userData: User = {
        id: result.user.id,
        email: result.user.email || '',
        username: result.userInfo?.givenName || result.user.email?.split('@')[0] || 'user',
        full_name: result.userInfo?.name || result.userInfo?.givenName || 'User',
        avatar_url: result.userInfo?.photo || undefined,
        bio: undefined,
        website: undefined,
        pronouns: undefined,
        followers_count: 0,
        following_count: 0,
        likes_count: 0,
        videos_count: 0,
        is_private: false,
        is_verified: false,
        created_at: result.user.created_at || new Date().toISOString(),
        updated_at: result.user.updated_at || new Date().toISOString(),
      };

      setUserState(userData);
      store.dispatch(setUser(userData));

      logger.debug('Google Sign-In successful, user data:', userData);
      return result;
    } catch (error: any) {
      logger.error('Google Sign-In error:', error);
      const errorMessage = error.message || 'Google Sign-In failed';
      Alert.alert('Google Sign-In Error', errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Enhanced logout to include Google sign out
  const enhancedLogout = async () => {
    setLoading(true);
    try {
      // Sign out from Google if signed in
      const isGoogleSignedIn = await GoogleAuthService.isSignedIn();
      if (isGoogleSignedIn) {
        await GoogleAuthService.signOut();
      }

      // Clear stored auth data
      await authStorage.clearAuthData();

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {throw error;}

      // Clear local state
      setUserState(null);
      store.dispatch(clearUser());

      navigation.navigate('Auth', { screen: 'Welcome' });
    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getCurrentUser = () => user;

  return {
    session,
    user,
    loading,
    login,
    signup,
    logout: enhancedLogout,
    resetPassword,
    updateProfile,
    getCurrentUser,
    signInWithGoogle,
  };
};
