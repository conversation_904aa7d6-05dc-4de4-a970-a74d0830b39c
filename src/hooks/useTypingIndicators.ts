import { useState, useEffect, useCallback, useRef } from 'react';
import { TypingIndicator } from '../types/messaging';
import { messagingApi } from '../store/api/messagingApi';
import { useAppDispatch } from '../store/hooks';
import logger from '../utils/logger';

interface UseTypingIndicatorsProps {
  conversationId: string;
  currentUserId?: string;
  autoStopDelay?: number; // milliseconds
}

export const useTypingIndicators = ({
  conversationId,
  currentUserId,
  autoStopDelay = 3000,
}: UseTypingIndicatorsProps) => {
  const dispatch = useAppDispatch();
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch typing indicators
  const { data: typingData, refetch } = messagingApi.useGetTypingIndicatorsQuery(
    conversationId,
    {
      pollingInterval: 2000, // Poll every 2 seconds as fallback
      skip: !conversationId,
    }
  );

  // Update typing users when data changes
  useEffect(() => {
    if (typingData) {
      setTypingUsers(typingData.filter(indicator => 
        indicator.user_id !== currentUserId && indicator.is_typing
      ));
    }
  }, [typingData, currentUserId]);

  // Start typing indicator
  const startTyping = useCallback(async (typingType: 'text' | 'voice' | 'media' = 'text') => {
    if (!conversationId || !currentUserId || isTyping) return;

    try {
      setIsTyping(true);
      
      await dispatch(messagingApi.endpoints.updateTypingIndicator.initiate({
        conversation_id: conversationId,
        is_typing: true,
        typing_type: typingType,
      }));

      logger.debug('Started typing indicator:', { conversationId, typingType });

      // Auto-stop typing after delay
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      typingTimeoutRef.current = setTimeout(() => {
        stopTyping();
      }, autoStopDelay);

    } catch (error) {
      logger.error('Error starting typing indicator:', error);
      setIsTyping(false);
    }
  }, [conversationId, currentUserId, isTyping, autoStopDelay, dispatch]);

  // Stop typing indicator
  const stopTyping = useCallback(async () => {
    if (!conversationId || !currentUserId || !isTyping) return;

    try {
      setIsTyping(false);

      await dispatch(messagingApi.endpoints.updateTypingIndicator.initiate({
        conversation_id: conversationId,
        is_typing: false,
        typing_type: 'text',
      }));

      logger.debug('Stopped typing indicator:', { conversationId });

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

    } catch (error) {
      logger.error('Error stopping typing indicator:', error);
    }
  }, [conversationId, currentUserId, isTyping, dispatch]);

  // Handle text input changes
  const handleTextChange = useCallback((text: string) => {
    if (!text.trim()) {
      // Stop typing if text is empty
      if (isTyping) {
        stopTyping();
      }
      return;
    }

    // Start typing if not already typing
    if (!isTyping) {
      startTyping('text');
    } else {
      // Reset the auto-stop timer
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      typingTimeoutRef.current = setTimeout(() => {
        stopTyping();
      }, autoStopDelay);
    }
  }, [isTyping, startTyping, stopTyping, autoStopDelay]);

  // Handle voice recording
  const handleVoiceRecording = useCallback((isRecording: boolean) => {
    if (isRecording) {
      startTyping('voice');
    } else {
      stopTyping();
    }
  }, [startTyping, stopTyping]);

  // Handle media sharing
  const handleMediaSharing = useCallback((isSharing: boolean) => {
    if (isSharing) {
      startTyping('media');
    } else {
      stopTyping();
    }
  }, [startTyping, stopTyping]);

  // Clean up old typing indicators
  const cleanupOldIndicators = useCallback(() => {
    const now = Date.now();
    const staleThreshold = 10000; // 10 seconds

    setTypingUsers(prev => prev.filter(indicator => {
      const lastActivity = new Date(indicator.last_activity_at).getTime();
      return (now - lastActivity) < staleThreshold;
    }));
  }, []);

  // Setup cleanup interval
  useEffect(() => {
    cleanupTimeoutRef.current = setInterval(cleanupOldIndicators, 5000); // Clean every 5 seconds

    return () => {
      if (cleanupTimeoutRef.current) {
        clearInterval(cleanupTimeoutRef.current);
      }
    };
  }, [cleanupOldIndicators]);

  // Handle real-time typing updates
  const handleTypingUpdate = useCallback((typingData: TypingIndicator) => {
    if (typingData.user_id === currentUserId) return; // Ignore own typing

    setTypingUsers(prev => {
      const filtered = prev.filter(t => t.user_id !== typingData.user_id);
      
      if (typingData.is_typing) {
        return [...filtered, typingData];
      } else {
        return filtered;
      }
    });

    // Refresh the query to get updated data
    refetch();
  }, [currentUserId, refetch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (cleanupTimeoutRef.current) {
        clearInterval(cleanupTimeoutRef.current);
      }
      // Stop typing when component unmounts
      if (isTyping) {
        stopTyping();
      }
    };
  }, [isTyping, stopTyping]);

  // Get typing text for display
  const getTypingText = useCallback(() => {
    if (typingUsers.length === 0) return '';

    if (typingUsers.length === 1) {
      const user = typingUsers[0];
      const name = user.user?.full_name || user.user?.username || 'Someone';
      
      switch (user.typing_type) {
        case 'voice':
          return `${name} is recording a voice message`;
        case 'media':
          return `${name} is sharing media`;
        default:
          return `${name} is typing`;
      }
    } else if (typingUsers.length === 2) {
      const names = typingUsers.map(u => u.user?.full_name || u.user?.username || 'Someone');
      return `${names[0]} and ${names[1]} are typing`;
    } else {
      const firstName = typingUsers[0].user?.full_name || typingUsers[0].user?.username || 'Someone';
      return `${firstName} and ${typingUsers.length - 1} others are typing`;
    }
  }, [typingUsers]);

  // Check if anyone is typing
  const hasTypingUsers = typingUsers.length > 0;

  // Get typing users by type
  const getTypingUsersByType = useCallback((type: 'text' | 'voice' | 'media') => {
    return typingUsers.filter(user => user.typing_type === type);
  }, [typingUsers]);

  return {
    // State
    typingUsers,
    isTyping,
    hasTypingUsers,
    typingText: getTypingText(),

    // Actions
    startTyping,
    stopTyping,
    handleTextChange,
    handleVoiceRecording,
    handleMediaSharing,
    handleTypingUpdate,

    // Utilities
    getTypingUsersByType,
    cleanupOldIndicators,
  };
};
