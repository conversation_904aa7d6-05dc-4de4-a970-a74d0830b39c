import { useState, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import {
  launchImageLibrary,
  launchCamera,
  ImagePickerResponse,
  MediaType,
  CameraOptions,
  ImageLibraryOptions,
} from 'react-native-image-picker';
import { supabase } from '../integrations/supabase/client';
import logger from '../utils/logger';

interface UseProfilePhotoProps {
  userId: string;
  onSuccess?: (avatarUrl: string) => void;
  onError?: (error: string) => void;
}

export const useProfilePhoto = ({
  userId,
  onSuccess,
  onError,
}: UseProfilePhotoProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const uploadImage = useCallback(async (imageUri: string): Promise<string> => {
    try {
      // Create a unique filename
      const fileExt = imageUri.split('.').pop();
      const fileName = `${userId}_${Date.now()}.${fileExt}`;
      const filePath = `profile/${userId}/${fileName}`;

      // Convert image URI to blob for upload
      const response = await fetch(imageUri);
      const blob = await response.blob();

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('profile')
        .upload(filePath, blob, {
          contentType: `image/${fileExt}`,
          upsert: true,
        });

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('profile')
        .getPublicUrl(data.path);

      return publicUrlData.publicUrl;
    } catch (error) {
      logger.error('Image upload error:', error);
      throw error;
    }
  }, [userId]);

  const handleImagePicker = useCallback((response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      if (response.errorMessage) {
        logger.error('Image picker error:', response.errorMessage);
        onError?.(response.errorMessage);
      }
      return;
    }

    if (!response.assets || response.assets.length === 0) {
      onError?.('No image selected');
      return;
    }

    const asset = response.assets[0];
    if (!asset.uri) {
      onError?.('Invalid image file');
      return;
    }

    handleImageUpload(asset.uri);
  }, []);

  const handleImageUpload = useCallback(async (imageUri: string) => {
    try {
      setIsUploading(true);
      logger.debug('Starting image upload:', imageUri);

      const avatarUrl = await uploadImage(imageUri);
      
      // Update user profile with new avatar URL
      const { error } = await supabase
        .from('users')
        .update({ 
          avatar_url: avatarUrl,
          profile_picture_url: avatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      logger.debug('Profile photo updated successfully');
      onSuccess?.(avatarUrl);
    } catch (error) {
      logger.error('Profile photo update error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile photo';
      onError?.(errorMessage);
    } finally {
      setIsUploading(false);
    }
  }, [userId, uploadImage, onSuccess, onError]);

  const showImagePicker = useCallback(() => {
    Alert.alert(
      'Change Profile Photo',
      'Choose how you want to update your profile photo',
      [
        {
          text: 'Camera',
          onPress: () => {
            const options: CameraOptions = {
              mediaType: 'photo' as MediaType,
              quality: 0.8,
              maxWidth: 800,
              maxHeight: 800,
            };
            launchCamera(options, handleImagePicker);
          },
        },
        {
          text: 'Photo Library',
          onPress: () => {
            const options: ImageLibraryOptions = {
              mediaType: 'photo' as MediaType,
              quality: 0.8,
              maxWidth: 800,
              maxHeight: 800,
            };
            launchImageLibrary(options, handleImagePicker);
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  }, [handleImagePicker]);

  return {
    isUploading,
    showImagePicker,
    handleImageUpload,
  };
};
