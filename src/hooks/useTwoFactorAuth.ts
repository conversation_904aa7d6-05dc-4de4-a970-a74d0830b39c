import { useState, useEffect } from 'react';
import { supabase } from '../integrations/supabase/client';
import { useAppSelector } from '../store/hooks';
import { selectCurrentUser } from '../store/slices/authSlice';
import {
  useAddPhoneNumberMutation,
  useRemovePhoneNumberMutation,
  useSendVerificationCodeMutation,
  useVerifyCodeMutation,
} from '../store/api/phoneVerificationApi';
import { Alert } from 'react-native';
import logger from '../utils/logger';

interface PhoneNumber {
  id: string;
  user_id: string;
  phone_number: string;
  is_verified: boolean;
  is_primary: boolean;
  created_at: string | null;
  updated_at: string | null;
}

export const useTwoFactorAuth = () => {
  const user = useAppSelector(selectCurrentUser);
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [sending, setSending] = useState(false);

  const [addPhoneNumberRequest] = useAddPhoneNumberMutation();
  const [removePhoneNumberRequest] = useRemovePhoneNumberMutation();
  const [sendCodeRequest] = useSendVerificationCodeMutation();
  const [verifyCodeRequest] = useVerifyCodeMutation();

  const fetchPhoneNumbers = async () => {
    if (!user) {return;}

    try {
      const { data, error } = await supabase
        .from('user_phone_numbers')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {throw error;}
      setPhoneNumbers(data || []);
    } catch (error) {
      logger.error('Error fetching phone numbers:', error);
      Alert.alert(
        'Error',
        'Unable to load phone numbers',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationCode = async (phoneNumber: string) => {
    if (!user) {return { success: false, error: 'User not authenticated' };}

    setSending(true);
    try {
      logger.debug(`📱 Sending SMS verification to ${phoneNumber} via API...`);
      await sendCodeRequest(phoneNumber).unwrap();

      Alert.alert(
        'Code Sent',
        `A verification code has been sent to ${phoneNumber}`,
        [{ text: 'OK' }]
      );

      return { success: true };
    } catch (error: any) {
      logger.error('Error sending verification code:', error);
      Alert.alert(
        'Error',
        error.error || error.message || 'Unable to send verification code',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.error || error.message };
    } finally {
      setSending(false);
    }
  };

  const verifyCode = async (phoneNumber: string, code: string) => {
    if (!user) {return { success: false, error: 'User not authenticated' };}

    setVerifying(true);
    try {
      logger.debug(`🔍 Verifying code ${code} for ${phoneNumber} via API...`);
      await verifyCodeRequest({ phoneNumber, code }).unwrap();

      await fetchPhoneNumbers();

      Alert.alert(
        'Phone Verified',
        'Your phone number has been verified successfully and 2FA is now enabled',
        [{ text: 'OK' }]
      );

      return { success: true };
    } catch (error: any) {
      logger.error('Error verifying code:', error);
      Alert.alert(
        'Error',
        error.error || error.message || 'Invalid verification code',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.error || error.message };
    } finally {
      setVerifying(false);
    }
  };

  const addPhoneNumber = async (phoneNumber: string) => {
    if (!user) {return { success: false, error: 'User not authenticated' };}

    try {
      await addPhoneNumberRequest(phoneNumber).unwrap();

      await fetchPhoneNumbers();
      return { success: true };
    } catch (error: any) {
      logger.error('Error adding phone number:', error);
      Alert.alert(
        'Error',
        error.error || error.message || 'Unable to add phone number',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.error || error.message };
    }
  };

  const removePhoneNumber = async (phoneId: string) => {
    if (!user) {return { success: false, error: 'User not authenticated' };}

    try {
      await removePhoneNumberRequest(phoneId).unwrap();

      await fetchPhoneNumbers();

      Alert.alert(
        'Phone Removed',
        'Phone number has been removed',
        [{ text: 'OK' }]
      );

      return { success: true };
    } catch (error: any) {
      logger.error('Error removing phone number:', error);
      Alert.alert(
        'Error',
        error.error || error.message || 'Unable to remove phone number',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.error || error.message };
    }
  };

  const disable2FA = async () => {
    if (!user) {return { success: false, error: 'User not authenticated' };}

    try {
      // Remove all phone numbers
      const { error: deleteError } = await supabase
        .from('user_phone_numbers')
        .delete()
        .eq('user_id', user.id);

      if (deleteError) {throw deleteError;}

      // Disable 2FA in security settings
      const { error: securityError } = await supabase
        .from('security_settings')
        .upsert({
          user_id: user.id,
          two_factor_enabled: false,
        }, {
          onConflict: 'user_id',
        });

      if (securityError) {throw securityError;}

      await fetchPhoneNumbers();

      Alert.alert(
        '2FA Disabled',
        'Two-factor authentication has been disabled',
        [{ text: 'OK' }]
      );

      return { success: true };
    } catch (error: any) {
      logger.error('Error disabling 2FA:', error);
      Alert.alert(
        'Error',
        error.message || 'Unable to disable 2FA',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.message };
    }
  };

  useEffect(() => {
    fetchPhoneNumbers();
  }, [user]);

  const hasVerifiedPhone = phoneNumbers.some(phone => phone.is_verified);
  const primaryPhone = phoneNumbers.find(phone => phone.is_primary);

  return {
    phoneNumbers,
    loading,
    verifying,
    sending,
    hasVerifiedPhone,
    primaryPhone,
    sendVerificationCode,
    verifyCode,
    addPhoneNumber,
    removePhoneNumber,
    disable2FA,
    refetch: fetchPhoneNumbers,
  };
};
