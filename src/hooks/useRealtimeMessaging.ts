import { useEffect, useRef, useCallback } from 'react';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../integrations/supabase/client';
import { useAppDispatch } from '../store/hooks';
import { messagingApi } from '../store/api/messagingApi';
import logger from '../utils/logger';

interface UseRealtimeMessagingProps {
  conversationId?: string;
  userId?: string;
  onNewMessage?: (message: any) => void;
  onMessageUpdate?: (message: any) => void;
  onTypingUpdate?: (typingData: any) => void;
  onPresenceUpdate?: (presenceData: any) => void;
  onReactionUpdate?: (reactionData: any) => void;
}

export const useRealtimeMessaging = ({
  conversationId,
  userId,
  onNewMessage,
  onMessageUpdate,
  onTypingUpdate,
  onPresenceUpdate,
  onReactionUpdate,
}: UseRealtimeMessagingProps) => {
  const dispatch = useAppDispatch();
  const channelRef = useRef<RealtimeChannel | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle new messages
  const handleNewMessage = useCallback((payload: RealtimePostgresChangesPayload<any>) => {
    logger.debug('New message received:', payload);
    
    if (payload.eventType === 'INSERT') {
      const newMessage = payload.new;
      
      // Only process messages for the current conversation
      if (conversationId && newMessage.conversation_id === conversationId) {
        onNewMessage?.(newMessage);
        
        // Invalidate messages query to refetch
        dispatch(messagingApi.util.invalidateTags(['Message']));
      }
      
      // Always invalidate conversations to update last message
      dispatch(messagingApi.util.invalidateTags(['Conversation']));
    }
  }, [conversationId, onNewMessage, dispatch]);

  // Handle message updates (read status, reactions, etc.)
  const handleMessageUpdate = useCallback((payload: RealtimePostgresChangesPayload<any>) => {
    logger.debug('Message updated:', payload);
    
    if (payload.eventType === 'UPDATE') {
      const updatedMessage = payload.new;
      onMessageUpdate?.(updatedMessage);
      
      // Invalidate messages query to refetch
      dispatch(messagingApi.util.invalidateTags(['Message']));
    }
  }, [onMessageUpdate, dispatch]);

  // Handle typing indicators
  const handleTypingUpdate = useCallback((payload: RealtimePostgresChangesPayload<any>) => {
    logger.debug('Typing indicator updated:', payload);
    
    if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
      const typingData = payload.new;
      
      // Only process typing for the current conversation and exclude current user
      if (conversationId && 
          typingData.conversation_id === conversationId && 
          typingData.user_id !== userId) {
        onTypingUpdate?.(typingData);
      }
    }
  }, [conversationId, userId, onTypingUpdate]);

  // Handle user presence updates
  const handlePresenceUpdate = useCallback((payload: RealtimePostgresChangesPayload<any>) => {
    logger.debug('User presence updated:', payload);
    
    if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
      const presenceData = payload.new;
      onPresenceUpdate?.(presenceData);
    }
  }, [onPresenceUpdate]);

  // Handle message reactions
  const handleReactionUpdate = useCallback((payload: RealtimePostgresChangesPayload<any>) => {
    logger.debug('Message reaction updated:', payload);
    
    if (payload.eventType === 'INSERT' || payload.eventType === 'DELETE') {
      const reactionData = payload.new || payload.old;
      onReactionUpdate?.(reactionData);
      
      // Invalidate messages query to update reaction counts
      dispatch(messagingApi.util.invalidateTags(['Message']));
    }
  }, [onReactionUpdate, dispatch]);

  // Send typing indicator
  const sendTypingIndicator = useCallback(async (isTyping: boolean, typingType: 'text' | 'voice' | 'media' = 'text') => {
    if (!conversationId || !userId) return;

    try {
      await dispatch(messagingApi.endpoints.updateTypingIndicator.initiate({
        conversation_id: conversationId,
        is_typing: isTyping,
        typing_type: typingType,
      }));

      // Auto-stop typing after 3 seconds
      if (isTyping) {
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        
        typingTimeoutRef.current = setTimeout(() => {
          sendTypingIndicator(false, typingType);
        }, 3000);
      }
    } catch (error) {
      logger.error('Error sending typing indicator:', error);
    }
  }, [conversationId, userId, dispatch]);

  // Update user presence
  const updatePresence = useCallback(async (status: 'online' | 'away' | 'busy' | 'offline') => {
    if (!userId) return;

    try {
      await dispatch(messagingApi.endpoints.updateUserPresence.initiate({
        status,
        device_info: {
          platform: 'mobile',
          timestamp: new Date().toISOString(),
        },
      }));
    } catch (error) {
      logger.error('Error updating presence:', error);
    }
  }, [userId, dispatch]);

  // Setup realtime subscriptions
  useEffect(() => {
    if (!supabase) return;

    // Create a single channel for all messaging-related subscriptions
    const channel = supabase.channel('messaging-realtime');

    // Subscribe to messages
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'messages',
      },
      handleNewMessage
    );

    // Subscribe to message updates (for read status, etc.)
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'messages',
      },
      handleMessageUpdate
    );

    // Subscribe to typing indicators
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'typing_indicators',
      },
      handleTypingUpdate
    );

    // Subscribe to user presence
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'user_presence',
      },
      handlePresenceUpdate
    );

    // Subscribe to message reactions
    channel.on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'message_reactions',
      },
      handleReactionUpdate
    );

    // Subscribe to the channel
    channel.subscribe((status) => {
      logger.debug('Realtime subscription status:', status);
      
      if (status === 'SUBSCRIBED') {
        logger.info('Successfully subscribed to realtime messaging');
        // Set user as online when connected
        updatePresence('online');
      }
    });

    channelRef.current = channel;

    // Cleanup function
    return () => {
      if (channelRef.current) {
        logger.debug('Unsubscribing from realtime messaging');
        channelRef.current.unsubscribe();
        channelRef.current = null;
      }
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    };
  }, [
    handleNewMessage,
    handleMessageUpdate,
    handleTypingUpdate,
    handlePresenceUpdate,
    handleReactionUpdate,
    updatePresence,
  ]);

  // Handle app state changes (foreground/background)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        updatePresence('online');
      } else if (nextAppState === 'background') {
        updatePresence('away');
      }
    };

    // Note: In a real React Native app, you'd use AppState from 'react-native'
    // For now, we'll handle visibility change for web
    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence('away');
      } else {
        updatePresence('online');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [updatePresence]);

  // Set user offline when component unmounts
  useEffect(() => {
    return () => {
      updatePresence('offline');
    };
  }, [updatePresence]);

  return {
    sendTypingIndicator,
    updatePresence,
    isConnected: channelRef.current?.state === 'joined',
  };
};
