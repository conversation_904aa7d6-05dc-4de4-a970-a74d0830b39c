# 🎣 Custom Hooks

This directory contains all custom React hooks used throughout the TS1 application. Hooks are organized by functionality and provide clean abstractions for business logic, API interactions, and common patterns.

## 📁 Directory Structure

```
src/hooks/
├── api/                    # API abstraction hooks
│   ├── useMessaging.ts    # Messaging operations
│   ├── useVideoLikes.ts   # Video like management
│   ├── useUserProfile.ts  # User profile operations
│   ├── useVideos.ts       # Video data management
│   └── index.ts           # Export barrel
├── useAuth.ts             # Authentication logic
├── useMediaHandlers.ts    # Media upload/processing
├── useCountryDetection.ts # Auto-detect user country
├── usePhoneVerification.ts # SMS verification flow
├── useVideoGeneration.ts  # Video creation logic
├── useTextOverlays.ts     # Text overlay management
└── README.md              # This file
```

## 🎯 Hook Categories

### **1. API Hooks (`/api/`)**
These hooks provide clean abstractions over RTK Query APIs, handling business logic, error management, and loading states.

**Key Benefits:**
- Consistent error handling across the app
- Simplified component code
- Centralized business logic
- Easy testing and mocking

**Available Hooks:**
- `useMessaging` - Conversation management
- `useConversationMessages` - Message operations
- `useMessageSender` - Simplified message sending
- `useVideoLikes` - Video like operations with optimistic updates
- `useUserProfile` - User profile management
- `useVideos` - Video data operations

### **2. Business Logic Hooks**
These hooks encapsulate complex business logic and state management for specific features.

**Examples:**
- `useAuth` - Authentication state and operations
- `usePhoneVerification` - SMS verification workflow
- `useVideoGeneration` - Video creation and processing
- `useCountryDetection` - Auto-detect user's country

### **3. Utility Hooks**
These hooks provide reusable functionality for common patterns and operations.

**Examples:**
- `useMediaHandlers` - Media upload and processing
- `useTextOverlays` - Text overlay management for videos

## 📋 Hook Development Guidelines

### **1. Hook Structure Template**
```typescript
import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import logger from '../../utils/logger';

/**
 * Custom hook description
 * 
 * Detailed explanation of what this hook does, its purpose,
 * and how it should be used.
 * 
 * @param param1 - Description of parameter
 * @param param2 - Description of optional parameter
 * @returns Object containing hook's return values
 * 
 * @example
 * ```tsx
 * const Component = () => {
 *   const { data, isLoading, performAction } = useCustomHook(param1);
 *   
 *   return (
 *     <View>
 *       {isLoading ? <Spinner /> : <DataView data={data} />}
 *       <Button onPress={performAction} />
 *     </View>
 *   );
 * };
 * ```
 */
export const useCustomHook = (param1: string, param2?: number) => {
  // State declarations
  const [data, setData] = useState<DataType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Business logic functions
  const performAction = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Perform operation
      const result = await someOperation(param1);
      setData(result);
      
      logger.debug('Operation completed successfully');
      return true;
    } catch (error) {
      logger.error('Operation failed:', error);
      setError(error.message);
      Alert.alert('Error', 'Operation failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [param1]);

  // Effects
  useEffect(() => {
    // Initialization logic
  }, [param1]);

  return {
    // Data
    data,
    error,
    
    // Loading states
    isLoading,
    
    // Actions
    performAction,
  };
};
```

### **2. Naming Conventions**
- Use descriptive names: `useMessaging`, `useVideoLikes`
- Follow the `use` prefix convention
- Group related functionality: `useConversationMessages`
- Use verbs for actions: `createConversation`, `sendMessage`

### **3. Return Object Structure**
```typescript
return {
  // Data first
  data,
  items,
  user,
  
  // Loading states
  isLoading,
  isUpdating,
  isSending,
  
  // Error states
  error,
  
  // Actions last
  performAction,
  refresh,
  update,
};
```

### **4. Error Handling Pattern**
```typescript
const performAction = useCallback(async (): Promise<boolean> => {
  try {
    // Operation logic
    await someAsyncOperation();
    
    logger.debug('Action completed successfully');
    return true;
  } catch (error) {
    logger.error('Action failed:', error);
    Alert.alert('Error', 'Action failed. Please try again.');
    return false;
  }
}, [dependencies]);
```

## 🧪 Testing Hooks

### **1. Unit Testing Template**
```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { useCustomHook } from '../useCustomHook';

describe('useCustomHook', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => useCustomHook('test'));
    
    expect(result.current.data).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should perform action successfully', async () => {
    const { result } = renderHook(() => useCustomHook('test'));
    
    await act(async () => {
      const success = await result.current.performAction();
      expect(success).toBe(true);
    });
    
    expect(result.current.data).toBeDefined();
  });

  it('should handle errors gracefully', async () => {
    // Mock error scenario
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const { result } = renderHook(() => useCustomHook('invalid'));
    
    await act(async () => {
      const success = await result.current.performAction();
      expect(success).toBe(false);
    });
    
    expect(result.current.error).toBeTruthy();
  });
});
```

### **2. Integration Testing**
```typescript
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { ComponentUsingHook } from '../ComponentUsingHook';

describe('Component with Hook Integration', () => {
  it('should work correctly with hook', async () => {
    const { getByTestId } = render(<ComponentUsingHook />);
    
    fireEvent.press(getByTestId('action-button'));
    
    await waitFor(() => {
      expect(getByTestId('success-message')).toBeTruthy();
    });
  });
});
```

## 📖 Usage Examples

### **API Hook Usage**
```typescript
// ✅ Good: Using API hook
const MessagesScreen = () => {
  const { conversations, createConversation, isLoadingConversations } = useMessaging();
  
  const handleStartChat = async (userId: string) => {
    const conversationId = await createConversation(userId);
    if (conversationId) {
      navigation.navigate('ChatDetail', { conversationId });
    }
  };
  
  return (
    <FlatList
      data={conversations}
      renderItem={({ item }) => <ConversationItem conversation={item} />}
      refreshing={isLoadingConversations}
    />
  );
};

// ❌ Bad: Direct API usage
const MessagesScreen = () => {
  const { data: conversations } = useGetConversationsQuery();
  const [createConversation] = useCreateConversationMutation();
  
  // Component with API logic mixed in
};
```

### **Business Logic Hook Usage**
```typescript
// ✅ Good: Business logic in hook
const EditPhoneScreen = () => {
  const { selectedCountry, setSelectedCountry, isDetectingLocation } = useCountryDetection();
  const {
    phoneNumber,
    setPhoneNumber,
    step,
    handleSendCode,
    handleVerifyCode
  } = usePhoneVerification({
    selectedCountry,
    onSuccess: () => navigation.goBack(),
  });
  
  return (
    <View>
      {step === 'phone' ? (
        <PhoneNumberInput
          selectedCountry={selectedCountry}
          phoneNumber={phoneNumber}
          onCountryChange={setSelectedCountry}
          onPhoneNumberChange={setPhoneNumber}
          isDetectingLocation={isDetectingLocation}
        />
      ) : (
        <VerificationCodeInput onVerify={handleVerifyCode} />
      )}
    </View>
  );
};
```

## 🚨 Common Pitfalls to Avoid

### **1. Don't Mix Concerns**
```typescript
// ❌ Bad: Hook doing too many things
const useMassiveHook = () => {
  // API calls + UI state + business logic + side effects
};

// ✅ Good: Focused, single-responsibility hooks
const useApiData = () => { /* API logic only */ };
const useUIState = () => { /* UI state only */ };
const useBusinessLogic = () => { /* Business logic only */ };
```

### **2. Don't Forget Cleanup**
```typescript
// ❌ Bad: No cleanup
useEffect(() => {
  const subscription = subscribe();
  // Missing cleanup
}, []);

// ✅ Good: Proper cleanup
useEffect(() => {
  const subscription = subscribe();
  return () => subscription.unsubscribe();
}, []);
```

### **3. Don't Ignore Dependencies**
```typescript
// ❌ Bad: Missing dependencies
useCallback(() => {
  doSomething(externalValue);
}, []); // Missing externalValue dependency

// ✅ Good: Correct dependencies
useCallback(() => {
  doSomething(externalValue);
}, [externalValue]);
```

## 📚 Further Reading

- [API Hooks Guide](../../docs/API_HOOKS.md) - Detailed guide on API abstraction hooks
- [React Hooks Documentation](https://reactjs.org/docs/hooks-intro.html)
- [Testing React Hooks](https://react-hooks-testing-library.com/)
- [Custom Hooks Best Practices](https://kentcdodds.com/blog/react-hooks-whats-going-to-happen-to-my-tests)

Remember: Hooks should encapsulate logic, not just group useState calls. Each hook should have a clear, single responsibility and provide a clean API for components to use.
