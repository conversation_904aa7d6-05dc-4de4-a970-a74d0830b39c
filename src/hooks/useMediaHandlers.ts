import { useState, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import {
  launchImageLibrary,
  ImageLibraryOptions,
  ImagePickerResponse,
} from 'react-native-image-picker';
import {
  check,
  request,
  PERMISSIONS,
  RESULTS,
} from 'react-native-permissions';
import logger from '../utils/logger';

interface MediaHandlersProps {
  conversationId: string;
  sendMessage: (params: any) => Promise<any>;
  onCloseAttachmentModal: () => void;
}

interface MediaData {
  path: string;
  duration: number;
  thumbnail?: string;
}

export const useMediaHandlers = ({
  conversationId,
  sendMessage,
  onCloseAttachmentModal,
}: MediaHandlersProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const processMediaSelection = useCallback(async (
    response: ImagePickerResponse,
    type: 'image' | 'video'
  ) => {
    if (response.didCancel || response.errorMessage) {
      if (response.errorMessage) {
        logger.error('Media selection error:', response.errorMessage);
        Alert.alert('Error', `Failed to select ${type}: ${response.errorMessage}`);
      }
      return;
    }

    if (!response.assets || response.assets.length === 0) {
      Alert.alert('Error', `No ${type} selected`);
      return;
    }

    const asset = response.assets[0];
    if (!asset.uri) {
      Alert.alert('Error', `Invalid ${type} file`);
      return;
    }

    try {
      setIsUploading(true);
      logger.debug('Starting media upload:', { 
        uri: asset.uri, 
        type, 
        fileName: asset.fileName 
      });

      const { uploadMessageMedia } = await import('../services/media/media-upload');
      const result = await uploadMessageMedia(asset.uri, conversationId);

      if (!result.publicUrl) {
        throw new Error('Upload failed: no public URL returned');
      }

      await sendMessage({
        conversation_id: conversationId,
        content: '',
        message_type: type,
        file_url: result.publicUrl,
        file_name: asset.fileName || `${type}_${Date.now()}`,
        file_size: asset.fileSize || 0,
        file_type: asset.type || `${type}/*`,
        thumbnail_url: type === 'video' ? result.thumbnailUrl : undefined,
      });

      logger.debug(`${type} message sent successfully`);
    } catch (error) {
      logger.error(`${type} upload error:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Upload Failed', `Failed to upload ${type}: ${errorMessage}`);
    } finally {
      setIsUploading(false);
      onCloseAttachmentModal();
    }
  }, [conversationId, sendMessage, onCloseAttachmentModal]);

  const handleSelectImage = useCallback(() => {
    const options: ImageLibraryOptions = { mediaType: 'photo' };
    launchImageLibrary(options, response => processMediaSelection(response, 'image'));
  }, [processMediaSelection]);

  const handleSelectVideo = useCallback(() => {
    const options: ImageLibraryOptions = { mediaType: 'video' };
    launchImageLibrary(options, response => processMediaSelection(response, 'video'));
  }, [processMediaSelection]);

  const handleTakePhoto = useCallback(async () => {
    try {
      setIsUploading(true);

      const { takePhoto } = await import('../services/media/camera');
      const response = await takePhoto();

      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        if (asset.uri) {
          await processMediaSelection(response, 'image');
        }
      }
    } catch (error) {
      logger.error('Camera photo error:', error);
      Alert.alert('Camera Error', 'Failed to take photo');
    } finally {
      setIsUploading(false);
    }
  }, [processMediaSelection]);

  const handleVideoRecordingComplete = useCallback(async (videoData: MediaData) => {
    try {
      setIsUploading(true);
      logger.debug('Video recorded successfully:', videoData);

      const { uploadMessageMedia } = await import('../services/media/media-upload');
      const result = await uploadMessageMedia(videoData.path, conversationId);

      if (!result.publicUrl) {
        throw new Error('Upload failed: no public URL returned');
      }

      await sendMessage({
        conversation_id: conversationId,
        content: '',
        message_type: 'video',
        file_url: result.publicUrl,
        file_name: `video_${Date.now()}.mp4`,
        file_size: videoData.duration,
        file_type: 'video/mp4',
        thumbnail_url: result.thumbnailUrl,
      });

      logger.debug('Video message sent successfully');
    } catch (error) {
      logger.error('Video upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Upload Failed', `Failed to upload video: ${errorMessage}`);
    } finally {
      setIsUploading(false);
    }
  }, [conversationId, sendMessage]);

  const handleAudioRecordingComplete = useCallback(async (audioData: MediaData) => {
    try {
      setIsUploading(true);
      logger.debug('Audio recorded successfully:', audioData);

      const { uploadMessageMedia } = await import('../services/media/media-upload');
      const result = await uploadMessageMedia(audioData.path, conversationId);

      if (!result.publicUrl) {
        throw new Error('Upload failed: no public URL returned');
      }

      await sendMessage({
        conversation_id: conversationId,
        content: '',
        message_type: 'audio',
        file_url: result.publicUrl,
        file_name: `audio_${Date.now()}.m4a`,
        file_size: audioData.duration,
        file_type: 'audio/m4a',
      });

      logger.debug('Audio message sent successfully');
    } catch (error) {
      logger.error('Audio upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Upload Failed', `Failed to upload audio: ${errorMessage}`);
    } finally {
      setIsUploading(false);
    }
  }, [conversationId, sendMessage]);

  const handleAudioRecord = useCallback(async () => {
    try {
      logger.debug('Requesting microphone permission...');
      const permission = Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.RECORD_AUDIO
        : PERMISSIONS.IOS.MICROPHONE;

      const result = await check(permission);
      
      if (result !== RESULTS.GRANTED) {
        const requestResult = await request(permission);
        if (requestResult !== RESULTS.GRANTED) {
          Alert.alert(
            'Permission Required',
            'Microphone permission is required to record audio messages.'
          );
          return;
        }
      }

      onCloseAttachmentModal();
      // The actual recording modal will be shown by the parent component
      return true;
    } catch (error) {
      logger.error('Audio permission error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Recording Failed', `Failed to start recording: ${errorMessage}`);
      return false;
    }
  }, [onCloseAttachmentModal]);

  return {
    isUploading,
    handleSelectImage,
    handleSelectVideo,
    handleTakePhoto,
    handleVideoRecordingComplete,
    handleAudioRecordingComplete,
    handleAudioRecord,
  };
};
