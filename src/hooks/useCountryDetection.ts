import { useState, useEffect } from 'react';
import { allCountries, getDefaultCountry, Country } from '../utils/phoneUtils';
import logger from '../utils/logger';

interface LocationApiResponse {
  country_code?: string;
  country?: string;
  countryCode?: string;
}

export const useCountryDetection = () => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(getDefaultCountry());
  const [isDetectingLocation, setIsDetectingLocation] = useState(true);

  useEffect(() => {
    const detectCountryFromLocation = async () => {
      try {
        setIsDetectingLocation(true);
        logger.debug('Starting location detection...');

        // Try multiple geolocation services for better reliability
        const services = [
          'https://ipapi.co/json/',
          'https://ip-api.com/json/',
          'https://ipinfo.io/json',
        ];

        for (const service of services) {
          try {
            logger.debug(`Trying service: ${service}`);
            const response = await fetch(service, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              logger.debug(`Service ${service} failed with status: ${response.status}`);
              continue;
            }

            const data = (await response.json()) as LocationApiResponse;
            logger.debug('Location data received:', data);

            // Handle different API response formats
            let countryCode: string | null = null;
            if (data.country_code) {
              countryCode = data.country_code.toUpperCase();
            } else if (data.country) {
              countryCode = data.country.toUpperCase();
            } else if (data.countryCode) {
              countryCode = data.countryCode.toUpperCase();
            }

            if (countryCode) {
              const detectedCountry = allCountries.find(
                country => country.code === countryCode
              );
              if (detectedCountry) {
                logger.debug('Country detected:', detectedCountry.name);
                setSelectedCountry(detectedCountry);
                break; // Successfully detected, exit loop
              }
            }
          } catch (serviceError) {
            logger.debug(`Service ${service} error:`, serviceError);
            continue; // Try next service
          }
        }
      } catch (error) {
        logger.error('Country detection error:', error);
      } finally {
        setIsDetectingLocation(false);
      }
    };

    detectCountryFromLocation();
  }, []);

  return {
    selectedCountry,
    setSelectedCountry,
    isDetectingLocation,
  };
};
