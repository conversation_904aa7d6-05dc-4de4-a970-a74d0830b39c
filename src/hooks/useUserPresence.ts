import { useState, useEffect, useCallback, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { UserPresence } from '../types/messaging';
import { messagingApi } from '../store/api/messagingApi';
import { useAppDispatch } from '../store/hooks';
import logger from '../utils/logger';

interface UseUserPresenceProps {
  conversationId?: string;
  currentUserId?: string;
  autoAwayDelay?: number; // milliseconds
}

export const useUserPresence = ({
  conversationId,
  currentUserId,
  autoAwayDelay = 300000, // 5 minutes
}: UseUserPresenceProps) => {
  const dispatch = useAppDispatch();
  const [currentStatus, setCurrentStatus] = useState<'online' | 'away' | 'busy' | 'offline'>('online');
  const [userPresences, setUserPresences] = useState<Map<string, UserPresence>>(new Map());
  const awayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Fetch user presence for conversation participants
  const { data: presenceData, refetch } = messagingApi.useGetUserPresenceQuery(
    conversationId || '',
    {
      pollingInterval: 30000, // Poll every 30 seconds
      skip: !conversationId,
    }
  );

  // Update user presences when data changes
  useEffect(() => {
    if (presenceData) {
      const presenceMap = new Map<string, UserPresence>();
      presenceData.forEach(presence => {
        presenceMap.set(presence.user_id, presence);
      });
      setUserPresences(presenceMap);
    }
  }, [presenceData]);

  // Update user presence
  const updatePresence = useCallback(async (
    status: 'online' | 'away' | 'busy' | 'offline',
    deviceInfo?: any
  ) => {
    if (!currentUserId) return;

    try {
      setCurrentStatus(status);
      lastActivityRef.current = Date.now();

      await dispatch(messagingApi.endpoints.updateUserPresence.initiate({
        status,
        device_info: {
          platform: 'mobile',
          timestamp: new Date().toISOString(),
          ...deviceInfo,
        },
      }));

      logger.debug('Updated user presence:', { status, userId: currentUserId });

      // Setup auto-away timer for online status
      if (status === 'online') {
        if (awayTimeoutRef.current) {
          clearTimeout(awayTimeoutRef.current);
        }

        awayTimeoutRef.current = setTimeout(() => {
          updatePresence('away');
        }, autoAwayDelay);
      } else {
        // Clear auto-away timer for non-online statuses
        if (awayTimeoutRef.current) {
          clearTimeout(awayTimeoutRef.current);
          awayTimeoutRef.current = null;
        }
      }

    } catch (error) {
      logger.error('Error updating user presence:', error);
    }
  }, [currentUserId, autoAwayDelay, dispatch]);

  // Handle user activity (reset away timer)
  const handleUserActivity = useCallback(() => {
    lastActivityRef.current = Date.now();

    // If user is away, set back to online
    if (currentStatus === 'away') {
      updatePresence('online');
    } else if (currentStatus === 'online') {
      // Reset the away timer
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current);
      }

      awayTimeoutRef.current = setTimeout(() => {
        updatePresence('away');
      }, autoAwayDelay);
    }
  }, [currentStatus, updatePresence, autoAwayDelay]);

  // Handle app state changes
  const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
    logger.debug('App state changed:', nextAppState);

    switch (nextAppState) {
      case 'active':
        updatePresence('online');
        break;
      case 'background':
        updatePresence('away');
        break;
      case 'inactive':
        // Don't change status for inactive (e.g., notification panel)
        break;
      default:
        updatePresence('offline');
        break;
    }
  }, [updatePresence]);

  // Setup app state listener
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Set initial status
    updatePresence('online');

    return () => {
      subscription?.remove();
    };
  }, [handleAppStateChange, updatePresence]);

  // Handle real-time presence updates
  const handlePresenceUpdate = useCallback((presenceData: UserPresence) => {
    setUserPresences(prev => {
      const updated = new Map(prev);
      updated.set(presenceData.user_id, presenceData);
      return updated;
    });

    // Refresh the query to get updated data
    refetch();
  }, [refetch]);

  // Get user presence by ID
  const getUserPresence = useCallback((userId: string): UserPresence | null => {
    return userPresences.get(userId) || null;
  }, [userPresences]);

  // Check if user is online
  const isUserOnline = useCallback((userId: string): boolean => {
    const presence = getUserPresence(userId);
    return presence?.status === 'online' && presence?.is_active;
  }, [getUserPresence]);

  // Check if user is away
  const isUserAway = useCallback((userId: string): boolean => {
    const presence = getUserPresence(userId);
    return presence?.status === 'away';
  }, [getUserPresence]);

  // Get user's last seen time
  const getUserLastSeen = useCallback((userId: string): Date | null => {
    const presence = getUserPresence(userId);
    return presence?.last_seen_at ? new Date(presence.last_seen_at) : null;
  }, [getUserPresence]);

  // Format last seen text
  const formatLastSeen = useCallback((userId: string): string => {
    const presence = getUserPresence(userId);
    if (!presence) return 'Unknown';

    if (presence.status === 'online' && presence.is_active) {
      return 'Online';
    }

    const lastSeen = new Date(presence.last_seen_at);
    const now = new Date();
    const diffInMs = now.getTime() - lastSeen.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`;
    } else {
      return lastSeen.toLocaleDateString();
    }
  }, [getUserPresence]);

  // Get online users count
  const getOnlineUsersCount = useCallback((): number => {
    return Array.from(userPresences.values()).filter(
      presence => presence.status === 'online' && presence.is_active
    ).length;
  }, [userPresences]);

  // Get all online users
  const getOnlineUsers = useCallback((): UserPresence[] => {
    return Array.from(userPresences.values()).filter(
      presence => presence.status === 'online' && presence.is_active
    );
  }, [userPresences]);

  // Set user as busy
  const setBusy = useCallback(() => {
    updatePresence('busy');
  }, [updatePresence]);

  // Set user as away
  const setAway = useCallback(() => {
    updatePresence('away');
  }, [updatePresence]);

  // Set user as online
  const setOnline = useCallback(() => {
    updatePresence('online');
  }, [updatePresence]);

  // Set user as offline
  const setOffline = useCallback(() => {
    updatePresence('offline');
  }, [updatePresence]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current);
      }
      // Set user offline when component unmounts
      updatePresence('offline');
    };
  }, [updatePresence]);

  return {
    // Current user state
    currentStatus,
    lastActivity: lastActivityRef.current,

    // User presence data
    userPresences,
    onlineUsersCount: getOnlineUsersCount(),
    onlineUsers: getOnlineUsers(),

    // Actions
    updatePresence,
    handleUserActivity,
    handlePresenceUpdate,
    setBusy,
    setAway,
    setOnline,
    setOffline,

    // Utilities
    getUserPresence,
    isUserOnline,
    isUserAway,
    getUserLastSeen,
    formatLastSeen,
  };
};
