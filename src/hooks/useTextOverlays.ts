import { useState, useCallback } from 'react';
import { TextOverlay, createDefaultTextOverlay } from '../utils/videoGenerator';

export const useTextOverlays = () => {
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  const [selectedOverlayId, setSelectedOverlayId] = useState<string | null>(null);

  const addTextOverlay = useCallback(() => {
    const newOverlay = createDefaultTextOverlay('Add text here');
    setTextOverlays(prev => [...prev, newOverlay]);
    setSelectedOverlayId(newOverlay.id);
    return newOverlay;
  }, []);

  const updateTextOverlay = useCallback((updatedOverlay: TextOverlay) => {
    setTextOverlays(prev =>
      prev.map(overlay =>
        overlay.id === updatedOverlay.id ? updatedOverlay : overlay
      )
    );
  }, []);

  const deleteTextOverlay = useCallback((id: string) => {
    setTextOverlays(prev => prev.filter(overlay => overlay.id !== id));
    if (selectedOverlayId === id) {
      setSelectedOverlayId(null);
    }
  }, [selectedOverlayId]);

  const selectOverlay = useCallback((id: string | null) => {
    setSelectedOverlayId(id);
  }, []);

  const clearAllOverlays = useCallback(() => {
    setTextOverlays([]);
    setSelectedOverlayId(null);
  }, []);

  const getSelectedOverlay = useCallback(() => {
    return textOverlays.find(overlay => overlay.id === selectedOverlayId) || null;
  }, [textOverlays, selectedOverlayId]);

  return {
    textOverlays,
    selectedOverlayId,
    addTextOverlay,
    updateTextOverlay,
    deleteTextOverlay,
    selectOverlay,
    clearAllOverlays,
    getSelectedOverlay,
  };
};
