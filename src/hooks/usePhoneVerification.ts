import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import {
  validatePhoneNumber as validatePhoneNumberUtil,
  getFullPhoneNumber,
  Country,
} from '../utils/phoneUtils';
import { sendSMSVerification, verifySMSCode } from '../services/twilio/verification';
import { useUpdatePhoneNumberMutation } from '../store/api/userManagementApi';
import logger from '../utils/logger';

interface UsePhoneVerificationProps {
  selectedCountry: Country;
  currentPhoneNumber?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const usePhoneVerification = ({
  selectedCountry,
  currentPhoneNumber,
  onSuccess,
  onError,
}: UsePhoneVerificationProps) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const [step, setStep] = useState<'phone' | 'verify'>('phone');
  
  const [updatePhoneNumber, { isLoading }] = useUpdatePhoneNumberMutation();

  const validatePhoneNumber = useCallback((value: string): string => {
    return validatePhoneNumberUtil(value, selectedCountry);
  }, [selectedCountry]);

  const handleSendCode = useCallback(async () => {
    const validationError = validatePhoneNumber(phoneNumber);
    if (validationError) {
      setError(validationError);
      onError?.(validationError);
      return;
    }

    const fullPhoneNumber = getFullPhoneNumber(phoneNumber, selectedCountry);
    if (fullPhoneNumber === currentPhoneNumber) {
      onSuccess?.();
      return;
    }

    setError('');

    try {
      // Call Twilio Verify service
      const result = await sendSMSVerification(fullPhoneNumber);

      if (result.success) {
        setStep('verify');
        Alert.alert(
          'Verification Code Sent',
          `A 6-digit verification code has been sent to ${fullPhoneNumber}`,
          [{ text: 'OK' }]
        );
      } else {
        const errorMessage = result.error || 'Failed to send verification code';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (error: any) {
      logger.error('SMS send error:', error);
      const errorMessage = error.message || 'Failed to send verification code';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [phoneNumber, selectedCountry, currentPhoneNumber, validatePhoneNumber, onSuccess, onError]);

  const handleVerifyCode = useCallback(async () => {
    if (!verificationCode.trim() || verificationCode.length !== 6) {
      const errorMessage = 'Please enter the 6-digit verification code';
      setError(errorMessage);
      onError?.(errorMessage);
      return;
    }

    setError('');

    try {
      const fullPhoneNumber = getFullPhoneNumber(phoneNumber, selectedCountry);

      // Verify the code with Twilio Verify service
      const result = await verifySMSCode(fullPhoneNumber, verificationCode);

      if (result.success && result.valid) {
        // Update phone number in our database
        await updatePhoneNumber(fullPhoneNumber).unwrap();

        Alert.alert(
          'Phone Number Updated',
          'Your phone number has been successfully updated and verified.',
          [
            {
              text: 'OK',
              onPress: onSuccess,
            },
          ]
        );
      } else {
        const errorMessage = result.error || 'Invalid verification code';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (error: any) {
      logger.error('SMS verification error:', error);
      const errorMessage = error.message || 'Failed to verify code';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [verificationCode, phoneNumber, selectedCountry, updatePhoneNumber, onSuccess, onError]);

  const handleResendCode = useCallback(async () => {
    setVerificationCode('');
    setError('');
    await handleSendCode();
  }, [handleSendCode]);

  const goBackToPhoneStep = useCallback(() => {
    setStep('phone');
    setVerificationCode('');
    setError('');
  }, []);

  return {
    phoneNumber,
    setPhoneNumber,
    verificationCode,
    setVerificationCode,
    error,
    setError,
    step,
    isLoading,
    handleSendCode,
    handleVerifyCode,
    handleResendCode,
    goBackToPhoneStep,
    validatePhoneNumber,
  };
};
