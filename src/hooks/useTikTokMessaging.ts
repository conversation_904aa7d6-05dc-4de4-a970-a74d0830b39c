import { useState, useEffect, useCallback, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useRealtimeMessagingContext } from '../contexts/RealtimeMessagingContext';
import { useTypingIndicators } from './useTypingIndicators';
import { useUserPresence } from './useUserPresence';
import { 
  useGetMessagesQuery, 
  useSendMessageMutation, 
  useMarkMessagesAsReadMutation,
  useAddMessageReactionMutation,
  useRemoveMessageReactionMutation,
} from '../store/api/messagingApi';
import { MessageWithSender, SendMessageRequest } from '../types/messaging';
import { uploadMessageMedia, uploadVoiceMessage } from '../services/media/media-upload';
import logger from '../utils/logger';

interface UseTikTokMessagingProps {
  conversationId: string;
  currentUserId?: string;
  autoMarkAsRead?: boolean;
  enableTypingIndicators?: boolean;
  enablePresence?: boolean;
}

interface TikTokMessagingState {
  // Messages
  messages: MessageWithSender[];
  isLoading: boolean;
  error: any;
  
  // Sending
  isSending: boolean;
  isUploading: boolean;
  uploadProgress: number;
  
  // Real-time features
  typingUsers: any[];
  isTyping: boolean;
  onlineUsers: any[];
  
  // UI state
  replyingTo: MessageWithSender | null;
  selectedMessages: Set<string>;
  showScrollToBottom: boolean;
}

interface TikTokMessagingActions {
  // Message actions
  sendTextMessage: (content: string) => Promise<void>;
  sendMediaMessage: (mediaPath: string, type: 'image' | 'video' | 'audio') => Promise<void>;
  sendVoiceMessage: (audioPath: string) => Promise<void>;
  replyToMessage: (message: MessageWithSender, content: string) => Promise<void>;
  forwardMessage: (messageId: string, targetConversationId: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  
  // Reactions
  addReaction: (messageId: string, reaction: string) => Promise<void>;
  removeReaction: (messageId: string, reaction: string) => Promise<void>;
  
  // Typing indicators
  startTyping: (type?: 'text' | 'voice' | 'media') => void;
  stopTyping: () => void;
  handleTextChange: (text: string) => void;
  
  // UI actions
  setReplyingTo: (message: MessageWithSender | null) => void;
  toggleMessageSelection: (messageId: string) => void;
  clearSelection: () => void;
  markAsRead: () => Promise<void>;
  
  // Scroll actions
  scrollToMessage: (messageId: string) => void;
  scrollToBottom: () => void;
}

export const useTikTokMessaging = ({
  conversationId,
  currentUserId,
  autoMarkAsRead = true,
  enableTypingIndicators = true,
  enablePresence = true,
}: UseTikTokMessagingProps): [TikTokMessagingState, TikTokMessagingActions] => {
  
  // State
  const [isSending, setIsSending] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [replyingTo, setReplyingTo] = useState<MessageWithSender | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // Refs
  const scrollRef = useRef<any>(null);
  const lastReadMessageId = useRef<string | null>(null);

  // Real-time messaging context
  const realtimeContext = useRealtimeMessagingContext();

  // API hooks
  const { data: messages = [], isLoading, error, refetch } = useGetMessagesQuery(conversationId);
  const [sendMessage] = useSendMessageMutation();
  const [markMessagesAsRead] = useMarkMessagesAsReadMutation();
  const [addReaction] = useAddMessageReactionMutation();
  const [removeReaction] = useRemoveMessageReactionMutation();

  // Typing indicators (conditional)
  const typingHook = useTypingIndicators({
    conversationId,
    currentUserId,
  });

  // User presence (conditional)
  const presenceHook = useUserPresence({
    conversationId,
    currentUserId,
  });

  // Auto mark as read when messages change
  useEffect(() => {
    if (autoMarkAsRead && messages.length > 0) {
      const latestMessage = messages[messages.length - 1];
      if (latestMessage.sender_id !== currentUserId && latestMessage.id !== lastReadMessageId.current) {
        markAsRead();
        lastReadMessageId.current = latestMessage.id;
      }
    }
  }, [messages, autoMarkAsRead, currentUserId]);

  // Handle app state changes for presence
  useEffect(() => {
    if (!enablePresence) return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        presenceHook.setOnline();
      } else if (nextAppState === 'background') {
        presenceHook.setAway();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [enablePresence, presenceHook]);

  // Message actions
  const sendTextMessage = useCallback(async (content: string) => {
    if (!content.trim() || isSending) return;

    setIsSending(true);
    try {
      const messageData: SendMessageRequest = {
        conversation_id: conversationId,
        content: content.trim(),
        message_type: 'text',
        reply_to_message_id: replyingTo?.id,
      };

      await sendMessage(messageData).unwrap();
      setReplyingTo(null);
      
      if (enableTypingIndicators) {
        typingHook.stopTyping();
      }

      logger.debug('Text message sent successfully');
    } catch (error) {
      logger.error('Failed to send text message:', error);
      throw error;
    } finally {
      setIsSending(false);
    }
  }, [conversationId, replyingTo, isSending, sendMessage, enableTypingIndicators, typingHook]);

  const sendMediaMessage = useCallback(async (mediaPath: string, type: 'image' | 'video' | 'audio') => {
    if (isUploading) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Upload media with progress
      const uploadResult = await uploadMessageMedia(
        mediaPath,
        conversationId,
        {
          generateThumbnail: type === 'video',
          compressVideo: type === 'video',
          quality: type === 'image' ? 0.85 : 0.8,
        }
      );

      setUploadProgress(100);

      // Send message with media
      const messageData: SendMessageRequest = {
        conversation_id: conversationId,
        content: null,
        message_type: type,
        media_url: uploadResult.publicUrl,
        thumbnail_url: uploadResult.thumbnailUrl,
        file_name: uploadResult.fileName,
        file_size: uploadResult.fileSize,
        file_type: uploadResult.mimeType,
        duration: uploadResult.duration,
        reply_to_message_id: replyingTo?.id,
      };

      await sendMessage(messageData).unwrap();
      setReplyingTo(null);

      logger.debug('Media message sent successfully:', { type, url: uploadResult.publicUrl });
    } catch (error) {
      logger.error('Failed to send media message:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [conversationId, replyingTo, isUploading, sendMessage]);

  const sendVoiceMessage = useCallback(async (audioPath: string) => {
    if (isUploading) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Upload voice message with compression
      const uploadResult = await uploadVoiceMessage(audioPath, conversationId);
      setUploadProgress(100);

      // Send voice message
      const messageData: SendMessageRequest = {
        conversation_id: conversationId,
        content: null,
        message_type: 'voice',
        media_url: uploadResult.publicUrl,
        file_name: uploadResult.fileName,
        file_size: uploadResult.fileSize,
        file_type: uploadResult.mimeType,
        duration: uploadResult.duration,
        reply_to_message_id: replyingTo?.id,
      };

      await sendMessage(messageData).unwrap();
      setReplyingTo(null);

      logger.debug('Voice message sent successfully');
    } catch (error) {
      logger.error('Failed to send voice message:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [conversationId, replyingTo, isUploading, sendMessage]);

  const replyToMessage = useCallback(async (message: MessageWithSender, content: string) => {
    setReplyingTo(message);
    await sendTextMessage(content);
  }, [sendTextMessage]);

  const forwardMessage = useCallback(async (messageId: string, targetConversationId: string) => {
    // TODO: Implement message forwarding
    logger.debug('Forward message:', { messageId, targetConversationId });
  }, []);

  const deleteMessage = useCallback(async (messageId: string) => {
    // TODO: Implement message deletion
    logger.debug('Delete message:', messageId);
  }, []);

  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    // TODO: Implement message editing
    logger.debug('Edit message:', { messageId, newContent });
  }, []);

  // Reaction actions
  const handleAddReaction = useCallback(async (messageId: string, reaction: string) => {
    try {
      await addReaction({ messageId, reaction }).unwrap();
      logger.debug('Reaction added:', { messageId, reaction });
    } catch (error) {
      logger.error('Failed to add reaction:', error);
      throw error;
    }
  }, [addReaction]);

  const handleRemoveReaction = useCallback(async (messageId: string, reaction: string) => {
    try {
      await removeReaction({ messageId, reaction }).unwrap();
      logger.debug('Reaction removed:', { messageId, reaction });
    } catch (error) {
      logger.error('Failed to remove reaction:', error);
      throw error;
    }
  }, [removeReaction]);

  // Typing actions
  const startTyping = useCallback((type: 'text' | 'voice' | 'media' = 'text') => {
    if (enableTypingIndicators) {
      typingHook.startTyping(type);
    }
  }, [enableTypingIndicators, typingHook]);

  const stopTyping = useCallback(() => {
    if (enableTypingIndicators) {
      typingHook.stopTyping();
    }
  }, [enableTypingIndicators, typingHook]);

  const handleTextChange = useCallback((text: string) => {
    if (enableTypingIndicators) {
      typingHook.handleTextChange(text);
    }
  }, [enableTypingIndicators, typingHook]);

  // UI actions
  const toggleMessageSelection = useCallback((messageId: string) => {
    setSelectedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedMessages(new Set());
  }, []);

  const markAsRead = useCallback(async () => {
    try {
      await markMessagesAsRead({ conversation_id: conversationId }).unwrap();
      logger.debug('Messages marked as read');
    } catch (error) {
      logger.error('Failed to mark messages as read:', error);
    }
  }, [conversationId, markMessagesAsRead]);

  // Scroll actions
  const scrollToMessage = useCallback((messageId: string) => {
    // TODO: Implement scroll to specific message
    logger.debug('Scroll to message:', messageId);
  }, []);

  const scrollToBottom = useCallback(() => {
    scrollRef.current?.scrollToEnd({ animated: true });
  }, []);

  // State object
  const state: TikTokMessagingState = {
    messages,
    isLoading,
    error,
    isSending,
    isUploading,
    uploadProgress,
    typingUsers: enableTypingIndicators ? typingHook.typingUsers : [],
    isTyping: enableTypingIndicators ? typingHook.isTyping : false,
    onlineUsers: enablePresence ? presenceHook.onlineUsers : [],
    replyingTo,
    selectedMessages,
    showScrollToBottom,
  };

  // Actions object
  const actions: TikTokMessagingActions = {
    sendTextMessage,
    sendMediaMessage,
    sendVoiceMessage,
    replyToMessage,
    forwardMessage,
    deleteMessage,
    editMessage,
    addReaction: handleAddReaction,
    removeReaction: handleRemoveReaction,
    startTyping,
    stopTyping,
    handleTextChange,
    setReplyingTo,
    toggleMessageSelection,
    clearSelection,
    markAsRead,
    scrollToMessage,
    scrollToBottom,
  };

  return [state, actions];
};
