import { useState, useCallback } from 'react';

export const useCameraUI = () => {
  const [showControls, setShowControls] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showEffects, setShowEffects] = useState(false);

  const toggleControls = useCallback(() => {
    setShowControls(!showControls);
  }, [showControls]);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    // Close other panels
    if (!showFilters) {
      setShowEffects(false);
      setShowControls(false);
    }
  }, [showFilters]);

  const toggleEffects = useCallback(() => {
    setShowEffects(!showEffects);
    // Close other panels
    if (!showEffects) {
      setShowFilters(false);
      setShowControls(false);
    }
  }, [showEffects]);

  const closeAllPanels = useCallback(() => {
    setShowControls(false);
    setShowFilters(false);
    setShowEffects(false);
  }, []);

  return {
    // State
    showControls,
    showFilters,
    showEffects,
    
    // Actions
    toggleControls,
    toggleFilters,
    toggleEffects,
    closeAllPanels,
    
    // Setters
    setShowControls,
    setShowFilters,
    setShowEffects,
  };
};
