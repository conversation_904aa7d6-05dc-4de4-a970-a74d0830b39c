import { useState, useRef, useCallback } from 'react';
import { Animated, Dimensions, Alert } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import logger from '../../utils/logger';

type NavigationProp = StackNavigationProp<MainStackParamList>;

const { height: screenHeight } = Dimensions.get('window');

export interface MediaItem {
  id: string;
  uri: string;
  type: 'photo' | 'video';
  thumbnail?: string;
  duration?: number;
}

export const useMediaGallery = () => {
  const navigation = useNavigation<NavigationProp>();
  const [showGallery, setShowGallery] = useState(false);
  const [galleryItems, setGalleryItems] = useState<MediaItem[]>([]);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);

  const gallerySlideAnim = useRef(new Animated.Value(screenHeight)).current;

  const openGallery = useCallback(() => {
    setShowGallery(true);
    Animated.spring(gallerySlideAnim, {
      toValue: 0,
      useNativeDriver: true,
    }).start();
  }, [gallerySlideAnim]);

  const closeGallery = useCallback(() => {
    Animated.spring(gallerySlideAnim, {
      toValue: screenHeight,
      useNativeDriver: true,
    }).start(() => {
      setShowGallery(false);
    });
  }, [gallerySlideAnim]);

  const selectFromLibrary = useCallback(() => {
    launchImageLibrary(
      {
        mediaType: 'mixed',
        quality: 0.8,
        selectionLimit: 1,
      },
      (response) => {
        if (response.assets && response.assets[0]) {
          const asset = response.assets[0];
          const mediaItem: MediaItem = {
            id: Date.now().toString(),
            uri: asset.uri!,
            type: asset.type?.startsWith('video') ? 'video' : 'photo',
            duration: asset.duration,
          };
          setSelectedMedia(mediaItem);
          setGalleryItems(prev => [mediaItem, ...prev]);
        }
      }
    );
  }, []);

  const handleVideoRecorded = useCallback(async (videoUri: string, recordingTime: number) => {
    logger.debug('Video recorded:', videoUri);

    try {
      // Add to gallery for local display
      const newMediaItem: MediaItem = {
        id: Date.now().toString(),
        uri: videoUri,
        type: 'video',
        duration: recordingTime,
      };
      setGalleryItems(prev => [newMediaItem, ...prev]);

      // Navigate to video upload screen for editing and metadata
      navigation.navigate('VideoUpload', {
        videoUri,
        duration: recordingTime,
      });

    } catch (error) {
      logger.error('Video recording error:', error);
      Alert.alert('Error', 'Failed to process recorded video. Please try again.');
    }
  }, [navigation]);

  const handlePhotoTaken = useCallback(async (photoUri: string) => {
    logger.debug('Photo taken:', photoUri);

    try {
      // Add to gallery for local display
      const newMediaItem: MediaItem = {
        id: Date.now().toString(),
        uri: photoUri,
        type: 'photo',
      };
      setGalleryItems(prev => [newMediaItem, ...prev]);

      // Navigate to photo editor (if exists) or video upload screen
      navigation.navigate('PhotoToVideoEditor', {
        photoUri,
      });

    } catch (error) {
      logger.error('Photo processing error:', error);
      Alert.alert('Error', 'Failed to process photo. Please try again.');
    }
  }, [navigation]);

  return {
    // State
    showGallery,
    galleryItems,
    selectedMedia,
    gallerySlideAnim,
    
    // Actions
    openGallery,
    closeGallery,
    selectFromLibrary,
    handleVideoRecorded,
    handlePhotoTaken,
    
    // Setters
    setShowGallery,
    setGalleryItems,
    setSelectedMedia,
  };
};
