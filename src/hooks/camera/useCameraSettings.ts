import { useState, useCallback } from 'react';

export const useCameraSettings = () => {
  const [cameraType, setCameraType] = useState<'front' | 'back'>('back');
  const [flashMode, setFlashMode] = useState<'off' | 'on' | 'auto'>('off');
  const [recordingMode, setRecordingMode] = useState<'15s' | '60s' | '3min'>('60s');
  const [captureMode, setCaptureMode] = useState<'video' | 'photo'>('video');
  const [speed, setSpeed] = useState(1);
  const [timerEnabled, setTimerEnabled] = useState(false);
  const [timerDuration, setTimerDuration] = useState<3 | 10>(3);
  const [beautyLevel, setBeautyLevel] = useState(0);
  const [zoom, setZoom] = useState(1);

  // Filter and effects
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [selectedEffect, setSelectedEffect] = useState<string | null>(null);

  const handleFlipCamera = useCallback(() => {
    setCameraType(prev => prev === 'front' ? 'back' : 'front');
  }, []);

  const handleFlashToggle = useCallback(() => {
    const modes: Array<'off' | 'on' | 'auto'> = ['off', 'on', 'auto'];
    const currentIndex = modes.indexOf(flashMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setFlashMode(modes[nextIndex]);
  }, [flashMode]);

  const handleSpeedChange = useCallback((newSpeed: number) => {
    setSpeed(newSpeed);
  }, []);

  const handleTimerToggle = useCallback(() => {
    setTimerEnabled(!timerEnabled);
  }, [timerEnabled]);

  const handleTimerDurationChange = useCallback((duration: 3 | 10) => {
    setTimerDuration(duration);
  }, []);

  const handleBeautyLevelChange = useCallback((level: number) => {
    setBeautyLevel(level);
  }, []);

  const handleFilterSelect = useCallback((filterId: string) => {
    setSelectedFilter(filterId === selectedFilter ? null : filterId);
  }, [selectedFilter]);

  const handleEffectSelect = useCallback((effectId: string) => {
    setSelectedEffect(effectId === selectedEffect ? null : effectId);
  }, [selectedEffect]);

  const handleZoomChange = useCallback((newZoom: number) => {
    setZoom(Math.max(1, Math.min(3, newZoom)));
  }, []);

  return {
    // State
    cameraType,
    flashMode,
    recordingMode,
    captureMode,
    speed,
    timerEnabled,
    timerDuration,
    beautyLevel,
    zoom,
    selectedFilter,
    selectedEffect,
    
    // Setters
    setCameraType,
    setFlashMode,
    setRecordingMode,
    setCaptureMode,
    setSpeed,
    setTimerEnabled,
    setTimerDuration,
    setBeautyLevel,
    setZoom,
    setSelectedFilter,
    setSelectedEffect,
    
    // Handlers
    handleFlipCamera,
    handleFlashToggle,
    handleSpeedChange,
    handleTimerToggle,
    handleTimerDurationChange,
    handleBeautyLevelChange,
    handleFilterSelect,
    handleEffectSelect,
    handleZoomChange,
  };
};
