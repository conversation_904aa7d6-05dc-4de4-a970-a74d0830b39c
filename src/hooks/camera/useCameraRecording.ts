import { useState, useRef, useCallback } from 'react';
import { Animated, Alert } from 'react-native';
import { Camera } from 'react-native-vision-camera';
import { getCameraRecordingConfig, handleCameraError } from '../../utils/cameraConfig';
import logger from '../../utils/logger';

interface UseCameraRecordingProps {
  camera: React.RefObject<Camera | null>;
  flashMode: 'off' | 'on' | 'auto';
  recordingMode: '15s' | '60s' | '3min';
  onVideoRecorded: (videoUri: string) => void;
}

export const useCameraRecording = ({
  camera,
  flashMode,
  recordingMode,
  onVideoRecorded,
}: UseCameraRecordingProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [retryCount, setRetryCount] = useState(0);

  const recordingTimer = useRef<number | null>(null);
  const recordButtonScale = useRef(new Animated.Value(1)).current;
  const recordingProgress = useRef(new Animated.Value(0)).current;

  // Camera health check before recording
  const checkCameraHealth = useCallback(async (): Promise<boolean> => {
    if (!camera.current) {
      logger.warn('Camera ref is null');
      return false;
    }

    try {
      // Basic camera availability check
      return true;
    } catch (error) {
      logger.error('Camera health check failed:', error);
      return false;
    }
  }, [camera]);

  const startVideoRecording = useCallback(async () => {
    if (!camera.current) {
      Alert.alert('Camera Error', 'Camera is not available. Please try again.');
      return;
    }

    // Perform camera health check
    const isCameraHealthy = await checkCameraHealth();
    if (!isCameraHealthy) {
      Alert.alert('Camera Error', 'Camera is not ready. Please try again.');
      return;
    }

    try {
      setIsRecording(true);
      recordingProgress.setValue(0);
      
      // Animate record button
      Animated.spring(recordButtonScale, {
        toValue: 1.2,
        useNativeDriver: true,
      }).start();

      // Start progress animation
      const maxTime = recordingMode === '15s' ? 15000 : recordingMode === '60s' ? 60000 : 180000;
      Animated.timing(recordingProgress, {
        toValue: 1,
        duration: maxTime,
        useNativeDriver: false,
      }).start();

      // Enhanced recording options with fallbacks for Android MediaCodec issues
      const cameraConfig = getCameraRecordingConfig(flashMode, 'normal');
      const recordingOptions = {
        ...cameraConfig,
        onRecordingFinished: (video: any) => {
          setIsRecording(false);
          // Reset animations
          Animated.spring(recordButtonScale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          recordingProgress.setValue(0);

          if (recordingTimer.current) {
            clearInterval(recordingTimer.current);
            recordingTimer.current = null;
          }
          setRecordingTime(0);

          // Reset retry count on successful recording
          setRetryCount(0);

          onVideoRecorded(video.path);
        },
        onRecordingError: (error: any) => {
          setIsRecording(false);
          logger.error('Recording error:', error);

          // Reset animations
          Animated.spring(recordButtonScale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          recordingProgress.setValue(0);

          if (recordingTimer.current) {
            clearInterval(recordingTimer.current);
            recordingTimer.current = null;
          }
          setRecordingTime(0);

          // Handle camera errors with detailed messages and retry option
          const errorMessage = handleCameraError(error);

          // Offer retry for MediaCodec errors (up to 2 retries)
          if ((error.code === 'capture/recorder-error' || error.message?.includes('MediaCodec')) && retryCount < 2) {
            Alert.alert(
              'Recording Error',
              `${errorMessage}\n\nWould you like to try again?`,
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Retry',
                  onPress: () => {
                    setRetryCount(prev => prev + 1);
                    // Wait a moment before retrying
                    setTimeout(() => {
                      startVideoRecording();
                    }, 1000);
                  }
                }
              ]
            );
          } else {
            Alert.alert('Recording Error', errorMessage);
            setRetryCount(0); // Reset retry count
          }
        },
      };

      await camera.current.startRecording(recordingOptions);

      // Start timer
      recordingTimer.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (error: any) {
      setIsRecording(false);
      logger.error('Failed to start recording:', error);

      // Reset animations on error
      Animated.spring(recordButtonScale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
      recordingProgress.setValue(0);

      // Handle camera errors with detailed messages
      const errorMessage = handleCameraError(error);
      Alert.alert('Camera Error', errorMessage);
    }
  }, [camera, flashMode, recordingMode, recordButtonScale, recordingProgress, onVideoRecorded]);

  const stopVideoRecording = useCallback(async () => {
    if (!camera.current || !isRecording) return;

    try {
      await camera.current.stopRecording();
      
      // Reset animations
      Animated.spring(recordButtonScale, {
        toValue: 1,
        useNativeDriver: true,
      }).start();

      recordingProgress.setValue(0);
      
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
      
      setRecordingTime(0);
    } catch (error) {
      logger.error('Failed to stop recording:', error);
    }
  }, [camera, isRecording, recordButtonScale, recordingProgress]);

  const takePhoto = useCallback(async () => {
    if (!camera.current) return;

    try {
      const photo = await camera.current.takePhoto({
        flash: flashMode === 'on' ? 'on' : 'off',
        enableShutterSound: true,
      });
      
      return photo.path;
    } catch (error) {
      logger.error('Failed to take photo:', error);
      Alert.alert('Error', 'Failed to take photo');
      return null;
    }
  }, [camera, flashMode]);

  return {
    isRecording,
    recordingTime,
    recordButtonScale,
    recordingProgress,
    startVideoRecording,
    stopVideoRecording,
    takePhoto,
  };
};
