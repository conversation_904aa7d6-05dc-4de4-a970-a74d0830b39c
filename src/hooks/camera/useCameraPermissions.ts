import { useEffect } from 'react';
import { useCameraPermission, useMicrophonePermission } from 'react-native-vision-camera';

export const useCameraPermissions = () => {
  const { 
    hasPermission: hasCameraPermission, 
    requestPermission: requestCameraPermission 
  } = useCameraPermission();
  
  const { 
    hasPermission: hasMicPermission, 
    requestPermission: requestMicPermission 
  } = useMicrophonePermission();

  const permissionsGranted = hasCameraPermission && hasMicPermission;

  // Request permissions on mount
  useEffect(() => {
    const requestPermissions = async () => {
      if (!hasCameraPermission) {
        await requestCameraPermission();
      }
      if (!hasMicPermission) {
        await requestMicPermission();
      }
    };
    requestPermissions();
  }, [hasCameraPermission, hasMicPermission, requestCameraPermission, requestMicPermission]);

  return {
    hasCameraPermission,
    hasMicPermission,
    permissionsGranted,
    requestCameraPermission,
    requestMicPermission,
  };
};
