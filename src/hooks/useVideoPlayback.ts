import { useState, useEffect, useRef, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

/**
 * Custom hook for managing video playback state
 * 
 * Handles automatic pausing when:
 * - Screen loses focus (navigation)
 * - App goes to background
 * - Component unmounts
 * 
 * @param isActive - Whether this video should be playing (e.g., visible in viewport)
 * @param initialPaused - Initial paused state
 * @returns Object with playback state and controls
 */
export const useVideoPlayback = (isActive: boolean = true, initialPaused: boolean = false) => {
  const [isPaused, setIsPaused] = useState(initialPaused);
  const [showPlayButton, setShowPlayButton] = useState(false);
  const appState = useRef(AppState.currentState);

  // Handle app state changes (background/foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // App came to foreground - resume if active
        if (isActive) {
          setIsPaused(false);
        }
      } else if (nextAppState.match(/inactive|background/)) {
        // App went to background - pause video
        setIsPaused(true);
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isActive]);

  // Handle screen focus/blur
  useFocusEffect(
    useCallback(() => {
      // Screen is focused - resume if active
      if (isActive) {
        setIsPaused(false);
      }

      return () => {
        // Screen is unfocused - pause video
        setIsPaused(true);
      };
    }, [isActive])
  );

  // Update paused state when isActive changes
  useEffect(() => {
    if (!isActive) {
      setIsPaused(true);
    } else {
      setIsPaused(false);
    }
  }, [isActive]);

  // Toggle play/pause
  const togglePlayPause = () => {
    if (!isActive) return; // Don't allow play if not active
    
    setIsPaused(!isPaused);
    setShowPlayButton(true);
    
    // Hide play button after 1 second
    setTimeout(() => {
      setShowPlayButton(false);
    }, 1000);
  };

  // Force pause (useful for navigation or other events)
  const pause = () => {
    setIsPaused(true);
  };

  // Force play (only if active)
  const play = () => {
    if (isActive) {
      setIsPaused(false);
    }
  };

  return {
    isPaused,
    showPlayButton,
    togglePlayPause,
    pause,
    play,
  };
};
