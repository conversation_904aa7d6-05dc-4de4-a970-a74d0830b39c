import { useCallback } from 'react';
import { Alert } from 'react-native';
import {
  useGetConversationsQuery,
  useCreateConversationMutation,
  useGetMessagesQuery,
  useSendMessageMutation,
  useMarkMessagesAsReadMutation,
} from '../../store/api/messagingApi';
import { ConversationWithDetails, MessageWithSender, SendMessageRequest } from '../../types/messaging';
import logger from '../../utils/logger';

/**
 * Custom hook for messaging operations
 *
 * Provides a clean abstraction over RTK Query messaging APIs, handling
 * conversations management, creation, and error handling automatically.
 *
 * @returns Object containing conversations data and management functions
 *
 * @example
 * ```tsx
 * const MessagesScreen = () => {
 *   const { conversations, createConversation, isLoadingConversations } = useMessaging();
 *
 *   const handleStartChat = async (userId: string) => {
 *     const conversationId = await createConversation(userId);
 *     if (conversationId) {
 *       navigation.navigate('ChatDetail', { conversationId });
 *     }
 *   };
 *
 *   return (
 *     <FlatList
 *       data={conversations}
 *       renderItem={({ item }) => <ConversationItem conversation={item} />}
 *       refreshing={isLoadingConversations}
 *     />
 *   );
 * };
 * ```
 */
export const useMessaging = () => {
  // Conversations
  const {
    data: conversations = [],
    isLoading: isLoadingConversations,
    error: conversationsError,
    refetch: refetchConversations,
  } = useGetConversationsQuery();

  const [createConversationMutation, { isLoading: isCreatingConversation }] = useCreateConversationMutation();

  // Create conversation with error handling
  const createConversation = useCallback(async (otherUserId: string): Promise<string | null> => {
    try {
      const conversationId = await createConversationMutation({ other_user_id: otherUserId }).unwrap();
      logger.debug('Conversation created successfully:', conversationId);
      return conversationId;
    } catch (error) {
      logger.error('Failed to create conversation:', error);
      Alert.alert('Error', 'Failed to create conversation. Please try again.');
      return null;
    }
  }, [createConversationMutation]);

  return {
    // Conversations
    conversations,
    isLoadingConversations,
    conversationsError,
    refetchConversations,
    createConversation,
    isCreatingConversation,
  };
};

/**
 * Custom hook for managing messages in a specific conversation
 *
 * Handles message fetching, sending, and read status management for a specific
 * conversation. Provides automatic error handling and loading states.
 *
 * @param conversationId - The ID of the conversation to manage
 * @returns Object containing messages data and management functions
 *
 * @example
 * ```tsx
 * const ChatDetailScreen = ({ route }) => {
 *   const { conversationId } = route.params;
 *   const {
 *     messages,
 *     isLoadingMessages,
 *     sendMessage,
 *     markMessagesAsRead
 *   } = useConversationMessages(conversationId);
 *
 *   useEffect(() => {
 *     if (messages.length > 0) {
 *       markMessagesAsRead();
 *     }
 *   }, [messages.length, markMessagesAsRead]);
 *
 *   return (
 *     <FlatList
 *       data={messages}
 *       renderItem={({ item }) => <MessageItem message={item} />}
 *       refreshing={isLoadingMessages}
 *     />
 *   );
 * };
 * ```
 */
export const useConversationMessages = (conversationId: string) => {
  // Messages
  const {
    data: messages = [],
    isLoading: isLoadingMessages,
    error: messagesError,
    refetch: refetchMessages,
  } = useGetMessagesQuery(conversationId, {
    refetchOnMountOrArgChange: true,
    skip: !conversationId,
  });

  const [sendMessageMutation, { isLoading: isSendingMessage }] = useSendMessageMutation();
  const [markAsReadMutation, { isLoading: isMarkingAsRead }] = useMarkMessagesAsReadMutation();

  // Send message with error handling
  const sendMessage = useCallback(async (messageData: Omit<SendMessageRequest, 'conversation_id'>): Promise<boolean> => {
    try {
      await sendMessageMutation({
        ...messageData,
        conversation_id: conversationId,
      }).unwrap();
      
      logger.debug('Message sent successfully');
      return true;
    } catch (error) {
      logger.error('Failed to send message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
      return false;
    }
  }, [sendMessageMutation, conversationId]);

  // Mark messages as read with error handling
  const markMessagesAsRead = useCallback(async (): Promise<boolean> => {
    try {
      await markAsReadMutation(conversationId).unwrap();
      logger.debug('Messages marked as read');
      return true;
    } catch (error) {
      logger.error('Failed to mark messages as read:', error);
      // Don't show error to user for read receipts
      return false;
    }
  }, [markAsReadMutation, conversationId]);

  return {
    // Messages data
    messages,
    isLoadingMessages,
    messagesError,
    refetchMessages,
    
    // Message actions
    sendMessage,
    isSendingMessage,
    markMessagesAsRead,
    isMarkingAsRead,
  };
};

/**
 * Hook for sending different types of messages
 */
export const useMessageSender = (conversationId: string) => {
  const { sendMessage, isSendingMessage } = useConversationMessages(conversationId);

  const sendTextMessage = useCallback(async (content: string): Promise<boolean> => {
    return sendMessage({
      content,
      message_type: 'text',
    });
  }, [sendMessage]);

  const sendMediaMessage = useCallback(async (
    fileUrl: string,
    messageType: 'image' | 'video' | 'audio',
    fileName?: string,
    fileSize?: number,
    thumbnailUrl?: string
  ): Promise<boolean> => {
    return sendMessage({
      content: '',
      message_type: messageType,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize,
      thumbnail_url: thumbnailUrl,
    });
  }, [sendMessage]);

  return {
    sendTextMessage,
    sendMediaMessage,
    isSendingMessage,
  };
};
