import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useAppDispatch } from '../../store/hooks';
import { updateUserProfile } from '../../store/slices/authSlice';
import {
  useGetCurrentUserCompleteQuery,
  useUpdateUserProfileMutation,
  useUpdatePhoneNumberMutation,
  useUpdateEmailMutation,
  useUpdateUsernameMutation,
} from '../../store/api/userManagementApi';
import { UpdateProfileData } from '../../types/user';
import logger from '../../utils/logger';

/**
 * Custom hook for user profile management
 * Provides a clean abstraction over user profile operations
 */
export const useUserProfile = () => {
  const dispatch = useAppDispatch();

  // Get current user data
  const {
    data: userData,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useGetCurrentUserCompleteQuery();

  // Profile update mutations
  const [updateProfileMutation, { isLoading: isUpdatingProfile }] = useUpdateUserProfileMutation();
  const [updatePhoneMutation, { isLoading: isUpdatingPhone }] = useUpdatePhoneNumberMutation();
  const [updateEmailMutation, { isLoading: isUpdatingEmail }] = useUpdateEmailMutation();
  const [updateUsernameMutation, { isLoading: isUpdatingUsername }] = useUpdateUsernameMutation();

  // Update profile with error handling and Redux sync
  const updateProfile = useCallback(async (profileData: UpdateProfileData): Promise<boolean> => {
    try {
      const updatedUser = await updateProfileMutation(profileData).unwrap();
      
      // Update Redux store
      dispatch(updateUserProfile(profileData));
      
      logger.debug('Profile updated successfully');
      return true;
    } catch (error) {
      logger.error('Failed to update profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
      return false;
    }
  }, [updateProfileMutation, dispatch]);

  // Update specific profile fields
  const updateFullName = useCallback(async (fullName: string): Promise<boolean> => {
    return updateProfile({ full_name: fullName });
  }, [updateProfile]);

  const updateBio = useCallback(async (bio: string): Promise<boolean> => {
    return updateProfile({ bio });
  }, [updateProfile]);

  const updateAvatarUrl = useCallback(async (avatarUrl: string): Promise<boolean> => {
    return updateProfile({ avatar_url: avatarUrl });
  }, [updateProfile]);

  // Update phone number with verification
  const updatePhoneNumber = useCallback(async (phoneNumber: string): Promise<boolean> => {
    try {
      await updatePhoneMutation(phoneNumber).unwrap();
      
      // Update Redux store
      dispatch(updateUserProfile({ phone_number: phoneNumber }));
      
      logger.debug('Phone number updated successfully');
      return true;
    } catch (error) {
      logger.error('Failed to update phone number:', error);
      Alert.alert('Error', 'Failed to update phone number. Please try again.');
      return false;
    }
  }, [updatePhoneMutation, dispatch]);

  // Update email with verification
  const updateEmail = useCallback(async (email: string): Promise<boolean> => {
    try {
      await updateEmailMutation(email).unwrap();
      
      // Update Redux store
      dispatch(updateUserProfile({ email }));
      
      logger.debug('Email updated successfully');
      Alert.alert('Success', 'Email updated successfully. Please check your inbox for verification.');
      return true;
    } catch (error) {
      logger.error('Failed to update email:', error);
      Alert.alert('Error', 'Failed to update email. Please try again.');
      return false;
    }
  }, [updateEmailMutation, dispatch]);

  // Update username
  const updateUsername = useCallback(async (username: string): Promise<boolean> => {
    try {
      await updateUsernameMutation(username).unwrap();
      
      // Update Redux store
      dispatch(updateUserProfile({ username }));
      
      logger.debug('Username updated successfully');
      return true;
    } catch (error) {
      logger.error('Failed to update username:', error);
      Alert.alert('Error', 'Failed to update username. Please try again.');
      return false;
    }
  }, [updateUsernameMutation, dispatch]);

  return {
    // User data
    userData,
    isLoadingProfile,
    profileError,
    refetchProfile,
    
    // Profile updates
    updateProfile,
    updateFullName,
    updateBio,
    updateAvatarUrl,
    updatePhoneNumber,
    updateEmail,
    updateUsername,
    
    // Loading states
    isUpdatingProfile,
    isUpdatingPhone,
    isUpdatingEmail,
    isUpdatingUsername,
    isUpdating: isUpdatingProfile || isUpdatingPhone || isUpdatingEmail || isUpdatingUsername,
  };
};
