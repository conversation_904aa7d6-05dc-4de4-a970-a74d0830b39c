import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useGetUserVideosQuery } from '../../store/api/videoApi';
import { Video } from '../../types/video';
import logger from '../../utils/logger';

/**
 * Custom hook for video management
 * Provides a clean abstraction over video operations
 */
export const useUserVideos = (userId: string, privacy: 'public' | 'private' | 'friends' = 'public') => {
  const {
    data: videos = [],
    isLoading: isLoadingVideos,
    error: videosError,
    refetch: refetchVideos,
  } = useGetUserVideosQuery(
    { userId, privacy },
    { skip: !userId }
  );

  // Refresh videos with error handling
  const refreshVideos = useCallback(async (): Promise<boolean> => {
    try {
      await refetchVideos().unwrap();
      logger.debug('Videos refreshed successfully');
      return true;
    } catch (error) {
      logger.error('Failed to refresh videos:', error);
      Alert.alert('Error', 'Failed to refresh videos. Please try again.');
      return false;
    }
  }, [refetchVideos]);

  return {
    // Video data
    videos,
    isLoadingVideos,
    videosError,
    
    // Actions
    refreshVideos,
    refetchVideos,
  };
};

/**
 * Hook for managing multiple video privacy levels
 */
export const useUserVideosByPrivacy = (userId: string) => {
  const publicVideos = useUserVideos(userId, 'public');
  const privateVideos = useUserVideos(userId, 'private');
  const friendsVideos = useUserVideos(userId, 'friends');

  const refreshAllVideos = useCallback(async (): Promise<boolean> => {
    try {
      await Promise.all([
        publicVideos.refreshVideos(),
        privateVideos.refreshVideos(),
        friendsVideos.refreshVideos(),
      ]);
      return true;
    } catch (error) {
      logger.error('Failed to refresh all videos:', error);
      return false;
    }
  }, [publicVideos, privateVideos, friendsVideos]);

  return {
    publicVideos: {
      videos: publicVideos.videos,
      isLoading: publicVideos.isLoadingVideos,
      error: publicVideos.videosError,
      refresh: publicVideos.refreshVideos,
    },
    privateVideos: {
      videos: privateVideos.videos,
      isLoading: privateVideos.isLoadingVideos,
      error: privateVideos.videosError,
      refresh: privateVideos.refreshVideos,
    },
    friendsVideos: {
      videos: friendsVideos.videos,
      isLoading: friendsVideos.isLoadingVideos,
      error: friendsVideos.videosError,
      refresh: friendsVideos.refreshVideos,
    },
    refreshAllVideos,
  };
};

/**
 * Hook for video statistics and metadata
 */
export const useVideoStats = (videos: Video[]) => {
  const getTotalViews = useCallback((): number => {
    return videos.reduce((total, video) => total + (video.views || 0), 0);
  }, [videos]);

  const getTotalLikes = useCallback((): number => {
    return videos.reduce((total, video) => total + (video.likes || 0), 0);
  }, [videos]);

  const getMostPopularVideo = useCallback((): Video | null => {
    if (videos.length === 0) return null;
    return videos.reduce((mostPopular, video) => 
      (video.views || 0) > (mostPopular.views || 0) ? video : mostPopular
    );
  }, [videos]);

  const getVideosByTag = useCallback((tag: string): Video[] => {
    return videos.filter(video => 
      video.tags && video.tags.includes(tag)
    );
  }, [videos]);

  return {
    totalViews: getTotalViews(),
    totalLikes: getTotalLikes(),
    totalVideos: videos.length,
    mostPopularVideo: getMostPopularVideo(),
    getTotalViews,
    getTotalLikes,
    getMostPopularVideo,
    getVideosByTag,
  };
};
