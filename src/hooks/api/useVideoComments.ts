import { useCallback, useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';
import {
  useGetVideoCommentsQuery,
  useCreateCommentMutation,
  useToggleCommentLikeMutation,
  useDeleteCommentMutation,
} from '../../store/api/commentsApi';
import { subscribeToVideoComments } from '../../utils/realtimeManager';
import logger from '../../utils/logger';

export interface UseVideoCommentsOptions {
  videoId: string;
  initialCommentsCount?: number;
}

export const useVideoComments = ({ videoId, initialCommentsCount = 0 }: UseVideoCommentsOptions) => {
  const currentUser = useAppSelector(selectCurrentUser);
  const dispatch = useAppDispatch();
  const [localCommentsCount, setLocalCommentsCount] = useState(initialCommentsCount);

  // API hooks
  const { 
    data: comments = [], 
    isLoading, 
    refetch,
    error 
  } = useGetVideoCommentsQuery({ videoId });

  const [createComment, { isLoading: isCreating }] = useCreateCommentMutation();
  const [toggleCommentLike, { isLoading: isTogglingLike }] = useToggleCommentLikeMutation();
  const [deleteComment, { isLoading: isDeleting }] = useDeleteCommentMutation();

  // Calculate total comments count including nested replies
  const calculateTotalCommentsCount = useCallback((commentsList: any[]): number => {
    let total = 0;
    
    const countComments = (comments: any[]): number => {
      let count = 0;
      for (const comment of comments) {
        count += 1; // Count the comment itself
        if (comment.replies && comment.replies.length > 0) {
          count += countComments(comment.replies); // Count nested replies
        }
      }
      return count;
    };

    return countComments(commentsList);
  }, []);

  // Update local count when comments data changes
  useEffect(() => {
    if (comments.length > 0) {
      const totalCount = calculateTotalCommentsCount(comments);
      setLocalCommentsCount(totalCount);
    } else if (comments.length === 0 && !isLoading) {
      setLocalCommentsCount(0);
    }
  }, [comments, calculateTotalCommentsCount, isLoading]);

  // Update local count when initial count changes
  useEffect(() => {
    if (comments.length === 0 && initialCommentsCount > 0) {
      setLocalCommentsCount(initialCommentsCount);
    }
  }, [initialCommentsCount, comments.length]);

  // Set up real-time subscription for comment updates
  useEffect(() => {
    if (!videoId) return;

    const channel = subscribeToVideoComments(videoId, dispatch, {
      onCommentAdded: (comment) => {
        logger.debug('Comment added via real-time:', comment);
        setLocalCommentsCount(prev => prev + 1);
        refetch(); // Refetch to get the latest comments
      },
      onCommentUpdated: (comment) => {
        logger.debug('Comment updated via real-time:', comment);
        refetch(); // Refetch to get the updated comment
      },
      onCommentDeleted: (commentId) => {
        logger.debug('Comment deleted via real-time:', commentId);
        setLocalCommentsCount(prev => Math.max(0, prev - 1));
        refetch(); // Refetch to remove the deleted comment
      }
    });

    return () => {
      if (channel) {
        channel.unsubscribe();
      }
    };
  }, [videoId, dispatch, refetch]);

  // Create comment with optimistic updates
  const addComment = useCallback(async (text: string, parentId?: string): Promise<boolean> => {
    if (!text.trim() || !currentUser) {
      logger.warn('Cannot add comment: missing text or user');
      return false;
    }

    try {
      // Optimistic update
      setLocalCommentsCount(prev => prev + 1);

      await createComment({
        text: text.trim(),
        video_id: videoId,
        parent_id: parentId,
      }).unwrap();

      logger.debug('Comment created successfully');
      return true;
    } catch (error) {
      logger.error('Failed to create comment:', error);
      
      // Revert optimistic update
      setLocalCommentsCount(prev => Math.max(0, prev - 1));
      
      return false;
    }
  }, [videoId, currentUser, createComment]);

  // Like/unlike comment
  const toggleLike = useCallback(async (commentId: string, isCurrentlyLiked: boolean, currentLikesCount: number): Promise<boolean> => {
    if (!currentUser) {
      logger.warn('Cannot toggle comment like: no user');
      return false;
    }

    try {
      await toggleCommentLike({
        commentId,
        isCurrentlyLiked,
        currentLikesCount,
        videoId,
      }).unwrap();

      logger.debug('Comment like toggled successfully');
      return true;
    } catch (error) {
      logger.error('Failed to toggle comment like:', error);
      return false;
    }
  }, [currentUser, toggleCommentLike, videoId]);

  // Delete comment with optimistic updates
  const removeComment = useCallback(async (commentId: string, parentId?: string): Promise<boolean> => {
    if (!currentUser) {
      logger.warn('Cannot delete comment: no user');
      return false;
    }

    try {
      // Find the comment to get its reply count for accurate count reduction
      const findComment = (commentsList: any[], id: string): any => {
        for (const comment of commentsList) {
          if (comment.id === id) return comment;
          if (comment.replies) {
            const found = findComment(comment.replies, id);
            if (found) return found;
          }
        }
        return null;
      };

      const commentToDelete = findComment(comments, commentId);
      const replyCount = commentToDelete?.replies?.length || 0;
      const totalToRemove = 1 + replyCount; // Comment + its replies

      // Optimistic update
      setLocalCommentsCount(prev => Math.max(0, prev - totalToRemove));

      await deleteComment({
        commentId,
        videoId,
        parentId,
      }).unwrap();

      logger.debug('Comment deleted successfully');
      return true;
    } catch (error) {
      logger.error('Failed to delete comment:', error);
      
      // Revert optimistic update
      const commentToDelete = comments.find(c => c.id === commentId);
      const replyCount = commentToDelete?.replies?.length || 0;
      const totalToRevert = 1 + replyCount;
      setLocalCommentsCount(prev => prev + totalToRevert);
      
      return false;
    }
  }, [currentUser, deleteComment, videoId, comments]);

  // Get formatted comments count
  const getFormattedCount = useCallback((count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }, []);

  return {
    // Data
    comments,
    commentsCount: localCommentsCount,
    formattedCommentsCount: getFormattedCount(localCommentsCount),
    
    // Loading states
    isLoading,
    isCreating,
    isTogglingLike,
    isDeleting,
    isProcessing: isCreating || isTogglingLike || isDeleting,
    
    // Actions
    addComment,
    toggleLike,
    removeComment,
    refetch,
    
    // Utilities
    calculateTotalCommentsCount,
    
    // Error state
    error,
  };
};
