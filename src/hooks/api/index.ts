/**
 * API Hooks Index
 * 
 * This file provides a centralized export for all API-related hooks.
 * Components should import from this file instead of directly using RTK Query hooks.
 * 
 * Benefits:
 * - Clean abstraction layer between components and store
 * - Consistent error handling and loading states
 * - Business logic separation from UI logic
 * - Easy testing and mocking
 * - Type safety and better developer experience
 */

// Messaging hooks
export {
  useMessaging,
  useConversationMessages,
  useMessageSender,
} from './useMessaging';

// Video hooks
export {
  useUserVideos,
  useUserVideosByPrivacy,
  useVideoStats,
} from './useVideos';

// Video likes hooks
export {
  useVideoLikes,
  useBulkVideoLikes,
} from './useVideoLikes';

// User profile hooks
export {
  useUserProfile,
} from './useUserProfile';

/**
 * Usage Examples:
 * 
 * // Instead of this (direct RTK Query usage):
 * const { data: conversations } = useGetConversationsQuery();
 * 
 * // Use this (abstracted hook):
 * const { conversations, isLoadingConversations } = useMessaging();
 * 
 * // Instead of this (complex message sending):
 * const [sendMessage] = useSendMessageMutation();
 * const handleSend = async () => {
 *   try {
 *     await sendMessage({ conversation_id, content, message_type: 'text' }).unwrap();
 *   } catch (error) {
 *     Alert.alert('Error', 'Failed to send message');
 *   }
 * };
 * 
 * // Use this (simplified):
 * const { sendTextMessage } = useMessageSender(conversationId);
 * const handleSend = () => sendTextMessage(content);
 */
