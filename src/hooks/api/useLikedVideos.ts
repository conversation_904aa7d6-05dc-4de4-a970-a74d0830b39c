import { useCallback, useEffect, useState } from 'react';
import { useAppSelector } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';
import { useGetLikedVideosQuery } from '../../store/api/videoApi';
import { subscribeToVideoLikes } from '../../utils/realtimeManager';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export interface UseLikedVideosOptions {
  enabled?: boolean;
  refetchOnMount?: boolean;
}

export const useLikedVideos = (options: UseLikedVideosOptions = {}) => {
  const { enabled = true, refetchOnMount = true } = options;
  const currentUser = useAppSelector(selectCurrentUser);
  const [localLikedVideos, setLocalLikedVideos] = useState<any[]>([]);

  // API query for liked videos
  const {
    data: likedVideos = [],
    isLoading,
    error,
    refetch,
    isFetching,
  } = useGetLikedVideosQuery(undefined, {
    skip: !currentUser || !enabled,
    refetchOnMountOrArgChange: refetchOnMount,
  });

  // Update local state when API data changes
  useEffect(() => {
    if (likedVideos && likedVideos.length >= 0) {
      setLocalLikedVideos(prevVideos => {
        // Only update if the data has actually changed
        if (prevVideos.length !== likedVideos.length) {
          return likedVideos;
        }
        
        // Check if any video IDs have changed
        const prevIds = prevVideos.map(v => v.id).sort();
        const newIds = likedVideos.map(v => v.id).sort();
        const hasChanged = prevIds.length !== newIds.length || 
          prevIds.some((id, index) => id !== newIds[index]);
        
        return hasChanged ? likedVideos : prevVideos;
      });
    }
  }, [likedVideos]);

  // Set up real-time subscriptions for all liked videos
  useEffect(() => {
    if (!currentUser?.id || !enabled || likedVideos.length === 0) return;

    const unsubscribeFunctions: (() => void)[] = [];

    // Subscribe to like changes for each video
    likedVideos.forEach((video) => {
      const unsubscribe = subscribeToVideoLikes(video.id, undefined, {
        onLikeRemoved: (videoId, userId) => {
          // If current user unliked this video, remove it from liked videos
          if (userId === currentUser.id) {
            setLocalLikedVideos(prev => prev.filter(v => v.id !== videoId));
            logger.debug('Video removed from liked videos:', videoId);
          }
        },
      });
      unsubscribeFunctions.push(unsubscribe);
    });

    return () => {
      unsubscribeFunctions.forEach(fn => fn());
    };
  }, [currentUser?.id, enabled, likedVideos]);

  // Subscribe to global video likes to add newly liked videos
  useEffect(() => {
    if (!currentUser?.id || !enabled) return;

    // We need to subscribe to all video likes to catch when user likes new videos
    // This is a bit more complex as we need to listen to the user's like activity
    const channel = supabase
      .channel(`user_likes:${currentUser.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'video_likes',
          filter: `user_id=eq.${currentUser.id}`,
        },
        async (payload) => {
          const videoId = (payload as any).new?.video_id;
          if (videoId) {
            logger.debug('User liked a new video:', videoId);
            // Refetch liked videos to include the new one
            refetch();
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [currentUser?.id, enabled, refetch]);

  // Add video to liked videos (optimistic update)
  const addLikedVideo = useCallback((video: any) => {
    setLocalLikedVideos(prev => {
      // Check if video is already in the list
      if (prev.some(v => v.id === video.id)) {
        return prev;
      }
      // Add to the beginning of the list (most recent first)
      return [video, ...prev];
    });
  }, []);

  // Remove video from liked videos (optimistic update)
  const removeLikedVideo = useCallback((videoId: string) => {
    setLocalLikedVideos(prev => prev.filter(v => v.id !== videoId));
  }, []);

  // Check if a specific video is liked
  const isVideoLiked = useCallback((videoId: string): boolean => {
    return localLikedVideos.some(video => video.id === videoId);
  }, [localLikedVideos]);

  // Get liked videos count
  const likedVideosCount = localLikedVideos.length;

  // Format videos for grid display
  const formatVideosForGrid = useCallback((videos: any[]) => {
    if (!videos || !Array.isArray(videos)) return [];

    return videos.map(video => {
      // Ensure video is an object
      if (!video || typeof video !== 'object') {
        return null;
      }

      // Safely handle views conversion
      const views = video.stats?.views;
      let viewsString = '0';
      if (views !== null && views !== undefined) {
        const viewsNum = Number(views);
        if (!isNaN(viewsNum) && viewsNum >= 0) {
          viewsString = String(viewsNum);
        }
      }

      // Safely handle likes
      const likes = video.stats?.likes;
      let likesNumber = 0;
      if (likes !== null && likes !== undefined) {
        const likesNum = Number(likes);
        if (!isNaN(likesNum) && likesNum >= 0) {
          likesNumber = likesNum;
        }
      }

      return {
        id: String(video.id || ''),
        thumbnail: String(video.thumbnail || ''),
        uri: String(video.url || ''),
        views: viewsString,
        likes: likesNumber,
        user: video.user,
        title: video.title,
        description: video.description,
        created_at: video.created_at,
      };
    }).filter(Boolean); // Remove any null entries
  }, []);

  // Get formatted liked videos for grid
  const likedVideosForGrid = formatVideosForGrid(localLikedVideos);

  // Refresh liked videos
  const refreshLikedVideos = useCallback(async () => {
    try {
      await refetch();
      logger.debug('Liked videos refreshed');
    } catch (error) {
      logger.error('Failed to refresh liked videos:', error);
    }
  }, [refetch]);

  return {
    // Data
    likedVideos: localLikedVideos,
    likedVideosForGrid,
    likedVideosCount,

    // Loading states
    isLoading,
    isFetching,
    isRefreshing: isFetching && !isLoading,

    // Actions
    addLikedVideo,
    removeLikedVideo,
    refreshLikedVideos,
    refetch,

    // Utilities
    isVideoLiked,
    formatVideosForGrid,

    // Error state
    error,
  };
};

// Hook for managing a single video's liked status in the context of the liked videos list
export const useVideoLikedStatus = (videoId: string) => {
  const { likedVideos, addLikedVideo, removeLikedVideo, isVideoLiked } = useLikedVideos();

  const toggleVideoInLikedList = useCallback((video: any, isLiked: boolean) => {
    if (isLiked) {
      addLikedVideo(video);
    } else {
      removeLikedVideo(videoId);
    }
  }, [videoId, addLikedVideo, removeLikedVideo]);

  return {
    isLiked: isVideoLiked(videoId),
    toggleVideoInLikedList,
    likedVideosCount: likedVideos.length,
  };
};
