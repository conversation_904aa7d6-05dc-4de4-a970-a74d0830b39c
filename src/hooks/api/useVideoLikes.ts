import { useCallback, useEffect, useState } from 'react';
import { useAppSelector } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';
import {
  useToggleVideoLikeMutation,
  useCheckVideoLikedQuery,
} from '../../store/api/videoApi';
import { subscribeToVideoLikes } from '../../utils/realtimeManager';
import logger from '../../utils/logger';

/**
 * Custom hook for managing video likes
 * Provides a clean abstraction over video like operations
 */
export const useVideoLikes = (videoId: string, initialLikeCount: number = 0) => {
  const currentUser = useAppSelector(selectCurrentUser);
  const [localLikesCount, setLocalLikesCount] = useState(initialLikeCount);
  const [localIsLiked, setLocalIsLiked] = useState(false);

  // Check if video is liked by current user
  const {
    data: isLiked,
    refetch: refetchLikeStatus,
    isLoading: isCheckingLiked
  } = useCheckVideoLikedQuery(videoId, {
    skip: !currentUser,
  });

  // Toggle like mutation
  const [toggleVideoLikeMutation, { isLoading: isToggling }] = useToggleVideoLikeMutation();

  // Update local state when API data changes
  useEffect(() => {
    if (isLiked !== undefined) {
      setLocalIsLiked(isLiked);
    }
  }, [isLiked]);

  // Update local likes count when initial count changes
  useEffect(() => {
    setLocalLikesCount(initialLikeCount);
  }, [initialLikeCount]);

  // Set up real-time subscription for like updates
  useEffect(() => {
    if (!videoId || !currentUser?.id) return;

    const unsubscribe = subscribeToVideoLikes(videoId, undefined, {
      onLikeAdded: (videoId, userId) => {
        setLocalLikesCount(prev => prev + 1);
        if (userId === currentUser.id) {
          setLocalIsLiked(true);
        }
      },
      onLikeRemoved: (videoId, userId) => {
        setLocalLikesCount(prev => Math.max(0, prev - 1));
        if (userId === currentUser.id) {
          setLocalIsLiked(false);
        }
      }
    });

    return unsubscribe;
  }, [videoId, currentUser?.id]);

  // Toggle like with optimistic updates
  const toggleLike = useCallback(async (): Promise<boolean> => {
    if (!videoId || !currentUser || isToggling) {
      logger.warn('Cannot toggle like: missing videoId, user, or already processing');
      return false;
    }

    const previousLikesCount = localLikesCount;
    const previousIsLiked = localIsLiked;

    try {
      // Optimistic update
      setLocalIsLiked(!localIsLiked);
      setLocalLikesCount(localIsLiked ? localLikesCount - 1 : localLikesCount + 1);

      // API call
      await toggleVideoLikeMutation({
        videoId,
        isCurrentlyLiked: localIsLiked,
        currentLikesCount: localLikesCount,
      }).unwrap();

      // Refetch like status to ensure consistency
      refetchLikeStatus();

      logger.debug('Video like toggled successfully:', videoId);
      return true;
    } catch (error) {
      logger.error('Failed to toggle like:', error);

      // Revert optimistic update on error
      setLocalIsLiked(previousIsLiked);
      setLocalLikesCount(previousLikesCount);

      return false;
    }
  }, [videoId, currentUser, localIsLiked, localLikesCount, isToggling, toggleVideoLikeMutation, refetchLikeStatus]);

  // Like video (for backward compatibility)
  const likeVideo = useCallback(async (): Promise<boolean> => {
    if (localIsLiked) return true;
    return await toggleLike();
  }, [localIsLiked, toggleLike]);

  // Unlike video (for backward compatibility)
  const unlikeVideo = useCallback(async (): Promise<boolean> => {
    if (!localIsLiked) return true;
    return await toggleLike();
  }, [localIsLiked, toggleLike]);

  return {
    // State
    likeCount: localLikesCount,
    isLiked: localIsLiked,

    // Actions
    likeVideo,
    unlikeVideo,
    toggleLike,

    // Loading states
    isLiking: isToggling && !localIsLiked,
    isUnliking: isToggling && localIsLiked,
    isProcessing: isToggling || isCheckingLiked,

    // Utilities
    refetchLikeStatus,
  };
};

/**
 * Hook for bulk video like operations
 */
export const useBulkVideoLikes = () => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectCurrentUser);

  const initializeMultipleVideos = useCallback((videos: Array<{ id: string; likeCount: number }>) => {
    videos.forEach(video => {
      dispatch(
        initializeLikeState({
          videoId: video.id,
          count: video.likeCount,
          isLiked: false,
        })
      );
    });
  }, [dispatch]);

  return {
    initializeMultipleVideos,
  };
};
