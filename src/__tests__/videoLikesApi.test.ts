import {likeVideoRequest, unlikeVideoRequest} from '../store/api/videoLikesApi';
import {supabase} from '../integrations/supabase/client';

jest.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {getUser: jest.fn()},
    from: jest.fn(),
  },
}));

describe('videoLikesApi', () => {
  const insertMock = jest.fn();
  const matchMock = jest.fn();
  const deleteMock = jest.fn(() => ({match: matchMock}));

  beforeEach(() => {
    insertMock.mockResolvedValue({data: {}, error: null});
    matchMock.mockResolvedValue({data: {}, error: null});
    (supabase.from as jest.Mock).mockReturnValue({
      insert: insertMock,
      delete: deleteMock,
    });
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: {user: {id: 'user1'}},
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should insert like row', async () => {
    await likeVideoRequest('video1');
    expect(supabase.from).toHaveBeenCalledWith('video_likes');
    expect(insertMock).toHaveBeenCalledWith({
      video_id: 'video1',
      user_id: 'user1',
    });
  });

  it('should delete like row', async () => {
    await unlikeVideoRequest('video1');
    expect(supabase.from).toHaveBeenCalledWith('video_likes');
    expect(deleteMock).toHaveBeenCalled();
    expect(matchMock).toHaveBeenCalledWith({
      video_id: 'video1',
      user_id: 'user1',
    });
  });
});
