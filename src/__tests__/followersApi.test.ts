import { configureStore } from '@reduxjs/toolkit';
import { followersApi } from '../store/api/followersApi';
import followersReducer, { 
  setFollowerCount, 
  incrementFollowerCount, 
  decrementFollowerCount,
  selectFollowerCount 
} from '../store/slices/followersSlice';

// Mock Supabase
jest.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(() => Promise.resolve({
        data: { user: { id: 'test-user-id' } }
      }))
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({
            data: null,
            error: null
          }))
        }))
      })),
      insert: jest.fn(() => Promise.resolve({
        data: null,
        error: null
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(() => ({
          eq: jest.fn(() => Promise.resolve({
            data: null,
            error: null
          }))
        }))
      }))
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        on: jest.fn(() => ({
          subscribe: jest.fn(() => ({
            unsubscribe: jest.fn()
          }))
        }))
      }))
    }))
  }
}));

describe('Followers API and State Management', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        followers: followersReducer,
        followersApi: followersApi.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(followersApi.middleware),
    });
  });

  describe('Followers Slice', () => {
    test('should set initial follower count', () => {
      const userId = 'test-user-123';
      const count = 42;

      store.dispatch(setFollowerCount({ userId, count }));

      const followerCount = selectFollowerCount(userId)(store.getState());
      expect(followerCount).toBe(count);
    });

    test('should increment follower count', () => {
      const userId = 'test-user-123';
      const initialCount = 10;

      // Set initial count
      store.dispatch(setFollowerCount({ userId, count: initialCount }));
      
      // Increment
      store.dispatch(incrementFollowerCount({ userId }));

      const followerCount = selectFollowerCount(userId)(store.getState());
      expect(followerCount).toBe(initialCount + 1);
    });

    test('should decrement follower count', () => {
      const userId = 'test-user-123';
      const initialCount = 10;

      // Set initial count
      store.dispatch(setFollowerCount({ userId, count: initialCount }));
      
      // Decrement
      store.dispatch(decrementFollowerCount({ userId }));

      const followerCount = selectFollowerCount(userId)(store.getState());
      expect(followerCount).toBe(initialCount - 1);
    });

    test('should not go below zero when decrementing', () => {
      const userId = 'test-user-123';
      const initialCount = 0;

      // Set initial count to 0
      store.dispatch(setFollowerCount({ userId, count: initialCount }));
      
      // Try to decrement below zero
      store.dispatch(decrementFollowerCount({ userId }));

      const followerCount = selectFollowerCount(userId)(store.getState());
      expect(followerCount).toBe(0);
    });

    test('should return 0 for unknown user', () => {
      const unknownUserId = 'unknown-user';
      const followerCount = selectFollowerCount(unknownUserId)(store.getState());
      expect(followerCount).toBe(0);
    });
  });
});
