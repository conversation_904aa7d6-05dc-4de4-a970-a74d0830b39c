import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from '../../contexts/ThemeContext';
import { GradientType, GradientDirectionType } from '../../styles/themes';

interface GradientBackgroundProps {
  type?: GradientType;
  direction?: GradientDirectionType;
  style?: ViewStyle;
  children?: React.ReactNode;
}

/**
 * A component that renders a gradient background
 */
export const GradientBackground: React.FC<GradientBackgroundProps> = ({
  type = 'primary',
  direction = 'topToBottom',
  style,
  children,
}) => {
  const { theme } = useTheme();
  const colors = theme.gradients[type];
  const { start, end } = theme.gradientDirections[direction];

  return (
    <LinearGradient
      colors={colors}
      start={start}
      end={end}
      style={[styles.container, style]}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default GradientBackground;