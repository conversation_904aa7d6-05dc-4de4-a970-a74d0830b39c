import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from './Text';
import Ionicons from 'react-native-vector-icons/Ionicons';

type ListItemProps = {
  title: string;
  rightComponent?: React.ReactNode;
  onPress?: () => void;
};

const ListItem: React.FC<ListItemProps> = ({ title, rightComponent, onPress }) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[styles.container, { borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <Text style={[styles.title, { color: theme.colors.text }]}>
        {title}
      </Text>
      {rightComponent || (
        <Ionicons
          name="chevron-forward"
          size={20}
          color={theme.colors.textSecondary}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 16,
  },
});

export default ListItem;
