import React from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
} from 'react-native';

const EMOJIS = [
  '😀',
  '😂',
  '😅',
  '😊',
  '😍',
  '😘',
  '😎',
  '😭',
  '😡',
  '👍',
  '🙏',
  '🎉',
  '❤️',
  '🔥',
  '✨',
  '🎂',
  '🍕',
  '🍻',
  '🥳',
  '🤔',
  '🙌',
  '🤷',
  '🤦',
  '👀',
  '🙈',
  '🎶',
  '🏆',
];

export interface EmojiPickerProps {
  visible: boolean;
  onSelect: (emoji: string) => void;
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({visible, onSelect}) => {
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <FlatList
        data={EMOJIS}
        keyExtractor={item => item}
        numColumns={8}
        renderItem={({item}) => (
          <TouchableOpacity
            style={styles.emojiButton}
            onPress={() => onSelect(item)}>
            <Text style={styles.emoji}>{item}</Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 250,
  },
  emojiButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  emoji: {
    fontSize: 24,
  },
});

export default EmojiPicker;
