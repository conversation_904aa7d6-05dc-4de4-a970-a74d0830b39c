import React from 'react';
import { Text as RNText, TextProps, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

type Props = TextProps & {
  color?: string;
  weight?: 'normal' | 'bold' | '600' | '700';
  size?: number;
  style?: any;
  variant?: 'h1' | 'h2' | 'h3' | 'body' | 'caption';
};

const Text: React.FC<Props> = ({
  children,
  color,
  weight = 'normal',
  size,
  style,
  ...rest
}) => {
  const { theme } = useTheme();

  return (
    <RNText
      style={[
        {
          color: color || theme.colors.text,
          fontWeight: weight,
          fontSize: size,
        },
        style,
      ]}
      {...rest}
    >
      {children}
    </RNText>
  );
};

export default Text;
