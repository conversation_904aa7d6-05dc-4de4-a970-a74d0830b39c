import React from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

const Loading: React.FC<{ size?: 'small' | 'large' | number; fullScreen?: boolean }> = ({
  size = 'large',
  fullScreen = false,
}) => {
  const { theme } = useTheme();

  return (
    <View
      style={[
        styles.container,
        fullScreen && { flex: 1, justifyContent: 'center', alignItems: 'center' },
      ]}>
      <ActivityIndicator size={size} color={theme.colors.primary} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
});

export default Loading;
