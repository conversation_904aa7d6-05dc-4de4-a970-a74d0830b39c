import React from 'react';
import { Modal, View, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

type ModalProps = {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  animationType?: 'none' | 'slide' | 'fade';
  transparent?: boolean;
  position?: 'center' | 'bottom';
};

const CustomModal: React.FC<ModalProps> = ({
  visible,
  onClose,
  children,
  animationType = 'fade',
  transparent = true,
  position = 'center',
}) => {
  const { theme } = useTheme();

 const getPositionStyle = (): { justifyContent: 'center' | 'flex-end' } => {
  switch (position) {
    case 'bottom':
      return { justifyContent: 'flex-end' };
    default:
      return { justifyContent: 'center' };
  }
};

  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]} />
      </TouchableWithoutFeedback>
      <View style={[styles.container, getPositionStyle()]}>
        <View
          style={[
            styles.content,
            {
              backgroundColor: theme.colors.background,
              borderRadius: position === 'bottom' ? 16 : 8,
              borderBottomLeftRadius: position === 'bottom' ? 0 : 8,
              borderBottomRightRadius: position === 'bottom' ? 0 : 8,
            },
          ]}>
          {children}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    flex: 1,
    alignItems: 'center',
  },
  content: {
    width: '90%',
    maxWidth: 400,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default CustomModal;
