import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { AnimationType } from '../../styles/themes';

interface AnimatedElementProps {
  type: AnimationType;
  style?: ViewStyle;
  children?: React.ReactNode;
  duration?: number;
  scale?: number;
  distance?: number;
}

/**
 * A component that applies animations to its children
 */
export const AnimatedElement: React.FC<AnimatedElementProps> = ({
  type,
  style,
  children,
  duration,
  scale,
  distance,
}) => {
  const { theme } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    switch (type) {
      case 'pulseGlow':
        theme.animations.pulseGlow(animatedValue, duration);
        break;
      case 'float':
        theme.animations.float(animatedValue, duration, distance);
        break;
      case 'cardHover':
        theme.animations.cardHover(animatedValue, duration, scale);
        break;
      case 'twinkle':
        theme.animations.twinkle(animatedValue, duration);
        break;
    }
  }, [type, animatedValue, duration, scale, distance, theme.animations]);

  const getAnimatedStyle = () => {
    switch (type) {
      case 'pulseGlow':
        return {
          opacity: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.5, 1],
          }),
          transform: [
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 1.02],
              }),
            },
          ],
          shadowOpacity: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 0.7],
          }),
          shadowRadius: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [5, 15],
          }),
        };
      case 'float':
        return {
          transform: [
            {
              translateY: animatedValue,
            },
          ],
        };
      case 'cardHover':
        return {
          transform: [
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [1, scale || 1.05],
              }),
            },
          ],
        };
      case 'twinkle':
        return {
          opacity: animatedValue.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.3, 1, 0.3],
          }),
          transform: [
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 0.5, 1],
                outputRange: [0.8, 1.2, 0.8],
              }),
            },
          ],
        };
      default:
        return {};
    }
  };

  return (
    <Animated.View style={[styles.container, style, getAnimatedStyle()]}>
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    shadowColor: '#00BFFF', // Electric Blue shadow
    shadowOffset: { width: 0, height: 0 },
  },
});

export default AnimatedElement;