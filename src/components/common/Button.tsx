import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, View } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';
import GradientBackground from './GradientBackground';

type ButtonProps = {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'gradient';
  gradientType?: 'primary' | 'secondary' | 'tertiary' | 'orange' | 'blue';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  style?: any;
  textStyle?: any;
};

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  gradientType = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  style,
  textStyle,
}) => {
  const { theme } = useTheme();

  const getButtonStyle = () => {
    switch (variant) {
      case 'secondary':
        return {
          backgroundColor: theme.colors.secondary,
          borderWidth: 0,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.primary,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
        };
      case 'gradient':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
          overflow: 'hidden',
        };
      default:
        return {
          backgroundColor: theme.colors.primary,
          borderWidth: 0,
        };
    }
  };

  const getTextStyle = () => {
    switch (variant) {
      case 'outline':
        return {
          color: theme.colors.primary,
        };
      case 'text':
        return {
          color: theme.colors.primary,
        };
      default:
        return {
          color: '#FFFFFF',
        };
    }
  };

  const getSizeStyle = () => {
    switch (size) {
      case 'sm':
        return {
          paddingVertical: theme.spacing.sm,
          paddingHorizontal: theme.spacing.md,
        };
      case 'lg':
        return {
          paddingVertical: theme.spacing.md,
          paddingHorizontal: theme.spacing.lg,
        };
      default:
        return {
          paddingVertical: theme.spacing.sm + 2,
          paddingHorizontal: theme.spacing.lg,
        };
    }
  };

  // Render a gradient button
  if (variant === 'gradient' && !disabled) {
    return (
      <TouchableOpacity
        style={[
          styles.button,
          getButtonStyle(),
          getSizeStyle(),
          fullWidth && { width: '100%' },
          style,
        ]}
        onPress={onPress}
        activeOpacity={0.7}
        disabled={loading}>
        <GradientBackground
          type={gradientType}
          direction="leftToRight"
          style={StyleSheet.absoluteFill}
        />
        {loading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <>
            {icon && <>{icon}</>}
            <Text
              style={[
                styles.text,
                { color: '#FFFFFF' },
                size === 'sm' && { fontSize: theme.fontSizes.sm },
                size === 'lg' && { fontSize: theme.fontSizes.lg },
                textStyle,
              ]}>
              {title}
            </Text>
          </>
        )}
      </TouchableOpacity>
    );
  }

  // Render a regular button
  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        getSizeStyle(),
        fullWidth && { width: '100%' },
        disabled && { opacity: 0.6 },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
      disabled={disabled || loading}>
      {loading ? (
        <ActivityIndicator color={getTextStyle().color} />
      ) : (
        <>
          {icon && <>{icon}</>}
          <Text
            style={[
              styles.text,
              getTextStyle(),
              size === 'sm' && { fontSize: theme.fontSizes.sm },
              size === 'lg' && { fontSize: theme.fontSizes.lg },
              textStyle,
            ]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
  },
  text: {
    fontFamily: 'poppins',
    fontWeight: '600',
    fontSize: normalize(14),
    marginLeft: 8,
  },
});

export default Button;
