import React, { useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from './Text';
import Avatar from './Avatar';
import Button from './Button';
import { useToggleFollowMutation } from '../../store/api/userManagementApi';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  initializeFollowerState,
  setFollowing,
  incrementFollowers,
  decrementFollowers,
  selectFollowerState,
} from '../../store/slices/followersSlice';
import { User } from '../../types/user';
import logger from '../../utils/logger';

interface UserListItemProps {
  user: User & { is_following?: boolean };
  showFollowButton?: boolean;
  onPress?: () => void;
}

const UserListItem: React.FC<UserListItemProps> = ({
  user,
  showFollowButton = false,
  onPress,
}) => {
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const followState = useAppSelector(selectFollowerState(user.id));
  const [toggleFollow, { isLoading }] = useToggleFollowMutation();

  useEffect(() => {
    if (!followState) {
      dispatch(
        initializeFollowerState({
          userId: user.id,
          isFollowing: user.is_following || false,
          count: user.followers_count || 0,
        })
      );
    }
  }, [dispatch, followState, user]);

  const handleFollowPress = async () => {
    try {
      const result = await toggleFollow(user.id).unwrap();
      dispatch(setFollowing({ userId: user.id, isFollowing: result.isFollowing }));
      if (result.isFollowing) {
        dispatch(incrementFollowers({ userId: user.id }));
      } else {
        dispatch(decrementFollowers({ userId: user.id }));
      }
    } catch (error) {
      logger.error('Follow toggle error:', error);
    }
  };

  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <TouchableOpacity
      style={[styles.container, { borderBottomColor: theme.colors.border }]}
      onPress={onPress}
    >
      <View style={styles.avatar}>
        <Avatar
          source={(() => {
            const imageUrl = user.profile_picture_url || user.avatar_url;
            return imageUrl ? { uri: imageUrl } : undefined;
          })()}
          size="lg"
        />
      </View>

      <View style={styles.userInfo}>
        <View style={styles.nameContainer}>
          <Text style={[styles.username, { color: theme.colors.text }]}>
            @{user.username}
          </Text>
          {user.is_verified && (
            <Text style={[styles.verified, { color: theme.colors.primary }]}>
              ✓
            </Text>
          )}
          {user.user_tag && user.user_tag !== 'Supporter' && (
            <View style={[styles.tagBadge, { backgroundColor: theme.colors.primary }]}>
              <Text style={[styles.tagText, { color: 'white' }]}>
                {user.user_tag}
              </Text>
            </View>
          )}
        </View>

        <Text style={[styles.fullName, { color: theme.colors.text }]}>
          {user.full_name}
        </Text>

        {user.bio && (
          <Text style={[styles.bio, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {user.bio}
          </Text>
        )}

        <Text style={[styles.followers, { color: theme.colors.textSecondary }]}>
          {formatFollowerCount(followState ? followState.count : user.followers_count || 0)} followers
        </Text>
      </View>

      {showFollowButton && (
        <View style={styles.buttonContainer}>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <Button
              title={followState?.isFollowing ? 'Following' : 'Follow'}
              onPress={handleFollowPress}
              style={[
                styles.followButton,
                followState?.isFollowing && { backgroundColor: theme.colors.border },
              ]}
              textStyle={[
                followState?.isFollowing && { color: theme.colors.text },
              ]}
              variant={followState?.isFollowing ? 'secondary' : 'primary'}
              size="sm"
            />
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 0.5,
  },
  avatar: {
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 6,
  },
  verified: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 6,
  },
  tagBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 4,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '600',
  },
  fullName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  bio: {
    fontSize: 12,
    marginBottom: 2,
  },
  followers: {
    fontSize: 12,
  },
  buttonContainer: {
    minWidth: 80,
    alignItems: 'center',
  },
  followButton: {
    minWidth: 80,
    paddingHorizontal: 16,
  },
});

export default UserListItem;
