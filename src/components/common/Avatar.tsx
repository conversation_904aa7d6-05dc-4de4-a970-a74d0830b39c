import React from 'react';
import { View, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';

type AvatarProps = {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  source?: { uri: string } | number;
  onPress?: () => void;
  border?: boolean;
  online?: boolean;
};

const Avatar: React.FC<AvatarProps> = ({
  size = 'md',
  source,
  onPress,
  border = false,
  online = false,
}) => {
  const { theme } = useTheme();

  const getSize = () => {
    switch (size) {
      case 'sm':
        return 32;
      case 'lg':
        return 64;
      case 'xl':
        return 96;
      default:
        return 48;
    }
  };

  const Container = onPress ? TouchableOpacity : View;

  return (
    <View style={styles.container}>
      <Container
        style={[
          styles.avatar,
          {
            width: getSize(),
            height: getSize(),
            borderRadius: getSize() / 2,
            borderWidth: border ? 2 : 0,
            borderColor: theme.colors.primary,
          },
        ]}
        onPress={onPress}>
        {source && (
          <Image
            source={source}
            style={[
              styles.image,
              {
                width: getSize(),
                height: getSize(),
                borderRadius: getSize() / 2,
              },
            ]}
          />
        )}
      </Container>
      {online && (
        <View
          style={[
            styles.status,
            {
              backgroundColor: theme.colors.success,
              borderColor: theme.colors.background,
              width: getSize() / 4,
              height: getSize() / 4,
              borderRadius: getSize() / 8,
              right: getSize() / 16,
              bottom: getSize() / 16,
            },
          ]}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  avatar: {
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#DDD',
  },
  image: {
    resizeMode: 'cover',
  },
  status: {
    position: 'absolute',
    borderWidth: 2,
  },
});

export default Avatar;
