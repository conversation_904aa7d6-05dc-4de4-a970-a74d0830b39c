import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { MessageWithSender } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MessageActionsProps {
  visible: boolean;
  message: MessageWithSender;
  onClose: () => void;
  onReaction: (reactionType: string) => void;
  onReply: () => void;
  onForward: () => void;
  onCopy: () => void;
  onDelete?: () => void;
  onEdit?: () => void;
  isMe: boolean;
}

const REACTIONS = [
  { type: 'heart', emoji: '💖', label: 'Love' },
  { type: 'like', emoji: '👍', label: 'Like' },
  { type: 'laugh', emoji: '😂', label: 'Laugh' },
  { type: 'wow', emoji: '😮', label: 'Wow' },
  { type: 'sad', emoji: '😢', label: 'Sad' },
  { type: 'angry', emoji: '😡', label: 'Angry' },
  { type: 'fire', emoji: '🔥', label: 'Fire' },
];

const MessageActions: React.FC<MessageActionsProps> = ({
  visible,
  message,
  onClose,
  onReaction,
  onReply,
  onForward,
  onCopy,
  onDelete,
  onEdit,
  isMe,
}) => {
  const { theme } = useTheme();

  const handleReaction = (reactionType: string) => {
    onReaction(reactionType);
    onClose();
  };

  const handleAction = (action: () => void) => {
    action();
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={[
          styles.container,
          { backgroundColor: theme.colors.background }
        ]}>
          {/* Reactions Row */}
          <View style={[
            styles.reactionsContainer,
            { backgroundColor: theme.colors.surface }
          ]}>
            {REACTIONS.map((reaction) => (
              <TouchableOpacity
                key={reaction.type}
                style={styles.reactionButton}
                onPress={() => handleReaction(reaction.type)}
                activeOpacity={0.7}
              >
                <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Actions Row */}
          <View style={[
            styles.actionsContainer,
            { backgroundColor: theme.colors.surface }
          ]}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleAction(onReply)}
            >
              <Ionicons
                name="arrow-undo"
                size={normalize(20)}
                color={theme.colors.text}
              />
              <Text style={[styles.actionText, { color: theme.colors.text }]}>
                Reply
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleAction(onForward)}
            >
              <Ionicons
                name="arrow-forward"
                size={normalize(20)}
                color={theme.colors.text}
              />
              <Text style={[styles.actionText, { color: theme.colors.text }]}>
                Forward
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleAction(onCopy)}
            >
              <Ionicons
                name="copy"
                size={normalize(20)}
                color={theme.colors.text}
              />
              <Text style={[styles.actionText, { color: theme.colors.text }]}>
                Copy
              </Text>
            </TouchableOpacity>

            {isMe && onEdit && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleAction(onEdit)}
              >
                <Ionicons
                  name="pencil"
                  size={normalize(20)}
                  color={theme.colors.text}
                />
                <Text style={[styles.actionText, { color: theme.colors.text }]}>
                  Edit
                </Text>
              </TouchableOpacity>
            )}

            {isMe && onDelete && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleAction(onDelete)}
              >
                <Ionicons
                  name="trash"
                  size={normalize(20)}
                  color={theme.colors.error}
                />
                <Text style={[styles.actionText, { color: theme.colors.error }]}>
                  Delete
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    borderRadius: normalize(16),
    marginHorizontal: normalize(20),
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  reactionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderTopLeftRadius: normalize(16),
    borderTopRightRadius: normalize(16),
    justifyContent: 'space-around',
  },
  reactionButton: {
    padding: normalize(8),
    borderRadius: normalize(20),
  },
  reactionEmoji: {
    fontSize: normalize(28),
  },
  actionsContainer: {
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderBottomLeftRadius: normalize(16),
    borderBottomRightRadius: normalize(16),
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(16),
    borderRadius: normalize(8),
    marginVertical: normalize(2),
  },
  actionText: {
    fontSize: normalize(16),
    fontWeight: '500',
    marginLeft: normalize(12),
  },
});

export default MessageActions;
