import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import { normalize } from '../../utils/responsive';

interface ChatHeaderProps {
  conversationName?: string;
  participantCount?: number;
  isOnline?: boolean;
  lastSeen?: string;
  avatarUrl?: string;
  onCallPress?: () => void;
  onVideoCallPress?: () => void;
  onInfoPress?: () => void;
  isTyping?: boolean;
  typingUsers?: string[];
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  conversationName = 'Chat',
  participantCount,
  isOnline = false,
  lastSeen,
  avatarUrl,
  onCallPress,
  onVideoCallPress,
  onInfoPress,
  isTyping = false,
  typingUsers = [],
}) => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const handleBackPress = () => {
    navigation.goBack();
  };

  const getSubtitle = () => {
    if (isTyping && typingUsers.length > 0) {
      if (typingUsers.length === 1) {
        return `${typingUsers[0]} is typing...`;
      } else if (typingUsers.length === 2) {
        return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;
      } else {
        return `${typingUsers.length} people are typing...`;
      }
    }

    if (participantCount && participantCount > 2) {
      return `${participantCount} participants`;
    }

    if (isOnline) {
      return 'Online';
    }

    if (lastSeen) {
      const lastSeenDate = new Date(lastSeen);
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) {
        return 'Just now';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}m ago`;
      } else if (diffInMinutes < 1440) {
        const hours = Math.floor(diffInMinutes / 60);
        return `${hours}h ago`;
      } else {
        return lastSeenDate.toLocaleDateString();
      }
    }
    return 'Offline';
  };

  return (
    <>
      <StatusBar
        backgroundColor={theme.colors.background}
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
      />
      <View style={[
        styles.container,
        {
          backgroundColor: theme.colors.background,
          borderBottomColor: theme.colors.border,
          paddingTop: insets.top,
        }
      ]}>
        {/* Left section - Back button and user info */}
        <View style={styles.leftSection}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
            activeOpacity={0.7}
          >
            <Ionicons
              name="arrow-back"
              size={normalize(24)}
              color={theme.colors.text}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.userInfoContainer}
            onPress={onInfoPress}
            activeOpacity={0.7}
          >
            <View style={styles.avatarContainer}>
              <Avatar
                source={avatarUrl ? { uri: avatarUrl } : undefined}
                size="md"
              />
              {isOnline && (
                <View style={[
                  styles.onlineIndicator,
                  { borderColor: theme.colors.background }
                ]} />
              )}
            </View>

            <View style={styles.userInfo}>
              <Text style={[
                styles.conversationName,
                { color: theme.colors.text }
              ]} numberOfLines={1}>
                {conversationName}
              </Text>

              <Text style={[
                styles.subtitle,
                {
                  color: isTyping ? theme.colors.primary :
                         isOnline ? theme.colors.success :
                         theme.colors.textSecondary
                }
              ]} numberOfLines={1}>
                {getSubtitle()}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Right section - Action buttons */}
        <View style={styles.rightSection}>
          {onCallPress && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.surface }
              ]}
              onPress={onCallPress}
              activeOpacity={0.7}
            >
              <Ionicons
                name="call"
                size={normalize(20)}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          )}

          {onVideoCallPress && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.surface }
              ]}
              onPress={onVideoCallPress}
              activeOpacity={0.7}
            >
              <Ionicons
                name="videocam"
                size={normalize(20)}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: theme.colors.surface }
            ]}
            onPress={onInfoPress}
            activeOpacity={0.7}
          >
            <Ionicons
              name="ellipsis-vertical"
              size={normalize(20)}
              color={theme.colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: normalize(8),
    marginRight: normalize(8),
    borderRadius: normalize(20),
  },
  userInfoContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: normalize(12),
  },
  avatar: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    backgroundColor: '#4CAF50',
    borderWidth: 2,
  },
  userInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationName: {
    fontSize: normalize(18),
    fontWeight: '700',
    lineHeight: normalize(22),
    marginBottom: normalize(2),
  },
  subtitle: {
    fontSize: normalize(13),
    lineHeight: normalize(16),
    fontWeight: '400',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: normalize(8),
  },
  actionButton: {
    width: normalize(40),
    height: normalize(40),
    borderRadius: normalize(20),
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
});

export default ChatHeader;
