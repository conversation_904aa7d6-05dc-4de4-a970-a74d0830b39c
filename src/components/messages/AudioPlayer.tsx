import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import logger from '../../utils/logger';

interface AudioPlayerProps {
  audioUrl: string;
  duration?: number;
  isMe: boolean;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioUrl, duration = 0, isMe }) => {
  const { theme } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [totalDuration, setTotalDuration] = useState(duration);
  const [isLoading, setIsLoading] = useState(false);
  const audioPlayer = useRef(new AudioRecorderPlayer()).current;
  const waveAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (isPlaying) {
        audioPlayer.stopPlayer();
        audioPlayer.removePlayBackListener();
      }
    };
  }, [audioPlayer, isPlaying]);

  useEffect(() => {
    if (isPlaying) {
      startWaveAnimation();
    } else {
      stopWaveAnimation();
    }
  }, [isPlaying]);

  const startWaveAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(waveAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(waveAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopWaveAnimation = () => {
    waveAnim.stopAnimation();
    waveAnim.setValue(0);
  };

  const handlePlayPause = async () => {
    try {
      if (isPlaying) {
        // Pause
        await audioPlayer.pausePlayer();
        setIsPlaying(false);
        audioPlayer.removePlayBackListener();
        logger.debug('Audio paused');
      } else {
        // Play
        setIsLoading(true);
        logger.debug('Starting audio playback:', audioUrl);

        const result = await audioPlayer.startPlayer(audioUrl);
        logger.debug('Audio player started:', result);

        audioPlayer.addPlayBackListener((e) => {
          setCurrentPosition(e.currentPosition);
          if (!totalDuration && e.duration) {
            setTotalDuration(e.duration);
          }

          // Check if playback finished
          if (e.currentPosition >= e.duration && e.duration > 0) {
            setIsPlaying(false);
            setCurrentPosition(0);
            audioPlayer.removePlayBackListener();
            logger.debug('Audio playback finished');
          }
        });

        setIsPlaying(true);
        setIsLoading(false);
      }
    } catch (error) {
      logger.error('Audio playback error:', error);
      setIsPlaying(false);
      setIsLoading(false);

      // Reset position on error
      setCurrentPosition(0);
    }
  };

  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgress = (): number => {
    if (totalDuration === 0) return 0;
    return currentPosition / totalDuration;
  };

  const renderWaveform = () => {
    const bars = Array.from({ length: 20 }, (_, index) => {
      const baseHeight = Math.random() * 20 + 10; // Random height between 10-30

      // Create different animation phases for each bar to make it more realistic
      const phase = (index * 0.1) % 1; // Different phase for each bar
      const animatedScale = waveAnim.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.3 + phase * 0.2, 1, 0.3 + phase * 0.2],
      });

      return (
        <View key={index} style={styles.waveBarContainer}>
          <Animated.View
            style={[
              styles.waveBar,
              {
                height: baseHeight,
                backgroundColor: isMe ? '#FFFFFF80' : theme.colors.primary + '80',
                transform: [{ scaleY: animatedScale }],
              },
            ]}
          />
        </View>
      );
    });

    return <View style={styles.waveform}>{bars}</View>;
  };

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: isMe ? theme.colors.primary : theme.colors.surface,
      }
    ]}>
      {/* Play/Pause Button */}
      <TouchableOpacity
        style={[
          styles.playButton,
          {
            backgroundColor: isMe ? '#FFFFFF20' : theme.colors.primary + '20',
          }
        ]}
        onPress={handlePlayPause}
        disabled={isLoading}
      >
        {isLoading ? (
          <Ionicons 
            name="hourglass-outline" 
            size={20} 
            color={isMe ? '#FFFFFF' : theme.colors.primary} 
          />
        ) : (
          <Ionicons
            name={isPlaying ? 'pause' : 'play'}
            size={20}
            color={isMe ? '#FFFFFF' : theme.colors.primary}
          />
        )}
      </TouchableOpacity>

      {/* Waveform */}
      <View style={styles.waveformContainer}>
        {renderWaveform()}
        
        {/* Progress Overlay */}
        <View
          style={[
            styles.progressOverlay,
            {
              width: `${getProgress() * 100}%`,
              backgroundColor: isMe ? '#FFFFFF40' : theme.colors.primary + '40',
            },
          ]}
        />
      </View>

      {/* Duration */}
      <Text style={[
        styles.duration,
        { color: isMe ? '#FFFFFF' : theme.colors.text }
      ]}>
        {formatTime(isPlaying ? currentPosition : totalDuration)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    minWidth: 200,
    maxWidth: 280,
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  waveformContainer: {
    flex: 1,
    height: 30,
    position: 'relative',
    justifyContent: 'center',
    marginRight: 12,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '100%',
  },
  waveBarContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 1,
  },
  waveBar: {
    width: 2,
    borderRadius: 1,
  },
  progressOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    borderRadius: 4,
  },
  duration: {
    fontSize: 12,
    fontFamily: 'monospace',
    minWidth: 35,
    textAlign: 'right',
  },
});

export default AudioPlayer;
