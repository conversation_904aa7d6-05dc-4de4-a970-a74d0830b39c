import React from 'react';
import { TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../common/Button';
import Text from '../common/Text';
import { startConversationWithUser } from '../../utils/messagingHelpers';
import { MainStackParamList } from '../../navigation/types';
import { NavigationProp } from '@react-navigation/native';

interface MessageButtonProps {
  userId: string;
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

const MessageButton: React.FC<MessageButtonProps> = ({
  userId,
  variant = 'secondary',
  size = 'md',
  disabled = false,
}) => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();

  const handlePress = async () => {
    try {
      await startConversationWithUser(userId, navigation);
    } catch (error) {
      Alert.alert('Error', 'Failed to start conversation. Please try again.');
    }
  };

  if (variant === 'icon') {
    return (
      <TouchableOpacity
        style={[
          styles.iconButton,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
          disabled && styles.disabled,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <Text style={{ color: disabled ? theme.colors.textSecondary : theme.colors.text }}>
          MSG
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <Button
      title="Message"
      onPress={handlePress}
      variant={variant}
      size={size}
      disabled={disabled}
    />
  );
};

const styles = StyleSheet.create({
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

export default MessageButton;