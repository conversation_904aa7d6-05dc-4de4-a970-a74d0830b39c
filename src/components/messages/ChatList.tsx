import React from 'react';
import { FlatList, StyleSheet, View, RefreshControl } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import ChatItem from './ChatItem';
import { ConversationWithDetails } from '../../types/messaging';
import logger from '../../utils/logger';

interface ChatListProps {
  conversations: ConversationWithDetails[];
  onChatPress: (conversationId: string) => void;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

const ChatList: React.FC<ChatListProps> = ({
  conversations,
  onChatPress,
  onRefresh,
  isRefreshing = false,
}) => {
  const { theme } = useTheme();
  
  // Log conversations data
  logger.debug('ChatList: Received conversations:', conversations);
  logger.debug('ChatList: Conversations count:', conversations.length);

  // Ensure we have a proper array
  const conversationsArray = Array.isArray(conversations) ? conversations : [];
  logger.debug('ChatList: Conversations array type check:', Array.isArray(conversationsArray));
  
  // Filter out any null/undefined conversations before rendering
  const validConversations = conversationsArray.filter((conv, index) => {
    if (conv == null) {
      logger.debug(`ChatList: Found null/undefined conversation at index ${index}`);
      return false;
    }
    if (!conv.id) {
      logger.debug(`ChatList: Found conversation without ID at index ${index}:`, conv);
      return false;
    }
    return true;
  });
  
  logger.debug('ChatList: Valid conversations after filtering:', validConversations.length);
  logger.debug('ChatList: Filtered out conversations:', conversations.length - validConversations.length);
  
  // Additional check for any remaining invalid conversations
  validConversations.forEach((conv, index) => {
    if (!conv || !conv.id) {
      logger.debug(`ChatList: Invalid conversation still present at index ${index}:`, conv);
    }
  });

  return (
    <FlatList
      data={validConversations}
      renderItem={({ item, index }) => {
        // Additional safety check in renderItem
        if (!item || !item.id) {
          logger.debug(`ChatList: renderItem received invalid item at index ${index}:`, item);
          return null;
        }
        
        return (
          <ChatItem
            conversation={item}
            onPress={() => onChatPress(item.id)}
          />
        );
      }}
      keyExtractor={(item, index) => {
        if (!item || !item.id) {
          logger.debug(`ChatList: keyExtractor received invalid item at index ${index}:`, item);
          return `invalid-${index}-${Math.random()}`;
        }
        return item.id;
      }}
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: theme.colors.divider }]} />}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor={theme.colors.primary}
          />
        ) : undefined
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  separator: {
    height: StyleSheet.hairlineWidth,
    marginLeft: 88, // Aligns with text, past the avatar
  },
});

export default ChatList;