import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { MessageWithSender } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

interface ReplyPreviewProps {
  replyingTo: MessageWithSender;
  onCancel: () => void;
  style?: any;
}

const ReplyPreview: React.FC<ReplyPreviewProps> = ({
  replyingTo,
  onCancel,
  style,
}) => {
  const { theme } = useTheme();

  const getPreviewContent = () => {
    switch (replyingTo.type) {
      case 'image':
        return (
          <View style={styles.mediaPreview}>
            <Image
              source={{ uri: replyingTo.media_url }}
              style={styles.previewImage}
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              📷 Photo
            </Text>
          </View>
        );
      
      case 'video':
        return (
          <View style={styles.mediaPreview}>
            <Image
              source={{ uri: replyingTo.thumbnail_url || replyingTo.media_url }}
              style={styles.previewImage}
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              🎥 Video
            </Text>
          </View>
        );
      
      case 'voice':
      case 'audio':
        return (
          <View style={styles.mediaPreview}>
            <Ionicons 
              name="mic" 
              size={normalize(16)} 
              color={theme.colors.textSecondary} 
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              🎵 Voice message
            </Text>
          </View>
        );
      
      case 'gif':
        return (
          <View style={styles.mediaPreview}>
            <Image
              source={{ uri: replyingTo.media_url }}
              style={styles.previewImage}
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              GIF
            </Text>
          </View>
        );
      
      case 'sticker':
        return (
          <View style={styles.mediaPreview}>
            <Image
              source={{ uri: replyingTo.media_url }}
              style={styles.previewImage}
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              Sticker
            </Text>
          </View>
        );
      
      case 'location':
        return (
          <View style={styles.mediaPreview}>
            <Ionicons 
              name="location" 
              size={normalize(16)} 
              color={theme.colors.textSecondary} 
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              📍 Location
            </Text>
          </View>
        );
      
      case 'file':
        return (
          <View style={styles.mediaPreview}>
            <Ionicons 
              name="document" 
              size={normalize(16)} 
              color={theme.colors.textSecondary} 
            />
            <Text style={[styles.mediaText, { color: theme.colors.textSecondary }]}>
              📎 {replyingTo.file_name || 'File'}
            </Text>
          </View>
        );
      
      case 'text':
      default:
        return (
          <Text 
            style={[styles.replyText, { color: theme.colors.textSecondary }]}
            numberOfLines={2}
          >
            {replyingTo.content}
          </Text>
        );
    }
  };

  return (
    <View style={[
      styles.container,
      { 
        backgroundColor: theme.colors.surface,
        borderTopColor: theme.colors.border,
        borderLeftColor: theme.colors.primary,
      },
      style
    ]}>
      <View style={styles.content}>
        <View style={styles.replyInfo}>
          <Text style={[styles.replyLabel, { color: theme.colors.primary }]}>
            Replying to {replyingTo.sender?.username || 'Unknown'}
          </Text>
          {getPreviewContent()}
        </View>
        
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name="close"
            size={normalize(20)}
            color={theme.colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderLeftWidth: 4,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
  },
  replyInfo: {
    flex: 1,
  },
  replyLabel: {
    fontSize: normalize(12),
    fontWeight: '600',
    marginBottom: normalize(4),
  },
  replyText: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
  mediaPreview: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewImage: {
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(4),
    marginRight: normalize(8),
  },
  mediaText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
  cancelButton: {
    padding: normalize(4),
    marginLeft: normalize(12),
  },
});

export default ReplyPreview;
