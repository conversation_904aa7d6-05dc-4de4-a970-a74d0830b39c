import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Avatar from '../common/Avatar';
import Text from '../common/Text';
import { formatMessageTime, formatLastMessagePreview } from '../../utils/messagingHelpers';
import { ConversationWithDetails } from '../../types/messaging';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';
import { normalize } from '../../utils/responsive';

interface ChatItemProps {
  conversation?: ConversationWithDetails;
  onPress: () => void;
}

const ChatItem: React.FC<ChatItemProps> = ({ conversation, onPress }) => {
  // Prevent crashes if conversation data is missing (this should not happen now)
  if (!conversation) {
    logger.debug('ChatItem: Received null/undefined conversation - this should not happen after filtering');
    return null;
  }
  
  if (!conversation.id) {
    logger.debug('ChatItem: Received conversation without ID:', conversation);
    return null;
  }

  logger.debug(`ChatItem: Rendering conversation ${conversation.id}`);

  const { theme } = useTheme();
  const isUnread = (conversation.unread_count || 0) > 0;

  // Determine if it's a group chat or direct message
  const isGroup = conversation.type === 'group';
  const user = conversation.other_participant;
  const lastMessage = conversation.last_message;
  
  // Log key info for debugging
  if (!user && !isGroup) {
    logger.debug(`ChatItem: Direct conversation ${conversation.id} missing other_participant`);
  }

  // Don't render if it's a direct chat without a participant
  if (!isGroup && !user) {
    logger.debug(`ChatItem: Skipping conversation ${conversation.id} - missing participant`);
    return null;
  }
  
  // Log if we have a last message
  if (!lastMessage) {
    logger.debug(`ChatItem: Conversation ${conversation.id} has no last message (last_message_id is null)`);
  }

  const displayName = isGroup ? conversation.name : (user?.full_name || user?.username);
  const avatarUrl = isGroup ? conversation.avatar_url : user?.avatar_url;
  const showOnline = !isGroup && user?.show_online_status;

  const getMessagePreview = () => {
    if (!lastMessage) {
      return 'Start a conversation';
    }
    
    // Handle placeholder message (no real message yet)
    if (!lastMessage.id) {
      return lastMessage.content || 'Start a conversation';
    }
    
    // Get message preview based on message type
    const messageType = lastMessage.type;
    
    if (messageType === 'image') {
      return 'Photo';
    } else if (messageType === 'video') {
      return 'Video';
    } else if (messageType === 'audio') {
      return 'Audio';
    }
    
    return formatLastMessagePreview(lastMessage.content, 35);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isUnread && { backgroundColor: theme.colors.surface + '15' }
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {/* Avatar with Online Indicator */}
      <View style={styles.avatarContainer}>
        <Avatar
          size="lg" // TikTok uses larger avatars
          source={avatarUrl ? { uri: avatarUrl } : undefined}
        />
        {showOnline && (
          <View style={[
            styles.onlineIndicator,
            {
              borderColor: theme.colors.background,
              backgroundColor: '#4CAF50'
            }
          ]} />
        )}
      </View>

      {/* Message Content */}
      <View style={styles.contentContainer}>
        {/* Top Row: Username and Timestamp */}
        <View style={styles.topRow}>
          <Text
            style={[
              styles.username,
              {
                color: theme.colors.text,
                fontWeight: isUnread ? '700' : '600'
              }
            ]}
            numberOfLines={1}
          >
            {displayName}
          </Text>
          <View style={styles.timestampContainer}>
            <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
              {formatMessageTime(lastMessage?.id ? lastMessage?.created_at : conversation.created_at)}
            </Text>
            {isUnread && (
              <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]} />
            )}
          </View>
        </View>

        {/* Bottom Row: Last Message and Status */}
        <View style={styles.bottomRow}>
          <View style={styles.messagePreviewContainer}>
            <Text
              style={[
                styles.lastMessage,
                {
                  color: isUnread ? theme.colors.text : theme.colors.textSecondary,
                  fontWeight: isUnread ? '500' : '400'
                },
              ]}
              numberOfLines={2}
            >
              {getMessagePreview()}
            </Text>
          </View>

          <View style={styles.statusContainer}>
            {isUnread && conversation.unread_count > 0 && (
              <View style={[styles.unreadBadge, { backgroundColor: theme.colors.primary }]}>
                <Text style={[styles.unreadText, { color: '#FFFFFF' }]}>
                  {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                </Text>
              </View>
            )}

            {lastMessage &&
              lastMessage.id &&
              lastMessage.sender_id === user?.id && (
              <View style={styles.messageStatusContainer}>
                {lastMessage.status === 'sent' && (
                  <Ionicons
                    name="checkmark"
                    size={normalize(14)}
                    color={theme.colors.textSecondary}
                  />
                )}
                {lastMessage.status === 'delivered' && (
                  <Ionicons
                    name="checkmark-done"
                    size={normalize(14)}
                    color={theme.colors.textSecondary}
                  />
                )}
                {lastMessage.status === 'read' && (
                  <Ionicons
                    name="checkmark-done"
                    size={normalize(14)}
                    color={theme.colors.primary}
                  />
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderRadius: 0,
  },
  avatarContainer: {
    marginRight: normalize(16),
    position: 'relative',
  },
  avatar: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: normalize(18),
    height: normalize(18),
    borderRadius: normalize(9),
    borderWidth: 3,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingTop: normalize(2),
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: normalize(6),
  },
  username: {
    fontSize: normalize(17),
    flex: 1,
    marginRight: normalize(12),
    lineHeight: normalize(22),
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    fontSize: normalize(13),
    fontWeight: '400',
  },
  unreadDot: {
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(4),
    marginLeft: normalize(8),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: normalize(2),
  },
  messagePreviewContainer: {
    flex: 1,
    marginRight: normalize(12),
  },
  lastMessage: {
    fontSize: normalize(15),
    lineHeight: normalize(20),
  },
  statusContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    minHeight: normalize(40),
  },
  unreadBadge: {
    minWidth: normalize(22),
    height: normalize(22),
    borderRadius: normalize(11),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(8),
    marginTop: normalize(4),
  },
  unreadText: {
    fontSize: normalize(12),
    fontWeight: '700',
  },
  messageStatusContainer: {
    marginTop: normalize(4),
  },
});

export default ChatItem;