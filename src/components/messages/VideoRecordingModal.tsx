import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  VideoRecordingState,
  setVideoRecordingUpdateCallback,
  getVideoRecordingState,
  startVideoRecording,
} from '../../services/media/camera';
import logger from '../../utils/logger';

const { width } = Dimensions.get('window');

interface VideoRecordingModalProps {
  visible: boolean;
  onClose: () => void;
  onRecordingComplete: (videoData: { path: string; duration: number; thumbnail?: string }) => void;
  onError: (error: string) => void;
}

const VideoRecordingModal: React.FC<VideoRecordingModalProps> = ({
  visible,
  onClose,
  onRecordingComplete,
  onError,
}) => {
  const { theme } = useTheme();
  const [recordingState, setRecordingState] = useState<VideoRecordingState>(getVideoRecordingState());
  const [isStarting, setIsStarting] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (visible) {
      // Set up the callback to receive recording updates
      setVideoRecordingUpdateCallback(setRecordingState);
      
      // Start scale animation for the record button
      startScaleAnimation();
    } else {
      // Clean up callback when modal is closed
      setVideoRecordingUpdateCallback(null);
      stopAnimations();
    }

    return () => {
      setVideoRecordingUpdateCallback(null);
      stopAnimations();
    };
  }, [visible]);

  const startScaleAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopAnimations = () => {
    pulseAnim.stopAnimation();
    scaleAnim.stopAnimation();
    pulseAnim.setValue(1);
    scaleAnim.setValue(1);
  };

  const handleStartRecording = async () => {
    try {
      setIsStarting(true);
      stopAnimations();
      startPulseAnimation();
      
      logger.debug('Starting video recording from modal...');
      const response = await startVideoRecording();
      
      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        if (asset.uri) {
          onRecordingComplete({
            path: asset.uri,
            duration: asset.duration || 0,
            thumbnail: asset.uri,
          });
        } else {
          throw new Error('No video file received');
        }
      } else if (response.didCancel) {
        // User cancelled, just close the modal
        onClose();
      } else {
        throw new Error('No video recorded');
      }
    } catch (error) {
      logger.error('Error recording video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError(`Failed to record video: ${errorMessage}`);
    } finally {
      setIsStarting(false);
      stopAnimations();
    }
  };

  const handleCancel = () => {
    stopAnimations();
    onClose();
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Record Video
            </Text>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Video Recording Indicator */}
          <View style={styles.recordingIndicator}>
            <Animated.View
              style={[
                styles.cameraContainer,
                {
                  transform: [
                    { scale: isStarting ? pulseAnim : scaleAnim }
                  ],
                  backgroundColor: isStarting 
                    ? theme.colors.error 
                    : theme.colors.primary,
                },
              ]}
            >
              <Ionicons
                name={isStarting ? "videocam" : "videocam-outline"}
                size={40}
                color="#FFFFFF"
              />
            </Animated.View>

            {/* Recording Ring */}
            {isStarting && (
              <View
                style={[
                  styles.recordingRing,
                  {
                    borderColor: theme.colors.error,
                  },
                ]}
              />
            )}
          </View>

          {/* Status */}
          <View style={styles.statusContainer}>
            <Text style={[styles.statusText, { color: theme.colors.text }]}>
              {isStarting ? 'Recording...' : 'Ready to record'}
            </Text>
            {recordingState.duration > 0 && (
              <Text style={[styles.durationText, { color: theme.colors.primary }]}>
                {formatDuration(recordingState.duration)}
              </Text>
            )}
          </View>

          {/* Record Button */}
          {!isStarting && (
            <TouchableOpacity
              style={[styles.recordButton, { backgroundColor: theme.colors.error }]}
              onPress={handleStartRecording}
            >
              <View style={styles.recordButtonInner} />
            </TouchableOpacity>
          )}

          {/* Instructions */}
          <View style={styles.instructionsContainer}>
            <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
              {isStarting
                ? 'Recording video... Tap stop when finished'
                : 'Tap the red button to start recording\nMaximum duration: 60 seconds'
              }
            </Text>
          </View>

          {/* Info */}
          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                Max 60s
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="videocam-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                High Quality
              </Text>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  modalContent: {
    width: width * 0.85,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 32,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  recordingIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    position: 'relative',
  },
  cameraContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  recordingRing: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 3,
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  durationText: {
    fontSize: 20,
    fontWeight: '700',
    fontFamily: 'monospace',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  recordButtonInner: {
    width: 24,
    height: 24,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  instructionsContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  infoText: {
    fontSize: 12,
  },
});

export default VideoRecordingModal;
