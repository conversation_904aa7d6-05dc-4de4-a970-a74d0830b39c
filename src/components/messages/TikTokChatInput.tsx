import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Platform,
  Keyboard,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

interface TikTokChatInputProps {
  messageText: string;
  onTextChange: (text: string) => void;
  onSendMessage: () => void;
  onSelectAttachment: () => void;
  onToggleEmoji: () => void;
  onVoiceRecord?: () => void;
  onCameraPress?: () => void;
  onGifPress?: () => void;
  onStickerPress?: () => void;
  isSending: boolean;
  isUploading: boolean;
  isRecording?: boolean;
  replyingTo?: any;
  onCancelReply?: () => void;
  placeholder?: string;
  disabled?: boolean;
}

const TikTokChatInput: React.FC<TikTokChatInputProps> = ({
  messageText,
  onTextChange,
  onSendMessage,
  onSelectAttachment,
  onToggleEmoji,
  onVoiceRecord,
  onCameraPress,
  onGifPress,
  onStickerPress,
  isSending,
  isUploading,
  isRecording = false,
  replyingTo,
  onCancelReply,
  placeholder = "Message...",
  disabled = false,
}) => {
  const { theme } = useTheme();
  const [inputHeight, setInputHeight] = useState(normalize(44));
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const inputRef = useRef<TextInput>(null);

  // TikTok-style animations
  const animatePress = useCallback(() => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  }, [scaleAnim]);

  const handleContentSizeChange = useCallback((event: any) => {
    const { height } = event.nativeEvent.contentSize;
    const newHeight = Math.max(normalize(44), Math.min(height + normalize(24), normalize(120)));
    setInputHeight(newHeight);
  }, []);

  const handleSendPress = useCallback(() => {
    if (messageText.trim() && !isSending && !disabled) {
      animatePress();
      onSendMessage();
    }
  }, [messageText, isSending, disabled, onSendMessage, animatePress]);

  const handleAttachmentPress = useCallback(() => {
    if (!disabled) {
      setShowAttachmentOptions(!showAttachmentOptions);
      Animated.timing(slideAnim, {
        toValue: showAttachmentOptions ? 0 : 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [disabled, showAttachmentOptions, slideAnim]);

  const handleVoicePress = useCallback(() => {
    if (!disabled && onVoiceRecord) {
      onVoiceRecord();
    }
  }, [disabled, onVoiceRecord]);

  // Keyboard handling
  useEffect(() => {
    const keyboardWillShow = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => setKeyboardHeight(e.endCoordinates.height)
    );
    const keyboardWillHide = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => setKeyboardHeight(0)
    );

    return () => {
      keyboardWillShow.remove();
      keyboardWillHide.remove();
    };
  }, []);

  const canSend = messageText.trim().length > 0 && !isSending && !disabled;
  const showVoiceButton = messageText.trim().length === 0 && !isUploading;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Reply Preview */}
      {replyingTo && (
        <View style={[styles.replyContainer, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.replyContent}>
            <View style={[styles.replyLine, { backgroundColor: theme.colors.primary }]} />
            <View style={styles.replyText}>
              <Text style={[styles.replyUsername, { color: theme.colors.primary }]} weight="600">
                {replyingTo.sender?.full_name || replyingTo.sender?.username}
              </Text>
              <Text style={[styles.replyMessage, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                {replyingTo.content || 'Media message'}
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={onCancelReply} style={styles.replyCancel}>
            <Ionicons name="close" size={normalize(18)} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Attachment Options */}
      {showAttachmentOptions && (
        <Animated.View 
          style={[
            styles.attachmentOptions,
            { 
              backgroundColor: theme.colors.surface,
              transform: [{ translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0],
              })}],
              opacity: slideAnim,
            }
          ]}
        >
          <TouchableOpacity 
            style={[styles.attachmentButton, { backgroundColor: theme.colors.primary + '20' }]}
            onPress={() => {
              onCameraPress?.();
              setShowAttachmentOptions(false);
            }}
          >
            <Ionicons name="camera" size={normalize(24)} color={theme.colors.primary} />
            <Text style={[styles.attachmentLabel, { color: theme.colors.text }]}>Camera</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.attachmentButton, { backgroundColor: theme.colors.secondary + '20' }]}
            onPress={() => {
              onSelectAttachment();
              setShowAttachmentOptions(false);
            }}
          >
            <Ionicons name="image" size={normalize(24)} color={theme.colors.secondary} />
            <Text style={[styles.attachmentLabel, { color: theme.colors.text }]}>Gallery</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.attachmentButton, { backgroundColor: theme.colors.accent + '20' }]}
            onPress={() => {
              onGifPress?.();
              setShowAttachmentOptions(false);
            }}
          >
            <Ionicons name="gif" size={normalize(24)} color={theme.colors.accent} />
            <Text style={[styles.attachmentLabel, { color: theme.colors.text }]}>GIF</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.attachmentButton, { backgroundColor: theme.colors.highlight + '20' }]}
            onPress={() => {
              onStickerPress?.();
              setShowAttachmentOptions(false);
            }}
          >
            <Ionicons name="happy" size={normalize(24)} color={theme.colors.highlight} />
            <Text style={[styles.attachmentLabel, { color: theme.colors.text }]}>Sticker</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Main Input Container */}
      <View style={[styles.inputContainer, { backgroundColor: theme.colors.surface }]}>
        {/* Attachment Button */}
        <TouchableOpacity
          style={[styles.iconButton, { opacity: disabled ? 0.5 : 1 }]}
          onPress={handleAttachmentPress}
          disabled={disabled}
        >
          <Ionicons 
            name={showAttachmentOptions ? "close" : "add"} 
            size={normalize(24)} 
            color={theme.colors.textSecondary} 
          />
        </TouchableOpacity>

        {/* Text Input */}
        <View style={[styles.textInputContainer, { height: inputHeight }]}>
          <TextInput
            ref={inputRef}
            style={[
              styles.textInput,
              {
                color: theme.colors.text,
                height: inputHeight - normalize(12),
              }
            ]}
            value={messageText}
            onChangeText={onTextChange}
            onContentSizeChange={handleContentSizeChange}
            placeholder={placeholder}
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            textAlignVertical="center"
            editable={!disabled}
            maxLength={2000} // TikTok-style character limit
          />
        </View>

        {/* Emoji Button */}
        <TouchableOpacity
          style={[styles.iconButton, { opacity: disabled ? 0.5 : 1 }]}
          onPress={onToggleEmoji}
          disabled={disabled}
        >
          <Ionicons name="happy-outline" size={normalize(24)} color={theme.colors.textSecondary} />
        </TouchableOpacity>

        {/* Send/Voice Button */}
        {showVoiceButton ? (
          <TouchableOpacity
            style={[
              styles.voiceButton,
              { 
                backgroundColor: isRecording ? theme.colors.error : theme.colors.primary,
                opacity: disabled ? 0.5 : 1,
              }
            ]}
            onPress={handleVoicePress}
            disabled={disabled}
          >
            <Ionicons 
              name={isRecording ? "stop" : "mic"} 
              size={normalize(20)} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        ) : (
          <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
            <TouchableOpacity
              style={[
                styles.sendButton,
                { 
                  backgroundColor: canSend ? theme.colors.primary : theme.colors.textSecondary,
                }
              ]}
              onPress={handleSendPress}
              disabled={!canSend}
            >
              {isSending || isUploading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Ionicons name="send" size={normalize(20)} color="#FFFFFF" />
              )}
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  replyContainer: {
    marginBottom: normalize(8),
    padding: normalize(12),
    borderRadius: normalize(12),
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyLine: {
    width: normalize(3),
    height: normalize(32),
    borderRadius: normalize(2),
    marginRight: normalize(12),
  },
  replyText: {
    flex: 1,
  },
  replyUsername: {
    fontSize: normalize(12),
    marginBottom: normalize(2),
  },
  replyMessage: {
    fontSize: normalize(14),
  },
  replyCancel: {
    padding: normalize(4),
  },
  attachmentOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: normalize(16),
    marginBottom: normalize(8),
    borderRadius: normalize(16),
    marginHorizontal: normalize(-8),
  },
  attachmentButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: normalize(60),
    height: normalize(60),
    borderRadius: normalize(30),
  },
  attachmentLabel: {
    fontSize: normalize(10),
    marginTop: normalize(4),
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: normalize(24),
    paddingHorizontal: normalize(4),
    paddingVertical: normalize(4),
    minHeight: normalize(52),
  },
  iconButton: {
    width: normalize(44),
    height: normalize(44),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: normalize(22),
  },
  textInputContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: normalize(8),
    minHeight: normalize(44),
  },
  textInput: {
    fontSize: normalize(16),
    fontFamily: 'System',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  sendButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(22),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: normalize(4),
  },
  voiceButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(22),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: normalize(4),
  },
});

export default TikTokChatInput;
