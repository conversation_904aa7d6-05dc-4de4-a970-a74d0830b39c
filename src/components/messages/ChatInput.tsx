import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

interface ChatInputProps {
  messageText: string;
  onTextChange: (text: string) => void;
  onSendMessage: () => void;
  onSelectAttachment: () => void;
  onToggleEmoji: () => void;
  onVoiceRecord?: () => void;
  onCameraPress?: () => void;
  onGifPress?: () => void;
  onStickerPress?: () => void;
  isSending: boolean;
  isUploading: boolean;
  isRecording?: boolean;
  replyingTo?: any;
  onCancelReply?: () => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
  messageText,
  onTextChange,
  onSendMessage,
  onSelectAttachment,
  onToggleEmoji,
  onVoiceRecord,
  onCameraPress,
  onGifPress,
  onStickerPress,
  isSending,
  isUploading,
  isRecording = false,
  replyingTo,
  onCancelReply,
}) => {
  const { theme } = useTheme();
  const [inputHeight, setInputHeight] = useState(normalize(44));
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleContentSizeChange = useCallback((event: any) => {
    const { height } = event.nativeEvent.contentSize;
    const newHeight = Math.max(normalize(44), Math.min(height + normalize(24), normalize(120)));
    setInputHeight(newHeight);
  }, []);

  const canSend = messageText.trim().length > 0 && !isSending && !isUploading;

  const handleSendPress = useCallback(() => {
    if (canSend) {
      // Animate send button
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      onSendMessage();
    }
  }, [canSend, onSendMessage, scaleAnim]);

  const toggleAttachmentOptions = useCallback(() => {
    setShowAttachmentOptions(!showAttachmentOptions);
  }, [showAttachmentOptions]);

  return (
    <View style={[
      styles.container,
      { backgroundColor: theme.colors.background }
    ]}>
      {/* Reply preview */}
      {replyingTo && (
        <View style={[
          styles.replyPreview,
          {
            backgroundColor: theme.colors.surface,
            borderTopColor: theme.colors.border,
          }
        ]}>
          <View style={styles.replyContent}>
            <Text style={[styles.replyLabel, { color: theme.colors.primary }]}>
              Replying to {replyingTo.sender?.username || 'Unknown'}
            </Text>
            <Text
              style={[styles.replyText, { color: theme.colors.textSecondary }]}
              numberOfLines={1}
            >
              {replyingTo.content || 'Media message'}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.cancelReplyButton}
            onPress={onCancelReply}
          >
            <Ionicons
              name="close"
              size={normalize(20)}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        </View>
      )}

      {/* Attachment options */}
      {showAttachmentOptions && (
        <View style={[
          styles.attachmentOptions,
          { backgroundColor: theme.colors.surface }
        ]}>
          <TouchableOpacity
            style={[styles.attachmentOption, { backgroundColor: theme.colors.primary }]}
            onPress={onCameraPress}
          >
            <Ionicons name="camera" size={normalize(24)} color="#FFFFFF" />
            <Text style={styles.attachmentOptionText}>Camera</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.attachmentOption, { backgroundColor: '#FF6B6B' }]}
            onPress={onGifPress}
          >
            <Ionicons name="gif" size={normalize(24)} color="#FFFFFF" />
            <Text style={styles.attachmentOptionText}>GIF</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.attachmentOption, { backgroundColor: '#4ECDC4' }]}
            onPress={onStickerPress}
          >
            <Ionicons name="happy" size={normalize(24)} color="#FFFFFF" />
            <Text style={styles.attachmentOptionText}>Sticker</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.attachmentOption, { backgroundColor: '#45B7D1' }]}
            onPress={onSelectAttachment}
          >
            <Ionicons name="document" size={normalize(24)} color="#FFFFFF" />
            <Text style={styles.attachmentOptionText}>File</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Main input container */}
      <View style={[
        styles.inputContainer,
        {
          backgroundColor: theme.colors.background,
          borderTopColor: theme.colors.border,
        }
      ]}>
        {/* Attachment button */}
        <TouchableOpacity
          style={[
            styles.attachmentButton,
            { backgroundColor: showAttachmentOptions ? theme.colors.primary : theme.colors.surface }
          ]}
          onPress={toggleAttachmentOptions}
          disabled={isUploading}
        >
          {isUploading ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <Ionicons
              name={showAttachmentOptions ? "close" : "add"}
              size={normalize(24)}
              color={showAttachmentOptions ? "#FFFFFF" : theme.colors.primary}
            />
          )}
        </TouchableOpacity>

        {/* Text input container */}
        <View style={[
          styles.textInputContainer,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
            height: inputHeight,
          }
        ]}>
          <TextInput
            style={[
              styles.textInput,
              {
                color: theme.colors.text,
                height: inputHeight - normalize(20),
              }
            ]}
            value={messageText}
            onChangeText={onTextChange}
            placeholder="Message..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            onContentSizeChange={handleContentSizeChange}
            maxLength={1000}
            returnKeyType="default"
          />

          {/* Emoji button */}
          <TouchableOpacity
            style={styles.emojiButton}
            onPress={onToggleEmoji}
          >
            <Ionicons
              name="happy-outline"
              size={normalize(22)}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        </View>

        {/* Send/Voice button */}
        {canSend ? (
          <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
            <TouchableOpacity
              style={[
                styles.sendButton,
                { backgroundColor: theme.colors.primary }
              ]}
              onPress={handleSendPress}
              disabled={isSending}
            >
              {isSending ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Ionicons
                  name="send"
                  size={normalize(20)}
                  color="#FFFFFF"
                />
              )}
            </TouchableOpacity>
          </Animated.View>
        ) : (
          <TouchableOpacity
            style={[
              styles.voiceButton,
              {
                backgroundColor: isRecording ? '#FF3040' : theme.colors.surface,
                borderColor: isRecording ? '#FF3040' : theme.colors.border,
              }
            ]}
            onPress={onVoiceRecord}
            disabled={isUploading}
          >
            <Ionicons
              name={isRecording ? "stop" : "mic"}
              size={normalize(22)}
              color={isRecording ? "#FFFFFF" : theme.colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  replyPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderTopWidth: 1,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: normalize(12),
    fontWeight: '600',
    marginBottom: normalize(2),
  },
  replyText: {
    fontSize: normalize(14),
  },
  cancelReplyButton: {
    padding: normalize(8),
  },
  attachmentOptions: {
    flexDirection: 'row',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    gap: normalize(12),
  },
  attachmentOption: {
    alignItems: 'center',
    justifyContent: 'center',
    width: normalize(60),
    height: normalize(60),
    borderRadius: normalize(30),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  attachmentOptionText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '600',
    marginTop: normalize(4),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderTopWidth: StyleSheet.hairlineWidth,
    gap: normalize(12),
  },
  attachmentButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(22),
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  textInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderWidth: 1,
    borderRadius: normalize(22),
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(10),
    minHeight: normalize(44),
  },
  textInput: {
    flex: 1,
    fontSize: normalize(16),
    textAlignVertical: 'center',
    paddingVertical: 0,
    fontWeight: '400',
  },
  emojiButton: {
    padding: normalize(6),
    marginLeft: normalize(8),
  },
  sendButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(22),
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  voiceButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(22),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
});

export default ChatInput;
