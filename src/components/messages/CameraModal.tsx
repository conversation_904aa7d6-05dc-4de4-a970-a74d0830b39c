import React from 'react';
import { View, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface CameraModalProps {
  visible: boolean;
  onClose: () => void;
  onTakePhoto: () => void;
  onRecordVideo: () => void;
}

const CameraModal: React.FC<CameraModalProps> = ({ 
  visible, 
  onClose, 
  onTakePhoto, 
  onRecordVideo 
}) => {
  const { theme } = useTheme();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Camera
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.optionsContainer}>
            <TouchableOpacity style={styles.option} onPress={onTakePhoto}>
              <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary }]}>
                <Ionicons name="camera-outline" size={32} color="#FFFFFF" />
              </View>
              <Text style={[styles.optionText, { color: theme.colors.text }]}>
                Take Photo
              </Text>
              <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
                Capture a single photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.option} onPress={onRecordVideo}>
              <View style={[styles.optionIcon, { backgroundColor: theme.colors.error }]}>
                <Ionicons name="videocam-outline" size={32} color="#FFFFFF" />
              </View>
              <Text style={[styles.optionText, { color: theme.colors.text }]}>
                Record Video
              </Text>
              <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
                Record up to 60 seconds
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={[styles.cancelText, { color: theme.colors.textSecondary }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  optionsContainer: {
    gap: 16,
    marginBottom: 24,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  optionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  optionDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  cancelButton: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CameraModal;
