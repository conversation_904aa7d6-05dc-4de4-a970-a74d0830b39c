import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import { normalize } from '../../utils/responsive';
import { MessageWithSender } from '../../types/messaging';
import { formatMessageTime } from '../../utils/messagingHelpers';

const { width: screenWidth } = Dimensions.get('window');
const maxBubbleWidth = screenWidth * 0.75;

interface TikTokMessageBubbleProps {
  message: MessageWithSender;
  isMe: boolean;
  showAvatar: boolean;
  showTimestamp?: boolean;
  onImagePress: (imageUrl: string) => void;
  onReaction?: (messageId: string, reaction: string) => void;
  onReply?: (message: MessageWithSender) => void;
  onForward?: (message: MessageWithSender) => void;
  onLongPress?: (message: MessageWithSender) => void;
  onDoublePress?: (message: MessageWithSender) => void;
}

const TikTokMessageBubble: React.FC<TikTokMessageBubbleProps> = ({
  message,
  isMe,
  showAvatar,
  showTimestamp = true,
  onImagePress,
  onReaction,
  onReply,
  onForward,
  onLongPress,
  onDoublePress,
}) => {
  const { theme } = useTheme();
  const [imageError, setImageError] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const reactionAnim = useRef(new Animated.Value(0)).current;

  // TikTok-style double tap detection
  const lastTap = useRef<number>(0);
  const handleDoubleTap = useCallback(() => {
    const now = Date.now();
    const DOUBLE_PRESS_DELAY = 300;
    
    if (now - lastTap.current < DOUBLE_PRESS_DELAY) {
      onDoublePress?.(message);
      // Add heart reaction on double tap (TikTok style)
      onReaction?.(message.id, '❤️');
      
      // Animate the bubble
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.1,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      lastTap.current = now;
    }
  }, [message, onDoublePress, onReaction, scaleAnim]);

  const handleLongPress = useCallback(() => {
    onLongPress?.(message);
    setShowReactions(true);
    Animated.timing(reactionAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [message, onLongPress, reactionAnim]);

  const handleReactionPress = useCallback((reaction: string) => {
    onReaction?.(message.id, reaction);
    setShowReactions(false);
    Animated.timing(reactionAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [message.id, onReaction, reactionAnim]);

  // Render message content based on type
  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <TouchableOpacity
            onPress={() => onImagePress(message.media_url || '')}
            style={styles.imageContainer}
            activeOpacity={0.8}
          >
            <Image
              source={{ uri: message.media_url || '' }}
              style={[
                styles.messageImage,
                { borderRadius: isMe ? normalize(18) : normalize(18) }
              ]}
              resizeMode="cover"
              onError={() => setImageError(true)}
            />
            {message.content && (
              <View style={[
                styles.imageCaption,
                { backgroundColor: isMe ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.9)' }
              ]}>
                <Text style={[
                  styles.imageCaptionText,
                  { color: isMe ? '#FFFFFF' : theme.colors.text }
                ]}>
                  {message.content}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        );

      case 'video':
        return (
          <View style={styles.videoContainer}>
            <Image
              source={{ uri: message.thumbnail_url || message.media_url || '' }}
              style={[
                styles.messageImage,
                { borderRadius: normalize(18) }
              ]}
              resizeMode="cover"
            />
            <View style={styles.playButton}>
              <Ionicons name="play" size={normalize(24)} color="#FFFFFF" />
            </View>
            {message.duration && (
              <View style={styles.durationBadge}>
                <Text style={styles.durationText}>
                  {Math.floor(message.duration / 60)}:{(message.duration % 60).toString().padStart(2, '0')}
                </Text>
              </View>
            )}
          </View>
        );

      case 'voice':
      case 'audio':
        return (
          <View style={styles.audioContainer}>
            <TouchableOpacity style={styles.audioPlayButton}>
              <Ionicons name="play" size={normalize(16)} color={isMe ? '#FFFFFF' : theme.colors.primary} />
            </TouchableOpacity>
            <View style={styles.audioWaveform}>
              {/* Simplified waveform visualization */}
              {Array.from({ length: 20 }).map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.waveformBar,
                    {
                      height: normalize(Math.random() * 20 + 8),
                      backgroundColor: isMe ? '#FFFFFF80' : theme.colors.primary + '80',
                    }
                  ]}
                />
              ))}
            </View>
            {message.duration && (
              <Text style={[
                styles.audioDuration,
                { color: isMe ? '#FFFFFF80' : theme.colors.textSecondary }
              ]}>
                {Math.floor(message.duration / 60)}:{(message.duration % 60).toString().padStart(2, '0')}
              </Text>
            )}
          </View>
        );

      case 'gif':
        return (
          <Image
            source={{ uri: message.media_url || '' }}
            style={[
              styles.gifImage,
              { borderRadius: normalize(18) }
            ]}
            resizeMode="cover"
          />
        );

      case 'sticker':
        return (
          <Image
            source={{ uri: message.media_url || '' }}
            style={styles.stickerImage}
            resizeMode="contain"
          />
        );

      case 'system':
        return (
          <Text style={[styles.systemMessage, { color: theme.colors.textSecondary }]}>
            {message.content}
          </Text>
        );

      default:
        return (
          <Text style={[
            styles.messageText,
            { color: isMe ? '#FFFFFF' : theme.colors.text }
          ]}>
            {message.content}
          </Text>
        );
    }
  };

  // Don't render system messages in bubbles
  if (message.type === 'system') {
    return (
      <View style={styles.systemMessageContainer}>
        <Text style={[styles.systemMessage, { color: theme.colors.textSecondary }]}>
          {message.content}
        </Text>
      </View>
    );
  }

  const bubbleBackgroundColor = isMe ? theme.colors.primary : theme.colors.surface;
  const hasReactions = message.reaction_counts && Object.keys(message.reaction_counts).length > 0;

  return (
    <Animated.View style={[
      styles.messageContainer,
      isMe ? styles.myMessage : styles.otherMessage,
      { transform: [{ scale: scaleAnim }] }
    ]}>
      {/* Avatar for other users */}
      {!isMe && showAvatar && (
        <Avatar
          source={message.sender?.avatar_url ? { uri: message.sender.avatar_url } : undefined}
          size="sm"
          style={styles.avatar}
        />
      )}
      
      <View style={styles.bubbleContainer}>
        {/* Sender name for group chats */}
        {!isMe && showAvatar && (
          <Text style={[
            styles.senderName,
            { color: theme.colors.primary }
          ]}>
            {message.sender?.full_name || message.sender?.username || 'Unknown'}
          </Text>
        )}
        
        {/* Reply indicator */}
        {message.reply_to_message_id && (
          <View style={[styles.replyIndicator, { backgroundColor: theme.colors.border }]}>
            <View style={[styles.replyLine, { backgroundColor: theme.colors.primary }]} />
            <Text style={[styles.replyText, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              Replying to message
            </Text>
          </View>
        )}
        
        {/* Message bubble */}
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <TouchableOpacity
            style={[
              styles.messageBubble,
              {
                backgroundColor: bubbleBackgroundColor,
                marginLeft: !isMe && !showAvatar ? normalize(40) : 0,
                borderTopLeftRadius: !isMe && showAvatar ? normalize(4) : normalize(18),
                borderTopRightRadius: isMe ? normalize(4) : normalize(18),
              }
            ]}
            onPress={handleDoubleTap}
            onLongPress={handleLongPress}
            activeOpacity={0.8}
          >
            {renderMessageContent()}
          </TouchableOpacity>
        </Animated.View>

        {/* Message reactions */}
        {hasReactions && (
          <View style={[
            styles.reactionsContainer,
            { alignSelf: isMe ? 'flex-end' : 'flex-start' }
          ]}>
            {Object.entries(message.reaction_counts || {}).map(([emoji, count]) => (
              <TouchableOpacity
                key={emoji}
                style={[styles.reactionBubble, { backgroundColor: theme.colors.surface }]}
                onPress={() => handleReactionPress(emoji)}
              >
                <Text style={styles.reactionEmoji}>{emoji}</Text>
                <Text style={[styles.reactionCount, { color: theme.colors.text }]}>
                  {count as number}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Timestamp and status */}
        {showTimestamp && (
          <View style={[
            styles.messageFooter,
            { alignSelf: isMe ? 'flex-end' : 'flex-start' }
          ]}>
            <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
              {formatMessageTime(message.created_at)}
            </Text>
            {isMe && (
              <Ionicons
                name={
                  message.status === 'read' ? 'checkmark-done' :
                  message.status === 'delivered' ? 'checkmark' :
                  message.status === 'sent' ? 'checkmark' : 'time'
                }
                size={normalize(12)}
                color={
                  message.status === 'read' ? theme.colors.primary :
                  message.status === 'failed' ? theme.colors.error :
                  theme.colors.textSecondary
                }
                style={styles.statusIcon}
              />
            )}
          </View>
        )}
      </View>

      {/* Quick reactions overlay */}
      {showReactions && (
        <Animated.View
          style={[
            styles.quickReactions,
            {
              backgroundColor: theme.colors.surface,
              opacity: reactionAnim,
              transform: [{
                scale: reactionAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                })
              }]
            }
          ]}
        >
          {['❤️', '😂', '😮', '😢', '😡', '👍'].map((emoji) => (
            <TouchableOpacity
              key={emoji}
              style={styles.quickReactionButton}
              onPress={() => handleReactionPress(emoji)}
            >
              <Text style={styles.quickReactionEmoji}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: normalize(2),
    paddingHorizontal: normalize(16),
  },
  myMessage: {
    justifyContent: 'flex-end',
  },
  otherMessage: {
    justifyContent: 'flex-start',
  },
  avatar: {
    marginRight: normalize(8),
    marginTop: normalize(4),
  },
  bubbleContainer: {
    maxWidth: maxBubbleWidth,
    flex: 1,
  },
  senderName: {
    fontSize: normalize(12),
    fontWeight: '600',
    marginBottom: normalize(4),
    marginLeft: normalize(12),
  },
  replyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: normalize(8),
    borderRadius: normalize(8),
    marginBottom: normalize(4),
  },
  replyLine: {
    width: normalize(3),
    height: normalize(16),
    borderRadius: normalize(2),
    marginRight: normalize(8),
  },
  replyText: {
    fontSize: normalize(12),
    flex: 1,
  },
  messageBubble: {
    padding: normalize(12),
    borderRadius: normalize(18),
    minHeight: normalize(44),
    justifyContent: 'center',
  },
  messageText: {
    fontSize: normalize(16),
    lineHeight: normalize(22),
  },
  imageContainer: {
    borderRadius: normalize(18),
    overflow: 'hidden',
    position: 'relative',
  },
  messageImage: {
    width: normalize(200),
    height: normalize(200),
  },
  imageCaption: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: normalize(12),
  },
  imageCaptionText: {
    fontSize: normalize(14),
  },
  videoContainer: {
    position: 'relative',
    borderRadius: normalize(18),
    overflow: 'hidden',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -normalize(20) }, { translateY: -normalize(20) }],
    width: normalize(40),
    height: normalize(40),
    borderRadius: normalize(20),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: normalize(8),
    right: normalize(8),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: normalize(6),
    paddingVertical: normalize(2),
    borderRadius: normalize(4),
  },
  durationText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '600',
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: normalize(150),
  },
  audioPlayButton: {
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(16),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: normalize(8),
  },
  audioWaveform: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    height: normalize(32),
  },
  waveformBar: {
    width: normalize(2),
    marginHorizontal: normalize(1),
    borderRadius: normalize(1),
  },
  audioDuration: {
    fontSize: normalize(12),
    marginLeft: normalize(8),
  },
  gifImage: {
    width: normalize(150),
    height: normalize(150),
  },
  stickerImage: {
    width: normalize(100),
    height: normalize(100),
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: normalize(8),
  },
  systemMessage: {
    fontSize: normalize(12),
    fontStyle: 'italic',
    textAlign: 'center',
  },
  reactionsContainer: {
    flexDirection: 'row',
    marginTop: normalize(4),
    flexWrap: 'wrap',
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    marginRight: normalize(4),
    marginBottom: normalize(2),
  },
  reactionEmoji: {
    fontSize: normalize(12),
    marginRight: normalize(4),
  },
  reactionCount: {
    fontSize: normalize(10),
    fontWeight: '600',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(4),
  },
  timestamp: {
    fontSize: normalize(10),
  },
  statusIcon: {
    marginLeft: normalize(4),
  },
  quickReactions: {
    position: 'absolute',
    top: -normalize(50),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(16),
    borderRadius: normalize(25),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  quickReactionButton: {
    width: normalize(40),
    height: normalize(40),
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickReactionEmoji: {
    fontSize: normalize(24),
  },
});

export default TikTokMessageBubble;
