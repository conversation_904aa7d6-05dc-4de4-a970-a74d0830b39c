import React, { useState, useCallback, useRef } from 'react';
import {
  FlatList,
  StyleSheet,
  View,
  RefreshControl,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import TikTokChatItem from './TikTokChatItem';
import Text from '../common/Text';
import { ConversationWithDetails } from '../../types/messaging';
import { normalize } from '../../utils/responsive';
import Ionicons from 'react-native-vector-icons/Ionicons';

const { width: screenWidth } = Dimensions.get('window');

interface TikTokChatListProps {
  conversations: ConversationWithDetails[];
  onChatPress: (conversationId: string) => void;
  onRefresh?: () => void;
  isRefreshing?: boolean;
  onNewChat?: () => void;
  searchQuery?: string;
  onArchiveChat?: (conversationId: string) => void;
  onDeleteChat?: (conversationId: string) => void;
  onPinChat?: (conversationId: string) => void;
}

const TikTokChatList: React.FC<TikTokChatListProps> = ({
  conversations,
  onChatPress,
  onRefresh,
  isRefreshing = false,
  onNewChat,
  searchQuery = '',
  onArchiveChat,
  onDeleteChat,
  onPinChat,
}) => {
  const { theme } = useTheme();
  const [swipedItemId, setSwipedItemId] = useState<string | null>(null);
  const scrollY = useRef(new Animated.Value(0)).current;

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    const displayName = conversation.display_name?.toLowerCase() || '';
    const lastMessageContent = conversation.last_message?.content?.toLowerCase() || '';
    
    return displayName.includes(query) || lastMessageContent.includes(query);
  });

  // Separate pinned and regular conversations
  const pinnedConversations = filteredConversations.filter(conv => conv.is_pinned);
  const regularConversations = filteredConversations.filter(conv => !conv.is_pinned);
  const sortedConversations = [...pinnedConversations, ...regularConversations];

  const handleChatPress = useCallback((conversationId: string) => {
    setSwipedItemId(null); // Close any open swipe actions
    onChatPress(conversationId);
  }, [onChatPress]);

  const handleSwipeAction = useCallback((conversationId: string, action: 'archive' | 'delete' | 'pin') => {
    setSwipedItemId(null);
    
    switch (action) {
      case 'archive':
        onArchiveChat?.(conversationId);
        break;
      case 'delete':
        onDeleteChat?.(conversationId);
        break;
      case 'pin':
        onPinChat?.(conversationId);
        break;
    }
  }, [onArchiveChat, onDeleteChat, onPinChat]);

  const renderChatItem = useCallback(({ item, index }: { item: ConversationWithDetails; index: number }) => {
    const inputRange = [-1, 0, normalize(80) * index, normalize(80) * (index + 2)];
    const opacityInputRange = [-1, 0, normalize(80) * index, normalize(80) * (index + 1)];
    
    const scale = scrollY.interpolate({
      inputRange,
      outputRange: [1, 1, 1, 0.8],
      extrapolate: 'clamp',
    });
    
    const opacity = scrollY.interpolate({
      inputRange: opacityInputRange,
      outputRange: [1, 1, 1, 0.6],
      extrapolate: 'clamp',
    });

    return (
      <Animated.View style={{ transform: [{ scale }], opacity }}>
        <TikTokChatItem
          conversation={item}
          onPress={() => handleChatPress(item.id)}
          onSwipeAction={(action) => handleSwipeAction(item.id, action)}
          isSwipeOpen={swipedItemId === item.id}
          onSwipeStateChange={(isOpen) => {
            setSwipedItemId(isOpen ? item.id : null);
          }}
        />
      </Animated.View>
    );
  }, [handleChatPress, handleSwipeAction, swipedItemId, scrollY]);

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="chatbubbles-outline" size={normalize(48)} color={theme.colors.textSecondary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]} weight="600">
        No conversations yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Start a new conversation to connect with friends
      </Text>
      {onNewChat && (
        <TouchableOpacity
          style={[styles.newChatButton, { backgroundColor: theme.colors.primary }]}
          onPress={onNewChat}
        >
          <Ionicons name="add" size={normalize(24)} color="#FFFFFF" />
          <Text style={styles.newChatButtonText} weight="600">
            Start Chatting
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSearchEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="search" size={normalize(48)} color={theme.colors.textSecondary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]} weight="600">
        No results found
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Try searching for a different name or message
      </Text>
    </View>
  );

  const renderSectionHeader = (title: string, count: number) => (
    <View style={[styles.sectionHeader, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.textSecondary }]} weight="600">
        {title} ({count})
      </Text>
    </View>
  );

  const renderListHeader = () => {
    if (searchQuery && filteredConversations.length === 0) {
      return null;
    }

    return (
      <View>
        {pinnedConversations.length > 0 && renderSectionHeader('Pinned', pinnedConversations.length)}
      </View>
    );
  };

  const renderListFooter = () => {
    if (regularConversations.length > 0 && pinnedConversations.length > 0) {
      return renderSectionHeader('Recent', regularConversations.length);
    }
    return null;
  };

  if (conversations.length === 0) {
    return renderEmptyState();
  }

  if (searchQuery && filteredConversations.length === 0) {
    return renderSearchEmptyState();
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Animated.FlatList
        data={sortedConversations}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
              colors={[theme.colors.primary]}
            />
          ) : undefined
        }
        ListHeaderComponent={renderListHeader}
        ListFooterComponent={renderListFooter}
        contentContainerStyle={styles.listContent}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={15}
        getItemLayout={(data, index) => ({
          length: normalize(80),
          offset: normalize(80) * index,
          index,
        })}
      />

      {/* Floating New Chat Button */}
      {onNewChat && conversations.length > 0 && (
        <TouchableOpacity
          style={[styles.floatingButton, { backgroundColor: theme.colors.primary }]}
          onPress={onNewChat}
        >
          <Ionicons name="add" size={normalize(24)} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: normalize(100), // Space for floating button
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  emptyIconContainer: {
    width: normalize(100),
    height: normalize(100),
    borderRadius: normalize(50),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: normalize(24),
  },
  emptyTitle: {
    fontSize: normalize(20),
    textAlign: 'center',
    marginBottom: normalize(8),
  },
  emptySubtitle: {
    fontSize: normalize(16),
    textAlign: 'center',
    lineHeight: normalize(24),
    marginBottom: normalize(32),
  },
  newChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(24),
    paddingVertical: normalize(12),
    borderRadius: normalize(25),
  },
  newChatButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(16),
    marginLeft: normalize(8),
  },
  sectionHeader: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  sectionTitle: {
    fontSize: normalize(12),
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  floatingButton: {
    position: 'absolute',
    bottom: normalize(20),
    right: normalize(20),
    width: normalize(56),
    height: normalize(56),
    borderRadius: normalize(28),
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default TikTokChatList;
