import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Avatar from '../common/Avatar';
import Text from '../common/Text';
import { formatMessageTime, formatLastMessagePreview } from '../../utils/messagingHelpers';
import { ConversationWithDetails } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');
const SWIPE_THRESHOLD = screenWidth * 0.25;

interface TikTokChatItemProps {
  conversation: ConversationWithDetails;
  onPress: () => void;
  onSwipeAction?: (action: 'archive' | 'delete' | 'pin') => void;
  isSwipeOpen?: boolean;
  onSwipeStateChange?: (isOpen: boolean) => void;
}

const TikTokChatItem: React.FC<TikTokChatItemProps> = ({
  conversation,
  onPress,
  onSwipeAction,
  isSwipeOpen = false,
  onSwipeStateChange,
}) => {
  const { theme } = useTheme();
  const [isPressed, setIsPressed] = useState(false);
  const translateX = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Determine conversation display info
  const isGroup = conversation.type === 'group';
  const user = conversation.other_participant;
  const lastMessage = conversation.last_message;
  const isUnread = (conversation.unread_count || 0) > 0;
  const isPinned = conversation.is_pinned;

  // Display name and avatar
  let displayName = conversation.name;
  let displayAvatar = conversation.avatar_url;
  let showOnline = false;

  if (!isGroup && user) {
    displayName = user.custom_nickname || user.full_name || user.username;
    displayAvatar = user.avatar_url;
    showOnline = true; // TODO: Get actual online status
  }

  // Last message preview
  const lastMessagePreview = lastMessage 
    ? formatLastMessagePreview(lastMessage)
    : 'No messages yet';

  const lastMessageTime = lastMessage?.created_at 
    ? formatMessageTime(lastMessage.created_at)
    : formatMessageTime(conversation.created_at);

  // Handle press animations
  const handlePressIn = useCallback(() => {
    setIsPressed(true);
    Animated.timing(scaleAnim, {
      toValue: 0.98,
      duration: 100,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handlePressOut = useCallback(() => {
    setIsPressed(false);
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  // Handle swipe gestures
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = useCallback((event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX } = event.nativeEvent;
      
      if (Math.abs(translationX) > SWIPE_THRESHOLD) {
        // Open swipe actions
        Animated.timing(translateX, {
          toValue: translationX > 0 ? screenWidth * 0.6 : -screenWidth * 0.6,
          duration: 200,
          useNativeDriver: true,
        }).start();
        onSwipeStateChange?.(true);
      } else {
        // Close swipe actions
        Animated.timing(translateX, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
        onSwipeStateChange?.(false);
      }
    }
  }, [translateX, onSwipeStateChange]);

  // Close swipe when another item is swiped
  React.useEffect(() => {
    if (!isSwipeOpen) {
      Animated.timing(translateX, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [isSwipeOpen, translateX]);

  const handleSwipeAction = useCallback((action: 'archive' | 'delete' | 'pin') => {
    onSwipeAction?.(action);
    // Close swipe after action
    Animated.timing(translateX, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [onSwipeAction, translateX]);

  return (
    <View style={styles.container}>
      {/* Swipe Actions Background */}
      <View style={[styles.swipeActionsContainer, { backgroundColor: theme.colors.background }]}>
        {/* Left actions */}
        <View style={styles.leftActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleSwipeAction('pin')}
          >
            <Ionicons 
              name={isPinned ? "pin" : "pin-outline"} 
              size={normalize(20)} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        </View>

        {/* Right actions */}
        <View style={styles.rightActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
            onPress={() => handleSwipeAction('archive')}
          >
            <Ionicons name="archive-outline" size={normalize(20)} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={() => handleSwipeAction('delete')}
          >
            <Ionicons name="trash-outline" size={normalize(20)} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main Chat Item */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
      >
        <Animated.View
          style={[
            styles.chatItem,
            {
              backgroundColor: isPressed ? theme.colors.surface + '80' : theme.colors.background,
              transform: [{ translateX }, { scale: scaleAnim }],
            }
          ]}
        >
          <TouchableOpacity
            style={styles.chatContent}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={1}
          >
            {/* Avatar with Online Indicator */}
            <View style={styles.avatarContainer}>
              <Avatar
                size="lg"
                source={displayAvatar ? { uri: displayAvatar } : undefined}
                style={[
                  styles.avatar,
                  isPinned && { borderWidth: 2, borderColor: theme.colors.primary }
                ]}
              />
              {showOnline && !isGroup && (
                <View style={[
                  styles.onlineIndicator,
                  {
                    borderColor: theme.colors.background,
                    backgroundColor: '#4CAF50'
                  }
                ]} />
              )}
              {isPinned && (
                <View style={[
                  styles.pinnedIndicator,
                  { backgroundColor: theme.colors.primary }
                ]}>
                  <Ionicons name="pin" size={normalize(10)} color="#FFFFFF" />
                </View>
              )}
            </View>

            {/* Message Content */}
            <View style={styles.contentContainer}>
              {/* Top Row: Username and Timestamp */}
              <View style={styles.topRow}>
                <View style={styles.nameContainer}>
                  <Text
                    style={[
                      styles.username,
                      {
                        color: theme.colors.text,
                        fontWeight: isUnread ? '700' : '600'
                      }
                    ]}
                    numberOfLines={1}
                  >
                    {displayName}
                  </Text>
                  {isGroup && (
                    <Ionicons 
                      name="people" 
                      size={normalize(12)} 
                      color={theme.colors.textSecondary}
                      style={styles.groupIcon}
                    />
                  )}
                </View>
                <View style={styles.timestampContainer}>
                  <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
                    {lastMessageTime}
                  </Text>
                  {isUnread && (
                    <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]} />
                  )}
                </View>
              </View>

              {/* Bottom Row: Last Message and Status */}
              <View style={styles.bottomRow}>
                <View style={styles.messageContainer}>
                  {lastMessage?.sender_id && (
                    <Text style={[styles.senderPrefix, { color: theme.colors.textSecondary }]}>
                      {lastMessage.sender?.username === 'You' ? 'You: ' : 
                       isGroup ? `${lastMessage.sender?.username}: ` : ''}
                    </Text>
                  )}
                  <Text
                    style={[
                      styles.lastMessage,
                      {
                        color: isUnread ? theme.colors.text : theme.colors.textSecondary,
                        fontWeight: isUnread ? '500' : '400'
                      }
                    ]}
                    numberOfLines={1}
                  >
                    {lastMessagePreview}
                  </Text>
                </View>

                <View style={styles.statusContainer}>
                  {/* Message Status for sent messages */}
                  {lastMessage?.sender_id === 'current_user_id' && (
                    <Ionicons
                      name={
                        lastMessage.status === 'read' ? 'checkmark-done' :
                        lastMessage.status === 'delivered' ? 'checkmark' :
                        lastMessage.status === 'sent' ? 'checkmark' : 'time'
                      }
                      size={normalize(12)}
                      color={
                        lastMessage.status === 'read' ? theme.colors.primary :
                        lastMessage.status === 'failed' ? theme.colors.error :
                        theme.colors.textSecondary
                      }
                      style={styles.statusIcon}
                    />
                  )}

                  {/* Unread Count */}
                  {isUnread && conversation.unread_count && conversation.unread_count > 0 && (
                    <View style={[styles.unreadBadge, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.unreadCount}>
                        {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                      </Text>
                    </View>
                  )}

                  {/* Muted Indicator */}
                  {conversation.notification_settings?.muted && (
                    <Ionicons
                      name="notifications-off"
                      size={normalize(12)}
                      color={theme.colors.textSecondary}
                      style={styles.mutedIcon}
                    />
                  )}
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: normalize(80),
    position: 'relative',
  },
  swipeActionsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalize(16),
  },
  leftActions: {
    flexDirection: 'row',
  },
  rightActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: normalize(50),
    height: normalize(50),
    borderRadius: normalize(25),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: normalize(4),
  },
  chatItem: {
    height: normalize(80),
    borderRadius: normalize(12),
    marginHorizontal: normalize(8),
    marginVertical: normalize(2),
  },
  chatContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
  },
  avatarContainer: {
    position: 'relative',
    marginRight: normalize(12),
  },
  avatar: {
    // Avatar styles handled by Avatar component
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: normalize(2),
    right: normalize(2),
    width: normalize(14),
    height: normalize(14),
    borderRadius: normalize(7),
    borderWidth: 2,
  },
  pinnedIndicator: {
    position: 'absolute',
    top: -normalize(4),
    right: -normalize(4),
    width: normalize(18),
    height: normalize(18),
    borderRadius: normalize(9),
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: normalize(4),
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  username: {
    fontSize: normalize(16),
    flex: 1,
  },
  groupIcon: {
    marginLeft: normalize(4),
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    fontSize: normalize(12),
  },
  unreadDot: {
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(4),
    marginLeft: normalize(4),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageContainer: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  senderPrefix: {
    fontSize: normalize(14),
  },
  lastMessage: {
    fontSize: normalize(14),
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    marginRight: normalize(4),
  },
  unreadBadge: {
    minWidth: normalize(20),
    height: normalize(20),
    borderRadius: normalize(10),
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: normalize(6),
    marginLeft: normalize(4),
  },
  unreadCount: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '600',
  },
  mutedIcon: {
    marginLeft: normalize(4),
  },
});

export default TikTokChatItem;
