import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  PanResponder,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  AudioRecordingState,
  setRecordingUpdateCallback,
  getRecordingState,
  pauseRecording,
  resumeRecording,
  stopRecording,
  cancelRecording,
} from '../../services/media/audio';
import logger from '../../utils/logger';

const { width, height } = Dimensions.get('window');

interface AudioRecordingModalProps {
  visible: boolean;
  onClose: () => void;
  onRecordingComplete: (audioData: { path: string; duration: number }) => void;
  onError: (error: string) => void;
}

const AudioRecordingModal: React.FC<AudioRecordingModalProps> = ({
  visible,
  onClose,
  onRecordingComplete,
  onError,
}) => {
  const { theme } = useTheme();
  const [recordingState, setRecordingState] = useState<AudioRecordingState>(getRecordingState());
  const [slideToCancel, setSlideToCancel] = useState(false);

  // Animations
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const waveAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const lockAnim = useRef(new Animated.Value(0)).current;

  // Pan responder for slide to cancel
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > 10 || Math.abs(gestureState.dy) > 10;
      },
      onPanResponderMove: (evt, gestureState) => {
        const { dx, dy } = gestureState;

        // Slide left to cancel
        if (dx < -50) {
          setSlideToCancel(true);
          slideAnim.setValue(Math.max(dx, -150));
        } else {
          setSlideToCancel(false);
          slideAnim.setValue(0);
        }

        // Slide up to lock
        if (dy < -50) {
          lockAnim.setValue(Math.max(dy, -100));
        } else {
          lockAnim.setValue(0);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        const { dx, dy } = gestureState;

        if (dx < -100) {
          // Cancel recording
          handleCancel();
        } else if (dy < -80) {
          // Lock recording (hands-free mode)
          lockAnim.setValue(-100);
          // Continue recording in locked mode
        } else {
          // Return to original position
          Animated.parallel([
            Animated.spring(slideAnim, { toValue: 0, useNativeDriver: true }),
            Animated.spring(lockAnim, { toValue: 0, useNativeDriver: true }),
          ]).start();
          setSlideToCancel(false);
        }
      },
    })
  ).current;

  useEffect(() => {
    if (visible) {
      setRecordingUpdateCallback(setRecordingState);
      startRecordingAnimations();
    } else {
      setRecordingUpdateCallback(null);
      stopAllAnimations();
      resetAnimations();
    }

    return () => {
      setRecordingUpdateCallback(null);
      stopAllAnimations();
    };
  }, [visible]);

  const startRecordingAnimations = () => {
    // Pulse animation for recording button
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Wave animation for audio visualization
    Animated.loop(
      Animated.sequence([
        Animated.timing(waveAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(waveAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Scale animation for the entire recording area
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const stopAllAnimations = () => {
    pulseAnim.stopAnimation();
    waveAnim.stopAnimation();
    scaleAnim.stopAnimation();
    slideAnim.stopAnimation();
    lockAnim.stopAnimation();
  };

  const resetAnimations = () => {
    pulseAnim.setValue(1);
    waveAnim.setValue(0);
    scaleAnim.setValue(0);
    slideAnim.setValue(0);
    lockAnim.setValue(0);
    setSlideToCancel(false);
  };

  const handleStop = async () => {
    try {
      stopAllAnimations();
      const result = await stopRecording();
      onRecordingComplete(result);
    } catch (error) {
      logger.error('Error stopping recording:', error);
      onError('Failed to stop recording');
    }
  };

  const handleCancel = async () => {
    try {
      stopAllAnimations();
      await cancelRecording();
      onClose();
    } catch (error) {
      logger.error('Error cancelling recording:', error);
      onClose();
    }
  };

  const formatDuration = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderWaveform = () => {
    const bars = Array.from({ length: 40 }, (_, index) => {
      const height = Math.random() * 30 + 10;
      const animatedHeight = waveAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [height * 0.2, height],
      });

      return (
        <Animated.View
          key={index}
          style={[
            styles.waveBar,
            {
              height: animatedHeight,
              backgroundColor: slideToCancel ? '#FF6B6B' : '#007AFF',
            },
          ]}
        />
      );
    });

    return <View style={styles.waveform}>{bars}</View>;
  };

  const getRecordingStatusText = (): string => {
    if (!recordingState.isRecording) return 'Ready to record';
    if (recordingState.isPaused) return 'Recording paused';
    return 'Recording...';
  };

  const getRecordingIcon = (): string => {
    if (!recordingState.isRecording) return 'mic-outline';
    if (recordingState.isPaused) return 'pause';
    return 'mic';
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={handleCancel}
      statusBarTranslucent
    >
      <View style={styles.modalContainer}>
        <Animated.View
          style={[
            styles.recordingContainer,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {/* Slide to Cancel Indicator */}
          <Animated.View
            style={[
              styles.slideIndicator,
              {
                opacity: slideAnim.interpolate({
                  inputRange: [-150, -50, 0],
                  outputRange: [1, 0.7, 0],
                  extrapolate: 'clamp',
                }),
                transform: [{ translateX: slideAnim }],
              },
            ]}
          >
            <Ionicons name="chevron-back" size={20} color="#FF6B6B" />
            <Text style={styles.slideText}>Slide to cancel</Text>
          </Animated.View>

          {/* Lock Indicator */}
          <Animated.View
            style={[
              styles.lockIndicator,
              {
                opacity: lockAnim.interpolate({
                  inputRange: [-100, -50, 0],
                  outputRange: [1, 0.7, 0],
                  extrapolate: 'clamp',
                }),
                transform: [{ translateY: lockAnim }],
              },
            ]}
          >
            <Ionicons name="lock-closed" size={20} color="#007AFF" />
            <Text style={styles.lockText}>Slide up to lock</Text>
          </Animated.View>

          {/* Main Recording Area */}
          <View style={[styles.recordingArea, { backgroundColor: theme.colors.surface }]}>
            {/* Duration and Waveform */}
            <View style={styles.audioVisualization}>
              <View style={styles.durationContainer}>
                <View style={styles.recordingDot} />
                <Text style={styles.durationText}>
                  {formatDuration(recordingState.duration)}
                </Text>
              </View>
              {renderWaveform()}
            </View>

            {/* Controls */}
            <View style={styles.controlsRow}>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={handleCancel}
              >
                <Ionicons name="trash-outline" size={24} color="#FF6B6B" />
              </TouchableOpacity>

              <Animated.View
                style={[
                  styles.recordButton,
                  {
                    transform: [{ scale: pulseAnim }],
                    backgroundColor: slideToCancel ? '#FF6B6B' : '#007AFF',
                  },
                ]}
              >
                <Ionicons name="mic" size={28} color="#FFFFFF" />
              </Animated.View>

              <TouchableOpacity
                style={styles.sendButton}
                onPress={handleStop}
              >
                <Ionicons name="send" size={24} color="#007AFF" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Instructions */}
          <Text style={styles.instructionText}>
            {slideToCancel
              ? 'Release to cancel'
              : 'Hold to record, slide up to lock, slide left to cancel'
            }
          </Text>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  recordingContainer: {
    alignItems: 'center',
    paddingBottom: 50,
  },
  slideIndicator: {
    position: 'absolute',
    left: 20,
    top: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    zIndex: 1000,
  },
  slideText: {
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  lockIndicator: {
    position: 'absolute',
    top: 20,
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    zIndex: 1000,
  },
  lockText: {
    color: '#007AFF',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  recordingArea: {
    width: width * 0.9,
    borderRadius: 25,
    padding: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  audioVisualization: {
    alignItems: 'center',
    marginBottom: 20,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF6B6B',
    marginRight: 8,
  },
  durationText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'monospace',
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: '100%',
    paddingHorizontal: 20,
  },
  waveBar: {
    width: 3,
    borderRadius: 1.5,
    marginHorizontal: 1,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  deleteButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,107,107,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,122,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 13,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
    marginTop: 16,
    paddingHorizontal: 20,
  },
});

export default AudioRecordingModal;
