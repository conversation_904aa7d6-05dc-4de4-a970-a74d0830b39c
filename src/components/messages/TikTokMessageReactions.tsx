import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Modal,
  Dimensions,
  FlatList,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import { normalize } from '../../utils/responsive';
import { MessageReaction } from '../../types/messaging';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface TikTokMessageReactionsProps {
  reactions: MessageReaction[];
  onReactionPress?: (reaction: string) => void;
  onRemoveReaction?: (reactionId: string) => void;
  currentUserId?: string;
  maxDisplayReactions?: number;
}

const TikTokMessageReactions: React.FC<TikTokMessageReactionsProps> = ({
  reactions,
  onReactionPress,
  onRemoveReaction,
  currentUserId,
  maxDisplayReactions = 3,
}) => {
  const { theme } = useTheme();
  const [showReactionDetails, setShowReactionDetails] = useState(false);
  const [selectedReaction, setSelectedReaction] = useState<string | null>(null);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const modalAnim = useRef(new Animated.Value(0)).current;

  // Group reactions by emoji
  const groupedReactions = reactions.reduce((acc, reaction) => {
    if (!acc[reaction.reaction]) {
      acc[reaction.reaction] = [];
    }
    acc[reaction.reaction].push(reaction);
    return acc;
  }, {} as Record<string, MessageReaction[]>);

  // Get top reactions for display
  const topReactions = Object.entries(groupedReactions)
    .sort(([, a], [, b]) => b.length - a.length)
    .slice(0, maxDisplayReactions);

  const totalReactions = reactions.length;
  const hasMoreReactions = Object.keys(groupedReactions).length > maxDisplayReactions;

  const handleReactionPress = useCallback((emoji: string) => {
    // Check if current user already reacted with this emoji
    const userReaction = groupedReactions[emoji]?.find(r => r.user_id === currentUserId);
    
    if (userReaction) {
      // Remove reaction
      onRemoveReaction?.(userReaction.id);
    } else {
      // Add reaction
      onReactionPress?.(emoji);
    }

    // Animate press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  }, [groupedReactions, currentUserId, onReactionPress, onRemoveReaction, scaleAnim]);

  const handleLongPress = useCallback(() => {
    setShowReactionDetails(true);
    Animated.timing(modalAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [modalAnim]);

  const closeReactionDetails = useCallback(() => {
    Animated.timing(modalAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowReactionDetails(false);
      setSelectedReaction(null);
    });
  }, [modalAnim]);

  const renderReactionItem = ({ item: [emoji, reactionList] }: { item: [string, MessageReaction[]] }) => {
    const count = reactionList.length;
    const hasUserReacted = reactionList.some(r => r.user_id === currentUserId);
    
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[
            styles.reactionBubble,
            {
              backgroundColor: hasUserReacted ? theme.colors.primary + '20' : theme.colors.surface,
              borderColor: hasUserReacted ? theme.colors.primary : 'transparent',
            }
          ]}
          onPress={() => handleReactionPress(emoji)}
          onLongPress={handleLongPress}
          activeOpacity={0.8}
        >
          <Text style={styles.reactionEmoji}>{emoji}</Text>
          <Text style={[
            styles.reactionCount,
            {
              color: hasUserReacted ? theme.colors.primary : theme.colors.text,
              fontWeight: hasUserReacted ? '600' : '500',
            }
          ]}>
            {count}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderReactionDetailItem = ({ item }: { item: MessageReaction }) => (
    <View style={[styles.reactionDetailItem, { backgroundColor: theme.colors.surface }]}>
      <Avatar
        source={item.user?.avatar_url ? { uri: item.user.avatar_url } : undefined}
        size="sm"
        style={styles.reactionDetailAvatar}
      />
      <View style={styles.reactionDetailContent}>
        <Text style={[styles.reactionDetailName, { color: theme.colors.text }]} weight="600">
          {item.user?.full_name || item.user?.username || 'Unknown User'}
        </Text>
        <Text style={[styles.reactionDetailTime, { color: theme.colors.textSecondary }]}>
          {new Date(item.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
      <Text style={styles.reactionDetailEmoji}>{item.reaction}</Text>
    </View>
  );

  const renderReactionTabs = () => {
    const allReactions = ['All', ...Object.keys(groupedReactions)];
    
    return (
      <View style={styles.reactionTabs}>
        {allReactions.map((reaction) => {
          const isSelected = selectedReaction === reaction || (reaction === 'All' && !selectedReaction);
          const count = reaction === 'All' ? totalReactions : groupedReactions[reaction]?.length || 0;
          
          return (
            <TouchableOpacity
              key={reaction}
              style={[
                styles.reactionTab,
                {
                  backgroundColor: isSelected ? theme.colors.primary : 'transparent',
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => setSelectedReaction(reaction === 'All' ? null : reaction)}
            >
              {reaction !== 'All' && (
                <Text style={styles.reactionTabEmoji}>{reaction}</Text>
              )}
              <Text style={[
                styles.reactionTabText,
                { color: isSelected ? '#FFFFFF' : theme.colors.text }
              ]}>
                {reaction === 'All' ? 'All' : ''} {count}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const getFilteredReactions = () => {
    if (!selectedReaction) {
      return reactions;
    }
    return groupedReactions[selectedReaction] || [];
  };

  if (totalReactions === 0) {
    return null;
  }

  return (
    <>
      {/* Reaction Bubbles */}
      <View style={styles.container}>
        <FlatList
          data={topReactions}
          renderItem={renderReactionItem}
          keyExtractor={([emoji]) => emoji}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.reactionsList}
        />
        
        {hasMoreReactions && (
          <TouchableOpacity
            style={[styles.moreReactions, { backgroundColor: theme.colors.surface }]}
            onPress={handleLongPress}
          >
            <Text style={[styles.moreReactionsText, { color: theme.colors.textSecondary }]}>
              +{Object.keys(groupedReactions).length - maxDisplayReactions}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Reaction Details Modal */}
      <Modal
        visible={showReactionDetails}
        transparent
        animationType="none"
        onRequestClose={closeReactionDetails}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeReactionDetails}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                backgroundColor: theme.colors.background,
                transform: [{
                  translateY: modalAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [screenHeight, 0],
                  })
                }],
                opacity: modalAnim,
              }
            ]}
          >
            <TouchableOpacity activeOpacity={1}>
              {/* Modal Header */}
              <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
                <Text style={[styles.modalTitle, { color: theme.colors.text }]} weight="600">
                  Reactions ({totalReactions})
                </Text>
                <TouchableOpacity onPress={closeReactionDetails} style={styles.closeButton}>
                  <Text style={[styles.closeButtonText, { color: theme.colors.textSecondary }]}>
                    ✕
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Reaction Tabs */}
              {renderReactionTabs()}

              {/* Reactions List */}
              <FlatList
                data={getFilteredReactions()}
                renderItem={renderReactionDetailItem}
                keyExtractor={(item) => item.id}
                style={styles.reactionDetailsList}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.reactionDetailsContent}
              />
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(4),
  },
  reactionsList: {
    paddingRight: normalize(8),
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    marginRight: normalize(4),
    borderWidth: 1,
    minHeight: normalize(24),
  },
  reactionEmoji: {
    fontSize: normalize(12),
    marginRight: normalize(4),
  },
  reactionCount: {
    fontSize: normalize(10),
    minWidth: normalize(12),
    textAlign: 'center',
  },
  moreReactions: {
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    minHeight: normalize(24),
    justifyContent: 'center',
  },
  moreReactionsText: {
    fontSize: normalize(10),
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: normalize(20),
    borderTopRightRadius: normalize(20),
    maxHeight: screenHeight * 0.7,
    minHeight: screenHeight * 0.4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: normalize(18),
  },
  closeButton: {
    padding: normalize(4),
  },
  closeButtonText: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  reactionTabs: {
    flexDirection: 'row',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(12),
  },
  reactionTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
    borderRadius: normalize(16),
    marginRight: normalize(8),
    borderWidth: 1,
  },
  reactionTabEmoji: {
    fontSize: normalize(14),
    marginRight: normalize(4),
  },
  reactionTabText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  reactionDetailsList: {
    flex: 1,
  },
  reactionDetailsContent: {
    paddingHorizontal: normalize(20),
    paddingBottom: normalize(20),
  },
  reactionDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderRadius: normalize(12),
    marginBottom: normalize(8),
  },
  reactionDetailAvatar: {
    marginRight: normalize(12),
  },
  reactionDetailContent: {
    flex: 1,
  },
  reactionDetailName: {
    fontSize: normalize(14),
    marginBottom: normalize(2),
  },
  reactionDetailTime: {
    fontSize: normalize(12),
  },
  reactionDetailEmoji: {
    fontSize: normalize(20),
  },
});

export default TikTokMessageReactions;
