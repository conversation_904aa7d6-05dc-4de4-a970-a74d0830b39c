import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

interface MessageReactionsProps {
  reactions: Record<string, number>;
  onReactionPress: (reactionType: string) => void;
  style?: ViewStyle;
}

const REACTION_EMOJIS: Record<string, string> = {
  like: '👍',
  love: '❤️',
  laugh: '😂',
  wow: '😮',
  sad: '😢',
  angry: '😡',
  fire: '🔥',
  heart: '💖',
};

const MessageReactions: React.FC<MessageReactionsProps> = ({
  reactions,
  onReactionPress,
  style,
}) => {
  const { theme } = useTheme();

  // Filter out reactions with 0 count and sort by count
  const activeReactions = Object.entries(reactions)
    .filter(([_, count]) => count > 0)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3); // Show max 3 reactions

  if (activeReactions.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      {activeReactions.map(([reactionType, count]) => (
        <TouchableOpacity
          key={reactionType}
          style={[
            styles.reactionBubble,
            { backgroundColor: theme.colors.surface }
          ]}
          onPress={() => onReactionPress(reactionType)}
          activeOpacity={0.7}
        >
          <Text style={styles.reactionEmoji}>
            {REACTION_EMOJIS[reactionType] || '👍'}
          </Text>
          {count > 1 && (
            <Text style={[
              styles.reactionCount,
              { color: theme.colors.textSecondary }
            ]}>
              {count}
            </Text>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: normalize(4),
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  reactionEmoji: {
    fontSize: normalize(14),
  },
  reactionCount: {
    fontSize: normalize(11),
    fontWeight: '600',
    marginLeft: normalize(4),
  },
});

export default MessageReactions;
