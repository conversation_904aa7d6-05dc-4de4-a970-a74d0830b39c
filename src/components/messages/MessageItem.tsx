import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import AudioPlayer from './AudioPlayer';
import VideoPlayer from './VideoPlayer';
import { MessageWithSender } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

interface MessageItemProps {
  message: MessageWithSender;
  isMe: boolean;
  showAvatar: boolean;
  onImagePress: (imageUrl: string) => void;
  onReaction?: (messageId: string, reactionType: string) => void;
  onReply?: (message: MessageWithSender) => void;
  onForward?: (message: MessageWithSender) => void;
  onLongPress?: (message: MessageWithSender) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isMe,
  showAvatar,
  onImagePress,
}) => {
  const { theme } = useTheme();

  // Process message for display
  const messageType = message.type;
  const isMedia = ['image', 'video', 'audio', 'voice', 'gif'].includes(messageType);
  const hasReactions = message.reaction_counts && Object.keys(message.reaction_counts).length > 0;

  // Function to render message content based on type
  const renderMessageContent = () => {
    switch (messageType) {
      case 'image':
        return (
          <TouchableOpacity
            onPress={() => onImagePress(message.media_url || '')}
            style={styles.imageContainer}
          >
            <Image
              source={{ uri: message.media_url || '' }}
              style={[
                styles.messageImage,
                { borderRadius: isMe ? normalize(18) : normalize(18) }
              ]}
              resizeMode="cover"
            />
            {message.content && (
              <View style={[
                styles.imageCaption,
                { backgroundColor: isMe ? theme.colors.primary : theme.colors.surface }
              ]}>
                <Text style={[
                  styles.imageCaptionText,
                  { color: isMe ? '#FFFFFF' : theme.colors.text }
                ]}>
                  {message.content}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        );

      case 'video':
        return (
          <View style={styles.videoContainer}>
            <VideoPlayer
              videoUrl={message.media_url || ''}
              thumbnail={message.thumbnail_url}
              isMe={isMe}
            />
            {message.content && (
              <View style={[
                styles.videoCaption,
                { backgroundColor: isMe ? theme.colors.primary : theme.colors.surface }
              ]}>
                <Text style={[
                  styles.videoCaptionText,
                  { color: isMe ? '#FFFFFF' : theme.colors.text }
                ]}>
                  {message.content}
                </Text>
              </View>
            )}
          </View>
        );

      case 'voice':
      case 'audio':
        return (
          <View style={[
            styles.audioContainer,
            { backgroundColor: isMe ? theme.colors.primary + '20' : theme.colors.surface }
          ]}>
            <AudioPlayer
              audioUrl={message.media_url || ''}
              duration={message.duration || 0}
              isMe={isMe}
            />
          </View>
        );

      case 'gif':
        return (
          <TouchableOpacity
            onPress={() => onImagePress(message.media_url || '')}
            style={styles.gifContainer}
          >
            <Image
              source={{ uri: message.media_url || '' }}
              style={styles.gifImage}
              resizeMode="cover"
            />
            <View style={styles.gifBadge}>
              <Text style={styles.gifBadgeText}>GIF</Text>
            </View>
          </TouchableOpacity>
        );

      case 'sticker':
        return (
          <Image
            source={{ uri: message.media_url || '' }}
            style={styles.stickerImage}
            resizeMode="contain"
          />
        );

      case 'location':
        return (
          <View style={[
            styles.locationContainer,
            { backgroundColor: isMe ? theme.colors.primary : theme.colors.surface }
          ]}>
            <Ionicons
              name="location"
              size={normalize(20)}
              color={isMe ? '#FFFFFF' : theme.colors.primary}
            />
            <Text style={[
              styles.locationText,
              { color: isMe ? '#FFFFFF' : theme.colors.text }
            ]}>
              {message.content || 'Location shared'}
            </Text>
          </View>
        );

      case 'text':
      default:
        return (
          <Text style={[
            styles.messageText,
            { color: isMe ? '#FFFFFF' : theme.colors.text }
          ]}>
            {message.content}
          </Text>
        );
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <View style={[
      styles.messageContainer,
      isMe ? styles.myMessage : styles.otherMessage
    ]}>
      {/* Avatar for other users */}
      {!isMe && showAvatar && (
        <Avatar
          source={message.sender?.avatar_url ? { uri: message.sender.avatar_url } : undefined}
          size="sm"
        />
      )}
      
      {/* Message bubble */}
      <View style={[
        styles.messageBubble,
        {
          backgroundColor: isMe ? theme.colors.primary : theme.colors.surface,
          marginLeft: !isMe && !showAvatar ? normalize(40) : 0,
        }
      ]}>
        {/* Sender name for group chats */}
        {!isMe && showAvatar && (
          <Text style={[
            styles.senderName,
            { color: theme.colors.primary }
          ]}>
            {message.sender?.full_name || message.sender?.username || 'Unknown'}
          </Text>
        )}
        
        {/* Message content */}
        {renderMessageContent()}
        
        {/* File info for media messages */}
        {isMedia && message.file_name && (
          <Text style={[
            styles.fileName,
            { color: isMe ? '#FFFFFF80' : theme.colors.textSecondary }
          ]}>
            {message.file_name}
          </Text>
        )}

        {/* Message status and timestamp */}
        <View style={styles.messageFooter}>
          <Text style={[
            styles.timestamp,
            { color: isMe ? '#FFFFFF60' : theme.colors.textSecondary }
          ]}>
            {formatTime(message.created_at)}
          </Text>

          {/* Message status for sent messages */}
          {isMe && (
            <View style={styles.statusContainer}>
              {message.status === 'sent' && (
                <Ionicons name="checkmark" size={normalize(12)} color="#FFFFFF60" />
              )}
              {message.status === 'delivered' && (
                <Ionicons name="checkmark-done" size={normalize(12)} color="#FFFFFF60" />
              )}
              {message.status === 'read' && (
                <Ionicons name="checkmark-done" size={normalize(12)} color="#4CAF50" />
              )}
              {message.status === 'failed' && (
                <Ionicons name="alert-circle" size={normalize(12)} color="#FF3040" />
              )}
            </View>
          )}
        </View>
      </View>

      {/* Message reactions - TODO: Implement MessageReactions component */}
      {hasReactions && (
        <View style={[
          styles.reactionsContainer,
          { alignSelf: isMe ? 'flex-end' : 'flex-start' }
        ]}>
          <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
            {Object.keys(message.reaction_counts || {}).length} reactions
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: normalize(4),
    paddingHorizontal: normalize(16),
    alignItems: 'flex-end',
  },
  myMessage: {
    justifyContent: 'flex-end',
  },
  otherMessage: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    marginRight: normalize(8),
    marginBottom: normalize(4),
  },
  avatar: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  messageBubble: {
    borderRadius: normalize(20),
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  senderName: {
    fontSize: normalize(12),
    fontWeight: '600',
    marginBottom: normalize(4),
  },
  messageText: {
    fontSize: normalize(16),
    lineHeight: normalize(22),
    fontWeight: '400',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: normalize(6),
  },
  timestamp: {
    fontSize: normalize(11),
    fontWeight: '400',
  },
  statusContainer: {
    marginLeft: normalize(8),
  },
  fileName: {
    fontSize: normalize(12),
    marginTop: normalize(4),
    fontStyle: 'italic',
    opacity: 0.8,
  },
  replyPreview: {
    borderLeftWidth: 3,
    paddingLeft: normalize(8),
    marginBottom: normalize(8),
  },
  replyText: {
    fontSize: normalize(12),
    fontStyle: 'italic',
  },
  forwardedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(4),
  },
  forwardedText: {
    fontSize: normalize(11),
    marginLeft: normalize(4),
    fontStyle: 'italic',
  },
  imageContainer: {
    borderRadius: normalize(18),
    overflow: 'hidden',
    position: 'relative',
  },
  messageImage: {
    width: normalize(220),
    height: normalize(220),
  },
  imageCaption: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
  },
  imageCaptionText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
  videoContainer: {
    borderRadius: normalize(18),
    overflow: 'hidden',
    position: 'relative',
  },
  videoPlayer: {
    width: normalize(220),
    height: normalize(280),
    borderRadius: normalize(18),
  },
  videoCaption: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
  },
  videoCaptionText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
  audioContainer: {
    borderRadius: normalize(20),
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    minWidth: normalize(200),
  },
  audioPlayer: {
    width: '100%',
    height: normalize(50),
  },
  gifContainer: {
    borderRadius: normalize(18),
    overflow: 'hidden',
    position: 'relative',
  },
  gifImage: {
    width: normalize(180),
    height: normalize(180),
  },
  gifBadge: {
    position: 'absolute',
    top: normalize(8),
    right: normalize(8),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: normalize(6),
    paddingVertical: normalize(2),
    borderRadius: normalize(4),
  },
  gifBadgeText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: 'bold',
  },
  stickerImage: {
    width: normalize(120),
    height: normalize(120),
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(10),
    borderRadius: normalize(20),
    minWidth: normalize(160),
  },
  locationText: {
    marginLeft: normalize(8),
    fontSize: normalize(14),
    fontWeight: '500',
  },
  reactionsContainer: {
    marginTop: normalize(4),
    marginHorizontal: normalize(16),
  },
});

export default MessageItem;
