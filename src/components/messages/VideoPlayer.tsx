import React, { useState, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Video from 'react-native-video';
import logger from '../../utils/logger';

const { width: screenWidth } = Dimensions.get('window');
const maxVideoWidth = Math.min(screenWidth * 0.7, 280);

interface VideoPlayerProps {
  videoUrl: string;
  duration?: number;
  thumbnail?: string;
  isMe: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  duration = 0,
  thumbnail,
  isMe: _isMe // Prefix with underscore to indicate intentionally unused
}) => {
  const { theme } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [totalDuration, setTotalDuration] = useState(duration);
  const [isLoading, setIsLoading] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [videoSize, setVideoSize] = useState({ width: maxVideoWidth, height: 200 });
  const videoRef = useRef<any>(null);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    setShowControls(true);
    
    // Hide controls after 3 seconds when playing
    if (!isPlaying) {
      setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }
  };

  const handleVideoPress = () => {
    setShowControls(!showControls);
    
    // Auto-hide controls after 3 seconds
    if (!showControls && isPlaying) {
      setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }
  };

  const handleLoad = (data: any) => {
    logger.debug('Video loaded:', data);
    setTotalDuration(data.duration * 1000); // Convert to milliseconds
    setIsLoading(false);
    
    // Calculate aspect ratio and set video size
    if (data.naturalSize) {
      const aspectRatio = data.naturalSize.width / data.naturalSize.height;
      const calculatedHeight = maxVideoWidth / aspectRatio;
      const finalHeight = Math.min(Math.max(calculatedHeight, 150), 400); // Min 150px, max 400px
      
      setVideoSize({
        width: maxVideoWidth,
        height: finalHeight,
      });
    }
  };

  const handleProgress = (data: any) => {
    setCurrentTime(data.currentTime * 1000); // Convert to milliseconds
  };

  const handleEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    setShowControls(true);
  };

  const handleError = (error: any) => {
    logger.error('Video playback error:', error);
    setIsLoading(false);
    setIsPlaying(false);
  };

  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgress = (): number => {
    if (totalDuration === 0) return 0;
    return currentTime / totalDuration;
  };

  return (
    <View style={[
      styles.container,
      {
        width: videoSize.width,
        height: videoSize.height,
      }
    ]}>
      {/* Video Component */}
      <Video
        ref={videoRef}
        source={{ uri: videoUrl }}
        style={styles.video}
        resizeMode="cover"
        paused={!isPlaying}
        onLoad={handleLoad}
        onProgress={handleProgress}
        onEnd={handleEnd}
        onError={handleError}
        onLoadStart={() => setIsLoading(true)}
        poster={thumbnail}
      />

      {/* Video Overlay */}
      <TouchableOpacity
        style={styles.videoOverlay}
        onPress={handleVideoPress}
        activeOpacity={1}
      >
        {/* Controls */}
        {showControls && (
          <View style={styles.controls}>
            {/* Play/Pause Button */}
            <TouchableOpacity
              style={[
                styles.playButton,
                { backgroundColor: 'rgba(0,0,0,0.6)' }
              ]}
              onPress={handlePlayPause}
            >
              {isLoading ? (
                <Ionicons name="hourglass-outline" size={32} color="#FFFFFF" />
              ) : (
                <Ionicons
                  name={isPlaying ? 'pause' : 'play'}
                  size={32}
                  color="#FFFFFF"
                />
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Progress Bar */}
        {showControls && totalDuration > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${getProgress() * 100}%`,
                    backgroundColor: theme.colors.primary,
                  },
                ]}
              />
            </View>
            <Text style={styles.timeText}>
              {formatTime(currentTime)} / {formatTime(totalDuration)}
            </Text>
          </View>
        )}

        {/* Duration Badge (when not playing) */}
        {!isPlaying && !showControls && totalDuration > 0 && (
          <View style={styles.durationBadge}>
            <Ionicons name="play" size={12} color="#FFFFFF" />
            <Text style={styles.durationText}>
              {formatTime(totalDuration)}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#000',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 1.5,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 1.5,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'monospace',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  durationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 12,
  },
  durationText: {
    color: '#FFFFFF',
    fontSize: 11,
    fontFamily: 'monospace',
    marginLeft: 3,
  },
});

export default VideoPlayer;
