import React from 'react';
import { View, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface AttachmentModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectImage: () => void;
  onSelectVideo: () => void;
  onOpenCamera?: () => void;
  onRecordAudio: () => void;
}

const AttachmentModal: React.FC<AttachmentModalProps> = ({
  visible,
  onClose,
  onSelectImage,
  onSelectVideo,
  onOpenCamera,
  onRecordAudio
}) => {
  const { theme } = useTheme();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity style={styles.option} onPress={onSelectImage}>
            <Ionicons name="images-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.optionText, { color: theme.colors.text }]}>Photo Gallery</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.option} onPress={onSelectVideo}>
            <Ionicons name="videocam-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.optionText, { color: theme.colors.text }]}>Video Gallery</Text>
          </TouchableOpacity>
          {onOpenCamera && (
            <TouchableOpacity style={styles.option} onPress={onOpenCamera}>
              <Ionicons name="camera-outline" size={24} color={theme.colors.text} />
              <Text style={[styles.optionText, { color: theme.colors.text }]}>Camera</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.option} onPress={onRecordAudio}>
            <Ionicons name="mic-outline" size={24} color={theme.colors.text} />
            <Text style={[styles.optionText, { color: theme.colors.text }]}>Voice Message</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={{ color: theme.colors.primary }}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  optionText: {
    fontSize: 16,
    marginLeft: 16,
  },
  closeButton: {
    marginTop: 16,
    alignItems: 'center',
  },
});

export default AttachmentModal;
