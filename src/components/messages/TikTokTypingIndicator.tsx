import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import { normalize } from '../../utils/responsive';
import { TypingIndicator } from '../../types/messaging';

interface TikTokTypingIndicatorProps {
  typingUsers: TypingIndicator[];
  maxDisplayUsers?: number;
}

const TikTokTypingIndicator: React.FC<TikTokTypingIndicatorProps> = ({
  typingUsers,
  maxDisplayUsers = 3,
}) => {
  const { theme } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;

  // Animate typing dots
  useEffect(() => {
    if (typingUsers.length > 0) {
      // Show indicator
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      // Animate dots
      const animateDots = () => {
        const createDotAnimation = (animValue: Animated.Value, delay: number) => {
          return Animated.loop(
            Animated.sequence([
              Animated.delay(delay),
              Animated.timing(animValue, {
                toValue: 1,
                duration: 400,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(animValue, {
                toValue: 0,
                duration: 400,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ])
          );
        };

        Animated.parallel([
          createDotAnimation(dot1Anim, 0),
          createDotAnimation(dot2Anim, 200),
          createDotAnimation(dot3Anim, 400),
        ]).start();
      };

      animateDots();
    } else {
      // Hide indicator
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 20,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      // Reset dot animations
      dot1Anim.setValue(0);
      dot2Anim.setValue(0);
      dot3Anim.setValue(0);
    }
  }, [typingUsers.length, fadeAnim, slideAnim, dot1Anim, dot2Anim, dot3Anim]);

  if (typingUsers.length === 0) {
    return null;
  }

  // Limit displayed users
  const displayUsers = typingUsers.slice(0, maxDisplayUsers);
  const remainingCount = typingUsers.length - maxDisplayUsers;

  // Generate typing text
  const getTypingText = () => {
    if (displayUsers.length === 1) {
      const user = displayUsers[0];
      const name = user.user?.full_name || user.user?.username || 'Someone';
      
      switch (user.typing_type) {
        case 'voice':
          return `${name} is recording a voice message`;
        case 'media':
          return `${name} is sharing media`;
        default:
          return `${name} is typing`;
      }
    } else if (displayUsers.length === 2) {
      const names = displayUsers.map(u => u.user?.full_name || u.user?.username || 'Someone');
      return `${names[0]} and ${names[1]} are typing`;
    } else {
      const firstName = displayUsers[0].user?.full_name || displayUsers[0].user?.username || 'Someone';
      if (remainingCount > 0) {
        return `${firstName} and ${displayUsers.length - 1 + remainingCount} others are typing`;
      } else {
        return `${firstName} and ${displayUsers.length - 1} others are typing`;
      }
    }
  };

  const renderTypingDots = () => {
    const dotStyle = (animValue: Animated.Value) => ({
      opacity: animValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0.3, 1],
      }),
      transform: [{
        scale: animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1.2],
        })
      }]
    });

    return (
      <View style={styles.dotsContainer}>
        <Animated.View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }, dotStyle(dot1Anim)]} />
        <Animated.View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }, dotStyle(dot2Anim)]} />
        <Animated.View style={[styles.dot, { backgroundColor: theme.colors.textSecondary }, dotStyle(dot3Anim)]} />
      </View>
    );
  };

  const renderUserAvatars = () => {
    if (displayUsers.length === 1) {
      const user = displayUsers[0];
      return (
        <Avatar
          source={user.user?.avatar_url ? { uri: user.user.avatar_url } : undefined}
          size="xs"
          style={styles.singleAvatar}
        />
      );
    }

    return (
      <View style={styles.avatarStack}>
        {displayUsers.slice(0, 2).map((user, index) => (
          <Avatar
            key={user.id}
            source={user.user?.avatar_url ? { uri: user.user.avatar_url } : undefined}
            size="xs"
            style={[
              styles.stackedAvatar,
              {
                marginLeft: index > 0 ? -normalize(8) : 0,
                zIndex: displayUsers.length - index,
              }
            ]}
          />
        ))}
        {remainingCount > 0 && (
          <View style={[
            styles.remainingCount,
            {
              backgroundColor: theme.colors.primary,
              marginLeft: -normalize(8),
            }
          ]}>
            <Text style={styles.remainingCountText}>
              +{remainingCount}
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      <View style={[styles.indicator, { backgroundColor: theme.colors.surface }]}>
        {/* User Avatars */}
        <View style={styles.avatarContainer}>
          {renderUserAvatars()}
        </View>

        {/* Typing Content */}
        <View style={styles.contentContainer}>
          {/* Typing Text */}
          <Text style={[styles.typingText, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {getTypingText()}
          </Text>

          {/* Animated Dots */}
          {renderTypingDots()}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
    maxWidth: '80%',
  },
  avatarContainer: {
    marginRight: normalize(8),
  },
  singleAvatar: {
    // Avatar component handles styling
  },
  avatarStack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stackedAvatar: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  remainingCount: {
    width: normalize(20),
    height: normalize(20),
    borderRadius: normalize(10),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  remainingCountText: {
    color: '#FFFFFF',
    fontSize: normalize(8),
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  typingText: {
    fontSize: normalize(12),
    flex: 1,
    marginRight: normalize(8),
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: normalize(4),
    height: normalize(4),
    borderRadius: normalize(2),
    marginHorizontal: normalize(1),
  },
});

export default TikTokTypingIndicator;
