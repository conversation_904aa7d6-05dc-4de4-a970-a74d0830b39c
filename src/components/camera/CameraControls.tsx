import React from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { normalize } from '../../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');

interface CameraControlsProps {
  // Camera settings
  cameraType: 'front' | 'back';
  flashMode: 'off' | 'on' | 'auto';
  isRecording: boolean;

  // Speed and timer controls
  speed: number; // 0.3x, 0.5x, 1x, 2x, 3x
  timerEnabled: boolean;
  timerDuration: 3 | 10;

  // Beauty and enhancement
  beautyLevel: number; // 0-100

  // Handlers
  onFlipCamera: () => void;
  onFlashToggle: () => void;
  onSpeedChange: (speed: number) => void;
  onTimerToggle: () => void;
  onTimerDurationChange: (duration: 3 | 10) => void;
  onBeautyLevelChange: (level: number) => void;
  onGridToggle: () => void;
  onStabilizationToggle: () => void;
}

const CameraControls: React.FC<CameraControlsProps> = ({
  cameraType,
  flashMode,
  isRecording,
  speed,
  timerEnabled,
  timerDuration,
  beautyLevel,
  onFlipCamera,
  onFlashToggle,
  onSpeedChange,
  onTimerToggle,
  onTimerDurationChange,
  onBeautyLevelChange,
  onGridToggle,
  onStabilizationToggle,
}) => {
  const speedOptions = [0.3, 0.5, 1, 2, 3];
  const beautyLevels = [0, 25, 50, 75, 100];

  const getFlashIcon = () => {
    switch (flashMode) {
      case 'on': return 'flash';
      case 'auto': return 'flash-auto';
      default: return 'flash-off';
    }
  };

  const getSpeedText = (speedValue: number) => {
    return speedValue === 1 ? '1x' : `${speedValue}x`;
  };

  const getBeautyIcon = () => {
    if (beautyLevel === 0) {return 'face-retouching-off';}
    return 'face-retouching-natural';
  };

  return (
    <View style={styles.container}>
      {/* Top Row Controls */}
      <View style={styles.topRow}>
        {/* Flash Control */}
        <TouchableOpacity
          style={[styles.controlButton, flashMode !== 'off' && styles.controlButtonActive]}
          onPress={onFlashToggle}
          disabled={isRecording}
        >
          <Ionicons name={getFlashIcon()} size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>Flash</Text>
        </TouchableOpacity>

        {/* Timer Control */}
        <TouchableOpacity
          style={[styles.controlButton, timerEnabled && styles.controlButtonActive]}
          onPress={onTimerToggle}
          disabled={isRecording}
        >
          <MaterialIcons name="timer" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>
            {timerEnabled ? `${timerDuration}s` : 'Timer'}
          </Text>
        </TouchableOpacity>

        {/* Grid Toggle */}
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onGridToggle}
          disabled={isRecording}
        >
          <MaterialIcons name="grid-on" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>Grid</Text>
        </TouchableOpacity>

        {/* Stabilization */}
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onStabilizationToggle}
          disabled={isRecording}
        >
          <MaterialIcons name="video-stable" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>Stable</Text>
        </TouchableOpacity>
      </View>

      {/* Speed Controls */}
      <View style={styles.speedRow}>
        <Text style={styles.sectionTitle}>Speed</Text>
        <View style={styles.speedControls}>
          {speedOptions.map((speedOption) => (
            <TouchableOpacity
              key={speedOption}
              style={[
                styles.speedButton,
                speed === speedOption && styles.speedButtonActive,
              ]}
              onPress={() => onSpeedChange(speedOption)}
              disabled={isRecording}
            >
              <Text style={[
                styles.speedText,
                speed === speedOption && styles.speedTextActive,
              ]}>
                {getSpeedText(speedOption)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Beauty Controls (only for front camera) */}
      {cameraType === 'front' && (
        <View style={styles.beautyRow}>
          <Text style={styles.sectionTitle}>Beauty</Text>
          <View style={styles.beautyControls}>
            {beautyLevels.map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.beautyButton,
                  beautyLevel === level && styles.beautyButtonActive,
                ]}
                onPress={() => onBeautyLevelChange(level)}
                disabled={isRecording}
              >
                <MaterialIcons
                  name={level === 0 ? 'face-retouching-off' : 'face-retouching-natural'}
                  size={normalize(16)}
                  color={beautyLevel === level ? '#FF0050' : '#FFFFFF'}
                />
                <Text style={[
                  styles.beautyText,
                  beautyLevel === level && styles.beautyTextActive,
                ]}>
                  {level === 0 ? 'Off' : `${level}%`}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Additional Controls Row */}
      <View style={styles.additionalRow}>
        {/* Flip Camera */}
        <TouchableOpacity
          style={styles.flipButton}
          onPress={onFlipCamera}
          disabled={isRecording}
        >
          <Ionicons name="camera-reverse" size={normalize(24)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>Flip</Text>
        </TouchableOpacity>

        {/* Aspect Ratio */}
        <TouchableOpacity
          style={styles.controlButton}
          disabled={isRecording}
        >
          <MaterialIcons name="aspect-ratio" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>9:16</Text>
        </TouchableOpacity>

        {/* Resolution */}
        <TouchableOpacity
          style={styles.controlButton}
          disabled={isRecording}
        >
          <MaterialIcons name="hd" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>HD</Text>
        </TouchableOpacity>

        {/* Mic Control */}
        <TouchableOpacity
          style={styles.controlButton}
          disabled={isRecording}
        >
          <Ionicons name="mic" size={normalize(20)} color="#FFFFFF" />
          <Text style={styles.controlLabel}>Mic</Text>
        </TouchableOpacity>
      </View>

      {/* Timer Duration Selector (when timer is enabled) */}
      {timerEnabled && (
        <View style={styles.timerDurationRow}>
          <Text style={styles.sectionTitle}>Timer Duration</Text>
          <View style={styles.timerDurationControls}>
            {[3, 10].map((duration) => (
              <TouchableOpacity
                key={duration}
                style={[
                  styles.timerDurationButton,
                  timerDuration === duration && styles.timerDurationButtonActive,
                ]}
                onPress={() => onTimerDurationChange(duration as 3 | 10)}
                disabled={isRecording}
              >
                <Text style={[
                  styles.timerDurationText,
                  timerDuration === duration && styles.timerDurationTextActive,
                ]}>
                  {duration}s
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: normalize(12),
    padding: normalize(16),
    margin: normalize(16),
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: normalize(16),
  },
  controlButton: {
    alignItems: 'center',
    padding: normalize(8),
    borderRadius: normalize(8),
    minWidth: normalize(60),
  },
  controlButtonActive: {
    backgroundColor: 'rgba(255, 0, 80, 0.2)',
  },
  controlLabel: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    marginTop: normalize(4),
    textAlign: 'center',
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontSize: normalize(14),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  speedRow: {
    marginBottom: normalize(16),
  },
  speedControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  speedButton: {
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(16),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: normalize(40),
    alignItems: 'center',
  },
  speedButtonActive: {
    backgroundColor: '#FF0050',
  },
  speedText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: normalize(12),
    fontWeight: '600',
  },
  speedTextActive: {
    color: '#FFFFFF',
  },
  beautyRow: {
    marginBottom: normalize(16),
  },
  beautyControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  beautyButton: {
    alignItems: 'center',
    padding: normalize(8),
    borderRadius: normalize(8),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: normalize(50),
  },
  beautyButtonActive: {
    backgroundColor: 'rgba(255, 0, 80, 0.2)',
  },
  beautyText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: normalize(10),
    marginTop: normalize(4),
  },
  beautyTextActive: {
    color: '#FF0050',
    fontWeight: '600',
  },
  additionalRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: normalize(8),
  },
  flipButton: {
    alignItems: 'center',
    padding: normalize(8),
    borderRadius: normalize(8),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: normalize(60),
  },
  timerDurationRow: {
    marginTop: normalize(8),
    paddingTop: normalize(12),
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  timerDurationControls: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timerDurationButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(16),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: normalize(8),
    minWidth: normalize(50),
    alignItems: 'center',
  },
  timerDurationButtonActive: {
    backgroundColor: '#FF0050',
  },
  timerDurationText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: normalize(12),
    fontWeight: '600',
  },
  timerDurationTextActive: {
    color: '#FFFFFF',
  },
});

export default CameraControls;
