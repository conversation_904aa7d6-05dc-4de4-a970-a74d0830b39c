import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');

interface Filter {
  id: string;
  name: string;
  preview: string;
}

interface FilterSelectorProps {
  filters: Filter[];
  selectedFilter: string | null;
  onFilterSelect: (filterId: string) => void;
  onClose: () => void;
}

const FilterSelector: React.FC<FilterSelectorProps> = ({
  filters,
  selectedFilter,
  onFilterSelect,
  onClose,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Filters</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={normalize(24)} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      >
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterItem,
              selectedFilter === filter.id && styles.filterItemSelected,
            ]}
            onPress={() => onFilterSelect(filter.id)}
          >
            <View style={styles.filterPreview}>
              <Text style={styles.filterEmoji}>{filter.preview}</Text>
            </View>
            <Text style={[
              styles.filterName,
              selectedFilter === filter.id && styles.filterNameSelected,
            ]}>
              {filter.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderTopLeftRadius: normalize(20),
    borderTopRightRadius: normalize(20),
    paddingTop: normalize(20),
    paddingBottom: normalize(40),
    zIndex: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
    marginBottom: normalize(20),
  },
  title: {
    color: '#FFFFFF',
    fontSize: normalize(18),
    fontWeight: '600',
  },
  closeButton: {
    padding: normalize(4),
  },
  filtersContainer: {
    paddingHorizontal: normalize(20),
  },
  filterItem: {
    alignItems: 'center',
    marginRight: normalize(16),
    padding: normalize(8),
    borderRadius: normalize(12),
  },
  filterItemSelected: {
    backgroundColor: 'rgba(255, 0, 80, 0.2)',
  },
  filterPreview: {
    width: normalize(60),
    height: normalize(60),
    borderRadius: normalize(30),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalize(8),
  },
  filterEmoji: {
    fontSize: normalize(24),
  },
  filterName: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: normalize(12),
    textAlign: 'center',
  },
  filterNameSelected: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default FilterSelector;
