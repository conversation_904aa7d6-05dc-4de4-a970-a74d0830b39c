import React from 'react';
import { View, TouchableOpacity, StyleSheet, Image, Animated } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { MediaItem } from '../../hooks/camera/useMediaGallery';

interface BottomControlsProps {
  captureMode: 'video' | 'photo';
  isRecording: boolean;
  galleryItems: MediaItem[];
  recordButtonScale: Animated.Value;
  onCaptureModeChange: (mode: 'video' | 'photo') => void;
  onStartVideoRecording: () => void;
  onStopVideoRecording: () => void;
  onTakePhoto: () => void;
  onOpenGallery: () => void;
  onFlipCamera: () => void;
}

export const BottomControls: React.FC<BottomControlsProps> = ({
  captureMode,
  isRecording,
  galleryItems,
  recordButtonScale,
  onCaptureModeChange,
  onStartVideoRecording,
  onStopVideoRecording,
  onTakePhoto,
  onOpenGallery,
  onFlipCamera,
}) => {
  const handleRecordPress = () => {
    if (captureMode === 'video') {
      if (isRecording) {
        onStopVideoRecording();
      } else {
        onStartVideoRecording();
      }
    } else {
      onTakePhoto();
    }
  };

  return (
    <View style={styles.container}>
      {/* Mode Selector */}
      <View style={styles.modeSelector}>
        <TouchableOpacity 
          onPress={() => onCaptureModeChange('photo')}
          style={[styles.modeButton, captureMode === 'photo' && styles.modeButtonActive]}
        >
          <Text style={[styles.modeText, captureMode === 'photo' && styles.modeTextActive]}>
            Photo
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          onPress={() => onCaptureModeChange('video')}
          style={[styles.modeButton, captureMode === 'video' && styles.modeButtonActive]}
        >
          <Text style={[styles.modeText, captureMode === 'video' && styles.modeTextActive]}>
            Video
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Controls */}
      <View style={styles.mainControls}>
        <TouchableOpacity onPress={onOpenGallery} style={styles.galleryButton}>
          {galleryItems.length > 0 ? (
            <Image source={{ uri: galleryItems[0].uri }} style={styles.galleryPreview} />
          ) : (
            <MaterialIcons name="photo-library" size={24} color="#FFFFFF" />
          )}
        </TouchableOpacity>

        {/* TikTok-style Record Button */}
        <Animated.View style={[styles.recordButtonContainer, { transform: [{ scale: recordButtonScale }] }]}>
          <TouchableOpacity
            style={[
              styles.recordButton,
              isRecording && styles.recordButtonRecording,
              captureMode === 'photo' && styles.recordButtonPhoto,
            ]}
            onPress={handleRecordPress}
            activeOpacity={0.8}
          >
            {isRecording ? (
              <View style={styles.recordButtonInner} />
            ) : (
              <View style={[styles.recordButtonInner, captureMode === 'photo' && styles.recordButtonInnerPhoto]} />
            )}
          </TouchableOpacity>
        </Animated.View>

        <TouchableOpacity onPress={onFlipCamera} style={styles.flipButton}>
          <Ionicons name="camera-reverse" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  modeSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 25,
    padding: 4,
  },
  modeButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  modeButtonActive: {
    backgroundColor: '#FF0050',
  },
  modeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  modeTextActive: {
    fontWeight: '700',
  },
  mainControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 40,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  galleryPreview: {
    width: 46,
    height: 46,
    borderRadius: 6,
  },
  recordButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#FF0050',
  },
  recordButtonRecording: {
    backgroundColor: '#FF0050',
  },
  recordButtonPhoto: {
    borderRadius: 12,
  },
  recordButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FF0050',
  },
  recordButtonInnerPhoto: {
    borderRadius: 8,
  },
  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
