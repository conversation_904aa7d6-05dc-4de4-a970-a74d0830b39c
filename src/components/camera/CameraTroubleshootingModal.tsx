import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Platform,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getRecommendedCameraSettings } from '../../utils/cameraConfig';

interface CameraTroubleshootingModalProps {
  visible: boolean;
  onClose: () => void;
  errorType?: 'recording' | 'permission' | 'storage' | 'general';
}

export const CameraTroubleshootingModal: React.FC<CameraTroubleshootingModalProps> = ({
  visible,
  onClose,
  errorType = 'general',
}) => {
  const { theme } = useTheme();
  const recommendations = getRecommendedCameraSettings();

  const getTroubleshootingSteps = () => {
    const commonSteps = [
      'Close other camera apps',
      'Restart the app',
      'Check available storage space',
      'Ensure good lighting conditions',
    ];

    const androidSteps = [
      'Clear app cache in device settings',
      'Restart your device',
      'Update the app to the latest version',
      'Check if other apps can record video',
    ];

    const iosSteps = [
      'Check camera permissions in Settings',
      'Restart your device',
      'Update the app to the latest version',
    ];

    switch (errorType) {
      case 'recording':
        return [
          ...commonSteps,
          'Try recording shorter videos',
          'Use lower quality settings',
          ...(Platform.OS === 'android' ? androidSteps : iosSteps),
        ];
      case 'permission':
        return [
          'Go to device Settings',
          'Find this app in the app list',
          'Enable Camera and Microphone permissions',
          'Restart the app',
        ];
      case 'storage':
        return [
          'Delete unnecessary photos and videos',
          'Clear app cache',
          'Move files to cloud storage',
          'Free up at least 1GB of space',
        ];
      default:
        return [...commonSteps, ...(Platform.OS === 'android' ? androidSteps : iosSteps)];
    }
  };

  const getErrorTitle = () => {
    switch (errorType) {
      case 'recording':
        return 'Recording Issues';
      case 'permission':
        return 'Permission Issues';
      case 'storage':
        return 'Storage Issues';
      default:
        return 'Camera Troubleshooting';
    }
  };

  const getErrorDescription = () => {
    switch (errorType) {
      case 'recording':
        return 'Having trouble recording videos? Try these solutions:';
      case 'permission':
        return 'Camera or microphone access denied. Follow these steps:';
      case 'storage':
        return 'Not enough storage space for recording. Here\'s how to fix it:';
      default:
        return 'Experiencing camera issues? Here are some solutions:';
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              {getErrorTitle()}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              {getErrorDescription()}
            </Text>

            {/* Troubleshooting Steps */}
            <View style={styles.stepsContainer}>
              {getTroubleshootingSteps().map((step, index) => (
                <View key={index} style={styles.stepItem}>
                  <View style={[styles.stepNumber, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.stepNumberText}>{index + 1}</Text>
                  </View>
                  <Text style={[styles.stepText, { color: theme.colors.text }]}>
                    {step}
                  </Text>
                </View>
              ))}
            </View>

            {/* Device-specific recommendations */}
            <View style={styles.recommendationsContainer}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Recommended Settings
              </Text>
              <View style={[styles.recommendationCard, { backgroundColor: theme.colors.background }]}>
                <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                  Quality: {recommendations.quality}
                </Text>
                <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                  FPS: {recommendations.fps}
                </Text>
                <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                  Stabilization: {recommendations.stabilization ? 'Enabled' : 'Disabled'}
                </Text>
              </View>
            </View>

            {/* Additional Tips */}
            <View style={styles.tipsContainer}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Additional Tips
              </Text>
              {recommendations.tips.map((tip, index) => (
                <View key={index} style={styles.tipItem}>
                  <Ionicons name="bulb-outline" size={16} color={theme.colors.primary} />
                  <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                    {tip}
                  </Text>
                </View>
              ))}
            </View>
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.okButton, { backgroundColor: theme.colors.primary }]}
              onPress={onClose}
            >
              <Text style={styles.okButtonText}>Got it</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    maxHeight: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    paddingHorizontal: 20,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 20,
  },
  stepsContainer: {
    marginBottom: 24,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  stepText: {
    flex: 1,
    fontSize: 15,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  recommendationsContainer: {
    marginBottom: 24,
  },
  recommendationCard: {
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  recommendationText: {
    fontSize: 14,
  },
  tipsContainer: {
    marginBottom: 20,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    gap: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 18,
  },
  footer: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  okButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  okButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
