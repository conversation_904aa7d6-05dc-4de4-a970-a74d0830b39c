import React from 'react';
import { View, StyleSheet } from 'react-native';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

interface TimerDisplayProps {
  time: number; // in seconds
  maxTime: number; // in seconds
}

const TimerDisplay: React.FC<TimerDisplayProps> = ({ time, maxTime }) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = time / maxTime;
  const progressWidth = progress * 100;

  return (
    <View style={styles.container}>
      <View style={styles.timerContainer}>
        <View style={styles.recordingDot} />
        <Text style={styles.timerText}>{formatTime(time)}</Text>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground} />
        <View style={[styles.progressFill, { width: `${progressWidth}%` }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: normalize(120),
    left: normalize(20),
    right: normalize(20),
    alignItems: 'center',
    zIndex: 15,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
    borderRadius: normalize(16),
    marginBottom: normalize(8),
  },
  recordingDot: {
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(4),
    backgroundColor: '#FF0050',
    marginRight: normalize(8),
  },
  timerText: {
    color: '#FFFFFF',
    fontSize: normalize(14),
    fontWeight: '600',
    fontFamily: 'monospace',
  },
  progressContainer: {
    width: '100%',
    height: normalize(4),
    position: 'relative',
  },
  progressBackground: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: normalize(2),
  },
  progressFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: '#FF0050',
    borderRadius: normalize(2),
  },
});

export default TimerDisplay;
