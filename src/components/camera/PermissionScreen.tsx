import React from 'react';
import { View, TouchableOpacity, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface PermissionScreenProps {
  onRequestPermissions: () => void;
}

export const PermissionScreen: React.FC<PermissionScreenProps> = ({
  onRequestPermissions,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <View style={styles.content}>
        <Ionicons name="camera-outline" size={80} color="#FFFFFF" />
        <Text style={styles.title}>Camera Access Required</Text>
        <Text style={styles.text}>
          To create amazing videos and photos, we need access to your camera and microphone.
        </Text>
        <TouchableOpacity 
          style={styles.button}
          onPress={onRequestPermissions}
        >
          <Text style={styles.buttonText}>Grant Access</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 20,
    marginBottom: 10,
  },
  text: {
    fontSize: 16,
    color: '#CCCCCC',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  button: {
    backgroundColor: '#FF0050',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
