import React from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import Text from '../common/Text';

interface RecordingIndicatorProps {
  isRecording: boolean;
  recordingTime: number;
  recordingProgress: Animated.Value;
}

export const RecordingIndicator: React.FC<RecordingIndicatorProps> = ({
  isRecording,
  recordingTime,
  recordingProgress,
}) => {
  if (!isRecording) return null;

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <>
      {/* Recording Indicator */}
      <View style={styles.indicator}>
        <View style={styles.recordingDot} />
        <Text style={styles.recordingTime}>
          {formatTime(recordingTime)}
        </Text>
      </View>

      {/* Progress Bar */}
      <Animated.View 
        style={[
          styles.progressBar, 
          { 
            width: recordingProgress.interpolate({
              inputRange: [0, 1],
              outputRange: ['0%', '100%'],
            }) 
          }
        ]} 
      />
    </>
  );
};

const styles = StyleSheet.create({
  indicator: {
    position: 'absolute',
    top: 100,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    zIndex: 10,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF0050',
    marginRight: 8,
  },
  recordingTime: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 4,
    backgroundColor: '#FF0050',
    zIndex: 5,
  },
});
