import React from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import Text from '../common/Text';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface Effect {
  id: string;
  name: string;
  icon: string;
}

interface EffectsPanelProps {
  visible: boolean;
  effects: Effect[];
  selectedEffect: string | null;
  onEffectSelect: (effectId: string) => void;
}

export const EffectsPanel: React.FC<EffectsPanelProps> = ({
  visible,
  effects,
  selectedEffect,
  onEffectSelect,
}) => {
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scroll}>
        {effects.map((effect) => (
          <TouchableOpacity
            key={effect.id}
            style={[styles.effectButton, selectedEffect === effect.id && styles.effectButtonActive]}
            onPress={() => onEffectSelect(effect.id)}
          >
            <MaterialIcons name={effect.icon as any} size={24} color="#FFFFFF" />
            <Text style={styles.effectName}>{effect.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 150,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  scroll: {
    paddingHorizontal: 20,
  },
  effectButton: {
    alignItems: 'center',
    marginHorizontal: 10,
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  effectButtonActive: {
    backgroundColor: 'rgba(255, 0, 80, 0.8)',
  },
  effectName: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
});
