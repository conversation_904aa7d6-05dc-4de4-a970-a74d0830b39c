import React from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import Text from '../common/Text';

interface Filter {
  id: string;
  name: string;
  preview: string;
}

interface FilterPanelProps {
  visible: boolean;
  filters: Filter[];
  selectedFilter: string | null;
  onFilterSelect: (filterId: string) => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  visible,
  filters,
  selectedFilter,
  onFilterSelect,
}) => {
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scroll}>
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[styles.filterButton, selectedFilter === filter.id && styles.filterButtonActive]}
            onPress={() => onFilterSelect(filter.id)}
          >
            <Text style={styles.filterEmoji}>{filter.preview}</Text>
            <Text style={styles.filterName}>{filter.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 150,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  scroll: {
    paddingHorizontal: 20,
  },
  filterButton: {
    alignItems: 'center',
    marginHorizontal: 10,
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  filterButtonActive: {
    backgroundColor: 'rgba(255, 0, 80, 0.8)',
  },
  filterEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  filterName: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
  },
});
