import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface SideControlsProps {
  showEffects: boolean;
  showFilters: boolean;
  onToggleEffects: () => void;
  onToggleFilters: () => void;
  onFlipCamera: () => void;
  onOpenGallery: () => void;
}

export const SideControls: React.FC<SideControlsProps> = ({
  showEffects,
  showFilters,
  onToggleEffects,
  onToggleFilters,
  onFlipCamera,
  onOpenGallery,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, showEffects && styles.activeButton]}
        onPress={onToggleEffects}
      >
        <MaterialIcons name="auto-awesome" size={24} color="#FFFFFF" />
        <Text style={styles.buttonText}>Effects</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, showFilters && styles.activeButton]}
        onPress={onToggleFilters}
      >
        <MaterialIcons name="tune" size={24} color="#FFFFFF" />
        <Text style={styles.buttonText}>Filters</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onFlipCamera} style={styles.button}>
        <Ionicons name="camera-reverse" size={24} color="#FFFFFF" />
        <Text style={styles.buttonText}>Flip</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onOpenGallery} style={styles.button}>
        <MaterialIcons name="photo-library" size={24} color="#FFFFFF" />
        <Text style={styles.buttonText}>Gallery</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -100 }],
    alignItems: 'center',
    gap: 24,
    zIndex: 10,
  },
  button: {
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    minWidth: 60,
  },
  activeButton: {
    backgroundColor: 'rgba(255, 0, 80, 0.8)',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
});
