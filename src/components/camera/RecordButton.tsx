import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { normalize } from '../../utils/responsive';

interface RecordButtonProps {
  isRecording: boolean;
  onStartRecording: () => void;
  onStopRecording: () => void;
  progress: number; // 0 to 1
}

const RecordButton: React.FC<RecordButtonProps> = ({
  isRecording,
  onStartRecording,
  onStopRecording,
  progress,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isRecording) {
      // Start pulsing animation
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
      };
    } else {
      pulseAnim.setValue(1);
    }
  }, [isRecording, pulseAnim]);

  const handlePress = () => {
    // Scale animation on press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    if (isRecording) {
      onStopRecording();
    } else {
      onStartRecording();
    }
  };

  // Create progress ring using multiple small segments
  const createProgressSegments = () => {
    const segments = [];
    const totalSegments = 60; // Number of segments for smooth progress
    const segmentAngle = (2 * Math.PI) / totalSegments;
    const radius = normalize(38);
    const centerX = normalize(40);
    const centerY = normalize(40);

    for (let i = 0; i < totalSegments; i++) {
      const angle = (i * segmentAngle) - (Math.PI / 2); // Start from top
      const isActive = i < (progress * totalSegments);

      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      segments.push(
        <View
          key={i}
          style={[
            styles.progressSegment,
            {
              left: x - normalize(1.5),
              top: y - normalize(1.5),
              backgroundColor: isActive ? '#FF0050' : 'rgba(255, 255, 255, 0.3)',
            },
          ]}
        />
      );
    }
    return segments;
  };

  return (
    <View style={styles.container}>
      {/* Progress Ring */}
      {isRecording && (
        <View style={styles.progressRing}>
          {createProgressSegments()}
        </View>
      )}

      {/* Record Button */}
      <Animated.View
        style={[
          styles.recordButtonContainer,
          {
            transform: [
              { scale: scaleAnim },
              { scale: isRecording ? pulseAnim : 1 },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.recordButton,
            isRecording && styles.recordButtonActive,
          ]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          <View
            style={[
              styles.recordButtonInner,
              isRecording && styles.recordButtonInnerActive,
            ]}
          />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressRing: {
    position: 'absolute',
    width: normalize(80),
    height: normalize(80),
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressSegment: {
    position: 'absolute',
    width: normalize(3),
    height: normalize(3),
    borderRadius: normalize(1.5),
  },
  recordButtonContainer: {
    width: normalize(70),
    height: normalize(70),
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordButton: {
    width: normalize(70),
    height: normalize(70),
    borderRadius: normalize(35),
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: '#FFFFFF',
  },
  recordButtonActive: {
    backgroundColor: 'rgba(255, 0, 80, 0.3)',
    borderColor: '#FF0050',
  },
  recordButtonInner: {
    width: normalize(50),
    height: normalize(50),
    borderRadius: normalize(25),
    backgroundColor: '#FF0050',
  },
  recordButtonInnerActive: {
    borderRadius: normalize(8),
    width: normalize(24),
    height: normalize(24),
  },
});

export default RecordButton;
