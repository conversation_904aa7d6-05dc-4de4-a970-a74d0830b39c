import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface TopControlsProps {
  flashMode: 'off' | 'on' | 'auto';
  timerEnabled: boolean;
  speed: number;
  onClose: () => void;
  onFlashToggle: () => void;
  onTimerToggle: () => void;
  onSpeedChange: () => void;
  onSelectFromLibrary: () => void;
}

export const TopControls: React.FC<TopControlsProps> = ({
  flashMode,
  timerEnabled,
  speed,
  onClose,
  onFlashToggle,
  onTimerToggle,
  onSpeedChange,
  onSelectFromLibrary,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onClose} style={styles.button}>
        <Ionicons name="close" size={28} color="#FFFFFF" />
      </TouchableOpacity>
      
      <View style={styles.centerControls}>
        <TouchableOpacity 
          onPress={onFlashToggle}
          style={[styles.button, flashMode === 'on' && styles.activeButton]}
        >
          <Ionicons 
            name={flashMode === 'on' ? 'flash' : 'flash-off'} 
            size={24} 
            color="#FFFFFF" 
          />
        </TouchableOpacity>
        
        <TouchableOpacity onPress={onTimerToggle} style={styles.button}>
          <MaterialIcons 
            name="timer" 
            size={24} 
            color={timerEnabled ? theme.colors.primary : '#FFFFFF'} 
          />
        </TouchableOpacity>
        
        <TouchableOpacity onPress={onSpeedChange} style={styles.button}>
          <Text style={styles.speedText}>{speed}x</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity onPress={onSelectFromLibrary} style={styles.button}>
        <MaterialIcons name="photo-library" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 10,
  },
  button: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: 'rgba(255, 0, 80, 0.8)',
  },
  centerControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  speedText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});
