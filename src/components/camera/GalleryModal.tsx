import React from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, Image, Animated } from 'react-native';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { MediaItem } from '../../hooks/camera/useMediaGallery';

interface GalleryModalProps {
  visible: boolean;
  galleryItems: MediaItem[];
  gallerySlideAnim: Animated.Value;
  onClose: () => void;
  onSelectItem?: (item: MediaItem) => void;
}

export const GalleryModal: React.FC<GalleryModalProps> = ({
  visible,
  galleryItems,
  gallerySlideAnim,
  onClose,
  onSelectItem,
}) => {
  if (!visible) return null;

  return (
    <Animated.View style={[styles.container, { transform: [{ translateY: gallerySlideAnim }] }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Gallery</Text>
        <TouchableOpacity onPress={onClose}>
          <Ionicons name="close" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content} numColumns={2}>
        <View style={styles.grid}>
          {galleryItems.map((item) => (
            <TouchableOpacity 
              key={item.id} 
              style={styles.item}
              onPress={() => onSelectItem?.(item)}
            >
              <Image source={{ uri: item.uri }} style={styles.itemImage} />
              {item.type === 'video' && (
                <View style={styles.itemOverlay}>
                  <Ionicons name="play" size={20} color="#FFFFFF" />
                  {item.duration && (
                    <Text style={styles.duration}>
                      {Math.floor(item.duration / 60)}:{(item.duration % 60).toString().padStart(2, '0')}
                    </Text>
                  )}
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    zIndex: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  item: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  itemImage: {
    width: '100%',
    height: '100%',
  },
  itemOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  duration: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
});
