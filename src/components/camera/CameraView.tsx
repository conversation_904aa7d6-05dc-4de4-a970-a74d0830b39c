import React, {useEffect, useRef, useState} from 'react';
import {View, StyleSheet, Alert, TouchableOpacity} from 'react-native';
import {
  Camera,
  useCameraDevices,
  VideoFile,
  useCameraPermission,
  useMicrophonePermission,
} from 'react-native-vision-camera';
import RNFS from 'react-native-fs';

import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import logger from '../../utils/logger';

interface CameraViewProps {
  cameraType: 'front' | 'back';
  flashMode: 'off' | 'on' | 'auto';
  selectedFilter?: string | null;
  selectedEffect?: string | null;
  isRecording: boolean;
  onVideoRecorded?: (videoUri: string) => void;
  shouldStartRecording?: boolean;
  shouldStopRecording?: boolean;
  onRecordingStarted?: () => void;
}

const CameraView: React.FC<CameraViewProps> = ({
  cameraType,
  flashMode,
  selectedFilter,
  selectedEffect,
  isRecording,
  onVideoRecorded,
  shouldStartRecording,
  shouldStopRecording,
  onRecordingStarted,
}) => {
  const camera = useRef<Camera>(null);
  const recordingDir = useRef<string | null>(null);
  const devices = useCameraDevices();
  const device = devices.find(d => d.position === cameraType);


  // Use hooks at the top level
  const {
    hasPermission: hasCameraPermission,
    requestPermission: requestCameraPermission,
  } = useCameraPermission();
  const {
    hasPermission: hasMicPermission,
    requestPermission: requestMicPermission,
  } = useMicrophonePermission();

  const [recording, setRecording] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  useEffect(() => {
    const requestPermissions = async () => {
      try {
        let cameraGranted = hasCameraPermission;
        let micGranted = hasMicPermission;

        if (!cameraGranted) {
          cameraGranted = await requestCameraPermission();
          if (!cameraGranted) {
            Alert.alert(
              'Camera Permission Denied',
              'Please allow camera access in your device settings.',
            );
            return;
          }
        }

        if (!micGranted) {
          micGranted = await requestMicPermission();
          if (!micGranted) {
            Alert.alert(
              'Microphone Permission Denied',
              'Please allow microphone access in your device settings.',
            );
            return;
          }
        }

        if (!device) {
          Alert.alert(
            'No Camera Found',
            'Please ensure your device has a camera.',
          );
          return;
        }

        setPermissionsGranted(cameraGranted && micGranted);
      } catch (error) {
        logger.error('Error requesting permissions:', error);
        Alert.alert('Permission Error', 'Failed to request permissions');
      }
    };

    requestPermissions();
  }, [
    hasCameraPermission,
    hasMicPermission,
    device,
    requestCameraPermission,
    requestMicPermission,
  ]);

  useEffect(() => {
    if (shouldStartRecording && !recording && permissionsGranted) {
      startRecording();
    }
  }, [shouldStartRecording, recording, permissionsGranted]);

  useEffect(() => {
    if (shouldStopRecording && recording) {
      stopRecording();
    }
  }, [shouldStopRecording, recording]);

  const startRecording = async () => {
    if (!camera.current || !permissionsGranted) {
      Alert.alert('Error', 'Camera not ready or permissions not granted');
      return;
    }

    try {
      onRecordingStarted?.();
      setRecording(true);

      const dir = `${RNFS.TemporaryDirectoryPath}/recording_${Date.now()}`;
      try {
        await RNFS.mkdir(dir);
        recordingDir.current = dir;
      } catch (e) {
        logger.error('Failed to create recording directory:', e);
      }

      await camera.current.startRecording({
        flash: flashMode === 'on' ? 'on' : 'off',
        fileType: 'mp4',
        videoCodec: 'h264',
        path: recordingDir.current ?? undefined,
        onRecordingFinished: (video: VideoFile) => {
          setRecording(false);
          recordingDir.current = null;
          onVideoRecorded?.(video.path);
          Alert.alert(
            'Video Recorded!',
            'Your video has been recorded successfully.',
          );
        },
        onRecordingError: async error => {
          setRecording(false);
          logger.error('Recording error:', error);
          Alert.alert(
            'Recording Error',
            error.message || 'Unknown recording error',
          );
          if (recordingDir.current) {
            try {
              await RNFS.unlink(recordingDir.current);
            } catch (cleanupError) {
              logger.error('Cleanup error:', cleanupError);
            }
            recordingDir.current = null;
          }
        },
      });
    } catch (error: any) {
      setRecording(false);
      logger.error('Start recording error:', error);
      Alert.alert('Error', error.message || 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (camera.current && recording) {
      try {
        await camera.current.stopRecording();
      } catch (error: any) {
        logger.error('Stop recording error:', error);
        Alert.alert('Error', 'Failed to stop recording');
      }
    }
  };

  // Show loading state while permissions are being requested
  if (!permissionsGranted || !device) {
    return (
      <View style={styles.container}>
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            {!device
              ? 'No camera found on this device'
              : 'Requesting camera and microphone permissions...'}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={camera}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        video={true}
        audio={true}
        // Add format for better compatibility
        format={
          device.formats.find(
            (f: {videoWidth: number}) => f.videoWidth >= 1920,
          ) || device.formats[0]
        }
      />

      {/* Overlay UI, buttons, etc. */}
      <View style={styles.controls}>
        {!recording ? (
          <TouchableOpacity
            onPress={startRecording}
            style={styles.recordButton}>
            <Ionicons name="radio-button-on" size={48} color="red" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={stopRecording} style={styles.recordButton}>
            <Ionicons name="stop-circle" size={48} color="white" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  messageText: {
    color: '#fff',
    textAlign: 'center',
    fontSize: 16,
  },
  controls: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordButton: {
    padding: 16,
    borderRadius: 32,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
});

export default CameraView;
