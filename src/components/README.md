# 🧩 Components Library

This directory contains all reusable UI components for the TS1 application. Components are organized by domain and functionality, following a modular architecture that promotes reusability and maintainability.

## 📁 Directory Structure

```
src/components/
├── common/                 # Generic reusable components
│   ├── Button.tsx         # Primary button component
│   ├── Input.tsx          # Form input component
│   ├── Text.tsx           # Themed text component
│   ├── LoadingSpinner.tsx # Loading indicator
│   └── SafeAreaWrapper.tsx # Safe area container
├── messages/              # Messaging domain components
│   ├── ChatHeader.tsx     # Chat screen header
│   ├── MessageItem.tsx    # Individual message display
│   ├── ChatInput.tsx      # Message input component
│   ├── AudioRecordingModal.tsx # Audio recording interface
│   ├── VideoRecordingModal.tsx # Video recording interface
│   └── index.ts           # Export barrel
├── video/                 # Video domain components
│   ├── VideoPlayer.tsx    # Video playback component
│   ├── VideoCard.tsx      # Video display card
│   ├── VideoMetadataForm.tsx # Video metadata form
│   └── form/              # Video form sub-components
│       ├── VideoFormField.tsx
│       ├── VideoTypeSelector.tsx
│       ├── TagsManager.tsx
│       ├── PrivacySelector.tsx
│       └── PermissionsToggle.tsx
├── phone/                 # Phone verification components
│   ├── CountrySelector.tsx
│   ├── PhoneNumberInput.tsx
│   └── VerificationCodeInput.tsx
├── camera/                # Camera domain components
│   ├── CameraControls.tsx
│   └── PhotoToVideoEditor.tsx
├── feed/                  # Feed domain components
│   ├── FeedItem.tsx
│   └── InteractionPanel.tsx
└── README.md              # This file
```

## 🎯 Component Categories

### **1. Common Components (`/common/`)**
Generic, reusable components that can be used throughout the application.

**Characteristics:**
- No domain-specific logic
- Highly reusable
- Theme-aware
- Well-documented props

**Examples:**
- `Button` - Primary action button with variants
- `Input` - Form input with validation support
- `Text` - Themed text component
- `LoadingSpinner` - Loading indicator

### **2. Domain Components**
Components specific to particular features or domains of the application.

**Characteristics:**
- Domain-specific functionality
- May use custom hooks
- Composed of common components
- Clear single responsibility

**Examples:**
- `MessageItem` - Displays chat messages
- `VideoCard` - Shows video information
- `CountrySelector` - Phone country selection

### **3. Compound Components**
Complex components broken down into smaller, focused sub-components.

**Characteristics:**
- Main component + sub-components
- Clear separation of concerns
- Easier to test and maintain
- Follows compound component pattern

**Examples:**
- `VideoMetadataForm` with form sub-components
- `PhoneNumberInput` with country selector

## 📋 Component Development Standards

### **1. Component Template**
```typescript
import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

/**
 * ComponentName - Brief description of the component's purpose
 * 
 * Detailed description of what this component does, its main features,
 * and any important usage notes.
 * 
 * @param prop1 - Description of required prop
 * @param prop2 - Description of optional prop with default value
 * @param onAction - Callback function description
 * 
 * @example
 * ```tsx
 * <ComponentName
 *   prop1="required value"
 *   prop2={optionalValue}
 *   onAction={(data) => handleAction(data)}
 * />
 * ```
 */

interface ComponentNameProps {
  /** Required prop description */
  prop1: string;
  
  /** Optional prop with default value */
  prop2?: number;
  
  /** Callback fired when action occurs */
  onAction: (data: ActionData) => void;
  
  /** Optional style override */
  style?: ViewStyle;
  
  /** Whether component is disabled */
  disabled?: boolean;
}

const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2 = 0,
  onAction,
  style,
  disabled = false,
}) => {
  const { theme } = useTheme();
  
  // Local state
  const [isPressed, setIsPressed] = useState(false);
  
  // Event handlers
  const handlePress = useCallback(() => {
    if (!disabled) {
      onAction({ prop1, prop2 });
    }
  }, [prop1, prop2, onAction, disabled]);
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.surface,
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Text style={[styles.text, { color: theme.colors.text }]}>
        {prop1}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: normalize(16),
    borderRadius: normalize(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
});

export default ComponentName;
```

### **2. Props Interface Guidelines**
```typescript
interface ComponentProps {
  // Required props first
  /** Primary data for the component */
  data: DataType;
  
  /** Callback for primary action */
  onAction: (data: DataType) => void;
  
  // Optional props with defaults
  /** Display variant (default: 'primary') */
  variant?: 'primary' | 'secondary' | 'outline';
  
  /** Size variant (default: 'medium') */
  size?: 'small' | 'medium' | 'large';
  
  /** Whether component is disabled (default: false) */
  disabled?: boolean;
  
  /** Whether to show loading state (default: false) */
  loading?: boolean;
  
  // Style overrides last
  /** Optional style override for container */
  style?: ViewStyle;
  
  /** Optional style override for text */
  textStyle?: TextStyle;
}
```

### **3. Styling Guidelines**
```typescript
// Use theme system
const { theme } = useTheme();

// Use normalize for responsive design
const styles = StyleSheet.create({
  container: {
    padding: normalize(16),
    borderRadius: normalize(8),
  },
  text: {
    fontSize: normalize(16),
    lineHeight: normalize(24),
  },
});

// Apply theme colors dynamically
<View style={[
  styles.container,
  { backgroundColor: theme.colors.surface }
]}>
  <Text style={[
    styles.text,
    { color: theme.colors.text }
  ]}>
    Content
  </Text>
</View>
```

## 🎨 Design System Integration

### **1. Theme Usage**
```typescript
// Always use theme colors
const { theme } = useTheme();

// Common theme properties
theme.colors.primary      // Primary brand color
theme.colors.secondary    // Secondary brand color
theme.colors.background   // Screen background
theme.colors.surface      // Card/component background
theme.colors.text         // Primary text color
theme.colors.textSecondary // Secondary text color
theme.colors.border       // Border color
theme.colors.error        // Error state color
theme.colors.success      // Success state color
```

### **2. Responsive Design**
```typescript
import { normalize } from '../../utils/responsive';

// Use normalize for all dimensions
const styles = StyleSheet.create({
  container: {
    padding: normalize(16),        // Responsive padding
    margin: normalize(8),          // Responsive margin
    borderRadius: normalize(8),    // Responsive border radius
  },
  text: {
    fontSize: normalize(16),       // Responsive font size
    lineHeight: normalize(24),     // Responsive line height
  },
});
```

### **3. Accessibility**
```typescript
// Add accessibility props
<TouchableOpacity
  accessible={true}
  accessibilityLabel="Send message"
  accessibilityHint="Sends the typed message to the conversation"
  accessibilityRole="button"
  onPress={handleSend}
>
  <Text>Send</Text>
</TouchableOpacity>

// Use semantic colors for states
<Text style={{
  color: isError ? theme.colors.error : theme.colors.text
}}>
  {message}
</Text>
```

## 🧪 Component Testing

### **1. Testing Template**
```typescript
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ThemeProvider } from '../../contexts/ThemeContext';
import ComponentName from '../ComponentName';

// Test wrapper with providers
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('ComponentName', () => {
  const defaultProps = {
    prop1: 'test value',
    onAction: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByText } = renderWithProviders(
      <ComponentName {...defaultProps} />
    );
    
    expect(getByText('test value')).toBeTruthy();
  });

  it('calls onAction when pressed', () => {
    const mockOnAction = jest.fn();
    const { getByText } = renderWithProviders(
      <ComponentName {...defaultProps} onAction={mockOnAction} />
    );
    
    fireEvent.press(getByText('test value'));
    expect(mockOnAction).toHaveBeenCalledWith({
      prop1: 'test value',
      prop2: 0,
    });
  });

  it('handles disabled state correctly', () => {
    const mockOnAction = jest.fn();
    const { getByText } = renderWithProviders(
      <ComponentName {...defaultProps} onAction={mockOnAction} disabled />
    );
    
    fireEvent.press(getByText('test value'));
    expect(mockOnAction).not.toHaveBeenCalled();
  });

  it('applies custom styles correctly', () => {
    const customStyle = { backgroundColor: 'red' };
    const { getByTestId } = renderWithProviders(
      <ComponentName {...defaultProps} style={customStyle} testID="component" />
    );
    
    const component = getByTestId('component');
    expect(component.props.style).toContainEqual(customStyle);
  });
});
```

### **2. Snapshot Testing**
```typescript
import renderer from 'react-test-renderer';

describe('ComponentName Snapshots', () => {
  it('matches snapshot with default props', () => {
    const tree = renderer
      .create(
        <ThemeProvider>
          <ComponentName {...defaultProps} />
        </ThemeProvider>
      )
      .toJSON();
    
    expect(tree).toMatchSnapshot();
  });

  it('matches snapshot with all props', () => {
    const tree = renderer
      .create(
        <ThemeProvider>
          <ComponentName
            {...defaultProps}
            prop2={10}
            disabled={true}
            variant="secondary"
          />
        </ThemeProvider>
      )
      .toJSON();
    
    expect(tree).toMatchSnapshot();
  });
});
```

## 📖 Usage Examples

### **Common Component Usage**
```typescript
// Button variations
<Button title="Primary Action" onPress={handlePrimary} />
<Button title="Secondary" variant="outline" onPress={handleSecondary} />
<Button title="Loading..." loading={isLoading} disabled />

// Input with validation
<Input
  label="Email"
  value={email}
  onChangeText={setEmail}
  placeholder="Enter your email"
  keyboardType="email-address"
  error={emailError}
/>

// Themed text
<Text variant="heading">Main Title</Text>
<Text variant="body" color="secondary">Description text</Text>
```

### **Domain Component Usage**
```typescript
// Message display
<MessageItem
  message={messageData}
  isCurrentUser={message.sender_id === currentUser.id}
  onPress={() => handleMessagePress(message)}
/>

// Video card
<VideoCard
  video={videoData}
  onPress={() => navigation.navigate('VideoDetail', { videoId: video.id })}
  onLike={() => handleLike(video.id)}
/>

// Phone input
<PhoneNumberInput
  selectedCountry={country}
  phoneNumber={phone}
  onCountryChange={setCountry}
  onPhoneNumberChange={setPhone}
/>
```

## 🚨 Common Anti-Patterns

### **❌ Don't: Create Monolithic Components**
```typescript
// Bad: 500+ line component with multiple responsibilities
const MassiveComponent = () => {
  // API calls + business logic + complex UI + styling
  // 500+ lines of mixed concerns
};
```

### **✅ Do: Break Into Focused Components**
```typescript
// Good: Focused components with single responsibilities
const UserProfile = () => (
  <ScrollView>
    <ProfileHeader user={user} />
    <ProfileStats stats={stats} />
    <ProfileActions onEdit={handleEdit} onShare={handleShare} />
  </ScrollView>
);
```

### **❌ Don't: Ignore Theme System**
```typescript
// Bad: Hardcoded colors and sizes
<View style={{ backgroundColor: '#FFFFFF', padding: 16 }}>
  <Text style={{ color: '#000000', fontSize: 16 }}>Content</Text>
</View>
```

### **✅ Do: Use Theme and Responsive Design**
```typescript
// Good: Theme-aware and responsive
const { theme } = useTheme();

<View style={[
  styles.container,
  { backgroundColor: theme.colors.surface }
]}>
  <Text style={[styles.text, { color: theme.colors.text }]}>
    Content
  </Text>
</View>

const styles = StyleSheet.create({
  container: { padding: normalize(16) },
  text: { fontSize: normalize(16) },
});
```

## 📚 Further Reading

- [Component Guidelines](../../docs/COMPONENTS.md) - Detailed component development standards
- [React Native Components](https://reactnative.dev/docs/components-and-apis)
- [Testing React Native Components](https://callstack.github.io/react-native-testing-library/)
- [Accessibility in React Native](https://reactnative.dev/docs/accessibility)

Remember: Components should be focused, reusable, and well-tested. Each component should have a clear purpose and provide a clean API for its consumers.
