import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

interface ProfileInfoItemProps {
  label: string;
  value: string;
  placeholder?: string;
  onPress?: () => void;
  showChevron?: boolean;
  isEditable?: boolean;
  customContent?: React.ReactNode;
}

const ProfileInfoItem: React.FC<ProfileInfoItemProps> = ({
  label,
  value,
  placeholder = 'Not set',
  onPress,
  showChevron = true,
  isEditable = true,
  customContent,
}) => {
  const { theme } = useTheme();

  const Component = isEditable && onPress ? TouchableOpacity : View;

  return (
    <Component
      style={[styles.infoItem, { borderBottomColor: theme.colors.border }]}
      onPress={onPress}
      disabled={!isEditable}
    >
      <View style={styles.infoLabelContainer}>
        <Text style={[styles.infoLabel, { color: theme.colors.text }]}>
          {label}
        </Text>
      </View>
      
      <View style={styles.infoValueContainer}>
        {customContent || (
          <Text 
            style={[
              styles.infoValue, 
              { 
                color: value ? theme.colors.text : theme.colors.textSecondary 
              }
            ]}
            numberOfLines={2}
          >
            {value || placeholder}
          </Text>
        )}
        
        {isEditable && showChevron && onPress && (
          <Ionicons 
            name="chevron-forward" 
            size={20} 
            color={theme.colors.textSecondary} 
          />
        )}
      </View>
    </Component>
  );
};

const styles = StyleSheet.create({
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(16),
    paddingHorizontal: normalize(16),
    borderBottomWidth: 1,
    minHeight: normalize(60),
  },
  infoLabelContainer: {
    flex: 1,
  },
  infoLabel: {
    fontSize: normalize(16),
    fontWeight: '500',
  },
  infoValueContainer: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  infoValue: {
    fontSize: normalize(16),
    textAlign: 'right',
    marginRight: normalize(8),
    flex: 1,
  },
});

export default ProfileInfoItem;
