import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

interface ProfilePhotoSectionProps {
  avatarUrl?: string;
  onChangePhoto: () => void;
  isUploading?: boolean;
}

const ProfilePhotoSection: React.FC<ProfilePhotoSectionProps> = ({
  avatarUrl,
  onChangePhoto,
  isUploading = false,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.photoSection}>
      <TouchableOpacity 
        onPress={onChangePhoto} 
        style={styles.avatarContainer}
        disabled={isUploading}
      >
        <Image
          source={
            avatarUrl
              ? { uri: avatarUrl }
              : require('../../assets/images/default-avatar.png')
          }
          style={styles.avatar}
        />
        <View style={[styles.cameraIcon, { backgroundColor: theme.colors.primary }]}>
          {isUploading ? (
            <Ionicons name="hourglass-outline" size={16} color="#FFFFFF" />
          ) : (
            <Ionicons name="camera" size={16} color="#FFFFFF" />
          )}
        </View>
      </TouchableOpacity>
      
      <TouchableOpacity 
        onPress={onChangePhoto}
        disabled={isUploading}
      >
        <Text style={[
          styles.changePhotoText, 
          { 
            color: isUploading ? theme.colors.textSecondary : theme.colors.primary 
          }
        ]}>
          {isUploading ? 'Uploading...' : 'Change photo'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  photoSection: {
    alignItems: 'center',
    paddingVertical: normalize(32),
    paddingHorizontal: normalize(16),
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: normalize(16),
  },
  avatar: {
    width: normalize(100),
    height: normalize(100),
    borderRadius: normalize(50),
    backgroundColor: '#f0f0f0',
  },
  cameraIcon: {
    position: 'absolute',
    bottom: normalize(4),
    right: normalize(4),
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(16),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  changePhotoText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
});

export default ProfilePhotoSection;
