import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

interface EditModalProps {
  visible: boolean;
  title: string;
  value: string;
  placeholder?: string;
  maxLength?: number;
  multiline?: boolean;
  onClose: () => void;
  onSave: (value: string) => void;
  isLoading?: boolean;
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  title,
  value,
  placeholder = '',
  maxLength = 100,
  multiline = false,
  onClose,
  onSave,
  isLoading = false,
}) => {
  const { theme } = useTheme();
  const [tempValue, setTempValue] = useState(value);

  useEffect(() => {
    setTempValue(value);
  }, [value, visible]);

  const handleSave = () => {
    if (tempValue.trim() === value.trim()) {
      onClose();
      return;
    }
    
    if (!tempValue.trim()) {
      Alert.alert('Error', `${title} cannot be empty`);
      return;
    }
    
    onSave(tempValue.trim());
  };

  const handleClose = () => {
    setTempValue(value);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
            <TouchableOpacity onPress={handleClose} style={styles.modalButton}>
              <Text style={[styles.modalButtonText, { color: theme.colors.textSecondary }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              {title}
            </Text>
            
            <TouchableOpacity 
              onPress={handleSave} 
              style={styles.modalButton}
              disabled={isLoading}
            >
              <Text style={[
                styles.modalButtonText, 
                { 
                  color: isLoading ? theme.colors.textSecondary : theme.colors.primary 
                }
              ]}>
                {isLoading ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Input */}
          <View style={styles.inputContainer}>
            <TextInput
              style={[
                styles.input,
                {
                  color: theme.colors.text,
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  height: multiline ? normalize(120) : normalize(50),
                  textAlignVertical: multiline ? 'top' : 'center',
                }
              ]}
              value={tempValue}
              onChangeText={setTempValue}
              placeholder={placeholder}
              placeholderTextColor={theme.colors.textSecondary}
              maxLength={maxLength}
              multiline={multiline}
              autoFocus
              editable={!isLoading}
            />
            
            {/* Character count */}
            <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
              {tempValue.length}/{maxLength}
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: normalize(20),
    borderTopRightRadius: normalize(20),
    paddingBottom: normalize(40),
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderBottomWidth: 1,
  },
  modalButton: {
    minWidth: normalize(60),
  },
  modalButtonText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  modalTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
    textAlign: 'center',
  },
  inputContainer: {
    padding: normalize(20),
  },
  input: {
    borderWidth: 1,
    borderRadius: normalize(12),
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    fontSize: normalize(16),
    marginBottom: normalize(8),
  },
  characterCount: {
    fontSize: normalize(12),
    textAlign: 'right',
  },
});

export default EditModal;
