import React from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Image, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Text from '../common/Text';

const { width: screenWidth } = Dimensions.get('window');
const videoWidth = (screenWidth - 4) / 3; // 3 columns with 2px gaps

type VideoItem = {
  id: string;
  thumbnail?: string;
  uri?: string;
  views?: string;
  likes?: number;
  user?: {
    avatar_url?: string;
  };
};

type VideoGridProps = {
  videos: VideoItem[];
  onVideoPress?: (videoId: string) => void;
};

const VideoGrid: React.FC<VideoGridProps> = ({ videos, onVideoPress }) => {
  const { theme } = useTheme();

  const formatCount = (count: number | undefined | null): string => {
    // Safety check for null/undefined values
    if (count === null || count === undefined || isNaN(Number(count))) {
      return '0';
    }

    const safeCount = Number(count);
    if (isNaN(safeCount) || safeCount < 0) {
      return '0';
    }

    if (safeCount >= 1000000) {
      return `${(safeCount / 1000000).toFixed(1)}M`;
    } else if (safeCount >= 1000) {
      return `${(safeCount / 1000).toFixed(1)}K`;
    }
    return safeCount.toString();
  };

  const renderVideoItem = ({ item, index }: { item: VideoItem; index: number }) => (
    <TouchableOpacity
      style={[
        styles.videoContainer,
        {
          marginRight: (index + 1) % 3 === 0 ? 0 : 2,
          marginBottom: 2,
        },
      ]}
      onPress={() => onVideoPress?.(item.id)}
    >
      <Image
        source={
          item.thumbnail
            ? { uri: item.thumbnail }
            : require('../../assets/images/default-avatar.png')
        }
        style={styles.videoThumbnail}
        resizeMode="cover"
      />

      {/* Play icon overlay */}
      <View style={styles.playIconContainer}>
        <MaterialCommunityIcons
          name="play"
          size={normalize(20)}
          color="rgba(255, 255, 255, 0.9)"
        />
      </View>

      {/* Video stats overlay */}
      <View style={styles.videoStats}>
        {item.views && typeof item.views === 'string' && item.views.trim() && item.views !== '0' && (
          <View style={styles.statItem}>
            <MaterialCommunityIcons
              name="play"
              size={normalize(12)}
              color="#FFFFFF"
            />
            <Text style={styles.statText}>{String(item.views)}</Text>
          </View>
        )}
        {item.likes !== null && item.likes !== undefined && typeof item.likes === 'number' && item.likes > 0 && (
          <View style={styles.statItem}>
            <MaterialCommunityIcons
              name="heart"
              size={normalize(12)}
              color="#FFFFFF"
            />
            <Text style={styles.statText}>{formatCount(item.likes)}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (!videos || videos.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <MaterialCommunityIcons
          name="video-outline"
          size={normalize(48)}
          color={theme.colors.textSecondary}
        />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No videos yet
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={videos}
      renderItem={renderVideoItem}
      keyExtractor={(item) => item.id}
      numColumns={3}
      scrollEnabled={false}
      contentContainerStyle={styles.grid}
      columnWrapperStyle={null}
    />
  );
};

const styles = StyleSheet.create({
  grid: {
    paddingHorizontal: 0,
    paddingBottom: normalize(20),
  },
  videoContainer: {
    width: videoWidth,
    height: videoWidth * (16 / 9), // 16:9 aspect ratio like TikTok
    backgroundColor: '#000',
    position: 'relative',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  playIconContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -10 }, { translateY: -10 }],
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: normalize(15),
    padding: normalize(8),
  },
  videoStats: {
    position: 'absolute',
    bottom: normalize(6),
    left: normalize(6),
    right: normalize(6),
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(2),
  },
  statText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '600',
    marginLeft: normalize(3),
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: normalize(60),
  },
  emptyText: {
    fontSize: normalize(16),
    marginTop: normalize(12),
    fontWeight: '500',
  },
});

export default VideoGrid;
