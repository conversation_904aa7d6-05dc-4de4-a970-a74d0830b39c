import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Text from '../../components/common/Text';

type ProfileTabsProps = {
  activeTab: 'videos' | 'liked' | 'private';
  onTabChange: (tab: 'videos' | 'liked' | 'private') => void;
  videosCount?: number;
  likedCount?: number;
  privateCount?: number;
};

const ProfileTabs: React.FC<ProfileTabsProps> = ({
  activeTab,
  onTabChange,
  videosCount,
  likedCount,
  privateCount,
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { borderColor: theme.colors.border }]}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'videos' && {
            borderBottomWidth: 2,
            borderBottomColor: theme.colors.text,
          },
        ]}
        onPress={() => onTabChange('videos')}
      >
        <MaterialCommunityIcons
          name="play-box-multiple"
          size={normalize(20)}
          color={activeTab === 'videos' ? theme.colors.text : theme.colors.textSecondary}
        />
        <Text
          style={[
            styles.tabText,
            {
              color: activeTab === 'videos' ? theme.colors.text : theme.colors.textSecondary,
              marginLeft: normalize(8),
            },
          ]}
        >
          Videos{videosCount !== undefined && videosCount !== null ? ` (${videosCount})` : ''}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'liked' && {
            borderBottomWidth: 2,
            borderBottomColor: theme.colors.text,
          },
        ]}
        onPress={() => onTabChange('liked')}
      >
        <FontAwesome
          name="heart"
          size={normalize(20)}
          color={activeTab === 'liked' ? theme.colors.text : theme.colors.textSecondary}
        />
        <Text
          style={[
            styles.tabText,
            {
              color: activeTab === 'liked' ? theme.colors.text : theme.colors.textSecondary,
              marginLeft: normalize(8),
            },
          ]}
        >
          Liked{likedCount !== undefined && likedCount !== null ? ` (${likedCount})` : ''}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'private' && {
            borderBottomWidth: 2,
            borderBottomColor: theme.colors.text,
          },
        ]}
        onPress={() => onTabChange('private')}
      >
        <MaterialCommunityIcons
          name="lock"
          size={normalize(20)}
          color={activeTab === 'private' ? theme.colors.text : theme.colors.textSecondary}
        />
        <Text
          style={[
            styles.tabText,
            {
              color: activeTab === 'private' ? theme.colors.text : theme.colors.textSecondary,
              marginLeft: normalize(8),
            },
          ]}
        >
          Private{privateCount !== undefined && privateCount !== null ? ` (${privateCount})` : ''}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    marginBottom: normalize(16),
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: normalize(12),
  },
  tabText: {
    fontSize: normalize(14),
    fontWeight: '600',
  },
});

export default ProfileTabs;
