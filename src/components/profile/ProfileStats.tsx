import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';
import Text from '../../components/common/Text';
type ProfileStatsProps = {
  following: number;
  followers: number;
  likes: number;
  videos: number;
  onPressFollowing: () => void;
  onPressFollowers: () => void;
};

const ProfileStats: React.FC<ProfileStatsProps> = ({
  following,
  followers,
  likes,
  videos,
  onPressFollowing,
  onPressFollowers,
}) => {
  const { theme } = useTheme();

  const formatNumber = (num: number | undefined | null) => {
    // Safety check for null/undefined values
    if (num === null || num === undefined || isNaN(num)) {
      return '0';
    }

    const safeNum = Number(num);
    if (safeNum >= 1000000) {
      return (safeNum / 1000000).toFixed(1) + 'M';
    }
    if (safeNum >= 1000) {
      return (safeNum / 1000).toFixed(1) + 'K';
    }
    return safeNum.toString();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.statItem} onPress={onPressFollowers}>
        <Text variant="h3" style={[styles.statNumber, { color: theme.colors.text }]}>
          {formatNumber(followers)}
        </Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
          Followers
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.statItem} onPress={onPressFollowing}>
        <Text variant="h3" style={[styles.statNumber, { color: theme.colors.text }]}>
          {formatNumber(following)}
        </Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
          Following
        </Text>
      </TouchableOpacity>

      <View style={styles.statItem}>
        <Text variant="h3" style={[styles.statNumber, { color: theme.colors.text }]}>
          {formatNumber(videos)}
        </Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
          Videos
        </Text>
      </View>

      <View style={styles.statItem}>
        <Text variant="h3" style={[styles.statNumber, { color: theme.colors.text }]}>
          {formatNumber(likes)}
        </Text>
        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
          Likes
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: normalize(16),
    marginBottom: normalize(24),
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: normalize(16),
    fontWeight: 'bold',
    marginBottom: normalize(4),
  },
  statLabel: {
    fontSize: normalize(12),
  },
});

export default ProfileStats;
