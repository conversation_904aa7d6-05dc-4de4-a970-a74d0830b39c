import React, { useEffect } from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { normalize } from '../../utils/responsive';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useToggleFollowMutation } from '../../store/api/userManagementApi';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  initializeFollowerState,
  setFollowing,
  incrementFollowers,
  decrementFollowers,
  selectFollowerState,
} from '../../store/slices/followersSlice';
import logger from '../../utils/logger';

type ProfileHeaderProps = {
  user: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    bio?: string;
    followers: number;
    following: number;
    likes: number;
    isOwnProfile: boolean;
    user_tag?: string;
    is_verified?: boolean;
    banner_image_url?: string;
    profile_picture_url?: string;
    isFollowing?: boolean;
  };
  onEditProfile: () => void;
  onFollow?: () => void;
};

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ user, onEditProfile, onFollow }) => {
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const followState = useAppSelector(selectFollowerState(user.id));
  const [toggleFollow, { isLoading }] = useToggleFollowMutation();

  useEffect(() => {
    if (!followState) {
      dispatch(
        initializeFollowerState({
          userId: user.id,
          isFollowing: false,
          count: user.followers,
        })
      );
    }
  }, [dispatch, followState, user]);

  const handleFollowPress = async () => {
    try {
      const result = await toggleFollow({
        targetUserId: user.id,
        isCurrentlyFollowing: followState?.isFollowing || false,
        currentFollowersCount: user.followers || 0,
        currentFollowingCount: user.following || 0
      }).unwrap();

      dispatch(setFollowing({ userId: user.id, isFollowing: result.isFollowing }));
      if (result.isFollowing) {
        dispatch(incrementFollowers({ userId: user.id }));
      } else {
        dispatch(decrementFollowers({ userId: user.id }));
      }
      onFollow?.();
    } catch (error) {
      logger.error('Follow toggle error:', error);
    }
  };

  logger.debug('Rendering ProfileHeader for user:', user.username);
  logger.debug('User avatar URL:', user.avatar_url);

  return (
    <View style={styles.container}>
      {/* Banner Section */}
      {user.banner_image_url && (
        <View style={styles.bannerContainer}>
          <Image
            source={{ uri: user.banner_image_url }}
            style={styles.bannerImage}
            resizeMode="cover"
          />
        </View>
      )}

      {/* Avatar Section */}
      <View style={styles.avatarContainer}>
        <Image
          source={
            user.avatar_url || user.profile_picture_url
              ? { uri: user.avatar_url || user.profile_picture_url }
              : require('../../assets/images/default-avatar.png')
          }
          style={styles.avatar}
        />
        {user.isOwnProfile && (
          <TouchableOpacity style={[styles.addButton, { backgroundColor: theme.colors.primary }]}>
            <Ionicons name="add" size={normalize(16)} color="#FFFFFF" />
          </TouchableOpacity>
        )}
        {/* Verification Badge */}
        {user.is_verified && (
          <View style={[styles.verificationBadge, { backgroundColor: theme.colors.primary }]}>
            <Ionicons name="checkmark" size={normalize(12)} color="#FFFFFF" />
          </View>
        )}
      </View>

      {/* User Info Section */}
      <View style={styles.userInfo}>
        <Text style={[styles.username, { color: theme.colors.text }]}>
          @{user.username || 'user'}
        </Text>

        <Text style={[styles.displayName, { color: theme.colors.text }]}>
          {user.full_name || 'User'}
        </Text>

        {/* User Tag */}
        {user.user_tag && (
          <View style={[styles.userTagContainer, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.userTag, { color: theme.colors.primary }]}>
              {user.user_tag}
            </Text>
          </View>
        )}

        {/* Bio */}
        {user.bio && typeof user.bio === 'string' && user.bio.trim() && (
          <Text style={[styles.bio, { color: theme.colors.text }]} numberOfLines={3}>
            {user.bio}
          </Text>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {user.isOwnProfile ? (
            <>
              <TouchableOpacity
                style={[styles.editButton, { borderColor: theme.colors.border }]}
                onPress={onEditProfile}
              >
                <Text style={[styles.buttonText, { color: theme.colors.text }]}>
                  Edit profile
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.shareButton, { borderColor: theme.colors.border }]}
              >
                <MaterialIcons name="share" size={16} color={theme.colors.text} />
              </TouchableOpacity>
            </>
          ) : (
            <>
              <TouchableOpacity
                style={[styles.followButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleFollowPress}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Ionicons name="time" size={16} color="#FFFFFF" />
                ) : (
                  <Text style={[styles.followButtonText, { color: '#FFFFFF' }]}>
                    {followState?.isFollowing ? 'Following' : 'Follow'}
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.messageButton, { borderColor: theme.colors.border }]}
              >
                <MaterialIcons name="message" size={16} color={theme.colors.text} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.shareButton, { borderColor: theme.colors.border }]}
              >
                <MaterialIcons name="keyboard-arrow-down" size={16} color={theme.colors.text} />
              </TouchableOpacity>
            </>
          )}
        </View>

        {/* Link or additional info */}
        <TouchableOpacity style={styles.linkContainer}>
          <MaterialIcons name="link" size={14} color={theme.colors.textSecondary} />
          <Text style={[styles.linkText, { color: theme.colors.textSecondary }]}>
            Add link
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
  },
  bannerContainer: {
    width: '100%',
    height: normalize(120),
    marginBottom: normalize(16),
    borderRadius: normalize(8),
    overflow: 'hidden',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: normalize(16),
    position: 'relative',
  },
  avatar: {
    width: normalize(96),
    height: normalize(96),
    borderRadius: normalize(48),
  },
  addButton: {
    position: 'absolute',
    bottom: normalize(-4),
    right: normalize(130),
    width: normalize(24),
    height: normalize(24),
    borderRadius: normalize(12),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verificationBadge: {
    position: 'absolute',
    bottom: normalize(4),
    right: normalize(134),
    width: normalize(20),
    height: normalize(20),
    borderRadius: normalize(10),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  userInfo: {
    alignItems: 'center',
  },
  username: {
    fontSize: normalize(24),
    fontWeight: 'bold',
    marginBottom: normalize(4),
  },
  displayName: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  userTagContainer: {
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    marginBottom: normalize(8),
  },
  userTag: {
    fontSize: normalize(12),
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  bio: {
    fontSize: normalize(15),
    textAlign: 'center',
    lineHeight: normalize(20),
    marginBottom: normalize(16),
    paddingHorizontal: normalize(20),
  },
  actionButtons: {
    flexDirection: 'row',
    marginBottom: normalize(12),
    gap: normalize(8),
  },
  editButton: {
    borderWidth: 1,
    borderRadius: normalize(2),
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(48),
    minWidth: normalize(160),
    alignItems: 'center',
  },
  followButton: {
    borderRadius: normalize(2),
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(48),
    minWidth: normalize(120),
    alignItems: 'center',
  },
  messageButton: {
    borderWidth: 1,
    borderRadius: normalize(2),
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(16),
    alignItems: 'center',
    justifyContent: 'center',
  },
  shareButton: {
    borderWidth: 1,
    borderRadius: normalize(2),
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(16),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  followButtonText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(4),
  },
  linkText: {
    fontSize: normalize(14),
    marginLeft: normalize(4),
  },
});

export default ProfileHeader;
