import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import UserListItem from '../common/UserListItem';

interface UserSuggestionsProps {
  users: any[];
  onUserPress?: (user: any) => void;
}

const UserSuggestions: React.FC<UserSuggestionsProps> = ({ users, onUserPress }) => {
  const { theme } = useTheme();

  if (!users || users.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        Suggested for you
      </Text>
      <FlatList
        data={users}
        renderItem={({ item }) => (
          <UserListItem
            user={item}
            showFollowButton={true}
            onPress={() => onUserPress?.(item)}
          />
        )}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
});

export default UserSuggestions;
