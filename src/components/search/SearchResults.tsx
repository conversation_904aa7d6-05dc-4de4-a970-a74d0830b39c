import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import UserListItem from '../common/UserListItem';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface SearchResultsProps {
  results: any[];
  isLoading?: boolean;
  onUserPress?: (userId: string) => void;
  onVideoPress?: (videoId: string) => void;
  searchQuery?: string;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isLoading = false,
  onUserPress,
  onVideoPress,
  searchQuery,
}) => {
  const { theme } = useTheme();

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          Searching...
        </Text>
      </View>
    );
  }

  if (!results || results.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="search-outline" size={64} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
          No results found
        </Text>
        {searchQuery && (
          <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
            No results for "{searchQuery}"
          </Text>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        Search Results ({results.length})
      </Text>
      <FlatList
        data={results}
        renderItem={({ item }) => (
          <UserListItem
            user={item}
            showFollowButton={true}
            onPress={() => onUserPress?.(item.id)}
          />
        )}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default SearchResults;
