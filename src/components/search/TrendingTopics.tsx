import React from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';

interface TrendingTopicsProps {
  topics: string[];
  onTopicPress?: (topic: string) => void;
}

const TrendingTopics: React.FC<TrendingTopicsProps> = ({ topics, onTopicPress }) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        Trending
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollView}>
        {topics.map((topic, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.topicButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
            onPress={() => onTopicPress?.(topic)}
          >
            <Text style={[styles.topicText, { color: theme.colors.text }]}>
              {topic}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  scrollView: {
    paddingLeft: 16,
  },
  topicButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  topicText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default TrendingTopics;
