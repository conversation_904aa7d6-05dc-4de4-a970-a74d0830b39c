import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import NotificationItem from './NotificationItem';
import { useGetNotificationsQuery, useMarkNotificationAsReadMutation } from '../../store/api/messagingApi';
import { NotificationData } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

interface NotificationCenterProps {
  onClose: () => void;
}

const NOTIFICATION_CATEGORIES = [
  { id: 'all', label: 'All', icon: 'notifications' },
  { id: 'social', label: 'Social', icon: 'people' },
  { id: 'content', label: 'Content', icon: 'videocam' },
  { id: 'security', label: 'Security', icon: 'shield-checkmark' },
  { id: 'system', label: 'System', icon: 'settings' },
];

const NotificationCenter: React.FC<NotificationCenterProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const {
    data: notifications = [],
    isLoading,
    error,
    refetch,
  } = useGetNotificationsQuery();

  const [markAsRead] = useMarkNotificationAsReadMutation();

  // Filter notifications by category
  const filteredNotifications = notifications.filter(notification => {
    if (selectedCategory === 'all') return true;
    return notification.category === selectedCategory;
  });

  // Group notifications by date
  const groupedNotifications = filteredNotifications.reduce((groups, notification) => {
    const date = new Date(notification.created_at).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(notification);
    return groups;
  }, {} as Record<string, NotificationData[]>);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleNotificationPress = async (notification: NotificationData) => {
    // Mark as read if not already read
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    if (notification.action_url) {
      // Navigate to specific screen
      // NavigationService.navigate(notification.action_url);
    }
  };

  const handleMarkAllAsRead = async () => {
    const unreadNotifications = filteredNotifications.filter(n => !n.is_read);
    
    // Mark all unread notifications as read
    await Promise.all(
      unreadNotifications.map(notification => markAsRead(notification.id))
    );
  };

  const renderCategoryTab = (category: typeof NOTIFICATION_CATEGORIES[0]) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryTab,
        {
          backgroundColor: selectedCategory === category.id 
            ? theme.colors.primary 
            : theme.colors.surface,
        }
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Ionicons
        name={category.icon as any}
        size={normalize(16)}
        color={selectedCategory === category.id ? '#FFFFFF' : theme.colors.textSecondary}
      />
      <Text
        style={[
          styles.categoryTabText,
          {
            color: selectedCategory === category.id ? '#FFFFFF' : theme.colors.textSecondary,
          }
        ]}
      >
        {category.label}
      </Text>
    </TouchableOpacity>
  );

  const renderNotificationGroup = ({ item }: { item: [string, NotificationData[]] }) => {
    const [date, notifications] = item;
    const isToday = date === new Date().toDateString();
    const isYesterday = date === new Date(Date.now() - 86400000).toDateString();
    
    let dateLabel = date;
    if (isToday) dateLabel = 'Today';
    else if (isYesterday) dateLabel = 'Yesterday';
    else dateLabel = new Date(date).toLocaleDateString();

    return (
      <View style={styles.notificationGroup}>
        <Text style={[styles.dateHeader, { color: theme.colors.textSecondary }]}>
          {dateLabel}
        </Text>
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onPress={() => handleNotificationPress(notification)}
          />
        ))}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="notifications-off"
        size={normalize(64)}
        color={theme.colors.textSecondary}
      />
      <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
        No notifications
      </Text>
      <Text style={[styles.emptyStateSubtitle, { color: theme.colors.textSecondary }]}>
        You're all caught up!
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Notifications
        </Text>
        <View style={styles.headerActions}>
          {filteredNotifications.some(n => !n.is_read) && (
            <TouchableOpacity
              style={styles.markAllButton}
              onPress={handleMarkAllAsRead}
            >
              <Text style={[styles.markAllText, { color: theme.colors.primary }]}>
                Mark all read
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={normalize(24)} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Category Tabs */}
      <View style={styles.categoryTabs}>
        {NOTIFICATION_CATEGORIES.map(renderCategoryTab)}
      </View>

      {/* Notifications List */}
      <FlatList
        data={Object.entries(groupedNotifications)}
        renderItem={renderNotificationGroup}
        keyExtractor={([date]) => date}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  headerTitle: {
    fontSize: normalize(24),
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: normalize(16),
  },
  markAllButton: {
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
  },
  markAllText: {
    fontSize: normalize(14),
    fontWeight: '600',
  },
  closeButton: {
    padding: normalize(4),
  },
  categoryTabs: {
    flexDirection: 'row',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(12),
    gap: normalize(8),
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
    gap: normalize(6),
  },
  categoryTabText: {
    fontSize: normalize(12),
    fontWeight: '600',
  },
  listContent: {
    paddingBottom: normalize(20),
  },
  notificationGroup: {
    marginBottom: normalize(20),
  },
  dateHeader: {
    fontSize: normalize(14),
    fontWeight: '600',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(8),
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(40),
    paddingVertical: normalize(60),
  },
  emptyStateTitle: {
    fontSize: normalize(20),
    fontWeight: '600',
    marginTop: normalize(16),
    marginBottom: normalize(8),
  },
  emptyStateSubtitle: {
    fontSize: normalize(16),
    textAlign: 'center',
    lineHeight: normalize(22),
  },
});

export default NotificationCenter;
