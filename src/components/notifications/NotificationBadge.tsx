import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { normalize } from '../../utils/responsive';

interface NotificationBadgeProps {
  count: number;
  maxCount?: number;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  showZero?: boolean;
  color?: string;
  textColor?: string;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  maxCount = 99,
  size = 'medium',
  style,
  showZero = false,
  color,
  textColor,
}) => {
  const { theme } = useTheme();

  // Don't show badge if count is 0 and show<PERSON>ero is false
  if (count === 0 && !showZero) {
    return null;
  }

  // Get size-specific styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: {
            minWidth: normalize(16),
            height: normalize(16),
            borderRadius: normalize(8),
            paddingHorizontal: normalize(4),
          },
          text: {
            fontSize: normalize(10),
            fontWeight: '700' as const,
          },
        };
      case 'large':
        return {
          container: {
            minWidth: normalize(28),
            height: normalize(28),
            borderRadius: normalize(14),
            paddingHorizontal: normalize(8),
          },
          text: {
            fontSize: normalize(14),
            fontWeight: '700' as const,
          },
        };
      case 'medium':
      default:
        return {
          container: {
            minWidth: normalize(20),
            height: normalize(20),
            borderRadius: normalize(10),
            paddingHorizontal: normalize(6),
          },
          text: {
            fontSize: normalize(12),
            fontWeight: '700' as const,
          },
        };
    }
  };

  const sizeStyles = getSizeStyles();

  // Format count display
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  // Determine badge color based on count
  const getBadgeColor = () => {
    if (color) return color;
    
    if (count === 0) return theme.colors.textSecondary;
    if (count < 5) return theme.colors.primary;
    if (count < 10) return '#FF9800'; // Orange for medium priority
    return '#FF3040'; // Red for high priority
  };

  // Determine text color
  const getTextColor = () => {
    if (textColor) return textColor;
    return '#FFFFFF';
  };

  return (
    <View
      style={[
        styles.container,
        sizeStyles.container,
        {
          backgroundColor: getBadgeColor(),
        },
        style,
      ]}
    >
      <Text
        style={[
          sizeStyles.text,
          {
            color: getTextColor(),
          },
        ]}
      >
        {displayCount}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default NotificationBadge;
