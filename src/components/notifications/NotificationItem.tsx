import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Avatar from '../common/Avatar';
import { NotificationData } from '../../types/messaging';
import { normalize } from '../../utils/responsive';

interface NotificationItemProps {
  notification: NotificationData;
  onPress: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onPress,
}) => {
  const { theme } = useTheme();

  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'message':
        return 'chatbubble';
      case 'like':
        return 'heart';
      case 'comment':
        return 'chatbubble-ellipses';
      case 'follow':
        return 'person-add';
      case 'mention':
        return 'at';
      case 'live':
        return 'videocam';
      case 'video_upload':
        return 'cloud-upload';
      case 'reaction':
        return 'happy';
      case 'security':
        return 'shield-checkmark';
      case 'system':
        return 'settings';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = () => {
    switch (notification.type) {
      case 'message':
        return theme.colors.primary;
      case 'like':
        return '#FF3040';
      case 'comment':
        return '#4ECDC4';
      case 'follow':
        return '#45B7D1';
      case 'mention':
        return '#FFA726';
      case 'live':
        return '#E91E63';
      case 'video_upload':
        return '#66BB6A';
      case 'reaction':
        return '#FFD54F';
      case 'security':
        return '#FF7043';
      case 'system':
        return theme.colors.textSecondary;
      default:
        return theme.colors.primary;
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days}d`;
    }
  };

  const getPriorityIndicator = () => {
    switch (notification.priority) {
      case 'urgent':
        return (
          <View style={[styles.priorityIndicator, { backgroundColor: '#FF3040' }]}>
            <Ionicons name="warning" size={normalize(12)} color="#FFFFFF" />
          </View>
        );
      case 'high':
        return (
          <View style={[styles.priorityIndicator, { backgroundColor: '#FFA726' }]}>
            <Ionicons name="alert-circle" size={normalize(12)} color="#FFFFFF" />
          </View>
        );
      default:
        return null;
    }
  };

  const getCategoryBadge = () => {
    const categoryColors = {
      general: theme.colors.textSecondary,
      social: '#45B7D1',
      content: '#66BB6A',
      security: '#FF7043',
      system: theme.colors.textSecondary,
    };

    return (
      <View style={[styles.categoryBadge, { backgroundColor: categoryColors[notification.category] + '20' }]}>
        <Text style={[styles.categoryText, { color: categoryColors[notification.category] }]}>
          {notification.category.toUpperCase()}
        </Text>
      </View>
    );
  };

  const renderNotificationContent = () => {
    const { data } = notification;

    switch (notification.type) {
      case 'message':
        return (
          <View style={styles.contentContainer}>
            {data?.senderAvatar && (
              <Avatar
                source={data.senderAvatar}
                size={normalize(40)}
                style={styles.avatar}
              />
            )}
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                {notification.title}
              </Text>
              <Text 
                style={[styles.body, { color: theme.colors.textSecondary }]}
                numberOfLines={2}
              >
                {notification.body}
              </Text>
            </View>
          </View>
        );

      case 'like':
      case 'comment':
        return (
          <View style={styles.contentContainer}>
            {data?.userAvatar && (
              <Avatar
                source={data.userAvatar}
                size={normalize(40)}
                style={styles.avatar}
              />
            )}
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                {notification.title}
              </Text>
              <Text 
                style={[styles.body, { color: theme.colors.textSecondary }]}
                numberOfLines={2}
              >
                {notification.body}
              </Text>
            </View>
            {data?.videoThumbnail && (
              <Image
                source={{ uri: data.videoThumbnail }}
                style={styles.thumbnail}
              />
            )}
          </View>
        );

      case 'follow':
        return (
          <View style={styles.contentContainer}>
            {data?.followerAvatar && (
              <Avatar
                source={data.followerAvatar}
                size={normalize(40)}
                style={styles.avatar}
              />
            )}
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                {notification.title}
              </Text>
              <Text 
                style={[styles.body, { color: theme.colors.textSecondary }]}
                numberOfLines={2}
              >
                {notification.body}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.followButton, { backgroundColor: theme.colors.primary }]}
            >
              <Text style={styles.followButtonText}>Follow Back</Text>
            </TouchableOpacity>
          </View>
        );

      case 'live':
        return (
          <View style={styles.contentContainer}>
            {data?.streamerAvatar && (
              <Avatar
                source={data.streamerAvatar}
                size={normalize(40)}
                style={styles.avatar}
              />
            )}
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                {notification.title}
              </Text>
              <Text 
                style={[styles.body, { color: theme.colors.textSecondary }]}
                numberOfLines={2}
              >
                {notification.body}
              </Text>
            </View>
            <View style={styles.liveIndicator}>
              <Text style={styles.liveText}>LIVE</Text>
            </View>
          </View>
        );

      default:
        return (
          <View style={styles.contentContainer}>
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: theme.colors.text }]}>
                {notification.title}
              </Text>
              <Text 
                style={[styles.body, { color: theme.colors.textSecondary }]}
                numberOfLines={2}
              >
                {notification.body}
              </Text>
            </View>
          </View>
        );
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: notification.is_read 
            ? 'transparent' 
            : theme.colors.surface + '30',
        }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Notification Icon */}
      <View style={[
        styles.iconContainer,
        { backgroundColor: getNotificationColor() }
      ]}>
        <Ionicons
          name={getNotificationIcon() as any}
          size={normalize(20)}
          color="#FFFFFF"
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Priority and Category indicators */}
        <View style={styles.headerRow}>
          {getPriorityIndicator()}
          {getCategoryBadge()}
        </View>

        {renderNotificationContent()}

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
            {formatTime(notification.created_at)}
          </Text>

          {notification.expires_at && (
            <Text style={[styles.expiryText, { color: theme.colors.warning }]}>
              Expires {formatTime(notification.expires_at)}
            </Text>
          )}

          {!notification.is_read && (
            <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: normalize(20),
    paddingVertical: normalize(16),
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: normalize(36),
    height: normalize(36),
    borderRadius: normalize(18),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: normalize(12),
    marginTop: normalize(2),
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatar: {
    marginRight: normalize(12),
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: normalize(16),
    fontWeight: '600',
    lineHeight: normalize(20),
    marginBottom: normalize(4),
  },
  body: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
  thumbnail: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(8),
    marginLeft: normalize(12),
  },
  followButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
    marginLeft: normalize(12),
  },
  followButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(12),
    fontWeight: '600',
  },
  liveIndicator: {
    backgroundColor: '#FF3040',
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(4),
    marginLeft: normalize(12),
  },
  liveText: {
    color: '#FFFFFF',
    fontSize: normalize(10),
    fontWeight: '700',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(8),
    gap: normalize(8),
  },
  timestamp: {
    fontSize: normalize(12),
    fontWeight: '400',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(8),
    gap: normalize(8),
  },
  priorityIndicator: {
    width: normalize(20),
    height: normalize(20),
    borderRadius: normalize(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryBadge: {
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(2),
    borderRadius: normalize(12),
  },
  categoryText: {
    fontSize: normalize(10),
    fontWeight: '600',
  },
  expiryText: {
    fontSize: normalize(11),
    fontStyle: 'italic',
  },
  unreadDot: {
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(4),
    marginLeft: 'auto',
  },
});

export default NotificationItem;
