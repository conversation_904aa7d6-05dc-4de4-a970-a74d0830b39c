import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Input from '../common/Input';
import { allCountries, Country } from '../../utils/phoneUtils';
import { normalize } from '../../utils/responsive';

const { height: screenHeight } = Dimensions.get('window');

interface CountrySelectorProps {
  selectedCountry: Country;
  onSelectCountry: (country: Country) => void;
  visible: boolean;
  onClose: () => void;
  disabled?: boolean;
}

const CountrySelector: React.FC<CountrySelectorProps> = ({
  selectedCountry,
  onSelectCountry,
  visible,
  onClose,
  disabled = false,
}) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCountries = allCountries.filter(country =>
    country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    country.dialCode.includes(searchQuery)
  );

  const handleSelectCountry = (country: Country) => {
    onSelectCountry(country);
    setSearchQuery('');
    onClose();
  };

  const renderCountryItem = ({ item }: { item: Country }) => (
    <TouchableOpacity
      style={[
        styles.countryItem,
        {
          backgroundColor: item.code === selectedCountry.code
            ? theme.colors.primary + '20'
            : 'transparent',
        },
      ]}
      onPress={() => handleSelectCountry(item)}
    >
      <Text style={[styles.countryFlag, { color: theme.colors.text }]}>
        {item.flag}
      </Text>
      <View style={styles.countryInfo}>
        <Text style={[styles.countryName, { color: theme.colors.text }]}>
          {item.name}
        </Text>
        <Text style={[styles.countryCode, { color: theme.colors.textSecondary }]}>
          {item.dialCode}
        </Text>
      </View>
      {item.code === selectedCountry.code && (
        <Text style={[styles.checkmark, { color: theme.colors.primary }]}>
          ✓
        </Text>
      )}
    </TouchableOpacity>
  );

  return (
    <>
      {/* Country Selector Button */}
      <TouchableOpacity
        style={[styles.countrySelector, { borderRightColor: theme.colors.border }]}
        onPress={() => !disabled && onClose()}
        disabled={disabled}
      >
        <Text style={[styles.countryFlag, { color: theme.colors.text }]}>
          {selectedCountry.flag}
        </Text>
        <Text style={[styles.dialCode, { color: theme.colors.text }]}>
          {selectedCountry.dialCode}
        </Text>
        <Text style={[styles.dropdownArrow, { color: theme.colors.textSecondary }]}>
          {visible ? '▲' : '▼'}
        </Text>
      </TouchableOpacity>

      {/* Country Dropdown */}
      {visible && (
        <View style={[
          styles.countryDropdown,
          {
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            shadowColor: theme.colors.text,
          }
        ]}>
          {/* Search Input */}
          <View style={styles.searchContainer}>
            <Input
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search countries..."
              style={[styles.searchInput, { backgroundColor: theme.colors.surface }]}
            />
          </View>

          {/* Countries List */}
          <FlatList
            data={filteredCountries}
            renderItem={renderCountryItem}
            keyExtractor={(item) => item.code}
            style={styles.countriesList}
            showsVerticalScrollIndicator={false}
            maxToRenderPerBatch={20}
            windowSize={10}
            initialNumToRender={15}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(16),
    borderRightWidth: 1,
    minWidth: normalize(100),
  },
  countryFlag: {
    fontSize: normalize(20),
    marginRight: normalize(8),
  },
  dialCode: {
    fontSize: normalize(16),
    fontWeight: '500',
    marginRight: normalize(4),
  },
  dropdownArrow: {
    fontSize: normalize(12),
  },
  countryDropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    maxHeight: screenHeight * 0.4,
    borderWidth: 1,
    borderRadius: normalize(8),
    marginTop: normalize(4),
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    zIndex: 1000,
  },
  searchContainer: {
    padding: normalize(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  searchInput: {
    marginBottom: 0,
  },
  countriesList: {
    maxHeight: screenHeight * 0.3,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  countryInfo: {
    flex: 1,
    marginLeft: normalize(12),
  },
  countryName: {
    fontSize: normalize(16),
    fontWeight: '500',
  },
  countryCode: {
    fontSize: normalize(14),
    marginTop: normalize(2),
  },
  checkmark: {
    fontSize: normalize(18),
    fontWeight: 'bold',
  },
});

export default CountrySelector;
