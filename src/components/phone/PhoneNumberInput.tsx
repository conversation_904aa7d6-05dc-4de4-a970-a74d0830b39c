import React from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Input from '../common/Input';
import CountrySelector from './CountrySelector';
import { 
  formatPhoneNumber as formatPhoneNumberUtil,
  Country 
} from '../../utils/phoneUtils';
import { normalize } from '../../utils/responsive';

interface PhoneNumberInputProps {
  selectedCountry: Country;
  phoneNumber: string;
  error?: string;
  isDetectingLocation?: boolean;
  onCountryChange: (country: Country) => void;
  onPhoneNumberChange: (phoneNumber: string) => void;
  onErrorChange?: (error: string) => void;
  placeholder?: string;
  editable?: boolean;
}

const PhoneNumberInput: React.FC<PhoneNumberInputProps> = ({
  selectedCountry,
  phoneNumber,
  error,
  isDetectingLocation = false,
  onCountryChange,
  onPhoneNumberChange,
  onErrorChange,
  placeholder = "Enter phone number",
  editable = true,
}) => {
  const { theme } = useTheme();
  const [showCountryDropdown, setShowCountryDropdown] = React.useState(false);

  const formatPhoneNumber = (value: string): string => {
    return formatPhoneNumberUtil(value, selectedCountry);
  };

  const handlePhoneNumberChange = (text: string) => {
    const digits = text.replace(/\D/g, '');
    onPhoneNumberChange(digits);
    onErrorChange?.('');
    
    // Close dropdown when user starts typing
    if (showCountryDropdown) {
      setShowCountryDropdown(false);
    }
  };

  const handleCountrySelect = (country: Country) => {
    onCountryChange(country);
    onErrorChange?.('');
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
        Phone Number
      </Text>

      <View style={[
        styles.phoneInputContainer,
        { 
          borderColor: error ? theme.colors.error : theme.colors.border,
          backgroundColor: theme.colors.surface,
        }
      ]}>
        {/* Country Selector */}
        <CountrySelector
          selectedCountry={selectedCountry}
          onSelectCountry={handleCountrySelect}
          visible={showCountryDropdown}
          onClose={() => setShowCountryDropdown(!showCountryDropdown)}
          disabled={isDetectingLocation}
        />

        {/* Phone Number Input */}
        <Input
          value={formatPhoneNumber(phoneNumber)}
          onChangeText={handlePhoneNumberChange}
          placeholder={placeholder}
          keyboardType="phone-pad"
          style={styles.phoneInput}
          editable={editable}
          maxLength={20}
        />
      </View>

      {/* Error Message */}
      {error ? (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      ) : null}

      {/* Location Detection Status */}
      {isDetectingLocation && (
        <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
          Detecting your location...
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: normalize(20),
  },
  inputLabel: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  phoneInputContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: normalize(8),
    overflow: 'hidden',
    position: 'relative',
  },
  phoneInput: {
    flex: 1,
    borderWidth: 0,
    borderRadius: 0,
    marginBottom: 0,
    paddingHorizontal: normalize(12),
  },
  errorText: {
    fontSize: normalize(14),
    marginTop: normalize(8),
  },
  statusText: {
    fontSize: normalize(12),
    marginTop: normalize(4),
    fontStyle: 'italic',
  },
});

export default PhoneNumberInput;
