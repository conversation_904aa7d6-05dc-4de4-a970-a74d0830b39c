import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Input from '../common/Input';
import Button from '../common/Button';
import { normalize } from '../../utils/responsive';

interface VerificationCodeInputProps {
  verificationCode: string;
  phoneNumber: string;
  error?: string;
  isLoading?: boolean;
  onCodeChange: (code: string) => void;
  onVerify: () => void;
  onResend: () => void;
  onGoBack: () => void;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  verificationCode,
  phoneNumber,
  error,
  isLoading = false,
  onCodeChange,
  onVerify,
  onResend,
  onGoBack,
}) => {
  const { theme } = useTheme();

  const handleCodeChange = (text: string) => {
    // Only allow digits and limit to 6 characters
    const digits = text.replace(/\D/g, '').slice(0, 6);
    onCodeChange(digits);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Verify Phone Number
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          We've sent a 6-digit verification code to:
        </Text>
        <Text style={[styles.phoneNumber, { color: theme.colors.text }]}>
          {phoneNumber}
        </Text>
      </View>

      {/* Verification Code Input */}
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Verification Code
        </Text>
        <Input
          value={verificationCode}
          onChangeText={handleCodeChange}
          placeholder="000000"
          keyboardType="number-pad"
          maxLength={6}
          style={[
            styles.codeInput,
            {
              borderColor: error ? theme.colors.error : theme.colors.border,
              textAlign: 'center',
              fontSize: normalize(24),
              letterSpacing: normalize(8),
            }
          ]}
          editable={!isLoading}
        />

        {/* Error Message */}
        {error ? (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {error}
          </Text>
        ) : null}
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Button
          title="Verify"
          onPress={onVerify}
          loading={isLoading}
          disabled={verificationCode.length !== 6 || isLoading}
          style={styles.verifyButton}
        />

        <TouchableOpacity
          onPress={onResend}
          style={styles.resendButton}
          disabled={isLoading}
        >
          <Text style={[
            styles.resendText,
            { 
              color: isLoading ? theme.colors.textSecondary : theme.colors.primary 
            }
          ]}>
            Resend Code
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={onGoBack}
          style={styles.backButton}
          disabled={isLoading}
        >
          <Text style={[
            styles.backText,
            { 
              color: isLoading ? theme.colors.textSecondary : theme.colors.textSecondary 
            }
          ]}>
            Change Phone Number
          </Text>
        </TouchableOpacity>
      </View>

      {/* Help Text */}
      <View style={styles.helpContainer}>
        <Text style={[styles.helpText, { color: theme.colors.textSecondary }]}>
          Didn't receive the code? Check your spam folder or try resending.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: normalize(20),
  },
  header: {
    alignItems: 'center',
    marginBottom: normalize(40),
  },
  title: {
    fontSize: normalize(24),
    fontWeight: 'bold',
    marginBottom: normalize(8),
  },
  subtitle: {
    fontSize: normalize(16),
    textAlign: 'center',
    marginBottom: normalize(8),
  },
  phoneNumber: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  inputContainer: {
    marginBottom: normalize(30),
  },
  inputLabel: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
    textAlign: 'center',
  },
  codeInput: {
    marginBottom: normalize(8),
  },
  errorText: {
    fontSize: normalize(14),
    textAlign: 'center',
    marginTop: normalize(8),
  },
  buttonContainer: {
    gap: normalize(16),
  },
  verifyButton: {
    marginBottom: normalize(8),
  },
  resendButton: {
    alignItems: 'center',
    paddingVertical: normalize(12),
  },
  resendText: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  backButton: {
    alignItems: 'center',
    paddingVertical: normalize(8),
  },
  backText: {
    fontSize: normalize(14),
  },
  helpContainer: {
    marginTop: normalize(30),
    alignItems: 'center',
  },
  helpText: {
    fontSize: normalize(12),
    textAlign: 'center',
    lineHeight: normalize(18),
  },
});

export default VerificationCodeInput;
