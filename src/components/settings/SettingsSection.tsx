import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { spacing } from '../../styles/spacing';

interface SettingsSectionProps {
  title: string;
  children: React.ReactNode;
  description?: string;
}

/**
 * Settings Section Component
 * Groups related settings with a title and optional description
 */
const SettingsSection: React.FC<SettingsSectionProps> = ({
  title,
  children,
  description,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          {title}
        </Text>
        {description && (
          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            {description}
          </Text>
        )}
      </View>
      <View style={[styles.content, { backgroundColor: theme.colors.surface }]}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.lg,
  },
  header: {
    paddingHorizontal: spacing.base,
    paddingBottom: spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  content: {
    borderRadius: 12,
    overflow: 'hidden',
  },
});

export default SettingsSection;
