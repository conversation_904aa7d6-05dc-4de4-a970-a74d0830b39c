import React from 'react';
import { View, StyleSheet, TouchableOpacity, Switch } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import { spacing, borderRadius } from '../../styles/spacing';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface SettingsItemProps {
  title: string;
  description?: string;
  value?: string | boolean;
  type?: 'toggle' | 'navigation' | 'selection' | 'action';
  icon?: string;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  disabled?: boolean;
  destructive?: boolean;
  showChevron?: boolean;
  loading?: boolean;
}

/**
 * Settings Item Component
 * Flexible component for different types of settings items
 */
const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  description,
  value,
  type = 'navigation',
  icon,
  onPress,
  onToggle,
  disabled = false,
  destructive = false,
  showChevron = true,
  loading = false,
}) => {
  const { theme } = useTheme();

  const handlePress = () => {
    if (disabled) {return;}
    if (type === 'toggle' && onToggle && typeof value === 'boolean') {
      onToggle(!value);
    } else if (onPress) {
      onPress();
    }
  };

  const renderRightContent = () => {
    switch (type) {
      case 'toggle':
        return (
          <Switch
            value={value as boolean}
            onValueChange={onToggle}
            disabled={disabled}
            trackColor={{
              false: theme.colors.border,
              true: theme.colors.primary,
            }}
            thumbColor={theme.colors.background}
          />
        );

      case 'selection':
        return (
          <View style={styles.selectionContainer}>
            <Text style={[styles.selectionValue, { color: theme.colors.textSecondary }]}>
              {value as string}
            </Text>
            {showChevron && (
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
              />
            )}
          </View>
        );

      case 'navigation':
      case 'action':
      default:
        return showChevron ? (
          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.textSecondary}
          />
        ) : null;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { borderBottomColor: theme.colors.divider },
        disabled && styles.disabled,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={styles.leftContent}>
        {icon && (
          <Ionicons
            name={icon}
            size={24}
            color={destructive ? theme.colors.error : theme.colors.text}
            style={styles.icon}
          />
        )}
        <View style={styles.textContainer}>
          <Text
            style={[
              styles.title,
              {
                color: destructive
                  ? theme.colors.error
                  : disabled
                  ? theme.colors.textTertiary
                  : theme.colors.text,
              },
            ]}
          >
            {title}
          </Text>
          {description && (
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              {description}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.rightContent}>
        {renderRightContent()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.base,
    paddingVertical: spacing.md,
    borderBottomWidth: 0.5,
    minHeight: 56,
  },
  disabled: {
    opacity: 0.5,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: spacing.md,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
  },
  description: {
    fontSize: 14,
    lineHeight: 18,
    marginTop: 2,
  },
  rightContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionValue: {
    fontSize: 16,
    marginRight: spacing.sm,
  },
});

export default SettingsItem;
