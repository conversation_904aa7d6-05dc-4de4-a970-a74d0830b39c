import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Keyboard,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { normalize } from '../../utils/responsive';
import { useAppSelector } from '../../store/hooks';
import { selectCurrentUser } from '../../store/slices/authSlice';

interface CommentInputProps {
  onSubmit: (text: string, replyToId?: string) => void;
  replyTo?: {
    id: string;
    username: string;
  };
  onCancelReply?: () => void;
  placeholder?: string;
  isLoading?: boolean;
}

const CommentInput: React.FC<CommentInputProps> = ({
  onSubmit,
  replyTo,
  onCancelReply,
  placeholder = 'Add a comment...',
  isLoading = false,
}) => {
  const { theme } = useTheme();
  const currentUser = useAppSelector(selectCurrentUser);
  const [text, setText] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const handleSubmit = () => {
    if (!text.trim()) return;
    
    if (!currentUser) {
      Alert.alert('Sign in required', 'Please sign in to comment');
      return;
    }

    onSubmit(text.trim(), replyTo?.id);
    setText('');
    Keyboard.dismiss();
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleCancelReply = () => {
    if (onCancelReply) {
      onCancelReply();
    }
    inputRef.current?.blur();
  };

  const canSend = text.trim().length > 0 && !isLoading;

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Reply indicator */}
      {replyTo && (
        <View style={[styles.replyIndicator, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.replyText, { color: theme.colors.textSecondary }]}>
            Replying to @{replyTo.username}
          </Text>
          <TouchableOpacity onPress={handleCancelReply} style={styles.cancelReplyButton}>
            <Ionicons 
              name="close" 
              size={normalize(16)} 
              color={theme.colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.inputRow}>
        {/* User avatar */}
        <View style={styles.avatarContainer}>
          <Image
            source={
              currentUser?.avatar_url
                ? { uri: currentUser.avatar_url }
                : require('../../assets/images/default-avatar.png')
            }
            style={styles.avatar}
          />
        </View>

        {/* Input container */}
        <View style={styles.inputContainer}>
          <View
            style={[
              styles.inputWrapper,
              {
                backgroundColor: theme.colors.surface,
                borderColor: isFocused ? theme.colors.primary : 'transparent',
              },
            ]}
          >
            <TextInput
              ref={inputRef}
              style={[
                styles.textInput,
                {
                  color: theme.colors.text,
                },
              ]}
              placeholder={placeholder}
              placeholderTextColor={theme.colors.textSecondary}
              value={text}
              onChangeText={setText}
              onFocus={handleFocus}
              onBlur={handleBlur}
              multiline
              maxLength={500}
              returnKeyType="send"
              onSubmitEditing={handleSubmit}
              blurOnSubmit={false}
            />

            {/* Emoji button */}
            <TouchableOpacity style={styles.emojiButton}>
              <MaterialIcons
                name="emoji-emotions"
                size={normalize(20)}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          {/* Character count */}
          {text.length > 400 && (
            <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
              {text.length}/500
            </Text>
          )}
        </View>

        {/* Send button */}
        <TouchableOpacity
          onPress={handleSubmit}
          disabled={!canSend}
          style={[
            styles.sendButton,
            {
              backgroundColor: canSend ? theme.colors.primary : 'transparent',
            },
          ]}
        >
          {isLoading ? (
            <Ionicons
              name="time"
              size={normalize(18)}
              color={canSend ? '#FFFFFF' : theme.colors.textSecondary}
            />
          ) : (
            <Ionicons
              name="send"
              size={normalize(18)}
              color={canSend ? '#FFFFFF' : theme.colors.textSecondary}
            />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
  },
  replyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(8),
    marginBottom: normalize(8),
  },
  replyText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  cancelReplyButton: {
    padding: normalize(4),
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  avatarContainer: {
    marginRight: normalize(12),
  },
  avatar: {
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(16),
  },
  inputContainer: {
    flex: 1,
    marginRight: normalize(8),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: normalize(20),
    borderWidth: 1,
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    minHeight: normalize(40),
    maxHeight: normalize(100),
  },
  textInput: {
    flex: 1,
    fontSize: normalize(14),
    lineHeight: normalize(18),
    paddingVertical: normalize(4),
    textAlignVertical: 'center',
  },
  emojiButton: {
    padding: normalize(4),
    marginLeft: normalize(4),
  },
  characterCount: {
    fontSize: normalize(10),
    textAlign: 'right',
    marginTop: normalize(4),
    marginRight: normalize(8),
  },
  sendButton: {
    width: normalize(36),
    height: normalize(36),
    borderRadius: normalize(18),
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CommentInput;
