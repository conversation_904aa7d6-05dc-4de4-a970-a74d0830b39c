import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image, TextInput } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

interface CommentItemProps {
  comment: {
    id: string;
    text: string;
    user_id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    likes: number;
    isLiked: boolean;
    created_at: string;
    updated_at?: string;
    is_edited?: boolean;
    is_pinned?: boolean;
    replies?: CommentItemProps['comment'][];
    parent_id?: string;
    mention_users?: string[];
  };
  isReply?: boolean;
  onLike: (commentId: string) => void;
  onReply: (commentId: string, username: string) => void;
  onUserPress: (userId: string) => void;
  onEdit?: (commentId: string, text: string) => void;
  onDelete?: (commentId: string) => void;
  onPin?: (commentId: string) => void;
  onReport?: (commentId: string) => void;
  currentUserId?: string;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  isReply = false,
  onLike,
  onReply,
  onUserPress,
  onEdit,
  onDelete,
  onPin,
  onReport,
  currentUserId,
}) => {
  const { theme } = useTheme();
  const [showReplies, setShowReplies] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(comment.text);

  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const commentDate = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - commentDate.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    return `${Math.floor(diffInSeconds / 604800)}w`;
  };

  const formatLikeCount = (count: number): string => {
    if (count === 0) return '';
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  const handleUserPress = () => {
    onUserPress(comment.user_id);
  };

  const handleLike = () => {
    onLike(comment.id);
  };

  const handleReply = () => {
    onReply(comment.id, comment.username);
  };

  const toggleReplies = () => {
    setShowReplies(!showReplies);
  };

  const handleEdit = () => {
    if (onEdit && editText.trim() !== comment.text) {
      onEdit(comment.id, editText.trim());
    }
    setIsEditing(false);
    setShowActions(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(comment.id);
    }
    setShowActions(false);
  };

  const handlePin = () => {
    if (onPin) {
      onPin(comment.id);
    }
    setShowActions(false);
  };

  const handleReport = () => {
    if (onReport) {
      onReport(comment.id);
    }
    setShowActions(false);
  };

  const isOwnComment = currentUserId === comment.user_id;

  const renderMentions = (text: string) => {
    if (!comment.mention_users || comment.mention_users.length === 0) {
      return text;
    }

    // Simple mention highlighting - in a real app, you'd want more sophisticated parsing
    let processedText = text;
    comment.mention_users.forEach(username => {
      processedText = processedText.replace(
        new RegExp(`@${username}`, 'g'),
        `@${username}`
      );
    });

    return processedText;
  };

  return (
    <View style={[styles.container, isReply && styles.replyContainer]}>
      {/* Main comment */}
      <View style={styles.commentRow}>
        {/* User avatar */}
        <TouchableOpacity onPress={handleUserPress} style={styles.avatarContainer}>
          <Image
            source={
              comment.avatar_url
                ? { uri: comment.avatar_url }
                : require('../../assets/images/default-avatar.png')
            }
            style={[styles.avatar, isReply && styles.replyAvatar]}
          />
        </TouchableOpacity>

        {/* Comment content */}
        <View style={styles.contentContainer}>
          <View style={styles.commentContent}>
            {/* Username and comment text */}
            <View style={styles.textContainer}>
              <View style={styles.usernameRow}>
                <TouchableOpacity onPress={handleUserPress}>
                  <Text style={[styles.username, { color: theme.colors.text }]}>
                    {comment.username}
                  </Text>
                </TouchableOpacity>
                {comment.is_pinned && (
                  <View style={[styles.pinnedBadge, { backgroundColor: theme.colors.primary }]}>
                    <Ionicons name="pin" size={normalize(10)} color="#FFFFFF" />
                  </View>
                )}
                {comment.is_edited && (
                  <Text style={[styles.editedText, { color: theme.colors.textSecondary }]}>
                    (edited)
                  </Text>
                )}
              </View>

              {isEditing ? (
                <View style={styles.editContainer}>
                  <TextInput
                    style={[styles.editInput, {
                      color: theme.colors.text,
                      borderColor: theme.colors.border,
                      backgroundColor: theme.colors.surface,
                    }]}
                    value={editText}
                    onChangeText={setEditText}
                    multiline
                    autoFocus
                  />
                  <View style={styles.editActions}>
                    <TouchableOpacity onPress={() => setIsEditing(false)} style={styles.editButton}>
                      <Text style={[styles.editButtonText, { color: theme.colors.textSecondary }]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={handleEdit} style={[styles.editButton, styles.saveButton]}>
                      <Text style={[styles.editButtonText, { color: theme.colors.primary }]}>
                        Save
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <Text style={[styles.commentText, { color: theme.colors.text }]}>
                  {renderMentions(comment.text)}
                </Text>
              )}
            </View>

            {/* Action buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity onPress={handleLike} style={styles.likeButton}>
                <Ionicons
                  name={comment.isLiked ? 'heart' : 'heart-outline'}
                  size={normalize(16)}
                  color={comment.isLiked ? '#FF3040' : theme.colors.textSecondary}
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setShowActions(!showActions)}
                style={styles.moreButton}
              >
                <Ionicons
                  name="ellipsis-horizontal"
                  size={normalize(16)}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Comment metadata */}
          <View style={styles.metadataRow}>
            <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>
              {formatTimeAgo(comment.created_at)}
            </Text>
            
            {comment.likes > 0 && (
              <>
                <Text style={[styles.separator, { color: theme.colors.textSecondary }]}>
                  •
                </Text>
                <Text style={[styles.likesText, { color: theme.colors.textSecondary }]}>
                  {formatLikeCount(comment.likes)} likes
                </Text>
              </>
            )}

            {!isReply && (
              <>
                <Text style={[styles.separator, { color: theme.colors.textSecondary }]}>
                  •
                </Text>
                <TouchableOpacity onPress={handleReply}>
                  <Text style={[styles.replyText, { color: theme.colors.textSecondary }]}>
                    Reply
                  </Text>
                </TouchableOpacity>
              </>
            )}
          </View>

          {/* Show replies button */}
          {!isReply && comment.replies && comment.replies.length > 0 && (
            <TouchableOpacity onPress={toggleReplies} style={styles.showRepliesButton}>
              <View style={[styles.repliesLine, { backgroundColor: theme.colors.border }]} />
              <Text style={[styles.showRepliesText, { color: theme.colors.textSecondary }]}>
                {showReplies ? 'Hide' : 'View'} {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
              </Text>
            </TouchableOpacity>
          )}

          {/* Action Menu */}
          {showActions && (
            <View style={[styles.actionMenu, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
              {isOwnComment && (
                <>
                  <TouchableOpacity onPress={() => setIsEditing(true)} style={styles.actionMenuItem}>
                    <Ionicons name="create-outline" size={normalize(16)} color={theme.colors.text} />
                    <Text style={[styles.actionMenuText, { color: theme.colors.text }]}>Edit</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={handleDelete} style={styles.actionMenuItem}>
                    <Ionicons name="trash-outline" size={normalize(16)} color="#FF3040" />
                    <Text style={[styles.actionMenuText, { color: '#FF3040' }]}>Delete</Text>
                  </TouchableOpacity>
                </>
              )}
              {onPin && (
                <TouchableOpacity onPress={handlePin} style={styles.actionMenuItem}>
                  <Ionicons name={comment.is_pinned ? "pin" : "pin-outline"} size={normalize(16)} color={theme.colors.text} />
                  <Text style={[styles.actionMenuText, { color: theme.colors.text }]}>
                    {comment.is_pinned ? 'Unpin' : 'Pin'}
                  </Text>
                </TouchableOpacity>
              )}
              {!isOwnComment && (
                <TouchableOpacity onPress={handleReport} style={styles.actionMenuItem}>
                  <Ionicons name="flag-outline" size={normalize(16)} color="#FF3040" />
                  <Text style={[styles.actionMenuText, { color: '#FF3040' }]}>Report</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </View>

      {/* Replies */}
      {!isReply && showReplies && comment.replies && (
        <View style={styles.repliesContainer}>
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              isReply={true}
              onLike={onLike}
              onReply={onReply}
              onUserPress={onUserPress}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: normalize(16),
  },
  replyContainer: {
    marginLeft: normalize(32),
    marginBottom: normalize(12),
  },
  commentRow: {
    flexDirection: 'row',
  },
  avatarContainer: {
    marginRight: normalize(12),
  },
  avatar: {
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(16),
  },
  replyAvatar: {
    width: normalize(24),
    height: normalize(24),
    borderRadius: normalize(12),
  },
  contentContainer: {
    flex: 1,
  },
  commentContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  textContainer: {
    flex: 1,
  },
  username: {
    fontSize: normalize(14),
    fontWeight: '600',
    marginBottom: normalize(2),
  },
  commentText: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
  likeButton: {
    padding: normalize(4),
    marginLeft: normalize(8),
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(8),
  },
  timeText: {
    fontSize: normalize(12),
  },
  separator: {
    fontSize: normalize(12),
    marginHorizontal: normalize(8),
  },
  likesText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  replyText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  showRepliesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: normalize(12),
  },
  repliesLine: {
    width: normalize(24),
    height: 1,
    marginRight: normalize(12),
  },
  showRepliesText: {
    fontSize: normalize(12),
    fontWeight: '500',
  },
  repliesContainer: {
    marginTop: normalize(12),
  },
  usernameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(4),
    gap: normalize(8),
  },
  pinnedBadge: {
    paddingHorizontal: normalize(6),
    paddingVertical: normalize(2),
    borderRadius: normalize(8),
    flexDirection: 'row',
    alignItems: 'center',
  },
  editedText: {
    fontSize: normalize(10),
    fontStyle: 'italic',
  },
  editContainer: {
    marginTop: normalize(8),
  },
  editInput: {
    borderWidth: 1,
    borderRadius: normalize(8),
    padding: normalize(12),
    fontSize: normalize(14),
    minHeight: normalize(80),
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: normalize(8),
    gap: normalize(12),
  },
  editButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
  },
  saveButton: {
    backgroundColor: 'transparent',
  },
  editButtonText: {
    fontSize: normalize(14),
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: normalize(8),
  },
  moreButton: {
    padding: normalize(4),
  },
  actionMenu: {
    position: 'absolute',
    top: normalize(40),
    right: 0,
    borderWidth: 1,
    borderRadius: normalize(8),
    padding: normalize(8),
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  actionMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(12),
    gap: normalize(8),
  },
  actionMenuText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
});

export default CommentItem;
