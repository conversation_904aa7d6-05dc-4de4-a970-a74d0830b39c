import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  TouchableOpacity,
  StatusBar,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { GestureHandlerRootView, PanGestureHandler, State } from 'react-native-gesture-handler';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';
import CommentItem from './CommentItem';
import CommentInput from './CommentInput';
import { useVideoComments } from '../../hooks/api/useVideoComments';

const { height: screenHeight } = Dimensions.get('window');

interface CommentsModalProps {
  visible: boolean;
  onClose: () => void;
  videoId: string;
  commentCount: number;
}

const CommentsModal: React.FC<CommentsModalProps> = ({
  visible,
  onClose,
  videoId,
  commentCount,
}) => {
  const { theme } = useTheme();
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  const [modalHeight] = useState(screenHeight * 0.75); // 75% of screen height like TikTok
  const [replyTo, setReplyTo] = useState<{ id: string; username: string } | undefined>();

  // Use the video comments hook
  const {
    comments,
    commentsCount,
    isLoading,
    isCreating,
    addComment,
    toggleLike,
    removeComment,
    refetch,
  } = useVideoComments({
    videoId,
    initialCommentsCount: commentCount,
  });

  useEffect(() => {
    if (visible) {
      // Slide up animation
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // Slide down animation
      Animated.spring(translateY, {
        toValue: screenHeight,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible, translateY]);

  const handleGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: translateY } }],
    { useNativeDriver: true }
  );

  const handleGestureStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationY, velocityY } = event.nativeEvent;
      
      // Close modal if dragged down significantly or with high velocity
      if (translationY > modalHeight * 0.3 || velocityY > 1000) {
        onClose();
      } else {
        // Snap back to open position
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();
      }
    }
  };

  const formatCommentCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const handleSubmitComment = async (text: string, replyToId?: string) => {
    try {
      const success = await addComment(text, replyToId);
      if (success) {
        setReplyTo(undefined);
      }
    } catch (error) {
      console.error('Failed to create comment:', error);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    try {
      // Find the comment to check if it's already liked
      const findComment = (comments: any[], id: string): any => {
        for (const comment of comments) {
          if (comment.id === id) return comment;
          if (comment.replies) {
            const found = findComment(comment.replies, id);
            if (found) return found;
          }
        }
        return null;
      };

      const comment = findComment(comments, commentId);
      if (comment) {
        await toggleLike(
          commentId,
          comment.isLiked || false,
          comment.likes || 0
        );
      }
    } catch (error) {
      console.error('Failed to like/unlike comment:', error);
    }
  };

  const handleReplyToComment = (commentId: string, username: string) => {
    setReplyTo({ id: commentId, username });
  };

  const handleUserPress = (userId: string) => {
    // Navigate to user profile
    console.log('Navigate to user:', userId);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <GestureHandlerRootView style={styles.overlay}>
        <StatusBar backgroundColor="rgba(0, 0, 0, 0.5)" barStyle="light-content" />
        
        {/* Background overlay */}
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />

        {/* Comments container */}
        <PanGestureHandler
          onGestureEvent={handleGestureEvent}
          onHandlerStateChange={handleGestureStateChange}
        >
          <Animated.View
            style={[
              styles.container,
              {
                backgroundColor: theme.colors.background,
                height: modalHeight,
                transform: [{ translateY }],
              },
            ]}
          >
            {/* Drag handle */}
            <View style={styles.dragHandle} />

            {/* Header */}
            <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
              <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
                {formatCommentCount(commentCount)} comments
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons 
                  name="close" 
                  size={normalize(24)} 
                  color={theme.colors.text} 
                />
              </TouchableOpacity>
            </View>

            {/* Comments list */}
            <View style={styles.commentsContainer}>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              ) : comments.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                    No comments yet. Be the first to comment!
                  </Text>
                </View>
              ) : (
                <FlatList
                  data={comments}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <CommentItem
                      comment={item}
                      onLike={handleLikeComment}
                      onReply={handleReplyToComment}
                      onUserPress={handleUserPress}
                    />
                  )}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.commentsList}
                />
              )}
            </View>

            {/* Comment input */}
            <CommentInput
              onSubmit={handleSubmitComment}
              replyTo={replyTo}
              onCancelReply={() => setReplyTo(undefined)}
              isLoading={isCreating}
            />
          </Animated.View>
        </PanGestureHandler>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    borderTopLeftRadius: normalize(16),
    borderTopRightRadius: normalize(16),
    paddingTop: normalize(8),
  },
  dragHandle: {
    width: normalize(36),
    height: normalize(4),
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: normalize(2),
    alignSelf: 'center',
    marginBottom: normalize(16),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingBottom: normalize(12),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  closeButton: {
    padding: normalize(4),
  },
  commentsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(32),
  },
  emptyText: {
    fontSize: normalize(14),
    textAlign: 'center',
  },
  commentsList: {
    paddingHorizontal: normalize(16),
    paddingTop: normalize(16),
    paddingBottom: normalize(8),
  },
});

export default CommentsModal;
