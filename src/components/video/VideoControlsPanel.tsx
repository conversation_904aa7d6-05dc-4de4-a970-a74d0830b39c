import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { MusicTrack } from './MusicSelector';
import { normalize } from '../../utils/responsive';
import { estimateVideoSize } from '../../utils/videoGenerator';

interface VideoControlsPanelProps {
  selectedMusic: MusicTrack | null;
  videoDuration: number;
  videoQuality: 'low' | 'medium' | 'high';
  onAddText: () => void;
  onSelectMusic: () => void;
  onDurationChange: (duration: number) => void;
  onQualityChange: (quality: 'low' | 'medium' | 'high') => void;
}

const VideoControlsPanel: React.FC<VideoControlsPanelProps> = ({
  selectedMusic,
  videoDuration,
  videoQuality,
  onAddText,
  onSelectMusic,
  onDurationChange,
  onQualityChange,
}) => {
  const { theme } = useTheme();

  const durationOptions = [
    { label: '5s', value: 5 },
    { label: '10s', value: 10 },
    { label: '15s', value: 15 },
    { label: '30s', value: 30 },
    { label: '60s', value: 60 },
  ];

  const qualityOptions = [
    { label: 'Low', value: 'low' as const },
    { label: 'Medium', value: 'medium' as const },
    { label: 'High', value: 'high' as const },
  ];

  const estimatedSize = estimateVideoSize(videoDuration, videoQuality);
  const estimatedSizeMB = (estimatedSize / 1024 / 1024).toFixed(1);

  return (
    <ScrollView style={styles.controlsContainer} showsVerticalScrollIndicator={false}>
      {/* Text Controls */}
      <View style={[styles.controlSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Text</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={onAddText}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={styles.addButtonText}>Add Text</Text>
        </TouchableOpacity>
      </View>

      {/* Music Controls */}
      <View style={[styles.controlSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Music</Text>
        <TouchableOpacity
          style={[styles.musicButton, { backgroundColor: theme.colors.background }]}
          onPress={onSelectMusic}
        >
          <Ionicons name="musical-notes" size={20} color={theme.colors.primary} />
          <Text style={[styles.musicButtonText, { color: theme.colors.text }]}>
            {selectedMusic ? selectedMusic.title : 'Select Music'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Duration Controls */}
      <View style={[styles.controlSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Duration</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.optionsRow}>
            {durationOptions.map(option => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: videoDuration === option.value
                      ? theme.colors.primary
                      : theme.colors.background,
                  },
                ]}
                onPress={() => onDurationChange(option.value)}
              >
                <Text
                  style={[
                    styles.optionText,
                    {
                      color: videoDuration === option.value
                        ? '#FFFFFF'
                        : theme.colors.text,
                    },
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Quality Controls */}
      <View style={[styles.controlSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quality</Text>
        <View style={styles.optionsRow}>
          {qualityOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                {
                  backgroundColor: videoQuality === option.value
                    ? theme.colors.primary
                    : theme.colors.background,
                },
              ]}
              onPress={() => onQualityChange(option.value)}
            >
              <Text
                style={[
                  styles.optionText,
                  {
                    color: videoQuality === option.value
                      ? '#FFFFFF'
                      : theme.colors.text,
                  },
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <Text style={[styles.estimatedSize, { color: theme.colors.textSecondary }]}>
          Estimated size: ~{estimatedSizeMB}MB
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  controlsContainer: {
    flex: 1,
    padding: normalize(16),
  },
  controlSection: {
    marginBottom: normalize(16),
    padding: normalize(16),
    borderRadius: normalize(12),
  },
  sectionTitle: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(12),
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: normalize(12),
    borderRadius: normalize(8),
    gap: normalize(8),
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(16),
    fontWeight: '600',
  },
  musicButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: normalize(12),
    borderRadius: normalize(8),
    gap: normalize(8),
  },
  musicButtonText: {
    fontSize: normalize(16),
    flex: 1,
  },
  optionsRow: {
    flexDirection: 'row',
    gap: normalize(8),
  },
  optionButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
    minWidth: normalize(60),
    alignItems: 'center',
  },
  optionText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
  estimatedSize: {
    fontSize: normalize(12),
    marginTop: normalize(8),
    textAlign: 'center',
  },
});

export default VideoControlsPanel;
