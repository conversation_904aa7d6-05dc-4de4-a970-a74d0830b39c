import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Alert,
  Dimensions,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Button from '../common/Button';
import VideoMetadataForm, { VideoMetadata } from './VideoMetadataForm';
import { uploadFeedMedia } from '../../services/media/media-upload';
import { createVideo } from '../../services/database/videoService';
import logger from '../../utils/logger';

const { height: screenHeight } = Dimensions.get('window');

interface VideoUploadModalProps {
  visible: boolean;
  onClose: () => void;
  videoUri: string;
  thumbnailUri?: string;
  duration?: number;
  userId: string;
  musicId?: string;
  customMusicUrl?: string;
  onUploadComplete?: (videoId: string) => void;
}

const VideoUploadModal: React.FC<VideoUploadModalProps> = ({
  visible,
  onClose,
  videoUri,
  thumbnailUri,
  duration,
  userId,
  musicId,
  customMusicUrl,
  onUploadComplete,
}) => {
  const { theme } = useTheme();
  const [isUploading, setIsUploading] = useState(false);
  const [metadata, setMetadata] = useState<VideoMetadata>({
    title: '',
    description: '',
    sport: '',
    team_mentioned: '',
    type: 'For fun',
    tags: [],
    privacy_setting: 'public',
    allow_comments: true,
    allow_downloads: true,
    author: '',
    music_volume: 0.5,
    original_audio_volume: 1.0,
  });

  const handleSubmit = async () => {
    try {
      setIsUploading(true);
      logger.debug('Starting video upload process:', {
        videoUri,
        thumbnailUri,
        metadata,
        userId,
      });

      // Step 1: Upload video file to storage
      logger.debug('Uploading video file...');
      const videoUploadResult = await uploadFeedMedia(videoUri, userId);
      
      if (!videoUploadResult.publicUrl) {
        throw new Error('Video upload failed: no public URL returned');
      }

      // Step 2: Upload thumbnail if available
      let thumbnailUrl: string | undefined;
      if (thumbnailUri) {
        try {
          logger.debug('Uploading thumbnail...');
          const thumbnailUploadResult = await uploadFeedMedia(thumbnailUri, userId);
          thumbnailUrl = thumbnailUploadResult.publicUrl;
        } catch (thumbnailError) {
          logger.warn('Thumbnail upload failed, continuing without thumbnail:', thumbnailError);
          // Continue without thumbnail - not critical
        }
      }

      // Step 3: Create video record in database
      logger.debug('Creating video record in database...');
      const videoRecord = await createVideo({
        user_id: userId,
        title: metadata.title,
        description: metadata.description,
        video_url: videoUploadResult.publicUrl,
        thumbnail_url: thumbnailUrl,
        duration: duration,
        original_audio_volume: metadata.original_audio_volume,
        music_id: musicId,
        music_volume: metadata.music_volume,
        privacy_setting: metadata.privacy_setting,
        allow_comments: metadata.allow_comments,
        allow_downloads: metadata.allow_downloads,
        author: metadata.author,
        sport: metadata.sport,
        tags: metadata.tags,
        team_mentioned: metadata.team_mentioned,
        type: metadata.type,
        custom_music_url: customMusicUrl,
        is_draft: false, // Publish immediately
      });

      logger.debug('Video upload and database creation successful:', videoRecord);

      // Success!
      Alert.alert(
        'Success!',
        'Your video has been published successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              onClose();
              if (onUploadComplete) {
                onUploadComplete(videoRecord.video_id);
              }
            },
          },
        ]
      );

    } catch (error) {
      logger.error('Video upload process error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert(
        'Upload Failed',
        `Failed to upload video: ${errorMessage}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    if (isUploading) {
      Alert.alert(
        'Upload in Progress',
        'Your video is currently being uploaded. Are you sure you want to cancel?',
        [
          { text: 'Continue Uploading', style: 'cancel' },
          { 
            text: 'Cancel Upload', 
            style: 'destructive',
            onPress: onClose,
          },
        ]
      );
    } else {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
            <Text style={[styles.cancelText, { color: theme.colors.text }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Upload Video
          </Text>
          <Button
            title="Publish"
            onPress={handleSubmit}
            loading={isUploading}
            disabled={!metadata.title.trim() || isUploading}
            style={styles.publishButton}
          />
        </View>

        {/* Form */}
        <ScrollView style={styles.formContainer}>
          <VideoMetadataForm
            onDataChange={setMetadata}
            showMusicControls={!!musicId || !!customMusicUrl}
            initialData={{
              privacy_setting: 'public',
              allow_comments: true,
              allow_downloads: true,
              type: 'For fun',
              music_volume: 0.5,
              original_audio_volume: 1.0,
            }}
          />
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxHeight: screenHeight * 0.9,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  cancelButton: {
    padding: 8,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  publishButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  formContainer: {
    flex: 1,
  },
});

export default VideoUploadModal;
