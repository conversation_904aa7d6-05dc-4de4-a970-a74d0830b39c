import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import {
  GestureDetector,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { TextOverlay } from '../../utils/videoGenerator';

interface TextOverlayEditorProps {
  overlay: TextOverlay;
  onUpdate: (overlay: TextOverlay) => void;
  onDelete: (id: string) => void;
  isSelected: boolean;
  onSelect: (id: string) => void;
  containerWidth: number;
  containerHeight: number;
}

export const TextOverlayEditor: React.FC<TextOverlayEditorProps> = ({
  overlay,
  onUpdate,
  onDelete,
  isSelected,
  onSelect,
  containerWidth,
  containerHeight,
}) => {
  const { theme } = useTheme();
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(overlay.text);

  // Animated values
  const translateX = useSharedValue((overlay.x / 100) * containerWidth);
  const translateY = useSharedValue((overlay.y / 100) * containerHeight);
  const scale = useSharedValue(1);
  const rotation = useSharedValue(overlay.rotation || 0);

  // Update overlay position
  const updatePosition = (x: number, y: number) => {
    const percentX = Math.max(0, Math.min(100, (x / containerWidth) * 100));
    const percentY = Math.max(0, Math.min(100, (y / containerHeight) * 100));
    
    onUpdate({
      ...overlay,
      x: percentX,
      y: percentY,
    });
  };

  // Update overlay rotation
  const updateRotation = (rot: number) => {
    onUpdate({
      ...overlay,
      rotation: rot,
    });
  };

  // Pan gesture handler
  const panGesture = Gesture.Pan()
    .onStart(() => {
      runOnJS(onSelect)(overlay.id);
    })
    .onUpdate((event) => {
      translateX.value = event.translationX + (overlay.x / 100) * containerWidth;
      translateY.value = event.translationY + (overlay.y / 100) * containerHeight;
    })
    .onEnd(() => {
      runOnJS(updatePosition)(translateX.value, translateY.value);
    });

  // Pinch gesture handler
  const pinchGesture = Gesture.Pinch()
    .onUpdate((event) => {
      scale.value = event.scale;
    })
    .onEnd(() => {
      const newFontSize = Math.max(12, Math.min(72, overlay.fontSize * scale.value));
      runOnJS(onUpdate)({
        ...overlay,
        fontSize: newFontSize,
      });
      scale.value = 1;
    });

  // Rotation gesture handler
  const rotationGesture = Gesture.Rotation()
    .onUpdate((event) => {
      rotation.value = (overlay.rotation || 0) + event.rotation;
    })
    .onEnd(() => {
      runOnJS(updateRotation)(rotation.value);
    });

  // Combine gestures
  const composedGesture = Gesture.Simultaneous(panGesture, pinchGesture, rotationGesture);

  // Animated style
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
        { rotate: `${rotation.value}rad` },
      ],
    };
  });

  const handleTextSubmit = () => {
    if (editText.trim()) {
      onUpdate({
        ...overlay,
        text: editText.trim(),
      });
    }
    setIsEditing(false);
  };

  const handleDoubleTap = () => {
    setIsEditing(true);
    setEditText(overlay.text);
  };

  const handleSingleTap = () => {
    onSelect(overlay.id);
  };

  if (isEditing) {
    return (
      <View style={[
        styles.editingContainer,
        {
          left: Math.max(10, Math.min(containerWidth - 210, (overlay.x / 100) * containerWidth - 100)),
          top: Math.max(10, Math.min(containerHeight - 80, (overlay.y / 100) * containerHeight - 25)),
        }
      ]}>
        <TextInput
          style={[
            styles.textInput,
            {
              fontSize: Math.max(16, Math.min(24, overlay.fontSize)),
              color: overlay.color,
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.primary,
            }
          ]}
          value={editText}
          onChangeText={setEditText}
          onSubmitEditing={handleTextSubmit}
          onBlur={handleTextSubmit}
          autoFocus
          multiline
          placeholder="Enter text..."
          placeholderTextColor={theme.colors.textSecondary}
          returnKeyType="done"
          blurOnSubmit={true}
        />
      </View>
    );
  }

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View style={[styles.container, animatedStyle]}>
        <TouchableOpacity
          onPress={handleSingleTap}
          onLongPress={handleDoubleTap}
          delayLongPress={500}
          activeOpacity={0.8}
          style={[
            styles.textContainer,
            {
              backgroundColor: overlay.backgroundColor || 'rgba(0, 0, 0, 0.3)',
              borderColor: isSelected ? theme.colors.primary : 'transparent',
              borderWidth: isSelected ? 2 : 0,
              borderStyle: isSelected ? 'dashed' : 'solid',
            }
          ]}
        >
          <Text
            style={[
              styles.overlayText,
              {
                fontSize: overlay.fontSize,
                color: overlay.color,
                fontFamily: overlay.fontFamily,
              }
            ]}
          >
            {overlay.text}
          </Text>

          {isSelected && (
            <TouchableOpacity
              style={[styles.deleteButton, { backgroundColor: theme.colors.error }]}
              onPress={() => onDelete(overlay.id)}
            >
              <Ionicons name="close" size={16} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  textContainer: {
    padding: 8,
    borderRadius: 4,
    minWidth: 50,
    minHeight: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayText: {
    textAlign: 'center',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editingContainer: {
    position: 'absolute',
    width: 200,
    zIndex: 1000,
  },
  textInput: {
    borderWidth: 2,
    borderRadius: 8,
    padding: 12,
    minHeight: 50,
    textAlignVertical: 'center',
  },
});
