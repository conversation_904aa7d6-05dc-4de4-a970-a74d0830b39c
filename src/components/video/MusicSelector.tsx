import React, { useMemo, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { launchImageLibrary } from 'react-native-image-picker';
import { useMusicLibraryQuery } from '../../store/api/musicLibraryApi';

export interface MusicTrack {
  id: string;
  title: string;
  artist: string;
  duration: number;
  file_url: string;
  thumbnail_url?: string;
  genre?: string;
}

interface MusicSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelectMusic: (track: MusicTrack | null) => void;
  selectedTrack?: MusicTrack | null;
}

export const MusicSelector: React.FC<MusicSelectorProps> = ({
  visible,
  onClose,
  onSelectMusic,
  selectedTrack,
}) => {
  const { theme } = useTheme();

  // Fetch music library
  const { data: musicLibrary = [], isLoading, error } = useMusicLibraryQuery();

  // Use the music library directly (search functionality can be added later)
  const filteredTracks = useMemo(() => {
    return musicLibrary || [];
  }, [musicLibrary]);

  const handleSelectFromDevice = useCallback(() => {
    launchImageLibrary(
      {
        mediaType: 'mixed',
        quality: 0.8,
        selectionLimit: 1,
      },
      (response) => {
        if (response.assets && response.assets[0]) {
          const asset = response.assets[0];
          if (asset.type?.startsWith('audio') && asset.uri) {
            const customTrack: MusicTrack = {
              id: `custom_${Date.now()}`,
              title: asset.fileName || 'Custom Audio',
              artist: 'Unknown',
              duration: asset.duration || 30,
              file_url: asset.uri,
            };
            onSelectMusic(customTrack);
            onClose();
          } else {
            Alert.alert('Invalid File', 'Please select an audio file.');
          }
        }
      }
    );
  }, [onSelectMusic, onClose]);

  const handleSelectTrack = useCallback((track: MusicTrack) => {
    onSelectMusic(track);
    onClose();
  }, [onSelectMusic, onClose]);

  const handleRemoveMusic = useCallback(() => {
    onSelectMusic(null);
    onClose();
  }, [onSelectMusic, onClose]);

  const formatDuration = useCallback((seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const renderMusicItem = useCallback(({ item }: { item: MusicTrack }) => (
    <TouchableOpacity
      style={[
        styles.musicItem,
        {
          backgroundColor: theme.colors.surface,
          borderColor: selectedTrack?.id === item.id ? theme.colors.primary : theme.colors.border,
        }
      ]}
      onPress={() => handleSelectTrack(item)}
    >
      <View style={styles.musicInfo}>
        <View style={[styles.thumbnail, { backgroundColor: theme.colors.background }]}>
          {item.thumbnail_url ? (
            <Text>🎵</Text>
          ) : (
            <Ionicons name="musical-notes" size={24} color={theme.colors.primary} />
          )}
        </View>
        
        <View style={styles.trackDetails}>
          <Text style={[styles.trackTitle, { color: theme.colors.text }]} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={[styles.trackArtist, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {item.artist}
          </Text>
          <Text style={[styles.trackDuration, { color: theme.colors.textSecondary }]}>
            {formatDuration(item.duration)}
          </Text>
        </View>
      </View>

      {selectedTrack?.id === item.id && (
        <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  ), [theme.colors, selectedTrack?.id, handleSelectTrack, formatDuration]);

  const keyExtractor = useCallback((item: MusicTrack) => item.id, []);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Select Music
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSelectFromDevice}
            >
              <Ionicons name="folder-open" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>From Device</Text>
            </TouchableOpacity>

            {selectedTrack && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
                onPress={handleRemoveMusic}
              >
                <Ionicons name="trash" size={20} color="#FFFFFF" />
                <Text style={styles.actionButtonText}>Remove Music</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Current Selection */}
          {selectedTrack && (
            <View style={[styles.currentSelection, { backgroundColor: theme.colors.background }]}>
              <Text style={[styles.currentLabel, { color: theme.colors.textSecondary }]}>
                Current Selection:
              </Text>
              <Text style={[styles.currentTrack, { color: theme.colors.text }]}>
                {selectedTrack.title} - {selectedTrack.artist}
              </Text>
            </View>
          )}

          {/* Music Library */}
          <View style={styles.libraryContainer}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Music Library
            </Text>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Ionicons name="musical-notes" size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
                  Loading music library...
                </Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
                <Text style={[styles.errorText, { color: theme.colors.error }]}>
                  Failed to load music library
                </Text>
                <TouchableOpacity
                  style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
                  onPress={() => {
                    // Trigger a refetch of the music library
                    // In a real app, you'd call a refetch function
                    console.log('Retrying music library fetch...');
                  }}
                >
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={filteredTracks}
                renderItem={renderMusicItem}
                keyExtractor={keyExtractor}
                showsVerticalScrollIndicator={false}
                style={styles.musicList}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <Ionicons name="musical-notes-outline" size={48} color={theme.colors.textSecondary} />
                    <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                      No music tracks available
                    </Text>
                  </View>
                }
              />
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    height: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  currentSelection: {
    marginHorizontal: 20,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  currentLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  currentTrack: {
    fontSize: 14,
    fontWeight: '600',
  },
  libraryContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  musicList: {
    flex: 1,
  },
  musicItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  musicInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  thumbnail: {
    width: 48,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  trackDetails: {
    flex: 1,
  },
  trackTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  trackArtist: {
    fontSize: 14,
    marginBottom: 2,
  },
  trackDuration: {
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 12,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});
