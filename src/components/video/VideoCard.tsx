import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { VideoRecord } from '../../services/database/videoService';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - 48) / 2; // 2 columns with padding

interface VideoCardProps {
  video: VideoRecord;
  onPress: (video: VideoRecord) => void;
  onLike?: (videoId: string) => void;
  onShare?: (video: VideoRecord) => void;
  showStats?: boolean;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  onPress,
  onLike,
  onShare,
  showStats = true,
}) => {
  const { theme } = useTheme();
  const [imageError, setImageError] = useState(false);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTypeIcon = (type?: string): string => {
    switch (type) {
      case 'live': return 'radio-outline';
      case 'highlight': return 'star-outline';
      case 'training': return 'fitness-outline';
      case 'challenge': return 'trophy-outline';
      case 'analysis': return 'analytics-outline';
      case 'interview': return 'mic-outline';
      case 'esport': return 'game-controller-outline';
      default: return 'play-outline';
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.colors.surface }]}
      onPress={() => onPress(video)}
      activeOpacity={0.8}
    >
      {/* Thumbnail */}
      <View style={styles.thumbnailContainer}>
        {video.thumbnail_url && !imageError ? (
          <Image
            source={{ uri: video.thumbnail_url }}
            style={styles.thumbnail}
            onError={() => setImageError(true)}
          />
        ) : (
          <View style={[styles.thumbnailPlaceholder, { backgroundColor: theme.colors.border }]}>
            <Ionicons name="videocam-outline" size={32} color={theme.colors.textSecondary} />
          </View>
        )}
        
        {/* Duration Badge */}
        {video.duration && (
          <View style={[styles.durationBadge, { backgroundColor: 'rgba(0, 0, 0, 0.8)' }]}>
            <Text style={styles.durationText}>
              {formatDuration(video.duration)}
            </Text>
          </View>
        )}

        {/* Type Badge */}
        {video.type && (
          <View style={[styles.typeBadge, { backgroundColor: theme.colors.primary }]}>
            <Ionicons name={getTypeIcon(video.type) as any} size={12} color={theme.colors.surface} />
          </View>
        )}

        {/* Privacy Indicator */}
        {video.privacy_setting !== 'public' && (
          <View style={[styles.privacyBadge, { backgroundColor: 'rgba(0, 0, 0, 0.8)' }]}>
            <Ionicons 
              name={video.privacy_setting === 'private' ? 'lock-closed' : 'people'} 
              size={12} 
              color="#FFFFFF" 
            />
          </View>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Title */}
        <Text
          style={[styles.title, { color: theme.colors.text }]}
          numberOfLines={2}
        >
          {video.title || 'Untitled Video'}
        </Text>

        {/* Author & Sport */}
        <View style={styles.metaRow}>
          {video.author && (
            <Text style={[styles.author, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {video.author}
            </Text>
          )}
          {video.sport && (
            <Text style={[styles.sport, { color: theme.colors.primary }]} numberOfLines={1}>
              {video.sport}
            </Text>
          )}
        </View>

        {/* Stats */}
        {showStats && (
          <View style={styles.statsRow}>
            <View style={styles.stat}>
              <Ionicons name="eye-outline" size={12} color={theme.colors.textSecondary} />
              <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
                {formatNumber(video.views_count || video.views || 0)}
              </Text>
            </View>
            
            <View style={styles.stat}>
              <Ionicons name="heart-outline" size={12} color={theme.colors.textSecondary} />
              <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
                {formatNumber(video.likes_count || video.likes || 0)}
              </Text>
            </View>

            {video.allow_comments && (
              <View style={styles.stat}>
                <Ionicons name="chatbubble-outline" size={12} color={theme.colors.textSecondary} />
                <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
                  {formatNumber(video.comments_count || 0)}
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Tags */}
        {video.tags && video.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {video.tags.slice(0, 2).map((tag, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: theme.colors.border }]}>
                <Text style={[styles.tagText, { color: theme.colors.textSecondary }]}>
                  #{tag}
                </Text>
              </View>
            ))}
            {video.tags.length > 2 && (
              <Text style={[styles.moreTagsText, { color: theme.colors.textSecondary }]}>
                +{video.tags.length - 2}
              </Text>
            )}
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actions}>
        {onLike && (
          <TouchableOpacity
            onPress={() => onLike(video.video_id)}
            style={styles.actionButton}
          >
            <Ionicons name="heart-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
        
        {onShare && (
          <TouchableOpacity
            onPress={() => onShare(video)}
            style={styles.actionButton}
          >
            <Ionicons name="share-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: cardWidth,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  thumbnailContainer: {
    position: 'relative',
    aspectRatio: 16 / 9,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  thumbnailPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  typeBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  privacyBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
    lineHeight: 18,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  author: {
    fontSize: 12,
    flex: 1,
    marginRight: 8,
  },
  sport: {
    fontSize: 12,
    fontWeight: '500',
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 11,
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 10,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingBottom: 12,
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
});

export default VideoCard;
