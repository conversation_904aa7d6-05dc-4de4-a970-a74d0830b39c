import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { TextOverlayEditor } from './TextOverlayEditor';
import { TextOverlay } from '../../utils/videoGenerator';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const PREVIEW_HEIGHT = screenHeight * 0.6;
const PREVIEW_WIDTH = screenWidth;

interface VideoPreviewAreaProps {
  photoUri: string;
  textOverlays: TextOverlay[];
  selectedOverlayId: string | null;
  onUpdateOverlay: (overlay: TextOverlay) => void;
  onSelectOverlay: (id: string | null) => void;
  onDeleteOverlay: (id: string) => void;
}

const VideoPreviewArea: React.FC<VideoPreviewAreaProps> = ({
  photoUri,
  textOverlays,
  selectedOverlayId,
  onUpdateOverlay,
  onSelectOverlay,
  onDeleteOverlay,
}) => {
  const handleImageError = (error: any) => {
    console.error('Image load error:', error);
    Alert.alert('Error', 'Failed to load image. Please try again.');
  };

  const imageUri = photoUri.startsWith('file://') ? photoUri : `file://${photoUri}`;

  return (
    <View style={styles.previewContainer}>
      <Image
        source={{ uri: imageUri }}
        style={styles.previewImage}
        resizeMode="cover"
        onError={handleImageError}
      />

      {/* Text Overlays Container */}
      <View style={styles.overlaysContainer}>
        {textOverlays.map(overlay => (
          <TextOverlayEditor
            key={overlay.id}
            overlay={overlay}
            isSelected={selectedOverlayId === overlay.id}
            onUpdate={onUpdateOverlay}
            onSelect={() => onSelectOverlay(overlay.id)}
            onDelete={() => onDeleteOverlay(overlay.id)}
            containerWidth={PREVIEW_WIDTH}
            containerHeight={PREVIEW_HEIGHT}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    width: PREVIEW_WIDTH,
    height: PREVIEW_HEIGHT,
    position: 'relative',
    backgroundColor: '#000',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  overlaysContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default VideoPreviewArea;
