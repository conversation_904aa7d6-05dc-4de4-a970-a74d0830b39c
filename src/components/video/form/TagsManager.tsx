import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Text from '../../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../../utils/responsive';

interface TagsManagerProps {
  tags: string[];
  onAddTag: (tag: string) => void;
  onRemoveTag: (tag: string) => void;
  maxTags?: number;
  placeholder?: string;
}

const TagsManager: React.FC<TagsManagerProps> = ({
  tags,
  onAddTag,
  onRemoveTag,
  maxTags = 10,
  placeholder = "Add a tag...",
}) => {
  const { theme } = useTheme();
  const [tagInput, setTagInput] = useState('');

  const handleAddTag = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !tags.includes(trimmedTag) && tags.length < maxTags) {
      onAddTag(trimmedTag);
      setTagInput('');
    }
  };

  const handleKeyPress = (event: any) => {
    if (event.nativeEvent.key === 'Enter' || event.nativeEvent.key === ' ') {
      event.preventDefault();
      handleAddTag();
    }
  };

  return (
    <View style={styles.section}>
      <Text style={[styles.label, { color: theme.colors.text }]}>
        Tags ({tags.length}/{maxTags})
      </Text>
      
      {/* Tag Input */}
      <View style={styles.tagInputContainer}>
        <TextInput
          value={tagInput}
          onChangeText={setTagInput}
          placeholder={placeholder}
          style={[
            styles.tagInput,
            {
              backgroundColor: theme.colors.surface,
              color: theme.colors.text,
              borderColor: theme.colors.border,
            }
          ]}
          placeholderTextColor={theme.colors.textSecondary}
          onSubmitEditing={handleAddTag}
          onKeyPress={handleKeyPress}
          maxLength={20}
          editable={tags.length < maxTags}
        />
        <TouchableOpacity
          style={[
            styles.addTagButton,
            {
              backgroundColor: tagInput.trim() && tags.length < maxTags
                ? theme.colors.primary
                : theme.colors.border,
            }
          ]}
          onPress={handleAddTag}
          disabled={!tagInput.trim() || tags.length >= maxTags}
        >
          <Ionicons
            name="add"
            size={normalize(20)}
            color={tagInput.trim() && tags.length < maxTags ? '#FFFFFF' : theme.colors.textSecondary}
          />
        </TouchableOpacity>
      </View>

      {/* Tags Display */}
      {tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {tags.map((tag, index) => {
            // Safety check to ensure tag is a valid string
            if (!tag || typeof tag !== 'string') return null;
            
            return (
              <TouchableOpacity
                key={index}
                onPress={() => onRemoveTag(tag)}
                style={[styles.tag, { backgroundColor: theme.colors.primary }]}
              >
                <Text style={[styles.tagText, { color: theme.colors.surface }]}>
                  {tag}
                </Text>
                <Ionicons name="close" size={16} color={theme.colors.surface} />
              </TouchableOpacity>
            );
          })}
        </View>
      )}

      {/* Help Text */}
      <Text style={[styles.helpText, { color: theme.colors.textSecondary }]}>
        Press Enter or Space to add a tag. Tap on a tag to remove it.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: normalize(20),
  },
  label: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  tagInputContainer: {
    flexDirection: 'row',
    gap: normalize(8),
    marginBottom: normalize(12),
  },
  tagInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: normalize(8),
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(10),
    fontSize: normalize(16),
  },
  addTagButton: {
    width: normalize(44),
    height: normalize(44),
    borderRadius: normalize(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: normalize(8),
    marginBottom: normalize(8),
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
    borderRadius: normalize(16),
    gap: normalize(6),
  },
  tagText: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
  helpText: {
    fontSize: normalize(12),
    lineHeight: normalize(16),
  },
});

export default TagsManager;
