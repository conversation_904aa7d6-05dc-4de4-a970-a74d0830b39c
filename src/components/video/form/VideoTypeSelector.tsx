import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Text from '../../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../../utils/responsive';

export type VideoType = 'live' | 'highlight' | 'analysis' | 'interview' | 'training' | 'challenge' | 'For fun' | 'esport';

interface VideoTypeOption {
  value: VideoType;
  label: string;
  icon: string;
}

const VIDEO_TYPES: VideoTypeOption[] = [
  { value: 'For fun', label: 'For Fun', icon: 'happy-outline' },
  { value: 'highlight', label: 'Highlight', icon: 'star-outline' },
  { value: 'training', label: 'Training', icon: 'fitness-outline' },
  { value: 'challenge', label: 'Challenge', icon: 'trophy-outline' },
  { value: 'analysis', label: 'Analysis', icon: 'analytics-outline' },
  { value: 'interview', label: 'Interview', icon: 'mic-outline' },
  { value: 'live', label: 'Live', icon: 'radio-outline' },
  { value: 'esport', label: 'Esport', icon: 'game-controller-outline' },
];

interface VideoTypeSelectorProps {
  selectedType?: VideoType;
  onSelectType: (type: VideoType) => void;
}

const VideoTypeSelector: React.FC<VideoTypeSelectorProps> = ({
  selectedType,
  onSelectType,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.label, { color: theme.colors.text }]}>
        Video Type
      </Text>
      <View style={styles.typeGrid}>
        {VIDEO_TYPES.map((type) => (
          <TouchableOpacity
            key={type.value}
            style={[
              styles.typeOption,
              {
                backgroundColor: selectedType === type.value
                  ? theme.colors.primary
                  : theme.colors.surface,
                borderColor: selectedType === type.value
                  ? theme.colors.primary
                  : theme.colors.border,
              },
            ]}
            onPress={() => onSelectType(type.value)}
          >
            <Ionicons
              name={type.icon as any}
              size={normalize(20)}
              color={selectedType === type.value ? '#FFFFFF' : theme.colors.text}
            />
            <Text
              style={[
                styles.typeLabel,
                {
                  color: selectedType === type.value ? '#FFFFFF' : theme.colors.text,
                },
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: normalize(20),
  },
  label: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: normalize(8),
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
    borderWidth: 1,
    gap: normalize(6),
  },
  typeLabel: {
    fontSize: normalize(14),
    fontWeight: '500',
  },
});

export default VideoTypeSelector;
