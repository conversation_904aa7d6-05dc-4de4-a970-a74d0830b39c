import React from 'react';
import {
  View,
  StyleSheet,
  TextInput,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Text from '../../common/Text';
import { normalize } from '../../../utils/responsive';

interface VideoFormFieldProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  required?: boolean;
  editable?: boolean;
}

const VideoFormField: React.FC<VideoFormFieldProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  multiline = false,
  maxLength,
  required = false,
  editable = true,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.label, { color: theme.colors.text }]}>
        {label} {required && <Text style={{ color: theme.colors.error }}>*</Text>}
      </Text>
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        maxLength={maxLength}
        multiline={multiline}
        numberOfLines={multiline ? 4 : 1}
        editable={editable}
        style={[
          multiline ? styles.textArea : styles.input,
          {
            backgroundColor: theme.colors.surface,
            color: theme.colors.text,
            borderColor: theme.colors.border,
          }
        ]}
        placeholderTextColor={theme.colors.textSecondary}
      />
      {maxLength && (
        <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
          {value.length}/{maxLength}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: normalize(20),
  },
  label: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  input: {
    borderWidth: 1,
    borderRadius: normalize(8),
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(12),
    fontSize: normalize(16),
  },
  textArea: {
    borderWidth: 1,
    borderRadius: normalize(8),
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(12),
    fontSize: normalize(16),
    minHeight: normalize(100),
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: normalize(12),
    textAlign: 'right',
    marginTop: normalize(4),
  },
});

export default VideoFormField;
