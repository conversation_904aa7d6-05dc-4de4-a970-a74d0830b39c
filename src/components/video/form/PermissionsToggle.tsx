import React from 'react';
import {
  View,
  StyleSheet,
  Switch,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Text from '../../common/Text';
import { normalize } from '../../../utils/responsive';

interface PermissionItem {
  key: string;
  label: string;
  description: string;
  value: boolean;
}

interface PermissionsToggleProps {
  allowComments: boolean;
  allowDownloads: boolean;
  onToggleComments: (value: boolean) => void;
  onToggleDownloads: (value: boolean) => void;
}

const PermissionsToggle: React.FC<PermissionsToggleProps> = ({
  allowComments,
  allowDownloads,
  onToggleComments,
  onToggleDownloads,
}) => {
  const { theme } = useTheme();

  const permissions: PermissionItem[] = [
    {
      key: 'comments',
      label: 'Allow Comments',
      description: 'Let people comment on your video',
      value: allowComments,
    },
    {
      key: 'downloads',
      label: 'Allow Downloads',
      description: 'Let people download your video',
      value: allowDownloads,
    },
  ];

  const handleToggle = (key: string, value: boolean) => {
    switch (key) {
      case 'comments':
        onToggleComments(value);
        break;
      case 'downloads':
        onToggleDownloads(value);
        break;
    }
  };

  return (
    <View style={styles.section}>
      <Text style={[styles.label, { color: theme.colors.text }]}>
        Permissions
      </Text>
      
      {permissions.map((permission) => (
        <View
          key={permission.key}
          style={[
            styles.permissionItem,
            { backgroundColor: theme.colors.surface }
          ]}
        >
          <View style={styles.permissionContent}>
            <Text style={[styles.permissionLabel, { color: theme.colors.text }]}>
              {permission.label}
            </Text>
            <Text style={[styles.permissionDescription, { color: theme.colors.textSecondary }]}>
              {permission.description}
            </Text>
          </View>
          <Switch
            value={permission.value}
            onValueChange={(value) => handleToggle(permission.key, value)}
            trackColor={{
              false: theme.colors.border,
              true: theme.colors.primary + '80',
            }}
            thumbColor={permission.value ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: normalize(20),
  },
  label: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: normalize(16),
    borderRadius: normalize(8),
    marginBottom: normalize(8),
  },
  permissionContent: {
    flex: 1,
    marginRight: normalize(16),
  },
  permissionLabel: {
    fontSize: normalize(16),
    fontWeight: '500',
    marginBottom: normalize(2),
  },
  permissionDescription: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
});

export default PermissionsToggle;
