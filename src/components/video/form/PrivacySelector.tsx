import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import Text from '../../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../../utils/responsive';

export type PrivacySetting = 'public' | 'friends' | 'private';

interface PrivacyOption {
  value: PrivacySetting;
  label: string;
  icon: string;
  description: string;
}

const PRIVACY_OPTIONS: PrivacyOption[] = [
  { 
    value: 'public', 
    label: 'Public', 
    icon: 'globe-outline', 
    description: 'Anyone can see this video' 
  },
  { 
    value: 'friends', 
    label: 'Friends', 
    icon: 'people-outline', 
    description: 'Only your friends can see this' 
  },
  { 
    value: 'private', 
    label: 'Private', 
    icon: 'lock-closed-outline', 
    description: 'Only you can see this video' 
  },
];

interface PrivacySelectorProps {
  selectedPrivacy: PrivacySetting;
  onSelectPrivacy: (privacy: PrivacySetting) => void;
}

const PrivacySelector: React.FC<PrivacySelectorProps> = ({
  selectedPrivacy,
  onSelectPrivacy,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.label, { color: theme.colors.text }]}>
        Privacy
      </Text>
      {PRIVACY_OPTIONS.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.privacyOption,
            {
              backgroundColor: selectedPrivacy === option.value
                ? theme.colors.primary + '20'
                : theme.colors.surface,
              borderColor: selectedPrivacy === option.value
                ? theme.colors.primary
                : theme.colors.border,
            },
          ]}
          onPress={() => onSelectPrivacy(option.value)}
        >
          <View style={styles.privacyContent}>
            <View style={styles.privacyHeader}>
              <Ionicons
                name={option.icon as any}
                size={normalize(20)}
                color={selectedPrivacy === option.value ? theme.colors.primary : theme.colors.text}
              />
              <Text
                style={[
                  styles.privacyLabel,
                  {
                    color: selectedPrivacy === option.value ? theme.colors.primary : theme.colors.text,
                  },
                ]}
              >
                {option.label}
              </Text>
            </View>
            <Text
              style={[
                styles.privacyDescription,
                { color: theme.colors.textSecondary },
              ]}
            >
              {option.description}
            </Text>
          </View>
          {selectedPrivacy === option.value && (
            <Ionicons
              name="checkmark-circle"
              size={normalize(24)}
              color={theme.colors.primary}
            />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: normalize(20),
  },
  label: {
    fontSize: normalize(16),
    fontWeight: '600',
    marginBottom: normalize(8),
  },
  privacyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: normalize(16),
    borderRadius: normalize(8),
    borderWidth: 1,
    marginBottom: normalize(8),
  },
  privacyContent: {
    flex: 1,
  },
  privacyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(4),
    gap: normalize(8),
  },
  privacyLabel: {
    fontSize: normalize(16),
    fontWeight: '600',
  },
  privacyDescription: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
  },
});

export default PrivacySelector;
