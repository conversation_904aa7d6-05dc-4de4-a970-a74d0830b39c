import React from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Video from 'react-native-video';
import { normalize } from '../../utils/responsive';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VideoPreviewModalProps {
  visible: boolean;
  videoUri: string | null;
  onClose: () => void;
  onPublish: () => void;
}

const VideoPreviewModal: React.FC<VideoPreviewModalProps> = ({
  visible,
  videoUri,
  onClose,
  onPublish,
}) => {
  const { theme } = useTheme();

  return (
    <Modal
      animationType="slide"
      transparent={false}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={[styles.previewModal, { backgroundColor: theme.colors.background }]}>
        {/* Preview Header */}
        <View style={[styles.previewHeader, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.previewTitle, { color: theme.colors.text }]}>
            Preview
          </Text>
          <TouchableOpacity
            style={[styles.publishButton, { backgroundColor: theme.colors.primary }]}
            onPress={onPublish}
          >
            <Text style={styles.publishButtonText}>
              Publish
            </Text>
          </TouchableOpacity>
        </View>

        {/* Video Player */}
        <View style={styles.videoContainer}>
          {videoUri ? (
            <Video
              source={{ uri: videoUri }}
              style={styles.videoPlayer}
              controls={true}
              resizeMode="contain"
              repeat={true}
              onError={(error) => {
                console.error('Video playback error:', error);
              }}
            />
          ) : (
            <View style={[styles.placeholderContainer, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="videocam-outline" size={64} color={theme.colors.textSecondary} />
              <Text style={[styles.placeholderText, { color: theme.colors.textSecondary }]}>
                No video to preview
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  previewModal: {
    flex: 1,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  previewTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  publishButton: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    borderRadius: normalize(20),
  },
  publishButtonText: {
    color: '#FFFFFF',
    fontSize: normalize(16),
    fontWeight: '600',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  videoPlayer: {
    width: screenWidth,
    height: screenHeight * 0.7,
  },
  placeholderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: screenWidth * 0.8,
    height: screenHeight * 0.5,
    borderRadius: normalize(12),
  },
  placeholderText: {
    fontSize: normalize(16),
    marginTop: normalize(16),
    textAlign: 'center',
  },
});

export default VideoPreviewModal;
