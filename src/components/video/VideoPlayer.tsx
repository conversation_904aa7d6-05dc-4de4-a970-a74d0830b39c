import React, { useState, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Dimensions, Image, Text } from 'react-native';
import Video from 'react-native-video';
import { useTheme } from '../../contexts/ThemeContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useVideoPlayback } from '../../hooks/useVideoPlayback';
import logger from '../../utils/logger';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VideoPlayerProps {
  uri: string;
  thumbnail?: string;
  paused?: boolean;
  isActive?: boolean;
  onPress?: () => void;
  muted?: boolean;
  onLoad?: (data: any) => void;
  onProgress?: (data: any) => void;
  onEnd?: () => void;
  style?: any;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  uri,
  thumbnail,
  paused = false,
  isActive = true,
  onPress,
  muted = false,
  onLoad,
  onProgress,
  onEnd,
  style,
}) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef<any>(null);

  // Use custom video playback hook
  const { isPaused, showPlayButton, togglePlayPause } = useVideoPlayback(isActive, paused);

  const handleLoad = (data: any) => {
    setIsLoading(false);
    onLoad?.(data);
  };

  const handlePress = () => {
    togglePlayPause();
    onPress?.();
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      activeOpacity={1}
    >
      {/* Thumbnail for loading state or error fallback */}
      {(isLoading || hasError) && thumbnail && (
        <Image
          source={{ uri: thumbnail }}
          style={styles.thumbnail}
          resizeMode="cover"
        />
      )}

      {/* Fallback placeholder when no thumbnail and error */}
      {hasError && !thumbnail && (
        <View style={styles.errorPlaceholder}>
          <Ionicons name="videocam-off" size={60} color="rgba(255, 255, 255, 0.5)" />
        </View>
      )}

      {/* Video Player */}
      {uri ? (
        <Video
          ref={videoRef}
          source={{ uri }}
          style={styles.video}
          resizeMode="cover"
          repeat
          paused={isPaused}
          muted={muted}
          onLoad={handleLoad}
          onProgress={onProgress}
          onEnd={onEnd}
          onError={(error) => {
            logger.error('Video error for URI:', uri, error);
            setIsLoading(false);
            setHasError(true);
          }}
          onLoadStart={() => {
            setIsLoading(true);
            setHasError(false);
          }}
        />
      ) : (
        <View style={styles.errorPlaceholder}>
          <Ionicons name="videocam-off" size={60} color="rgba(255, 255, 255, 0.5)" />
          <Text style={styles.errorText}>Video URL missing</Text>
        </View>
      )}

      {/* Play/Pause Button Overlay */}
      {showPlayButton && (
        <View style={styles.playButtonOverlay}>
          <TouchableOpacity style={styles.playButton}>
            <Ionicons
              name={isPaused ? 'play' : 'pause'}
              size={60}
              color="rgba(255, 255, 255, 0.9)"
            />
          </TouchableOpacity>
        </View>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <Ionicons name="refresh" size={40} color="white" />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    height: screenHeight,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  thumbnail: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
  playButtonOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  playButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 50,
    padding: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 3,
  },
  errorPlaceholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    zIndex: 1,
  },
  errorText: {
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 10,
    fontSize: 16,
  },
});

export default VideoPlayer;
