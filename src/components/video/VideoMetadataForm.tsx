import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import VideoFormField from './form/VideoFormField';
import VideoTypeSelector, { VideoType } from './form/VideoTypeSelector';
import TagsManager from './form/TagsManager';
import PrivacySelector, { PrivacySetting } from './form/PrivacySelector';
import PermissionsToggle from './form/PermissionsToggle';
import { normalize } from '../../utils/responsive';

export interface VideoMetadata {
  title: string;
  description: string;
  sport: string;
  team_mentioned: string;
  type: VideoType;
  tags: string[];
  privacy_setting: PrivacySetting;
  allow_comments: boolean;
  allow_downloads: boolean;
  author?: string;
  music_volume?: number;
  original_audio_volume?: number;
}

interface VideoMetadataFormProps {
  initialData?: Partial<VideoMetadata>;
  onDataChange: (metadata: VideoMetadata) => void;
  showMusicControls?: boolean;
}

const VideoMetadataForm: React.FC<VideoMetadataFormProps> = ({
  initialData,
  onDataChange,
  showMusicControls = false,
}) => {
  const { theme } = useTheme();

  const [metadata, setMetadata] = useState<VideoMetadata>({
    title: '',
    description: '',
    sport: '',
    team_mentioned: '',
    type: 'For fun',
    tags: [],
    privacy_setting: 'public',
    allow_comments: true,
    allow_downloads: true,
    author: '',
    music_volume: 0.7,
    original_audio_volume: 0.3,
    ...initialData,
  });

  // Update parent component when metadata changes
  useEffect(() => {
    onDataChange(metadata);
  }, [metadata, onDataChange]);

  // Update handlers
  const updateField = <K extends keyof VideoMetadata>(
    field: K,
    value: VideoMetadata[K]
  ) => {
    setMetadata(prev => ({ ...prev, [field]: value }));
  };

  const handleAddTag = (tag: string) => {
    if (!metadata.tags.includes(tag) && metadata.tags.length < 10) {
      updateField('tags', [...metadata.tags, tag]);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    updateField('tags', metadata.tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.content}>
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Basic Information
          </Text>
          
          <VideoFormField
            label="Title"
            value={metadata.title}
            onChangeText={(text) => updateField('title', text)}
            placeholder="Give your video a catchy title..."
            maxLength={100}
            required
          />

          <VideoFormField
            label="Description"
            value={metadata.description}
            onChangeText={(text) => updateField('description', text)}
            placeholder="Tell people what your video is about..."
            multiline
            maxLength={500}
          />
        </View>

        {/* Video Type */}
        <VideoTypeSelector
          selectedType={metadata.type}
          onSelectType={(type) => updateField('type', type)}
        />

        {/* Sports Information */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Sports Information
          </Text>
          
          <VideoFormField
            label="Sport"
            value={metadata.sport}
            onChangeText={(text) => updateField('sport', text)}
            placeholder="e.g., Football, Basketball, Tennis..."
            maxLength={50}
          />

          <VideoFormField
            label="Team Mentioned"
            value={metadata.team_mentioned}
            onChangeText={(text) => updateField('team_mentioned', text)}
            placeholder="e.g., Manchester United, Lakers..."
            maxLength={50}
          />

          <VideoFormField
            label="Author"
            value={metadata.author || ''}
            onChangeText={(text) => updateField('author', text)}
            placeholder="Content creator or author name..."
            maxLength={50}
          />
        </View>

        {/* Tags */}
        <TagsManager
          tags={metadata.tags}
          onAddTag={handleAddTag}
          onRemoveTag={handleRemoveTag}
          maxTags={10}
          placeholder="Add a tag..."
        />

        {/* Privacy Settings */}
        <PrivacySelector
          selectedPrivacy={metadata.privacy_setting}
          onSelectPrivacy={(privacy) => updateField('privacy_setting', privacy)}
        />

        {/* Permissions */}
        <PermissionsToggle
          allowComments={metadata.allow_comments}
          allowDownloads={metadata.allow_downloads}
          onToggleComments={(value) => updateField('allow_comments', value)}
          onToggleDownloads={(value) => updateField('allow_downloads', value)}
        />

        {/* Music Controls (if music is selected) */}
        {showMusicControls && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Audio Settings
            </Text>

            <View style={styles.volumeControl}>
              <Text style={[styles.volumeLabel, { color: theme.colors.text }]}>
                Music Volume: {Math.round((metadata.music_volume || 0.7) * 100)}%
              </Text>
              {/* Volume slider would go here */}
              {/* TODO: Add slider component for music volume */}
            </View>

            <View style={styles.volumeControl}>
              <Text style={[styles.volumeLabel, { color: theme.colors.text }]}>
                Original Audio: {Math.round((metadata.original_audio_volume || 0.3) * 100)}%
              </Text>
              {/* Volume slider would go here */}
              {/* TODO: Add slider component for original audio volume */}
            </View>
          </View>
        )}

        <View style={styles.bottomSpace} />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: normalize(16),
  },
  section: {
    marginBottom: normalize(24),
  },
  sectionTitle: {
    fontSize: normalize(18),
    fontWeight: 'bold',
    marginBottom: normalize(16),
  },
  volumeControl: {
    marginBottom: normalize(16),
  },
  volumeLabel: {
    fontSize: normalize(14),
    fontWeight: '500',
    marginBottom: normalize(8),
  },
  bottomSpace: {
    height: normalize(40),
  },
});

export default VideoMetadataForm;
