import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { normalize } from '../../utils/responsive';

interface VideoInfoProps {
  username: string;
  description: string;
  song?: string;
  onUserPress: () => void;
  onSongPress?: () => void;
}

const VideoInfo: React.FC<VideoInfoProps> = ({
  username,
  description,
  song,
  onUserPress,
  onSongPress,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      {/* Username */}
      <TouchableOpacity onPress={onUserPress} style={styles.usernameContainer}>
        <Text style={[styles.username, { color: '#FFFFFF' }]}>
          @{username}
        </Text>
      </TouchableOpacity>

      {/* Description */}
      <Text style={[styles.description, { color: '#FFFFFF' }]} numberOfLines={3}>
        {description}
      </Text>

      {/* Song/Audio Info */}
      {song && (
        <TouchableOpacity
          style={styles.songContainer}
          onPress={onSongPress}
        >
          <Ionicons
            name="musical-notes"
            size={normalize(14)}
            color="#FFFFFF"
          />
          <Text
            style={[styles.songText, { color: '#FFFFFF' }]}
            numberOfLines={1}
          >
            {song}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: normalize(80), // Position just above navigation bar (~70px nav + 10px margin)
    left: normalize(12),
    right: normalize(70), // Leave space for action buttons
    zIndex: 10,
  },
  usernameContainer: {
    marginBottom: normalize(8),
  },
  username: {
    fontSize: normalize(16),
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  description: {
    fontSize: normalize(14),
    lineHeight: normalize(18),
    marginBottom: normalize(12),
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  songContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(6),
    borderRadius: normalize(20),
    alignSelf: 'flex-start',
  },
  songText: {
    fontSize: normalize(12),
    marginLeft: normalize(6),
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
});

export default VideoInfo;
