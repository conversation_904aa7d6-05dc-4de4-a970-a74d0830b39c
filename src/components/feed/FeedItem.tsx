import React, { useState } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import VideoPlayer from '../video/VideoPlayer';
import InteractionPanel from './InteractionPanel';
import VideoInfo from './VideoInfo';
import logger from '../../utils/logger';
import { useVideoLikes } from '../../hooks/api/useVideoLikes';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FeedItemProps {
  video: {
    id: string;
    uri: string;
    thumbnail?: string;
    description: string;
    song?: string;
    user: {
      id: string;
      username: string;
      full_name: string;
      avatar_url?: string;
    };
    likes: number;
    comments: number;
    shares: number;
    views: string;
  };
  isActive: boolean;
  onUserPress: (userId: string) => void;
  onVideoPress: (videoId: string) => void;
}

const FeedItem: React.FC<FeedItemProps> = ({
  video,
  isActive,
  onUserPress,
  onVideoPress,
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false);

  // Use the video likes hook
  const { likeCount, isLiked, toggleLike, isProcessing } = useVideoLikes(
    video.id,
    video.likes || 0
  );

  const handleLike = async () => {
    if (isProcessing) return;
    await toggleLike();
  };

  const handleComment = () => {
    // Navigate to comments screen
    logger.debug('Open comments for video:', video.id);
  };

  const handleShare = () => {
    // Open share modal
    logger.debug('Share video:', video.id);
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleUserPress = () => {
    onUserPress(video.user.id);
  };

  const handleVideoPress = () => {
    onVideoPress(video.id);
  };

  const handleSongPress = () => {
    // Navigate to song/sound page
    logger.debug('Open song:', video.song);
  };

  
  return (
    <View style={styles.container}>
      <VideoPlayer
        uri={video.uri}
        thumbnail={video.thumbnail}
        paused={!isActive}
        isActive={isActive}
        onPress={handleVideoPress}
        muted={false}
      />

      <VideoInfo
        username={video.user.username}
        description={video.description}
        song={video.song}
        onUserPress={handleUserPress}
        onSongPress={handleSongPress}
      />

      <InteractionPanel
        videoId={video.id}
        likes={likeCount}
        comments={video.comments || 0}
        shares={video.shares || 0}
        isLiked={isLiked}
        isBookmarked={isBookmarked}
        userAvatar={video.user.avatar_url}
        onLike={handleLike}
        onComment={handleComment}
        onShare={handleShare}
        onBookmark={handleBookmark}
        onUserPress={handleUserPress}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    height: screenHeight,
    backgroundColor: '#000',
  },
});

export default FeedItem;
