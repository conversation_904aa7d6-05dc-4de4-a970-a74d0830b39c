import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { normalize } from '../../utils/responsive';
import CommentsModal from '../comments/CommentsModal';
import { useVideoComments } from '../../hooks/api/useVideoComments';

interface InteractionPanelProps {
  videoId: string;
  likes: number;
  comments: number;
  shares: number;
  isLiked?: boolean;
  isBookmarked?: boolean;
  userAvatar?: string;
  onLike: () => void;
  onComment: () => void;
  onShare: () => void;
  onBookmark: () => void;
  onUserPress: () => void;
}

const InteractionPanel: React.FC<InteractionPanelProps> = ({
  videoId,
  likes,
  comments,
  shares,
  isLiked = false,
  isBookmarked = false,
  userAvatar,
  onLike,
  onComment,
  onShare,
  onBookmark,
  onUserPress,
}) => {
  const { theme } = useTheme();
  const [showComments, setShowComments] = useState(false);

  // Use the video comments hook for accurate count
  const { commentsCount, formattedCommentsCount } = useVideoComments({
    videoId,
    initialCommentsCount: comments,
  });

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <View style={styles.container}>
      {/* User Avatar */}
      <TouchableOpacity style={styles.avatarContainer} onPress={onUserPress}>
        <Image
          source={
            userAvatar
              ? { uri: userAvatar }
              : require('../../assets/images/default-avatar.png')
          }
          style={styles.avatar}
        />
        <View style={[styles.followButton, { backgroundColor: theme.colors.primary }]}>
          <Ionicons name="add" size={normalize(12)} color="#FFFFFF" />
        </View>
      </TouchableOpacity>

      {/* Like Button */}
      <TouchableOpacity style={styles.actionButton} onPress={onLike}>
        <Ionicons
          name={isLiked ? 'heart' : 'heart-outline'}
          size={normalize(32)}
          color={isLiked ? '#FF3040' : '#FFFFFF'}
        />
        <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
          {formatCount(likes)}
        </Text>
      </TouchableOpacity>

      {/* Comment Button */}
      <TouchableOpacity style={styles.actionButton} onPress={() => setShowComments(true)}>
        <Ionicons
          name="chatbubble-outline"
          size={normalize(30)}
          color="#FFFFFF"
        />
        <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
          {formattedCommentsCount}
        </Text>
      </TouchableOpacity>

      {/* Share Button */}
      <TouchableOpacity style={styles.actionButton} onPress={onShare}>
        <Ionicons
          name="arrow-redo-outline"
          size={normalize(30)}
          color="#FFFFFF"
        />
        <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
          {formatCount(shares)}
        </Text>
      </TouchableOpacity>

      {/* Bookmark Button */}
      <TouchableOpacity style={styles.actionButton} onPress={onBookmark}>
        <Ionicons
          name={isBookmarked ? 'bookmark' : 'bookmark-outline'}
          size={normalize(28)}
          color={isBookmarked ? '#FFD700' : '#FFFFFF'}
        />
      </TouchableOpacity>

      {/* More Options */}
      <TouchableOpacity style={styles.actionButton}>
        <MaterialCommunityIcons
          name="dots-horizontal"
          size={normalize(28)}
          color="#FFFFFF"
        />
      </TouchableOpacity>

      {/* Comments Modal */}
      <CommentsModal
        visible={showComments}
        onClose={() => setShowComments(false)}
        videoId={videoId}
        commentCount={commentsCount}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: normalize(8),
    bottom: normalize(140), // Moved up slightly to give more space for video info
    alignItems: 'center',
    zIndex: 10,
  },
  avatarContainer: {
    marginBottom: normalize(16),
    alignItems: 'center',
  },
  avatar: {
    width: normalize(46),
    height: normalize(46),
    borderRadius: normalize(23),
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  followButton: {
    position: 'absolute',
    bottom: normalize(-8),
    width: normalize(20),
    height: normalize(20),
    borderRadius: normalize(10),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  actionButton: {
    alignItems: 'center',
    marginBottom: normalize(16),
    paddingVertical: normalize(6),
  },
  actionText: {
    fontSize: normalize(12),
    fontWeight: '600',
    marginTop: normalize(4),
    textAlign: 'center',
  },
});

export default InteractionPanel;
