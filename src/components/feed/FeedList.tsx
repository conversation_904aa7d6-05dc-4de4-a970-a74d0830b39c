import React, { useState, useRef, useCallback, useEffect } from 'react';
import { FlatList, Dimensions, ViewToken } from 'react-native';
import FeedItem from './FeedItem';
import logger from '../../utils/logger';

const { height: screenHeight } = Dimensions.get('window');

interface FeedListProps {
  videos: Array<{
    id: string;
    uri: string;
    thumbnail?: string;
    description: string;
    song?: string;
    user: {
      id: string;
      username: string;
      full_name: string;
      avatar_url?: string;
    };
    likes: number;
    comments: number;
    shares: number;
    views: string;
  }>;
  onUserPress: (userId: string) => void;
  onVideoPress: (videoId: string) => void;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  refreshing?: boolean;
}

const FeedList: React.FC<FeedListProps> = ({
  videos,
  onUserPress,
  onVideoPress,
  onRefresh,
  onLoadMore,
  refreshing = false,
}) => {
  const [activeVideoIndex, setActiveVideoIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const onViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0) {
        const activeIndex = viewableItems[0].index;
        if (activeIndex !== null) {
          setActiveVideoIndex(activeIndex);
        }
      }
    },
    []
  );

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  const renderItem = ({ item, index }: { item: any; index: number }) => (
    <FeedItem
      video={item}
      isActive={index === activeVideoIndex}
      onUserPress={onUserPress}
      onVideoPress={onVideoPress}
    />
  );

  const getItemLayout = (_: any, index: number) => ({
    length: screenHeight,
    offset: screenHeight * index,
    index,
  });


  return (
    <FlatList
      ref={flatListRef}
      data={videos}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      pagingEnabled
      showsVerticalScrollIndicator={false}
      snapToInterval={screenHeight}
      snapToAlignment="start"
      decelerationRate="fast"
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={viewabilityConfig}
      getItemLayout={getItemLayout}
      onRefresh={onRefresh}
      refreshing={refreshing}
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.5}
      removeClippedSubviews={true}
      maxToRenderPerBatch={3}
      windowSize={5}
      initialNumToRender={2}
    />
  );
};

export default FeedList;
