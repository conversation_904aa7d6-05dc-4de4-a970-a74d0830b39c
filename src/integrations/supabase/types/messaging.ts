/**
 * TikTok-Style Messaging Database Types
 * Updated to match the new database schema
 */

import { Json, BaseRow, BaseInsert, BaseUpdate } from './base';

/**
 * Conversations table - TikTok-style messaging
 */
export interface ConversationRow extends BaseRow {
  type: 'direct' | 'group';
  name: string | null; // Group name (null for direct chats)
  description: string | null; // Group description
  avatar_url: string | null; // Group avatar (null for direct chats)
  created_by: string; // References public.users.id
  last_message_id: string | null;
  last_message_at: string | null;
  last_activity_at: string | null;
  is_active: boolean;
  is_archived: boolean;

  // Group-specific settings
  group_settings: {
    allow_members_to_add_others: boolean;
    allow_members_to_change_info: boolean;
    message_history_visible_to_new_members: boolean;
    disappearing_messages_timer: number | null;
  } | null;

  // Privacy and moderation
  is_public: boolean;
  invite_link: string | null;
  invite_link_expires_at: string | null;

  // Additional metadata
  metadata: <PERSON>son;
}

export interface ConversationInsert extends BaseInsert {
  type: 'direct' | 'group';
  name?: string | null;
  description?: string | null;
  avatar_url?: string | null;
  created_by: string;
  last_message_id?: string | null;
  last_message_at?: string | null;
  last_activity_at?: string | null;
  is_active?: boolean;
  is_archived?: boolean;
  group_settings?: {
    allow_members_to_add_others?: boolean;
    allow_members_to_change_info?: boolean;
    message_history_visible_to_new_members?: boolean;
    disappearing_messages_timer?: number | null;
  } | null;
  is_public?: boolean;
  invite_link?: string | null;
  invite_link_expires_at?: string | null;
  metadata?: Json;
}

export interface ConversationUpdate extends BaseUpdate {
  type?: 'direct' | 'group';
  name?: string | null;
  description?: string | null;
  avatar_url?: string | null;
  created_by?: string;
  last_message_id?: string | null;
  last_message_at?: string | null;
  last_activity_at?: string | null;
  is_active?: boolean;
  is_archived?: boolean;
  group_settings?: {
    allow_members_to_add_others?: boolean;
    allow_members_to_change_info?: boolean;
    message_history_visible_to_new_members?: boolean;
    disappearing_messages_timer?: number | null;
  } | null;
  is_public?: boolean;
  invite_link?: string | null;
  invite_link_expires_at?: string | null;
  metadata?: Json;
}

/**
 * Messages table - TikTok-style messaging
 */
export interface MessageRow extends BaseRow {
  conversation_id: string;
  sender_id: string; // References public.users.id

  // Message content
  content: string | null; // Can be null for media-only messages
  type: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'file' | 'gif' | 'sticker' | 'location' | 'contact' | 'system';

  // Media and file information
  media_url: string | null;
  thumbnail_url: string | null;
  file_name: string | null;
  file_size: number | null;
  file_type: string | null;
  duration: number | null; // For audio/video messages in seconds

  // Message relationships
  reply_to_message_id: string | null;
  forwarded_from_message_id: string | null;
  thread_id: string | null; // For threaded conversations

  // Message status and delivery
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

  // Read tracking (optimized for performance)
  read_by: string[];
  read_at: Json; // {user_id: timestamp}
  delivered_to: string[];
  delivered_at: Json; // {user_id: timestamp}

  // Message modifications
  is_edited: boolean;
  edited_at: string | null;
  edit_history: Json; // Array of previous content versions

  is_deleted: boolean;
  deleted_at: string | null;
  deleted_by: string | null;

  // Message features
  is_forwarded: boolean;
  forward_count: number;
  is_pinned: boolean;
  pinned_by: string | null;
  pinned_at: string | null;

  // Disappearing messages
  expires_at: string | null;
  is_expired: boolean;

  // Reactions (optimized structure)
  reaction_counts: Json; // {emoji: count}
  has_reactions: boolean; // For quick filtering

  // Additional metadata
  metadata: Json;
}

export interface MessageInsert extends BaseInsert {
  conversation_id: string;
  sender_id: string;
  content?: string | null;
  type?: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'file' | 'gif' | 'sticker' | 'location' | 'contact' | 'system';
  media_url?: string | null;
  thumbnail_url?: string | null;
  file_name?: string | null;
  file_size?: number | null;
  file_type?: string | null;
  duration?: number | null;
  reply_to_message_id?: string | null;
  forwarded_from_message_id?: string | null;
  thread_id?: string | null;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  read_by?: string[];
  read_at?: Json;
  delivered_to?: string[];
  delivered_at?: Json;
  is_edited?: boolean;
  edited_at?: string | null;
  edit_history?: Json;
  is_deleted?: boolean;
  deleted_at?: string | null;
  deleted_by?: string | null;
  is_forwarded?: boolean;
  forward_count?: number;
  is_pinned?: boolean;
  pinned_by?: string | null;
  pinned_at?: string | null;
  expires_at?: string | null;
  is_expired?: boolean;
  reaction_counts?: Json;
  has_reactions?: boolean;
  metadata?: Json;
}

export interface MessageUpdate extends BaseUpdate {
  conversation_id?: string;
  sender_id?: string;
  content?: string | null;
  type?: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'file' | 'gif' | 'sticker' | 'location' | 'contact' | 'system';
  media_url?: string | null;
  thumbnail_url?: string | null;
  file_name?: string | null;
  file_size?: number | null;
  file_type?: string | null;
  duration?: number | null;
  reply_to_message_id?: string | null;
  forwarded_from_message_id?: string | null;
  thread_id?: string | null;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  read_by?: string[];
  read_at?: Json;
  delivered_to?: string[];
  delivered_at?: Json;
  is_edited?: boolean;
  edited_at?: string | null;
  edit_history?: Json;
  is_deleted?: boolean;
  deleted_at?: string | null;
  deleted_by?: string | null;
  is_forwarded?: boolean;
  forward_count?: number;
  is_pinned?: boolean;
  pinned_by?: string | null;
  pinned_at?: string | null;
  expires_at?: string | null;
  is_expired?: boolean;
  reaction_counts?: Json;
  has_reactions?: boolean;
  metadata?: Json;
}

/**
 * Conversation participants table - TikTok-style
 */
export interface ConversationParticipantRow extends BaseRow {
  conversation_id: string;
  user_id: string; // References public.users.id

  // Participant role and permissions
  role: 'admin' | 'member' | 'moderator';

  // Participation tracking
  joined_at: string;
  left_at: string | null;
  is_active: boolean;

  // Message tracking for read receipts
  last_read_message_id: string | null;
  last_read_at: string | null;
  last_seen_at: string | null;

  // Notification preferences
  notification_settings: {
    muted: boolean;
    muted_until: string | null;
    push_notifications: boolean;
    message_preview: boolean;
    sound: boolean;
    vibration: boolean;
  };

  // Participant-specific settings
  custom_nickname: string | null; // Custom name for this participant in this chat
  is_pinned: boolean; // Pin conversation for this user
  is_archived: boolean; // Archive conversation for this user
}

export interface ConversationParticipantInsert extends BaseInsert {
  conversation_id: string;
  user_id: string;
  role?: 'admin' | 'member' | 'moderator';
  joined_at?: string;
  left_at?: string | null;
  is_active?: boolean;
  last_read_message_id?: string | null;
  last_read_at?: string | null;
  last_seen_at?: string | null;
  notification_settings?: {
    muted?: boolean;
    muted_until?: string | null;
    push_notifications?: boolean;
    message_preview?: boolean;
    sound?: boolean;
    vibration?: boolean;
  };
  custom_nickname?: string | null;
  is_pinned?: boolean;
  is_archived?: boolean;
}

export interface ConversationParticipantUpdate extends BaseUpdate {
  conversation_id?: string;
  user_id?: string;
  role?: 'admin' | 'member' | 'moderator';
  joined_at?: string;
  left_at?: string | null;
  is_active?: boolean;
  last_read_message_id?: string | null;
  last_read_at?: string | null;
  last_seen_at?: string | null;
  notification_settings?: {
    muted?: boolean;
    muted_until?: string | null;
    push_notifications?: boolean;
    message_preview?: boolean;
    sound?: boolean;
    vibration?: boolean;
  };
  custom_nickname?: string | null;
  is_pinned?: boolean;
  is_archived?: boolean;
}

/**
 * Message reactions table - TikTok-style emoji reactions
 */
export interface MessageReactionRow extends BaseRow {
  message_id: string;
  user_id: string; // References public.users.id

  // TikTok-style reaction types (emojis)
  reaction: string; // Store actual emoji or reaction code
  reaction_type: 'emoji' | 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry' | 'fire' | 'heart' | 'thumbs_up' | 'thumbs_down';
}

export interface MessageReactionInsert extends BaseInsert {
  message_id: string;
  user_id: string;
  reaction: string;
  reaction_type?: 'emoji' | 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry' | 'fire' | 'heart' | 'thumbs_up' | 'thumbs_down';
}

export interface MessageReactionUpdate extends BaseUpdate {
  message_id?: string;
  user_id?: string;
  reaction?: string;
  reaction_type?: 'emoji' | 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry' | 'fire' | 'heart' | 'thumbs_up' | 'thumbs_down';
}

/**
 * Message delivery tracking table
 */
export interface MessageDeliveryRow extends BaseRow {
  message_id: string;
  user_id: string; // References public.users.id
  status: 'sent' | 'delivered' | 'read' | 'failed';
  delivered_at: string | null;
  read_at: string | null;
  failed_reason: string | null;
}

export interface MessageDeliveryInsert extends BaseInsert {
  message_id: string;
  user_id: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  delivered_at?: string | null;
  read_at?: string | null;
  failed_reason?: string | null;
}

export interface MessageDeliveryUpdate extends BaseUpdate {
  message_id?: string;
  user_id?: string;
  status?: 'sent' | 'delivered' | 'read' | 'failed';
  delivered_at?: string | null;
  read_at?: string | null;
  failed_reason?: string | null;
}

/**
 * Typing indicators table
 */
export interface TypingIndicatorRow extends BaseRow {
  conversation_id: string;
  user_id: string; // References public.users.id
  is_typing: boolean;
  typing_type: 'text' | 'voice' | 'media';
  started_at: string;
  last_activity_at: string;
}

export interface TypingIndicatorInsert extends BaseInsert {
  conversation_id: string;
  user_id: string;
  is_typing?: boolean;
  typing_type?: 'text' | 'voice' | 'media';
  started_at?: string;
  last_activity_at?: string;
}

export interface TypingIndicatorUpdate extends BaseUpdate {
  conversation_id?: string;
  user_id?: string;
  is_typing?: boolean;
  typing_type?: 'text' | 'voice' | 'media';
  started_at?: string;
  last_activity_at?: string;
}

/**
 * User presence table
 */
export interface UserPresenceRow extends BaseRow {
  user_id: string; // References public.users.id
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen_at: string;
  is_active: boolean;
  device_info: Json;
}

export interface UserPresenceInsert extends BaseInsert {
  user_id: string;
  status?: 'online' | 'away' | 'busy' | 'offline';
  last_seen_at?: string;
  is_active?: boolean;
  device_info?: Json;
}

export interface UserPresenceUpdate extends BaseUpdate {
  user_id?: string;
  status?: 'online' | 'away' | 'busy' | 'offline';
  last_seen_at?: string;
  is_active?: boolean;
  device_info?: Json;
}

/**
 * Database table types for TikTok-style messaging
 */
export type ConversationsTable = {
  Row: ConversationRow;
  Insert: ConversationInsert;
  Update: ConversationUpdate;
  Relationships: [];
};

export type MessagesTable = {
  Row: MessageRow;
  Insert: MessageInsert;
  Update: MessageUpdate;
  Relationships: [];
};

export type ConversationParticipantsTable = {
  Row: ConversationParticipantRow;
  Insert: ConversationParticipantInsert;
  Update: ConversationParticipantUpdate;
  Relationships: [];
};

export type MessageReactionsTable = {
  Row: MessageReactionRow;
  Insert: MessageReactionInsert;
  Update: MessageReactionUpdate;
  Relationships: [];
};

export type MessageDeliveryTable = {
  Row: MessageDeliveryRow;
  Insert: MessageDeliveryInsert;
  Update: MessageDeliveryUpdate;
  Relationships: [];
};

export type TypingIndicatorsTable = {
  Row: TypingIndicatorRow;
  Insert: TypingIndicatorInsert;
  Update: TypingIndicatorUpdate;
  Relationships: [];
};

export type UserPresenceTable = {
  Row: UserPresenceRow;
  Insert: UserPresenceInsert;
  Update: UserPresenceUpdate;
  Relationships: [];
};
