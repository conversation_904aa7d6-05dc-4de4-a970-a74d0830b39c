/**
 * Supabase types index - Re-exports all database types
 * 
 * This file provides a clean interface for importing database types
 * throughout the application.
 */

// Main database schema
export type { Database } from './database';

// Base types and utilities
export * from './base';

// User-related types
export * from './user';

// Messaging-related types
export * from './messaging';

// Video-related types
export * from './video';

// Type helpers
export type {
  Tables,
  TablesInsert,
  TablesUpdate,
  Enums,
  Json
} from './database';
