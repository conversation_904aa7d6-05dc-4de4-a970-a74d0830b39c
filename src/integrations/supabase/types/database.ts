/**
 * Main database schema combining all table types
 */

import { Json } from './base';
import { 
  UserTable, 
  UserFollowersTable, 
  BlockedUsersTable, 
  UserSettingsTable 
} from './user';
import {
  ConversationsTable,
  MessagesTable,
  ConversationParticipantsTable,
  MessageReactionsTable,
  MessageDeliveryTable,
  TypingIndicatorsTable,
  UserPresenceTable
} from './messaging';
import { 
  VideosTable, 
  VideoLikesTable, 
  VideoCommentsTable, 
  MusicLibraryTable 
} from './video';

/**
 * Complete database schema
 */
export type Database = {
  public: {
    Tables: {
      blocked_users: BlockedUsersTable;
      conversations: ConversationsTable;
      conversation_participants: ConversationParticipantsTable;
      messages: MessagesTable;
      message_reactions: MessageReactionsTable;
      message_delivery: MessageDeliveryTable;
      typing_indicators: TypingIndicatorsTable;
      user_presence: UserPresenceTable;
      music_library: MusicLibraryTable;
      users: UserTable;
      user_followers: UserFollowersTable;
      user_settings: UserSettingsTable;
      videos: VideosTable;
      video_comments: VideoCommentsTable;
      video_likes: VideoLikesTable;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      create_conversation: {
        Args: {
          participant_ids: string[];
          conversation_type: string;
          conversation_name?: string;
        };
        Returns: string;
      };
      delete_user_video: {
        Args: {
          video_id_param: string;
          user_id_param: string;
        };
        Returns: undefined;
      };
      get_fingerprint_account_count: {
        Args: {
          fingerprint_param: string;
        };
        Returns: number;
      };
      get_mac_address_count: {
        Args: {
          mac_address_param: string;
        };
        Returns: number;
      };
      is_conversation_participant: {
        Args: {
          conversation_id_param: string;
          user_id_param: string;
        };
        Returns: boolean;
      };
      send_message: {
        Args: {
          conversation_id_param: string;
          sender_id_param: string;
          content_param: string;
          message_type_param: string;
          file_url_param?: string;
          file_type_param?: string;
          file_name_param?: string;
          file_size_param?: number;
          thumbnail_url_param?: string;
          parent_message_id_param?: string;
        };
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

/**
 * Type helpers for easier access
 */
export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"])
  ? (Database["public"]["Tables"])[PublicTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"])[TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"])
  ? (Database["public"]["Tables"])[PublicTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"])[TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"])
  ? (Database["public"]["Tables"])[PublicTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof (Database["public"]["Enums"])
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicEnumNameOrOptions["schema"]]["Enums"])
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicEnumNameOrOptions["schema"]]["Enums"])[EnumName]
  : PublicEnumNameOrOptions extends keyof (Database["public"]["Enums"])
  ? (Database["public"]["Enums"])[PublicEnumNameOrOptions]
  : never;

/**
 * Re-export common types
 */
export type { Json } from './base';
export * from './user';
export * from './messaging';
export * from './video';
export * from './base';
