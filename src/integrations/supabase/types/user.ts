/**
 * User-related database types
 */

import { Json, BaseRow, BaseInsert, BaseUpdate, UserMetadata } from './base';

export interface UserRow extends BaseRow {
  avatar_url: string | null;
  bio: string | null;
  date_of_birth: string | null;
  email: string;
  first_name: string | null;
  full_name: string | null;
  last_name: string | null;
  phone_number: string | null;
  profile_picture_url: string | null;
  username: string;
  followers_count: number | null;
  following_count: number | null;
  is_verified: boolean | null;
  location: string | null;
  website: string | null;
  metadata: UserMetadata | null;
  last_seen: string | null;
  status: string | null;
  privacy_settings: Json | null;
  notification_settings: Json | null;
}

export interface UserInsert extends BaseInsert {
  avatar_url?: string | null;
  bio?: string | null;
  date_of_birth?: string | null;
  email: string;
  first_name?: string | null;
  full_name?: string | null;
  last_name?: string | null;
  phone_number?: string | null;
  profile_picture_url?: string | null;
  username: string;
  followers_count?: number | null;
  following_count?: number | null;
  is_verified?: boolean | null;
  location?: string | null;
  website?: string | null;
  metadata?: UserMetadata | null;
  last_seen?: string | null;
  status?: string | null;
  privacy_settings?: Json | null;
  notification_settings?: Json | null;
}

export interface UserUpdate extends BaseUpdate {
  avatar_url?: string | null;
  bio?: string | null;
  date_of_birth?: string | null;
  email?: string;
  first_name?: string | null;
  full_name?: string | null;
  last_name?: string | null;
  phone_number?: string | null;
  profile_picture_url?: string | null;
  username?: string;
  followers_count?: number | null;
  following_count?: number | null;
  is_verified?: boolean | null;
  location?: string | null;
  website?: string | null;
  metadata?: UserMetadata | null;
  last_seen?: string | null;
  status?: string | null;
  privacy_settings?: Json | null;
  notification_settings?: Json | null;
}

/**
 * User followers table
 */
export interface UserFollowerRow extends BaseRow {
  follower_id: string;
  following_id: string;
  followed_at: string;
}

export interface UserFollowerInsert extends BaseInsert {
  follower_id: string;
  following_id: string;
  followed_at?: string;
}

export interface UserFollowerUpdate extends BaseUpdate {
  follower_id?: string;
  following_id?: string;
  followed_at?: string;
}

/**
 * Blocked users table
 */
export interface BlockedUserRow extends BaseRow {
  blocked_user_id: string;
  blocker_user_id: string;
}

export interface BlockedUserInsert extends BaseInsert {
  blocked_user_id: string;
  blocker_user_id: string;
}

export interface BlockedUserUpdate extends BaseUpdate {
  blocked_user_id?: string;
  blocker_user_id?: string;
}

/**
 * User settings table
 */
export interface UserSettingsRow extends BaseRow {
  user_id: string;
  theme: string | null;
  language: string | null;
  notifications_enabled: boolean | null;
  privacy_level: string | null;
  two_factor_enabled: boolean | null;
  email_notifications: boolean | null;
  push_notifications: boolean | null;
  sms_notifications: boolean | null;
  marketing_emails: boolean | null;
  settings_data: Json | null;
}

export interface UserSettingsInsert extends BaseInsert {
  user_id: string;
  theme?: string | null;
  language?: string | null;
  notifications_enabled?: boolean | null;
  privacy_level?: string | null;
  two_factor_enabled?: boolean | null;
  email_notifications?: boolean | null;
  push_notifications?: boolean | null;
  sms_notifications?: boolean | null;
  marketing_emails?: boolean | null;
  settings_data?: Json | null;
}

export interface UserSettingsUpdate extends BaseUpdate {
  user_id?: string;
  theme?: string | null;
  language?: string | null;
  notifications_enabled?: boolean | null;
  privacy_level?: string | null;
  two_factor_enabled?: boolean | null;
  email_notifications?: boolean | null;
  push_notifications?: boolean | null;
  sms_notifications?: boolean | null;
  marketing_emails?: boolean | null;
  settings_data?: Json | null;
}

/**
 * User database operations
 */
export type UserTable = {
  Row: UserRow;
  Insert: UserInsert;
  Update: UserUpdate;
  Relationships: [];
};

export type UserFollowersTable = {
  Row: UserFollowerRow;
  Insert: UserFollowerInsert;
  Update: UserFollowerUpdate;
  Relationships: [];
};

export type BlockedUsersTable = {
  Row: BlockedUserRow;
  Insert: BlockedUserInsert;
  Update: BlockedUserUpdate;
  Relationships: [];
};

export type UserSettingsTable = {
  Row: UserSettingsRow;
  Insert: UserSettingsInsert;
  Update: UserSettingsUpdate;
  Relationships: [];
};
