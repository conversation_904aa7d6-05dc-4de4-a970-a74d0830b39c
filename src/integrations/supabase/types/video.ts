/**
 * Video-related database types
 */

import { <PERSON><PERSON>, BaseRow, BaseInsert, BaseUpdate } from './base';

/**
 * Videos table
 */
export interface VideoRow extends BaseRow {
  user_id: string;
  title: string | null;
  description: string | null;
  video_url: string;
  thumbnail_url: string | null;
  duration: number | null;
  original_audio_volume: number | null;
  music_id: string | null;
  music_volume: number | null;
  privacy_setting: string;
  allow_comments: boolean | null;
  allow_downloads: boolean | null;
  views_count: number | null;
  likes_count: number | null;
  comments_count: number | null;
  shares_count: number | null;
  is_draft: boolean | null;
  published_at: string | null;
  author: string | null;
  duration_sec: number | null;
  likes: number | null;
  sport: string | null;
  tags: string[] | null;
  team_mentioned: string | null;
  type: string | null;
  upload_date: string | null;
  video_id: string;
  views: number | null;
  custom_music_url: string | null;
}

export interface VideoInsert extends BaseInsert {
  user_id: string;
  title?: string | null;
  description?: string | null;
  video_url: string;
  thumbnail_url?: string | null;
  duration?: number | null;
  original_audio_volume?: number | null;
  music_id?: string | null;
  music_volume?: number | null;
  privacy_setting?: string;
  allow_comments?: boolean | null;
  allow_downloads?: boolean | null;
  views_count?: number | null;
  likes_count?: number | null;
  comments_count?: number | null;
  shares_count?: number | null;
  is_draft?: boolean | null;
  published_at?: string | null;
  author?: string | null;
  duration_sec?: number | null;
  likes?: number | null;
  sport?: string | null;
  tags?: string[] | null;
  team_mentioned?: string | null;
  type?: string | null;
  upload_date?: string | null;
  video_id: string;
  views?: number | null;
  custom_music_url?: string | null;
}

export interface VideoUpdate extends BaseUpdate {
  user_id?: string;
  title?: string | null;
  description?: string | null;
  video_url?: string;
  thumbnail_url?: string | null;
  duration?: number | null;
  original_audio_volume?: number | null;
  music_id?: string | null;
  music_volume?: number | null;
  privacy_setting?: string;
  allow_comments?: boolean | null;
  allow_downloads?: boolean | null;
  views_count?: number | null;
  likes_count?: number | null;
  comments_count?: number | null;
  shares_count?: number | null;
  is_draft?: boolean | null;
  published_at?: string | null;
  author?: string | null;
  duration_sec?: number | null;
  likes?: number | null;
  sport?: string | null;
  tags?: string[] | null;
  team_mentioned?: string | null;
  type?: string | null;
  upload_date?: string | null;
  video_id?: string;
  views?: number | null;
  custom_music_url?: string | null;
}

/**
 * Video likes table
 */
export interface VideoLikeRow extends BaseRow {
  user_id: string;
  video_id: string;
  liked_at: string;
}

export interface VideoLikeInsert extends BaseInsert {
  user_id: string;
  video_id: string;
  liked_at?: string;
}

export interface VideoLikeUpdate extends BaseUpdate {
  user_id?: string;
  video_id?: string;
  liked_at?: string;
}

/**
 * Video comments table
 */
export interface VideoCommentRow extends BaseRow {
  video_id: string;
  user_id: string;
  content: string;
  parent_comment_id: string | null;
  likes_count: number | null;
  is_edited: boolean | null;
  edited_at: string | null;
  is_deleted: boolean | null;
  deleted_at: string | null;
}

export interface VideoCommentInsert extends BaseInsert {
  video_id: string;
  user_id: string;
  content: string;
  parent_comment_id?: string | null;
  likes_count?: number | null;
  is_edited?: boolean | null;
  edited_at?: string | null;
  is_deleted?: boolean | null;
  deleted_at?: string | null;
}

export interface VideoCommentUpdate extends BaseUpdate {
  video_id?: string;
  user_id?: string;
  content?: string;
  parent_comment_id?: string | null;
  likes_count?: number | null;
  is_edited?: boolean | null;
  edited_at?: string | null;
  is_deleted?: boolean | null;
  deleted_at?: string | null;
}

/**
 * Music library table
 */
export interface MusicLibraryRow extends BaseRow {
  title: string;
  artist: string | null;
  duration: number | null;
  file_url: string;
  genre: string | null;
  is_premium: boolean | null;
  thumbnail_url: string | null;
  album: string | null;
  year: number | null;
  bpm: number | null;
  mood: string | null;
  tags: string[] | null;
}

export interface MusicLibraryInsert extends BaseInsert {
  title: string;
  artist?: string | null;
  duration?: number | null;
  file_url: string;
  genre?: string | null;
  is_premium?: boolean | null;
  thumbnail_url?: string | null;
  album?: string | null;
  year?: number | null;
  bpm?: number | null;
  mood?: string | null;
  tags?: string[] | null;
}

export interface MusicLibraryUpdate extends BaseUpdate {
  title?: string;
  artist?: string | null;
  duration?: number | null;
  file_url?: string;
  genre?: string | null;
  is_premium?: boolean | null;
  thumbnail_url?: string | null;
  album?: string | null;
  year?: number | null;
  bpm?: number | null;
  mood?: string | null;
  tags?: string[] | null;
}

/**
 * Database table types
 */
export type VideosTable = {
  Row: VideoRow;
  Insert: VideoInsert;
  Update: VideoUpdate;
  Relationships: [];
};

export type VideoLikesTable = {
  Row: VideoLikeRow;
  Insert: VideoLikeInsert;
  Update: VideoLikeUpdate;
  Relationships: [];
};

export type VideoCommentsTable = {
  Row: VideoCommentRow;
  Insert: VideoCommentInsert;
  Update: VideoCommentUpdate;
  Relationships: [];
};

export type MusicLibraryTable = {
  Row: MusicLibraryRow;
  Insert: MusicLibraryInsert;
  Update: MusicLibraryUpdate;
  Relationships: [];
};
