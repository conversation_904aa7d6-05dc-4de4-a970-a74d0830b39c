/**
 * Base types for Supabase integration
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

/**
 * Common database operation types
 */
export interface BaseRow {
  id: string;
  created_at: string;
  updated_at?: string;
}

export interface BaseInsert {
  id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface BaseUpdate {
  id?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Common relationship patterns
 */
export interface UserRelationship {
  user_id: string;
}

export interface TimestampFields {
  created_at: string;
  updated_at: string;
}

/**
 * Metadata types
 */
export interface ConversationMetadata {
  theme?: string;
  notifications?: boolean;
  archived?: boolean;
  pinned?: boolean;
}

export interface MessageMetadata {
  edited?: boolean;
  edited_at?: string;
  reply_to?: string;
  forwarded?: boolean;
  reactions?: Record<string, string[]>;
}

export interface UserMetadata {
  preferences?: Record<string, any>;
  settings?: Record<string, any>;
  onboarding_completed?: boolean;
}

/**
 * Status enums
 */
export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

export enum ConversationType {
  DIRECT = 'direct',
  GROUP = 'group',
  CHANNEL = 'channel'
}

export enum UserStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  AWAY = 'away',
  BUSY = 'busy'
}

/**
 * Common utility types
 */
export type DatabaseOperation<T> = {
  Row: T;
  Insert: Partial<T>;
  Update: Partial<T>;
  Relationships: any[];
};

export type TableName = 
  | 'users'
  | 'conversations'
  | 'messages'
  | 'videos'
  | 'music_library'
  | 'blocked_users'
  | 'conversation_participants'
  | 'message_reactions'
  | 'user_followers'
  | 'user_settings'
  | 'video_likes'
  | 'video_comments';
