// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { Database } from './types';

const SUPABASE_URL = 'https://nllfhiuufnpqacalkmef.supabase.co';
const SUPABASE_PUBLISHABLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sbGZoaXV1Zm5wcWFjYWxrbWVmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzMTIxNDIsImV4cCI6MjA2MDg4ODE0Mn0.j77REsS7i2wBjD1qa-yrC27D5Bb0GgZ5DvBvSx73heQ';

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  },
);
