export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      blocked_users: {
        Row: {
          blocked_user_id: string
          blocker_user_id: string
          created_at: string
          id: string
        }
        Insert: {
          blocked_user_id: string
          blocker_user_id: string
          created_at?: string
          id?: string
        }
        Update: {
          blocked_user_id?: string
          blocker_user_id?: string
          created_at?: string
          id?: string
        }
        Relationships: []
      }
      comment_likes: {
        Row: {
          comment_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          comment_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          comment_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          created_at: string
          id: string
          parent_id: string | null
          text: string
          updated_at: string
          user_id: string
          video_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          parent_id?: string | null
          text: string
          updated_at?: string
          user_id: string
          video_id: string
        }
        Update: {
          created_at?: string
          id?: string
          parent_id?: string | null
          text?: string
          updated_at?: string
          user_id?: string
          video_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      conversation_participants: {
        Row: {
          conversation_id: string
          custom_nickname: string | null
          id: string
          is_active: boolean
          is_archived: boolean | null
          is_pinned: boolean | null
          joined_at: string
          last_read_at: string | null
          last_read_message_id: string | null
          left_at: string | null
          notification_settings: Json | null
          role: string
          user_id: string
        }
        Insert: {
          conversation_id: string
          custom_nickname?: string | null
          id?: string
          is_active?: boolean
          is_archived?: boolean | null
          is_pinned?: boolean | null
          joined_at?: string
          last_read_at?: string | null
          last_read_message_id?: string | null
          left_at?: string | null
          notification_settings?: Json | null
          role?: string
          user_id: string
        }
        Update: {
          conversation_id?: string
          custom_nickname?: string | null
          id?: string
          is_active?: boolean
          is_archived?: boolean | null
          is_pinned?: boolean | null
          joined_at?: string
          last_read_at?: string | null
          last_read_message_id?: string | null
          left_at?: string | null
          notification_settings?: Json | null
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          avatar_url: string | null
          created_at: string
          created_by: string
          description: string | null
          group_settings: Json | null
          id: string
          invite_link: string | null
          invite_link_expires_at: string | null
          is_active: boolean
          is_archived: boolean | null
          is_public: boolean | null
          last_activity_at: string | null
          last_message_at: string | null
          last_message_id: string | null
          metadata: Json
          name: string | null
          participants: string[]
          type: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          created_by: string
          description?: string | null
          group_settings?: Json | null
          id?: string
          invite_link?: string | null
          invite_link_expires_at?: string | null
          is_active?: boolean
          is_archived?: boolean | null
          is_public?: boolean | null
          last_activity_at?: string | null
          last_message_at?: string | null
          last_message_id?: string | null
          metadata?: Json
          name?: string | null
          participants?: string[]
          type: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          created_by?: string
          description?: string | null
          group_settings?: Json | null
          id?: string
          invite_link?: string | null
          invite_link_expires_at?: string | null
          is_active?: boolean
          is_archived?: boolean | null
          is_public?: boolean | null
          last_activity_at?: string | null
          last_message_at?: string | null
          last_message_id?: string | null
          metadata?: Json
          name?: string | null
          participants?: string[]
          type?: string
          updated_at?: string
        }
        Relationships: []
      }
      data_export_requests: {
        Row: {
          completed_at: string | null
          created_at: string | null
          expires_at: string | null
          export_url: string | null
          id: string
          request_type: string
          status: string
          user_id: string
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          export_url?: string | null
          id?: string
          request_type?: string
          status?: string
          user_id: string
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          export_url?: string | null
          id?: string
          request_type?: string
          status?: string
          user_id?: string
        }
        Relationships: []
      }
      login_sessions: {
        Row: {
          created_at: string
          device_info: Json | null
          id: string
          ip_address: string
          is_active: boolean | null
          location_info: Json | null
          login_time: string
          logout_time: string | null
          session_id: string
          user_agent: string
          user_id: string
        }
        Insert: {
          created_at?: string
          device_info?: Json | null
          id?: string
          ip_address: string
          is_active?: boolean | null
          location_info?: Json | null
          login_time?: string
          logout_time?: string | null
          session_id: string
          user_agent: string
          user_id: string
        }
        Update: {
          created_at?: string
          device_info?: Json | null
          id?: string
          ip_address?: string
          is_active?: boolean | null
          location_info?: Json | null
          login_time?: string
          logout_time?: string | null
          session_id?: string
          user_agent?: string
          user_id?: string
        }
        Relationships: []
      }
      message_delivery: {
        Row: {
          created_at: string | null
          delivered_at: string | null
          failed_reason: string | null
          id: string
          message_id: string
          read_at: string | null
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          delivered_at?: string | null
          failed_reason?: string | null
          id?: string
          message_id: string
          read_at?: string | null
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          delivered_at?: string | null
          failed_reason?: string | null
          id?: string
          message_id?: string
          read_at?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_delivery_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_delivery_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      message_reactions: {
        Row: {
          created_at: string | null
          id: string
          message_id: string
          reaction: string
          reaction_type: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          message_id: string
          reaction: string
          reaction_type?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          message_id?: string
          reaction?: string
          reaction_type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_reactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string | null
          conversation_id: string
          created_at: string | null
          deleted_at: string | null
          deleted_by: string | null
          delivered_at: Json | null
          delivered_to: string[] | null
          duration: number | null
          edit_history: Json | null
          edited_at: string | null
          expires_at: string | null
          file_name: string | null
          file_size: number | null
          file_type: string | null
          forward_count: number | null
          forwarded_from_message_id: string | null
          has_reactions: boolean | null
          id: string
          is_edited: boolean | null
          is_expired: boolean | null
          is_pinned: boolean | null
          media_url: string | null
          message_type: string
          metadata: Json | null
          pinned_at: string | null
          pinned_by: string | null
          reaction_counts: Json | null
          reply_to_message_id: string | null
          sender_id: string
          status: string | null
          thread_id: string | null
          thumbnail_url: string | null
          updated_at: string | null
        }
        Insert: {
          content?: string | null
          conversation_id: string
          created_at?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          delivered_at?: Json | null
          delivered_to?: string[] | null
          duration?: number | null
          edit_history?: Json | null
          edited_at?: string | null
          expires_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          forward_count?: number | null
          forwarded_from_message_id?: string | null
          has_reactions?: boolean | null
          id?: string
          is_edited?: boolean | null
          is_expired?: boolean | null
          is_pinned?: boolean | null
          media_url?: string | null
          message_type?: string
          metadata?: Json | null
          pinned_at?: string | null
          pinned_by?: string | null
          reaction_counts?: Json | null
          reply_to_message_id?: string | null
          sender_id: string
          status?: string | null
          thread_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: string | null
          conversation_id?: string
          created_at?: string | null
          deleted_at?: string | null
          deleted_by?: string | null
          delivered_at?: Json | null
          delivered_to?: string[] | null
          duration?: number | null
          edit_history?: Json | null
          edited_at?: string | null
          expires_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_type?: string | null
          forward_count?: number | null
          forwarded_from_message_id?: string | null
          has_reactions?: boolean | null
          id?: string
          is_edited?: boolean | null
          is_expired?: boolean | null
          is_pinned?: boolean | null
          media_url?: string | null
          message_type?: string
          metadata?: Json | null
          pinned_at?: string | null
          pinned_by?: string | null
          reaction_counts?: Json | null
          reply_to_message_id?: string | null
          sender_id?: string
          status?: string | null
          thread_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_deleted_by_fkey"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_forwarded_from_message_id_fkey"
            columns: ["forwarded_from_message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_pinned_by_fkey"
            columns: ["pinned_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_reply_to_message_id_fkey"
            columns: ["reply_to_message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      music_library: {
        Row: {
          artist: string | null
          created_at: string | null
          duration: number
          file_url: string
          genre: string | null
          id: string
          is_active: boolean | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          artist?: string | null
          created_at?: string | null
          duration: number
          file_url: string
          genre?: string | null
          id?: string
          is_active?: boolean | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          artist?: string | null
          created_at?: string | null
          duration?: number
          file_url?: string
          genre?: string | null
          id?: string
          is_active?: boolean | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notification_delivery_log: {
        Row: {
          clicked_at: string | null
          created_at: string
          delivered_at: string | null
          delivery_method: string
          error_message: string | null
          external_id: string | null
          id: string
          notification_id: string
          opened_at: string | null
          provider: string | null
          status: string
        }
        Insert: {
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          delivery_method: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          notification_id: string
          opened_at?: string | null
          provider?: string | null
          status: string
        }
        Update: {
          clicked_at?: string | null
          created_at?: string
          delivered_at?: string | null
          delivery_method?: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          notification_id?: string
          opened_at?: string | null
          provider?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "notification_delivery_log_notification_id_fkey"
            columns: ["notification_id"]
            isOneToOne: false
            referencedRelation: "notifications"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_settings: {
        Row: {
          badge_count_enabled: boolean | null
          comment_notifications: boolean | null
          created_at: string | null
          email_notifications: boolean | null
          follow_notifications: boolean | null
          group_similar: boolean | null
          like_notifications: boolean | null
          live_notifications: boolean | null
          marketing_notifications: boolean | null
          mention_notifications: boolean | null
          message_notifications: boolean | null
          notification_sound: string | null
          preview_enabled: boolean | null
          push_notifications: boolean | null
          quiet_hours_enabled: boolean | null
          quiet_hours_end: string | null
          quiet_hours_start: string | null
          reaction_notifications: boolean | null
          security_notifications: boolean | null
          sms_notifications: boolean | null
          updated_at: string | null
          user_id: string
          vibration_enabled: boolean | null
          video_upload_notifications: boolean | null
        }
        Insert: {
          badge_count_enabled?: boolean | null
          comment_notifications?: boolean | null
          created_at?: string | null
          email_notifications?: boolean | null
          follow_notifications?: boolean | null
          group_similar?: boolean | null
          like_notifications?: boolean | null
          live_notifications?: boolean | null
          marketing_notifications?: boolean | null
          mention_notifications?: boolean | null
          message_notifications?: boolean | null
          notification_sound?: string | null
          preview_enabled?: boolean | null
          push_notifications?: boolean | null
          quiet_hours_enabled?: boolean | null
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          reaction_notifications?: boolean | null
          security_notifications?: boolean | null
          sms_notifications?: boolean | null
          updated_at?: string | null
          user_id: string
          vibration_enabled?: boolean | null
          video_upload_notifications?: boolean | null
        }
        Update: {
          badge_count_enabled?: boolean | null
          comment_notifications?: boolean | null
          created_at?: string | null
          email_notifications?: boolean | null
          follow_notifications?: boolean | null
          group_similar?: boolean | null
          like_notifications?: boolean | null
          live_notifications?: boolean | null
          marketing_notifications?: boolean | null
          mention_notifications?: boolean | null
          message_notifications?: boolean | null
          notification_sound?: string | null
          preview_enabled?: boolean | null
          push_notifications?: boolean | null
          quiet_hours_enabled?: boolean | null
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          reaction_notifications?: boolean | null
          security_notifications?: boolean | null
          sms_notifications?: boolean | null
          updated_at?: string | null
          user_id?: string
          vibration_enabled?: boolean | null
          video_upload_notifications?: boolean | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          action_url: string | null
          body: string | null
          category: string
          created_at: string
          data: Json | null
          delivered_at: string | null
          expires_at: string | null
          id: string
          is_delivered: boolean | null
          is_read: boolean | null
          priority: string
          read_at: string | null
          title: string | null
          type: string
          user_id: string
        }
        Insert: {
          action_url?: string | null
          body?: string | null
          category?: string
          created_at?: string
          data?: Json | null
          delivered_at?: string | null
          expires_at?: string | null
          id?: string
          is_delivered?: boolean | null
          is_read?: boolean | null
          priority?: string
          read_at?: string | null
          title?: string | null
          type: string
          user_id: string
        }
        Update: {
          action_url?: string | null
          body?: string | null
          category?: string
          created_at?: string
          data?: Json | null
          delivered_at?: string | null
          expires_at?: string | null
          id?: string
          is_delivered?: boolean | null
          is_read?: boolean | null
          priority?: string
          read_at?: string | null
          title?: string | null
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      personal_data_settings: {
        Row: {
          analytics_tracking: boolean | null
          created_at: string | null
          data_deletion_date: string | null
          data_deletion_requested: boolean | null
          data_export_date: string | null
          data_export_requested: boolean | null
          data_retention_period: number | null
          marketing_emails: boolean | null
          third_party_sharing: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          analytics_tracking?: boolean | null
          created_at?: string | null
          data_deletion_date?: string | null
          data_deletion_requested?: boolean | null
          data_export_date?: string | null
          data_export_requested?: boolean | null
          data_retention_period?: number | null
          marketing_emails?: boolean | null
          third_party_sharing?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          analytics_tracking?: boolean | null
          created_at?: string | null
          data_deletion_date?: string | null
          data_deletion_requested?: boolean | null
          data_export_date?: string | null
          data_export_requested?: boolean | null
          data_retention_period?: number | null
          marketing_emails?: boolean | null
          third_party_sharing?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      phone_verifications: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          is_used: boolean
          phone_number: string
          user_id: string
          verification_code: string
        }
        Insert: {
          created_at?: string
          expires_at: string
          id?: string
          is_used?: boolean
          phone_number: string
          user_id: string
          verification_code: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          is_used?: boolean
          phone_number?: string
          user_id?: string
          verification_code?: string
        }
        Relationships: []
      }
      privacy_settings: {
        Row: {
          allow_comments: boolean | null
          allow_downloads: boolean | null
          allow_duets: boolean | null
          allow_messages_from: string | null
          created_at: string | null
          profile_visibility: boolean
          show_online_status: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          allow_comments?: boolean | null
          allow_downloads?: boolean | null
          allow_duets?: boolean | null
          allow_messages_from?: string | null
          created_at?: string | null
          profile_visibility?: boolean
          show_online_status?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          allow_comments?: boolean | null
          allow_downloads?: boolean | null
          allow_duets?: boolean | null
          allow_messages_from?: string | null
          created_at?: string | null
          profile_visibility?: boolean
          show_online_status?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          banner_image_url: string | null
          bio: string | null
          profile_picture_url: string | null
          user_id: string
          user_tag: Database["public"]["Enums"]["user_tag"] | null
        }
        Insert: {
          banner_image_url?: string | null
          bio?: string | null
          profile_picture_url?: string | null
          user_id: string
          user_tag?: Database["public"]["Enums"]["user_tag"] | null
        }
        Update: {
          banner_image_url?: string | null
          bio?: string | null
          profile_picture_url?: string | null
          user_id?: string
          user_tag?: Database["public"]["Enums"]["user_tag"] | null
        }
        Relationships: []
      }
      security_activities: {
        Row: {
          activity_type: string
          city: string | null
          coordinates: string | null
          country: string | null
          created_at: string
          details: Json | null
          device_info: string
          id: string
          ip_address: string
          location: string | null
          region: string | null
          session_id: string | null
          timezone: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          activity_type: string
          city?: string | null
          coordinates?: string | null
          country?: string | null
          created_at?: string
          details?: Json | null
          device_info: string
          id?: string
          ip_address: string
          location?: string | null
          region?: string | null
          session_id?: string | null
          timezone?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          activity_type?: string
          city?: string | null
          coordinates?: string | null
          country?: string | null
          created_at?: string
          details?: Json | null
          device_info?: string
          id?: string
          ip_address?: string
          location?: string | null
          region?: string | null
          session_id?: string | null
          timezone?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: []
      }
      security_settings: {
        Row: {
          backup_codes_generated: boolean
          created_at: string
          device_management: boolean
          login_notifications: boolean
          password_last_changed: string | null
          security_questions_set: boolean
          session_timeout: number
          two_factor_enabled: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          backup_codes_generated?: boolean
          created_at?: string
          device_management?: boolean
          login_notifications?: boolean
          password_last_changed?: string | null
          security_questions_set?: boolean
          session_timeout?: number
          two_factor_enabled?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          backup_codes_generated?: boolean
          created_at?: string
          device_management?: boolean
          login_notifications?: boolean
          password_last_changed?: string | null
          security_questions_set?: boolean
          session_timeout?: number
          two_factor_enabled?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      settings: {
        Row: {
          allow_messages_from: string | null
          dark_mode: boolean | null
          is_private_account: boolean | null
          show_activity_status: boolean | null
          user_id: string
        }
        Insert: {
          allow_messages_from?: string | null
          dark_mode?: boolean | null
          is_private_account?: boolean | null
          show_activity_status?: boolean | null
          user_id: string
        }
        Update: {
          allow_messages_from?: string | null
          dark_mode?: boolean | null
          is_private_account?: boolean | null
          show_activity_status?: boolean | null
          user_id?: string
        }
        Relationships: []
      }
      typing_indicators: {
        Row: {
          conversation_id: string
          id: string
          is_typing: boolean | null
          last_activity_at: string | null
          started_at: string | null
          typing_type: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          id?: string
          is_typing?: boolean | null
          last_activity_at?: string | null
          started_at?: string | null
          typing_type?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          id?: string
          is_typing?: boolean | null
          last_activity_at?: string | null
          started_at?: string | null
          typing_type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "typing_indicators_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "typing_indicators_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_fingerprints: {
        Row: {
          created_at: string | null
          fingerprint: string
          id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          fingerprint: string
          id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          fingerprint?: string
          id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_follows: {
        Row: {
          created_at: string
          follower_id: string
          following_id: string
          id: string
        }
        Insert: {
          created_at?: string
          follower_id: string
          following_id: string
          id?: string
        }
        Update: {
          created_at?: string
          follower_id?: string
          following_id?: string
          id?: string
        }
        Relationships: []
      }
      user_mac_addresses: {
        Row: {
          created_at: string
          id: string
          mac_address: unknown
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          mac_address: unknown
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          mac_address?: unknown
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_phone_numbers: {
        Row: {
          created_at: string
          id: string
          is_primary: boolean
          is_verified: boolean
          phone_number: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_primary?: boolean
          is_verified?: boolean
          phone_number: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_primary?: boolean
          is_verified?: boolean
          phone_number?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_presence: {
        Row: {
          created_at: string | null
          device_info: Json | null
          id: string
          is_active: boolean | null
          last_seen_at: string | null
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          device_info?: Json | null
          id?: string
          is_active?: boolean | null
          last_seen_at?: string | null
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          device_info?: Json | null
          id?: string
          is_active?: boolean | null
          last_seen_at?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_presence_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_push_tokens: {
        Row: {
          created_at: string
          device_type: string | null
          id: string
          is_active: boolean
          platform: string
          token: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          device_type?: string | null
          id?: string
          is_active?: boolean
          platform: string
          token: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          device_type?: string | null
          id?: string
          is_active?: boolean
          platform?: string
          token?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          auth_user_id: string | null
          created_at: string | null
          date_of_birth: string | null
          email: string
          full_name: string | null
          gender: string | null
          id: string
          is_banned: boolean | null
          is_verified: boolean | null
          password_hash: string
          phone_number: string | null
          updated_at: string | null
          username: string
        }
        Insert: {
          auth_user_id?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email: string
          full_name?: string | null
          gender?: string | null
          id?: string
          is_banned?: boolean | null
          is_verified?: boolean | null
          password_hash: string
          phone_number?: string | null
          updated_at?: string | null
          username: string
        }
        Update: {
          auth_user_id?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string
          full_name?: string | null
          gender?: string | null
          id?: string
          is_banned?: boolean | null
          is_verified?: boolean | null
          password_hash?: string
          phone_number?: string | null
          updated_at?: string | null
          username?: string
        }
        Relationships: []
      }
      video_likes: {
        Row: {
          created_at: string
          id: string
          user_id: string
          video_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          user_id: string
          video_id: string
        }
        Update: {
          created_at?: string
          id?: string
          user_id?: string
          video_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_likes_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "videos"
            referencedColumns: ["id"]
          },
        ]
      }
      videos: {
        Row: {
          allow_comments: boolean | null
          allow_downloads: boolean | null
          author: string | null
          comments_count: number | null
          created_at: string | null
          custom_music_url: string | null
          description: string | null
          duration: number | null
          duration_sec: number | null
          id: string
          is_draft: boolean | null
          likes: number | null
          likes_count: number | null
          music_id: string | null
          music_volume: number | null
          original_audio_volume: number | null
          privacy_setting: string
          processing_metadata: Json | null
          published_at: string | null
          shares_count: number | null
          sport: string | null
          tags: string[] | null
          team_mentioned: string | null
          thumbnail_url: string | null
          title: string | null
          type: string | null
          updated_at: string | null
          upload_date: string | null
          user_id: string
          video_id: string
          video_url: string
          views: number | null
          views_count: number | null
        }
        Insert: {
          allow_comments?: boolean | null
          allow_downloads?: boolean | null
          author?: string | null
          comments_count?: number | null
          created_at?: string | null
          custom_music_url?: string | null
          description?: string | null
          duration?: number | null
          duration_sec?: number | null
          id?: string
          is_draft?: boolean | null
          likes?: number | null
          likes_count?: number | null
          music_id?: string | null
          music_volume?: number | null
          original_audio_volume?: number | null
          privacy_setting?: string
          processing_metadata?: Json | null
          published_at?: string | null
          shares_count?: number | null
          sport?: string | null
          tags?: string[] | null
          team_mentioned?: string | null
          thumbnail_url?: string | null
          title?: string | null
          type?: string | null
          updated_at?: string | null
          upload_date?: string | null
          user_id: string
          video_id: string
          video_url: string
          views?: number | null
          views_count?: number | null
        }
        Update: {
          allow_comments?: boolean | null
          allow_downloads?: boolean | null
          author?: string | null
          comments_count?: number | null
          created_at?: string | null
          custom_music_url?: string | null
          description?: string | null
          duration?: number | null
          duration_sec?: number | null
          id?: string
          is_draft?: boolean | null
          likes?: number | null
          likes_count?: number | null
          music_id?: string | null
          music_volume?: number | null
          original_audio_volume?: number | null
          privacy_setting?: string
          processing_metadata?: Json | null
          published_at?: string | null
          shares_count?: number | null
          sport?: string | null
          tags?: string[] | null
          team_mentioned?: string | null
          thumbnail_url?: string | null
          title?: string | null
          type?: string | null
          updated_at?: string | null
          upload_date?: string | null
          user_id?: string
          video_id?: string
          video_url?: string
          views?: number | null
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "videos_music_id_fkey"
            columns: ["music_id"]
            isOneToOne: false
            referencedRelation: "music_library"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      comments_with_details: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          full_name: string | null
          id: string | null
          likes: number | null
          parent_id: string | null
          reply_count: number | null
          text: string | null
          updated_at: string | null
          user_id: string | null
          username: string | null
          video_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      add_message_reaction: {
        Args: {
          message_id_param: string
          user_id_param: string
          reaction_param: string
          reaction_type_param?: string
        }
        Returns: undefined
      }
      add_participant_to_conversation: {
        Args: { p_conversation_id: string; p_participant_id: string }
        Returns: boolean
      }
      can_message_user: {
        Args: { sender_id: string; recipient_id: string }
        Returns: boolean
      }
      can_users_message: {
        Args: { user1_auth_id: string; user2_auth_id: string }
        Returns: boolean
      }
      cleanup_expired_messages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_old_typing_indicators: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_conversation: {
        Args:
          | {
              creator_id_param: string
              participant_ids_param: string[]
              conversation_type_param?: string
              conversation_name_param?: string
              group_settings_param?: Json
            }
          | { p_participant_id: string; p_name?: string; p_type?: string }
          | { participant_id: string }
          | { participant_ids: string[]; conversation_type?: string }
        Returns: string
      }
      create_message: {
        Args: { p_conversation_id: string; p_content: string }
        Returns: undefined
      }
      debug_messaging_permissions: {
        Args: { user1_auth_id: string; user2_auth_id: string }
        Returns: {
          user1_can_message_user2: boolean
          user2_can_message_user1: boolean
          user2_profile_visibility: boolean
          user2_allow_messages_from: string
          is_blocked: boolean
          user1_follows_user2: boolean
          user2_follows_user1: boolean
        }[]
      }
      debug_user_follows: {
        Args: { user1_auth_id: string; user2_auth_id: string }
        Returns: {
          user1_username: string
          user2_username: string
          user1_follows_user2: boolean
          user2_follows_user1: boolean
          can_message: boolean
        }[]
      }
      delete_user_video: {
        Args: { video_id_param: string }
        Returns: boolean
      }
      expire_old_messages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_comment_like_count: {
        Args: { comment_uuid: string }
        Returns: number
      }
      get_fingerprint_account_count: {
        Args: { fp: string }
        Returns: number
      }
      get_mac_address_count: {
        Args: { mac_addr: unknown }
        Returns: number
      }
      get_reply_count: {
        Args: { comment_uuid: string }
        Returns: number
      }
      get_unread_count: {
        Args: { conversation_id_param: string; user_id_param: string }
        Returns: number
      }
      is_conversation_participant: {
        Args:
          | { conversation_id_param: string; user_id_param: string }
          | { user_id: string; conversation_participants: string[] }
        Returns: boolean
      }
      is_conversation_participant_for_media: {
        Args: { conversation_id: string }
        Returns: boolean
      }
      mark_messages_as_read: {
        Args:
          | { conversation_id_param: string; user_id_param: string }
          | {
              conversation_id_param: string
              user_id_param: string
              message_ids_param?: string[]
            }
          | { p_conversation_id: string }
        Returns: boolean
      }
      remove_message_reaction: {
        Args: {
          message_id_param: string
          user_id_param: string
          reaction_param: string
        }
        Returns: undefined
      }
      send_message: {
        Args: {
          p_conversation_id: string
          p_content: string
          p_type?: string
          p_media_url?: string
          p_reply_to_message_id?: string
        }
        Returns: string
      }
      test_follow_relationship: {
        Args: { user1_auth_id: string; user2_auth_id: string }
        Returns: {
          user1_follows_user2: boolean
          user2_follows_user1: boolean
          can_message: boolean
          follow_count: number
        }[]
      }
      update_typing_indicator: {
        Args: {
          conversation_id_param: string
          user_id_param: string
          is_typing_param: boolean
          typing_type_param?: string
        }
        Returns: undefined
      }
      update_user_presence: {
        Args: {
          user_id_param: string
          status_param: string
          device_info_param?: Json
        }
        Returns: undefined
      }
      user_liked_comment: {
        Args: { comment_uuid: string; user_uuid: string }
        Returns: boolean
      }
    }
    Enums: {
      user_tag:
        | "Célébrité"
        | "Partenaire"
        | "Club/Pro."
        | "Créateur"
        | "Supporter"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      user_tag: [
        "Célébrité",
        "Partenaire",
        "Club/Pro.",
        "Créateur",
        "Supporter",
      ],
    },
  },
} as const