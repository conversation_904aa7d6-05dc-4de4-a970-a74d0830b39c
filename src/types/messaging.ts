export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'gif' | 'sticker' | 'location' | 'file';
  media_url?: string;
  thumbnail_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  duration?: number; // in seconds for audio/video
  reply_to_message_id?: string;
  forwarded_from_message_id?: string;
  is_forwarded?: boolean;
  expires_at?: string; // for disappearing messages
  reaction_counts?: Record<string, number>;
  created_at: string;
  updated_at: string;
  is_read: boolean;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  read_by: string[];
  read_at: Record<string, string>;
  is_edited: boolean;
  edited_at?: string;
  is_deleted: boolean;
  deleted_at?: string;
  /**
   * Optional metadata for media messages. Audio recordings may include
   * additional format information, location data, etc.
   */
  metadata?: Record<string, any>;
}

export interface MessageReaction {
  id: string;
  message_id: string;
  user_id: string;
  reaction_type: 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry' | 'fire' | 'heart';
  created_at: string;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string; // For group chats
  avatar_url?: string; // For group chats
  participants: string[];
  created_at: string;
  updated_at: string;
  last_message_id?: string;
  last_message_at?: string;
  metadata?: Record<string, any>;
}

export interface ConversationWithDetails extends Conversation {
  last_message?: Message;
  unread_count: number;
  other_participant?: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    show_online_status?: boolean;
  };
}

export interface MessageWithSender extends Message {
  sender: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    is_online?: boolean;
  };
}

export interface CreateConversationRequest {
  other_user_id: string;
}

export interface SendMessageRequest {
  conversation_id: string;
  content?: string; // Can be null for media-only messages
  message_type?: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'gif' | 'sticker' | 'location' | 'contact' | 'system' | 'file';
  media_url?: string;
  file_url?: string; // Alternative to media_url
  thumbnail_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  duration?: number; // For audio/video messages in seconds
  reply_to_message_id?: string;
  forwarded_from_message_id?: string;
  thread_id?: string; // For threaded conversations
  expires_at?: string; // For disappearing messages
  metadata?: Record<string, any>;
}

export interface ConversationParticipant {
  id: string;
  conversation_id: string;
  user_id: string;
  role: 'admin' | 'member' | 'moderator';
  joined_at: string;
  left_at?: string;
  is_active: boolean;
  last_read_message_id?: string;
  last_read_at?: string;
  notification_settings: Record<string, any>;
}

export interface TypingIndicator {
  id: string;
  conversation_id: string;
  user_id: string;
  is_typing: boolean;
  last_activity: string;
}

export interface AddParticipantRequest {
  conversation_id: string;
  participant_id: string; // auth user id of the participant to add
}

export interface TypingStatus {
  user_id: string;
  username: string;
  is_typing: boolean;
}

export interface NotificationData {
  id: string;
  user_id: string;
  type: 'message' | 'like' | 'comment' | 'follow' | 'mention' | 'live' | 'video_upload' | 'system' | 'security' | 'reaction';
  title: string;
  body: string;
  data: Record<string, any>;
  is_read: boolean;
  is_delivered: boolean;
  delivered_at?: string;
  read_at?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category: 'general' | 'social' | 'content' | 'security' | 'system';
  action_url?: string;
  expires_at?: string;
  created_at: string;
}

export interface NotificationDeliveryLog {
  id: string;
  notification_id: string;
  delivery_method: 'push' | 'email' | 'sms' | 'in_app';
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced';
  provider?: string;
  external_id?: string;
  error_message?: string;
  delivered_at?: string;
  opened_at?: string;
  clicked_at?: string;
  created_at: string;
}

// TikTok-style messaging types
export interface TypingIndicator {
  id: string;
  conversation_id: string;
  user_id: string;
  is_typing: boolean;
  typing_type: 'text' | 'voice' | 'media';
  started_at: string;
  last_activity_at: string;
  user?: {
    username: string;
    full_name: string;
    avatar_url?: string;
  };
}

export interface UserPresence {
  id: string;
  user_id: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen_at: string;
  is_active: boolean;
  device_info: Record<string, any>;
  user?: {
    username: string;
    full_name: string;
    avatar_url?: string;
  };
}

export interface MessageReaction {
  id: string;
  message_id: string;
  user_id: string;
  reaction: string; // Emoji or reaction code
  reaction_type: 'emoji' | 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry' | 'fire' | 'heart' | 'thumbs_up' | 'thumbs_down';
  created_at: string;
  user?: {
    username: string;
    full_name: string;
    avatar_url?: string;
  };
}

export interface MessageDelivery {
  id: string;
  message_id: string;
  user_id: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  delivered_at?: string;
  read_at?: string;
  failed_reason?: string;
  created_at: string;
  updated_at: string;
}

// Enhanced conversation participant with TikTok-style features
export interface EnhancedConversationParticipant extends ConversationParticipant {
  custom_nickname?: string;
  is_pinned: boolean;
  is_archived: boolean;
  notification_settings: {
    muted: boolean;
    muted_until?: string;
    push_notifications: boolean;
    message_preview: boolean;
    sound: boolean;
    vibration: boolean;
  };
}

// Enhanced conversation with TikTok-style features
export interface EnhancedConversation extends Conversation {
  group_settings?: {
    allow_members_to_add_others: boolean;
    allow_members_to_change_info: boolean;
    message_history_visible_to_new_members: boolean;
    disappearing_messages_timer?: number;
  };
  is_public: boolean;
  invite_link?: string;
  invite_link_expires_at?: string;
  last_activity_at?: string;
  is_archived: boolean;
}

// Enhanced message with TikTok-style features
export interface EnhancedMessage extends Message {
  thread_id?: string;
  delivered_to: string[];
  delivered_at: Record<string, string>;
  edit_history: any[];
  deleted_by?: string;
  forward_count: number;
  is_pinned: boolean;
  pinned_by?: string;
  pinned_at?: string;
  is_expired: boolean;
  has_reactions: boolean;
}
