export interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  phone_number?: string | null;
  date_of_birth?: string | null;
  gender?: string | null;
  avatar_url?: string;
  profile_picture_url?: string;
  banner_image_url?: string;
  bio?: string;
  website?: string;
  user_tag?: 'Celebrity' | 'Partner' | 'Club/Pro' | 'Creator' | 'Supporter' | null;
  followers_count: number;
  following_count: number;
  likes_count: number;
  videos_count: number;
  is_private: boolean;
  is_verified: boolean;
  is_banned?: boolean;
  auth_user_id?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  bio?: string;
  website?: string;
  pronouns?: string;
  followers_count: number;
  following_count: number;
  likes_count: number;
  videos_count: number;
  is_private: boolean;
  is_verified: boolean;
  is_following?: boolean;
  is_followed_by?: boolean;
}

export interface UpdateProfileData {
  username?: string;
  full_name?: string;
  bio?: string;
  website?: string;
  user_tag?: 'Celebrity' | 'Partner' | 'Club/Pro' | 'Creator' | 'Supporter';
  is_private?: boolean;
  date_of_birth?: string;
  phone_number?: string;
  gender?: string;
  profile_picture_url?: string;
}

export interface UserStats {
  followers: number;
  following: number;
  likes: number;
  videos: number;
}
