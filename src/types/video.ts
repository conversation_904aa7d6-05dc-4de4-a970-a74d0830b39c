export interface Video {
  id: string;
  url: string;
  thumbnail?: string;
  duration: number;
  title: string;
  description?: string;
  tags?: string[];
  created_at: string;
  updated_at: string;
  user_id: string;
  user: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
  };
  stats: {
    likes: number;
    comments: number;
    shares: number;
    views: number;
  };
  is_liked?: boolean;
  is_bookmarked?: boolean;
}

export interface VideoComment {
  id: string;
  video_id: string;
  user_id: string;
  content: string;
  created_at: string;
  user: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
  };
  replies?: VideoComment[];
  likes: number;
  is_liked?: boolean;
}

export interface VideoUpload {
  title: string;
  description?: string;
  tags?: string[];
  privacy: 'public' | 'private' | 'friends';
  allow_comments: boolean;
  allow_duet: boolean;
  allow_stitch: boolean;
}
