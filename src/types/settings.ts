export interface UserSettings {
  user_id: string;
  is_private_account: boolean;
  allow_messages_from: 'Everyone' | 'Friends' | 'NoOne';
  show_activity_status: boolean;
  dark_mode: boolean;
}

export interface PrivacySettings {
  user_id: string;
  profile_visibility: boolean;
  allow_messages_from: 'everyone' | 'followers' | 'nobody';
  show_online_status: boolean;
  allow_downloads: boolean;
  allow_duets: boolean;
  allow_comments: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface NotificationSettings {
  user_id: string;
  push_notifications: boolean;
  email_notifications: boolean;
  like_notifications: boolean;
  comment_notifications: boolean;
  follow_notifications: boolean;
  live_notifications: boolean;
  message_notifications: boolean;
  created_at: string;
  updated_at: string;
}

export interface SecuritySettings {
  user_id: string;
  two_factor_enabled: boolean;
  login_notifications: boolean;
  device_management: boolean;
  session_timeout: number;
  password_last_changed?: string;
  security_questions_set: boolean;
  backup_codes_generated: boolean;
  created_at: string;
  updated_at: string;
}

export interface PersonalDataSettings {
  user_id: string;
  data_export_requested: boolean;
  data_export_date?: string;
  data_deletion_requested: boolean;
  data_deletion_date?: string;
  marketing_emails: boolean;
  analytics_tracking: boolean;
  third_party_sharing: boolean;
  data_retention_period: number;
  created_at: string;
  updated_at: string;
}
