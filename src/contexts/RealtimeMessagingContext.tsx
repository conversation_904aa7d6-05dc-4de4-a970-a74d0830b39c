import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useRealtimeMessaging } from '../hooks/useRealtimeMessaging';
import { useTypingIndicators } from '../hooks/useTypingIndicators';
import { useUserPresence } from '../hooks/useUserPresence';
import { TypingIndicator, UserPresence, MessageWithSender } from '../types/messaging';
import { useAuth } from './AuthContext';
import logger from '../utils/logger';

interface RealtimeMessagingContextType {
  // Connection status
  isConnected: boolean;
  
  // Typing indicators
  typingUsers: TypingIndicator[];
  isTyping: boolean;
  startTyping: (type?: 'text' | 'voice' | 'media') => void;
  stopTyping: () => void;
  handleTextChange: (text: string) => void;
  handleVoiceRecording: (isRecording: boolean) => void;
  handleMediaSharing: (isSharing: boolean) => void;
  
  // User presence
  currentStatus: 'online' | 'away' | 'busy' | 'offline';
  userPresences: Map<string, UserPresence>;
  onlineUsersCount: number;
  isUserOnline: (userId: string) => boolean;
  formatLastSeen: (userId: string) => string;
  setBusy: () => void;
  setAway: () => void;
  setOnline: () => void;
  
  // Message events
  onNewMessage?: (message: MessageWithSender) => void;
  onMessageUpdate?: (message: MessageWithSender) => void;
  onReactionUpdate?: (reactionData: any) => void;
  
  // Current conversation
  currentConversationId?: string;
  setCurrentConversationId: (conversationId: string | undefined) => void;
}

const RealtimeMessagingContext = createContext<RealtimeMessagingContextType | undefined>(undefined);

interface RealtimeMessagingProviderProps {
  children: React.ReactNode;
}

export const RealtimeMessagingProvider: React.FC<RealtimeMessagingProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [currentConversationId, setCurrentConversationId] = useState<string | undefined>();
  const [newMessages, setNewMessages] = useState<MessageWithSender[]>([]);
  const [messageUpdates, setMessageUpdates] = useState<MessageWithSender[]>([]);
  const [reactionUpdates, setReactionUpdates] = useState<any[]>([]);

  // Get current user's public ID
  const currentUserId = user?.id; // This should be the public.users.id

  // Real-time messaging hook
  const {
    sendTypingIndicator,
    updatePresence,
    isConnected,
  } = useRealtimeMessaging({
    conversationId: currentConversationId,
    userId: currentUserId,
    onNewMessage: useCallback((message: MessageWithSender) => {
      logger.debug('New message received in context:', message);
      setNewMessages(prev => [...prev, message]);
    }, []),
    onMessageUpdate: useCallback((message: MessageWithSender) => {
      logger.debug('Message updated in context:', message);
      setMessageUpdates(prev => [...prev, message]);
    }, []),
    onReactionUpdate: useCallback((reactionData: any) => {
      logger.debug('Reaction updated in context:', reactionData);
      setReactionUpdates(prev => [...prev, reactionData]);
    }, []),
  });

  // Typing indicators hook
  const {
    typingUsers,
    isTyping,
    startTyping,
    stopTyping,
    handleTextChange,
    handleVoiceRecording,
    handleMediaSharing,
  } = useTypingIndicators({
    conversationId: currentConversationId || '',
    currentUserId,
  });

  // User presence hook
  const {
    currentStatus,
    userPresences,
    onlineUsersCount,
    isUserOnline,
    formatLastSeen,
    setBusy,
    setAway,
    setOnline,
    handleUserActivity,
  } = useUserPresence({
    conversationId: currentConversationId,
    currentUserId,
  });

  // Handle user activity for presence
  useEffect(() => {
    const handleActivity = () => {
      handleUserActivity();
    };

    // Add event listeners for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [handleUserActivity]);

  // Enhanced typing functions that use the real-time hook
  const enhancedStartTyping = useCallback(async (type: 'text' | 'voice' | 'media' = 'text') => {
    startTyping(type);
    await sendTypingIndicator(true, type);
  }, [startTyping, sendTypingIndicator]);

  const enhancedStopTyping = useCallback(async () => {
    stopTyping();
    await sendTypingIndicator(false);
  }, [stopTyping, sendTypingIndicator]);

  const enhancedHandleTextChange = useCallback((text: string) => {
    handleTextChange(text);
    // The typing indicator will be sent automatically by the hook
  }, [handleTextChange]);

  const enhancedHandleVoiceRecording = useCallback((isRecording: boolean) => {
    handleVoiceRecording(isRecording);
    // The typing indicator will be sent automatically by the hook
  }, [handleVoiceRecording]);

  const enhancedHandleMediaSharing = useCallback((isSharing: boolean) => {
    handleMediaSharing(isSharing);
    // The typing indicator will be sent automatically by the hook
  }, [handleMediaSharing]);

  // Enhanced presence functions
  const enhancedSetBusy = useCallback(async () => {
    setBusy();
    await updatePresence('busy');
  }, [setBusy, updatePresence]);

  const enhancedSetAway = useCallback(async () => {
    setAway();
    await updatePresence('away');
  }, [setAway, updatePresence]);

  const enhancedSetOnline = useCallback(async () => {
    setOnline();
    await updatePresence('online');
  }, [setOnline, updatePresence]);

  // Callback functions for message events
  const [onNewMessage, setOnNewMessage] = useState<((message: MessageWithSender) => void) | undefined>();
  const [onMessageUpdate, setOnMessageUpdate] = useState<((message: MessageWithSender) => void) | undefined>();
  const [onReactionUpdate, setOnReactionUpdate] = useState<((reactionData: any) => void) | undefined>();

  // Process new messages
  useEffect(() => {
    if (newMessages.length > 0) {
      newMessages.forEach(message => {
        onNewMessage?.(message);
      });
      setNewMessages([]);
    }
  }, [newMessages, onNewMessage]);

  // Process message updates
  useEffect(() => {
    if (messageUpdates.length > 0) {
      messageUpdates.forEach(message => {
        onMessageUpdate?.(message);
      });
      setMessageUpdates([]);
    }
  }, [messageUpdates, onMessageUpdate]);

  // Process reaction updates
  useEffect(() => {
    if (reactionUpdates.length > 0) {
      reactionUpdates.forEach(reactionData => {
        onReactionUpdate?.(reactionData);
      });
      setReactionUpdates([]);
    }
  }, [reactionUpdates, onReactionUpdate]);

  // Log connection status changes
  useEffect(() => {
    logger.info('Realtime messaging connection status:', isConnected ? 'Connected' : 'Disconnected');
  }, [isConnected]);

  // Log conversation changes
  useEffect(() => {
    if (currentConversationId) {
      logger.info('Switched to conversation:', currentConversationId);
    } else {
      logger.info('Left conversation');
    }
  }, [currentConversationId]);

  const contextValue: RealtimeMessagingContextType = {
    // Connection status
    isConnected,
    
    // Typing indicators
    typingUsers,
    isTyping,
    startTyping: enhancedStartTyping,
    stopTyping: enhancedStopTyping,
    handleTextChange: enhancedHandleTextChange,
    handleVoiceRecording: enhancedHandleVoiceRecording,
    handleMediaSharing: enhancedHandleMediaSharing,
    
    // User presence
    currentStatus,
    userPresences,
    onlineUsersCount,
    isUserOnline,
    formatLastSeen,
    setBusy: enhancedSetBusy,
    setAway: enhancedSetAway,
    setOnline: enhancedSetOnline,
    
    // Message events
    onNewMessage,
    onMessageUpdate,
    onReactionUpdate,
    
    // Current conversation
    currentConversationId,
    setCurrentConversationId,
  };

  return (
    <RealtimeMessagingContext.Provider value={contextValue}>
      {children}
    </RealtimeMessagingContext.Provider>
  );
};

export const useRealtimeMessagingContext = (): RealtimeMessagingContextType => {
  const context = useContext(RealtimeMessagingContext);
  if (!context) {
    throw new Error('useRealtimeMessagingContext must be used within a RealtimeMessagingProvider');
  }
  return context;
};
