import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import { Theme, lightTheme, darkTheme } from '../styles/themes';

type ThemeContextType = {
  theme: Theme;
  themeName: ColorSchemeName;
  toggleTheme: () => void;
  setTheme: (themeName: ColorSchemeName) => void;
};

const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  themeName: 'light',
  toggleTheme: () => {},
  setTheme: () => {},
});

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [themeName, setThemeName] = useState<ColorSchemeName>(Appearance.getColorScheme() || 'light');

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setThemeName(colorScheme || 'light');
    });

    return () => subscription.remove();
  }, []);

  const theme = themeName === 'dark' ? darkTheme : lightTheme;

  const toggleTheme = () => {
    setThemeName(themeName === 'light' ? 'dark' : 'light');
  };

  const setTheme = (name: ColorSchemeName) => {
    setThemeName(name || 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, themeName, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
