/**
 * TS1 Color Palette
 *
 * Colors are carefully chosen to match the futuristic, tech-forward
 * design language with vibrant electric colors and strong contrast.
 */

export const lightColors = {
  // Brand Colors
  primary: '#FB4C0D',        // TS-Orange - main brand color
  secondary: '#00BFFF',      // Electric Blue - accent color
  accent: '#7B2CBF',         // Neon Violet - tertiary accent
  highlight: '#FF0080',      // Saturated Fuchsia - highlight color

  // Background Colors
  background: '#FFFFFF',     // Pure white background
  surface: '#F8F9FA',        // Slightly off-white for cards/surfaces
  surfaceVariant: '#F1F3F4', // Even lighter variant
  card: '#FFFFFF',           // Card background

  // Text Colors
  text: '#0D0D0D',           // Deep black text
  textSecondary: '#333333',  // Metallic gray for secondary content
  textTertiary: '#666666',   // Light text for hints
  textInverse: '#FFFFFF',    // White text for dark backgrounds

  // Border & Divider Colors
  border: '#E1E2E3',         // Light borders
  divider: '#F0F0F0',        // Subtle dividers
  ring: '#00BFFF',           // Focus ring color (Electric Blue)

  // Status Colors
  error: '#FF3040',          // Error red
  destructive: '#FF3040',    // Destructive action color
  success: '#00D924',        // Success green
  warning: '#FFB800',        // Warning amber
  info: '#00BFFF',           // Info blue (Electric Blue)

  // Interactive Colors
  muted: {
    DEFAULT: '#F1F3F4',
    foreground: '#666666',
  },
  popover: {
    DEFAULT: '#FFFFFF',
    foreground: '#0D0D0D',
  },
};

export const darkColors = {
  // Brand Colors - Same across themes
  primary: '#FB4C0D',        // TS-Orange
  secondary: '#00BFFF',      // Electric Blue
  accent: '#7B2CBF',         // Neon Violet
  highlight: '#FF0080',      // Saturated Fuchsia

  // Background Colors
  background: '#0D0D0D',     // Deep black background
  surface: '#161823',        // Dark surface
  surfaceVariant: '#1F2937', // Slightly lighter variant
  card: '#161823',           // Card background

  // Text Colors
  text: '#FFFFFF',           // Pure white text
  textSecondary: '#A8A8B3',  // Muted text for secondary content
  textTertiary: '#6B7280',   // Very muted text for hints
  textInverse: '#0D0D0D',    // Black text for light backgrounds

  // Border & Divider Colors
  border: '#2D3748',         // Dark borders
  divider: '#374151',        // Subtle dividers
  ring: '#00BFFF',           // Focus ring color (Electric Blue)

  // Status Colors
  error: '#FF4757',          // Slightly brighter error red
  destructive: '#FF4757',    // Destructive action color
  success: '#2ED573',        // Brighter success green
  warning: '#FFA502',        // Brighter warning amber
  info: '#00BFFF',           // Info blue (Electric Blue)

  // Interactive Colors
  muted: {
    DEFAULT: '#1F2937',
    foreground: '#A8A8B3',
  },
  popover: {
    DEFAULT: '#161823',
    foreground: '#FFFFFF',
  },
};

export type Colors = typeof lightColors;
