import { Platform } from 'react-native';

/**
 * TS1 Typography System
 *
 * Font sizes and weights optimized for mobile readability
 * with a modern, tech-forward aesthetic.
 */

export const fontSizes = {
  // Micro text (captions, timestamps)
  xs: 10,

  // Small text (secondary info, metadata)
  sm: 12,

  // Body text (main content)
  base: 14,

  // Large body text (usernames, titles)
  lg: 16,

  // Headings
  xl: 18,        // Small headings
  '2xl': 20,     // Medium headings
  '3xl': 24,     // Large headings
  '4xl': 28,     // Extra large headings
  '5xl': 32,     // Display text
  '6xl': 36,     // Hero text
};

export const fontWeights = {
  light: '300',      // Light text
  regular: '400',    // Normal text
  medium: '500',     // Medium emphasis
  semibold: '600',   // Strong emphasis
  bold: '700',       // Bold text
  extrabold: '800',  // Extra bold (rare use)
};

export const lineHeights = {
  tight: 1.2,        // Tight line height for headings
  normal: 1.4,       // Normal line height for body text
  relaxed: 1.6,      // Relaxed line height for long text
  loose: 1.8,        // Loose line height for captions
};

export const fonts = {
  // Primary font - Poppins for most text
  poppins: 'Poppins',
  
  // Secondary font - Orbitron for futuristic headings and accents
  orbitron: 'Orbitron',
  
  // System fonts as fallbacks
  default: Platform.select({
    ios: 'SF Pro Display',     // iOS system font
    android: 'Roboto',         // Android system font
    default: 'sans-serif',     // Fallback
  }),

  // Monospace for code/numbers
  mono: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    default: 'monospace',
  }),
};

/**
 * Typography Presets
 * Common text styles used throughout the app
 */
export const textPresets = {
  // Display text (hero sections)
  display: {
    fontSize: fontSizes['5xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight,
  },

  // Main headings
  heading: {
    fontSize: fontSizes['3xl'],
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.tight,
  },

  // Sub headings
  subheading: {
    fontSize: fontSizes.xl,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
  },

  // Body text
  body: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },

  // Large body text
  bodyLarge: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },

  // Small text
  caption: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.relaxed,
  },

  // Micro text
  micro: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },

  // Button text
  button: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.tight,
  },

  // Username text
  username: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.normal,
  },
};

export type FontSizes = keyof typeof fontSizes;
export type FontWeights = keyof typeof fontWeights;
export type LineHeights = keyof typeof lineHeights;
export type TextPresets = keyof typeof textPresets;
