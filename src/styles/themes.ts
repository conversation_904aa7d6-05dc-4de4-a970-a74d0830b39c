import { lightColors, darkColors, Colors } from './colors';
import { fontSizes, fontWeights, fonts, FontSizes, FontWeights } from './typography';
import { spacing, Spacing } from './spacing';
import { dimensions } from './dimensions';
import { gradients, gradientDirections, GradientType, GradientDirectionType } from './gradients';
import { animations, timings, easings, AnimationType, AnimationTiming, AnimationEasing } from './animations';

export interface Theme {
  colors: Colors;
  fontSizes: typeof fontSizes;
  fontWeights: typeof fontWeights;
  fonts: typeof fonts;
  spacing: typeof spacing;
  dimensions: typeof dimensions;
  gradients: typeof gradients;
  gradientDirections: typeof gradientDirections;
  animations: typeof animations;
  timings: typeof timings;
  easings: typeof easings;
}

export const lightTheme: Theme = {
  colors: lightColors,
  fontSizes,
  fontWeights,
  fonts,
  spacing,
  dimensions,
  gradients,
  gradientDirections,
  animations,
  timings,
  easings,
};

export const darkTheme: Theme = {
  colors: darkColors,
  fontSizes,
  fontWeights,
  fonts,
  spacing,
  dimensions,
  gradients,
  gradientDirections,
  animations,
  timings,
  easings,
};

export type { 
  Colors, 
  FontSizes, 
  FontWeights, 
  Spacing,
  GradientType,
  GradientDirectionType,
  AnimationType,
  AnimationTiming,
  AnimationEasing
};
