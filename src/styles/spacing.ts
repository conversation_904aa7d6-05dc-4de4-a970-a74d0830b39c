/**
 * TikTok Spacing System
 *
 * Consistent spacing scale based on 4px grid system
 * optimized for mobile touch interfaces.
 */

export const spacing = {
  // No spacing
  none: 0,

  // Micro spacing (1px, 2px)
  px: 1,
  '0.5': 2,

  // Base spacing scale (4px increments)
  xs: 4,          // Extra small - tight spacing
  sm: 8,          // Small - compact spacing
  md: 12,         // Medium-small - comfortable spacing
  base: 16,       // Base - standard spacing
  lg: 20,         // Large - generous spacing
  xl: 24,         // Extra large - spacious
  '2xl': 32,      // 2X large - very spacious
  '3xl': 40,      // 3X large - section spacing
  '4xl': 48,      // 4X large - major section spacing
  '5xl': 64,      // 5X large - page spacing
  '6xl': 80,      // 6X large - hero spacing
};

/**
 * Component-specific spacing
 * Common spacing patterns for UI components
 */
export const componentSpacing = {
  // Button spacing
  buttonPadding: {
    horizontal: spacing.base,
    vertical: spacing.md,
  },

  // Card spacing
  cardPadding: spacing.base,
  cardMargin: spacing.sm,

  // List item spacing
  listItemPadding: spacing.base,
  listItemGap: spacing.xs,

  // Screen spacing
  screenPadding: spacing.base,
  screenMargin: spacing.lg,

  // Tab bar spacing
  tabBarHeight: 80,
  tabBarPadding: spacing.sm,

  // Header spacing
  headerHeight: 56,
  headerPadding: spacing.base,

  // Input spacing
  inputPadding: {
    horizontal: spacing.base,
    vertical: spacing.md,
  },

  // Icon spacing
  iconMargin: spacing.xs,
  iconPadding: spacing.sm,
};

/**
 * Touch target sizes
 * Minimum touch targets for accessibility
 */
export const touchTargets = {
  small: 32,      // Small touch target
  medium: 44,     // Standard touch target (iOS HIG)
  large: 48,      // Large touch target (Material Design)
  extraLarge: 56, // Extra large touch target
};

/**
 * Border radius scale
 * Consistent border radius for UI elements
 */
export const borderRadius = {
  none: 0,
  xs: 2,          // Very small radius
  sm: 4,          // Small radius
  base: 8,        // Standard radius
  md: 12,         // Medium radius
  lg: 16,         // Large radius
  xl: 20,         // Extra large radius
  '2xl': 24,      // 2X large radius
  '3xl': 32,      // 3X large radius
  full: 9999,     // Fully rounded (pills, circles)
};

export type Spacing = keyof typeof spacing;
export type ComponentSpacing = keyof typeof componentSpacing;
export type TouchTargets = keyof typeof touchTargets;
export type BorderRadius = keyof typeof borderRadius;
