import { Animated, Easing } from 'react-native';

/**
 * TS1 Animation System
 *
 * Predefined animations for use throughout the app
 */

// Animation timing configurations
export const timings = {
  fast: 200,
  normal: 300,
  slow: 500,
  verySlow: 800,
};

// Animation easings
export const easings = {
  default: Easing.bezier(0.4, 0, 0.2, 1), // Standard material easing
  accelerate: Easing.bezier(0.4, 0, 1, 1), // Accelerate easing
  decelerate: Easing.bezier(0, 0, 0.2, 1), // Decelerate easing
  sharp: Easing.bezier(0.4, 0, 0.6, 1), // Sharp easing
  bounce: Easing.bounce, // Bounce easing
  elastic: Easing.elastic(1), // Elastic easing
};

// Predefined animations
export const animations = {
  // Pulse glow animation - mimics the pulse-glow from web theme
  pulseGlow: (value: Animated.Value, duration = 2000) => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(value, {
          toValue: 1,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
        Animated.timing(value, {
          toValue: 0,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    return value;
  },
  
  // Float animation - mimics the float from web theme
  float: (value: Animated.Value, duration = 6000, distance = 10) => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(value, {
          toValue: -distance,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
        Animated.timing(value, {
          toValue: 0,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    return value;
  },
  
  // Card hover animation - mimics the card-hover from web theme
  cardHover: (value: Animated.Value, duration = 300, scale = 1.05) => {
    Animated.timing(value, {
      toValue: scale,
      duration,
      easing: easings.default,
      useNativeDriver: true,
    }).start();
    
    return value;
  },
  
  // Twinkle animation - mimics the twinkle from web theme
  twinkle: (value: Animated.Value, duration = 5000) => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(value, {
          toValue: 1,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
        Animated.timing(value, {
          toValue: 0.3,
          duration: duration / 2,
          easing: easings.default,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    return value;
  },
};

export type AnimationTiming = keyof typeof timings;
export type AnimationEasing = keyof typeof easings;
export type AnimationType = keyof typeof animations;