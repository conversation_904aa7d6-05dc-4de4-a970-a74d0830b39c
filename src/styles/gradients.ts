/**
 * TS1 Gradient System
 *
 * Predefined gradients for use throughout the app
 */

export const gradients = {
  // Primary gradient - Electric Blue to Neon Violet to Saturated Fuchsia
  primary: ['#00BFFF', '#7B2CBF', '#FF0080'],
  
  // Secondary gradient - Electric Blue to Neon Violet
  secondary: ['#00BFFF', '#7B2CBF'],
  
  // Tertiary gradient - Neon Violet to Saturated Fuchsia
  tertiary: ['#7B2CBF', '#FF0080'],
  
  // Orange gradient - TS-Orange variations
  orange: ['#FB4C0D', '#FF7A45'],
  
  // Blue gradient - Electric Blue variations
  blue: ['#00BFFF', '#0080FF'],
  
  // Dark gradient - For backgrounds
  dark: ['#0D0D0D', '#161823'],
};

// Gradient directions
export const gradientDirections = {
  topToBottom: { start: { x: 0.5, y: 0 }, end: { x: 0.5, y: 1 } },
  leftToRight: { start: { x: 0, y: 0.5 }, end: { x: 1, y: 0.5 } },
  topLeftToBottomRight: { start: { x: 0, y: 0 }, end: { x: 1, y: 1 } },
  topRightToBottomLeft: { start: { x: 1, y: 0 }, end: { x: 0, y: 1 } },
  diagonal: { start: { x: 0.1, y: 0.1 }, end: { x: 0.9, y: 0.9 } },
};

export type GradientType = keyof typeof gradients;
export type GradientDirectionType = keyof typeof gradientDirections;