export interface Filter {
  id: string;
  name: string;
  preview: string;
}

export interface Effect {
  id: string;
  name: string;
  icon: string;
}

export const CAMERA_FILTERS: Filter[] = [
  { id: 'none', name: 'Original', preview: '🎬' },
  { id: 'vintage', name: 'Vintage', preview: '📸' },
  { id: 'dramatic', name: 'Dramatic', preview: '🎭' },
  { id: 'bright', name: '<PERSON>', preview: '☀️' },
  { id: 'cool', name: 'Cool', preview: '❄️' },
  { id: 'warm', name: 'Warm', preview: '🔥' },
];

export const CAMERA_EFFECTS: Effect[] = [
  { id: 'none', name: 'None', icon: 'clear' },
  { id: 'beauty', name: 'Beauty', icon: 'face-retouching-natural' },
  { id: 'blur', name: 'Blur', icon: 'blur-on' },
  { id: 'vintage', name: 'Vintage', icon: 'photo-filter' },
  { id: 'black-white', name: 'B&W', icon: 'filter-b-and-w' },
  { id: 'sepia', name: '<PERSON><PERSON>', icon: 'filter-vintage' },
];

export const RECORDING_MODES = ['15s', '60s', '3min'] as const;
export const SPEED_OPTIONS = [0.5, 1, 2] as const;
export const TIMER_DURATIONS = [3, 10] as const;
