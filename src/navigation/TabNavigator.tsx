import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, StyleSheet } from 'react-native';
import { useSafeAreaInsets, EdgeInsets } from 'react-native-safe-area-context';
import { TabParamList } from './types';
import HomeScreen from '../screens/main/HomeScreen';
import FriendsScreen from '../screens/main/FriendsScreen';
import CameraScreen from '../screens/main/CameraScreen';
import InboxScreen from '../screens/main/InboxScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import { useTheme } from '../contexts/ThemeContext';
import Text from '../components/common/Text';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAppSelector } from '../store';
const Tab = createBottomTabNavigator<TabParamList>();


/**
 * Custom tab icon component with label
 * Matches TikTok's exact tab bar design with proper spacing and typography
 */
const TabIcon = ({ icon, label, focused, color }: { icon: React.ReactNode; label: string; focused: boolean; color: string }) => {
  return (
    <View style={styles.tabIconContainer}>
      {icon}
      <Text
        style={[
          styles.tabLabel,
          {
            color: focused ? '#FFFFFF' : 'rgba(255, 255, 255, 0.6)',
          },
        ]}
      >
        {label}
      </Text>
    </View>
  );
};

/**
 * Custom upload button component
 * TikTok's signature upload button with gradient-like effect
 */
const UploadButton = ({ focused, insets }: { focused: boolean; insets: EdgeInsets }) => {
  return (
    <View
      style={[
        styles.uploadButton,
        {
          marginBottom: insets.bottom / 2,
          backgroundColor: focused ? '#FF0050' : '#FE2C55',
          transform: [{ scale: focused ? 1.05 : 1 }], // Subtle scale on focus
        },
      ]}
    >
      <MaterialCommunityIcons name="plus" size={18} color="white" />
    </View>
  );
};

const TabNavigator = () => {
  const { theme } = useTheme();
  const user = useAppSelector(state => state.auth.user);
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#FFFFFF',
        tabBarInactiveTintColor: 'rgba(255, 255, 255, 0.6)',
        tabBarStyle: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderTopWidth: 0,
          position: 'absolute',
          paddingBottom: insets.bottom || 8,
          height: 46 + (insets.bottom || 8) + 8,
          paddingTop: 8,
          paddingHorizontal: 8,
        },
        tabBarItemStyle: { 
          justifyContent: 'center', 
          alignItems: 'center',
          flex: 1, // Distribute space evenly
        },
        tabBarShowLabel: false,
      }}>
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <TabIcon
              icon={<Ionicons name={focused ? 'home' : 'home-outline'} size={26} color={color} />}
              label="Home"
              focused={focused}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Friends"
        component={FriendsScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <TabIcon
              icon={<Ionicons name={focused ? 'people' : 'people-outline'} size={26} color={color} />}
              label="Friends"
              focused={focused}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Upload"
        component={CameraScreen}
        options={{
          tabBarIcon: ({ focused }) => <UploadButton focused={focused} insets={insets} />,
        }}
      />
      <Tab.Screen
        name="Inbox"
        component={InboxScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <TabIcon
              icon={<Ionicons name={focused ? 'chatbubble-ellipses' : 'chatbubble-ellipses-outline'} size={26} color={color} />}
              label="Inbox"
              focused={focused}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <TabIcon
              icon={<FontAwesome name={focused ? 'user' : 'user-o'} size={22} color={color} />}
              label="Profile"
              focused={focused}
              color={color}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 46,              // Fixed height for consistent alignment
    minWidth: 60,            // Minimum width to fit content
    paddingTop: 4,           // Small top padding
    paddingHorizontal: 2,    // Small horizontal padding
  },
  tabLabel: {
    fontSize: 10,            // TikTok's exact label size
    fontWeight: '600',       // Semibold for better readability
    textAlign: 'center',
    marginTop: 2,            // Tight spacing between icon and label
    letterSpacing: 0,        // Remove letter spacing to save space
  },
  uploadButton: {
    width: 48,               // Larger touch target
    height: 28,              // TikTok's upload button height
    borderRadius: 4,         // Less rounded for TikTok style
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',     // Add shadow for depth
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,            // Android shadow
  },
});

export default TabNavigator;
