import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MainStackParamList } from './types';
import TabNavigator from './TabNavigator';
//import VideoViewScreen from '../screens/video/VideoViewScreen';
//import VideoDetailsScreen from '../screens/video/VideoDetailsScreen';
import VideoUploadScreen from '../screens/video/VideoUploadScreen';
import MessagesScreen from '../screens/messages/MessagesScreen';
import ChatDetailScreen from '../screens/messages/ChatDetailScreen';
import NewConversationScreen from '../screens/messages/NewConversationScreen';
import UserProfileScreen from '../screens/profile/UserProfileScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import SettingsScreen from '../screens/profile/SettingsScreen';
import NotificationSettingsScreen from '../screens/profile/NotificationSettingsScreen';
import PrivacySettingsScreen from '../screens/profile/PrivacySettingsScreen';
import AccountSettingsScreen from '../screens/profile/AccountSettingsScreen';
import EditUsernameScreen from '../screens/profile/EditUsernameScreen';
import EditEmailScreen from '../screens/profile/EditEmailScreen';
import EditPhoneScreen from '../screens/profile/EditPhoneScreen';
import EditDateOfBirthScreen from '../screens/profile/EditDateOfBirthScreen';
import ChangePasswordScreen from '../screens/profile/ChangePasswordScreen';
import BlockedAccountsScreen from '../screens/profile/BlockedAccountsScreen';
import FollowersScreen from '../screens/profile/FollowersScreen';
import FollowingScreen from '../screens/profile/FollowingScreen';
import SecuritySettingsScreen from '../screens/profile/SecuritySettingsScreen';
import LoginActivitiesScreen from '../screens/profile/LoginActivitiesScreen';
import Setup2FAScreen from '../screens/profile/Setup2FAScreen';
import GiftGridTestScreen from '../screens/test/GiftGridTestScreen';
import PersonalDataSettingsScreen from '../screens/profile/PersonalDataSettingsScreen';
import { PhotoToVideoEditor } from '../screens/camera/PhotoToVideoEditor';
const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName="Tabs"
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}>
      <Stack.Screen name="Tabs" component={TabNavigator} />
      <Stack.Screen
        name="VideoUpload"
        component={VideoUploadScreen}
        options={{ title: 'Upload Video' }}
      />

     {/* <Stack.Screen name="VideoView" component={VideoViewScreen} /> */}
     {/* <Stack.Screen name="VideoDetails" component={VideoDetailsScreen} /> */}
      <Stack.Screen name="Messages" component={MessagesScreen} />
      <Stack.Screen
        name="Chat"
        component={ChatDetailScreen}
        options={{
          title: 'Chat',
          headerShown: true,
          animation: 'slide_from_right'
        }}
      />
      <Stack.Screen
        name="NewConversation"
        component={NewConversationScreen}
        options={{
          title: 'New Message',
          headerShown: false,
          animation: 'slide_from_right'
        }}
      />
      <Stack.Screen name="UserProfile" component={UserProfileScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
        options={{ title: 'Notifications' }}
      />
      <Stack.Screen
        name="PrivacySettings"
        component={PrivacySettingsScreen}
        options={{ title: 'Privacy' }}
      />
      <Stack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{ title: 'Security' }}
      />
      <Stack.Screen
        name="AccountSettings"
        component={AccountSettingsScreen}
        options={{ title: 'Account' }}
      />
      <Stack.Screen
        name="PersonalDataSettings"
        component={PersonalDataSettingsScreen}
        options={{ title: 'Personal Data' }}
      />
      <Stack.Screen
        name="EditUsername"
        component={EditUsernameScreen}
        options={{ title: 'Username' }}
      />
      <Stack.Screen
        name="EditEmail"
        component={EditEmailScreen}
        options={{ title: 'Email' }}
      />
      <Stack.Screen
        name="EditPhone"
        component={EditPhoneScreen}
        options={{ title: 'Phone Number' }}
      />
      <Stack.Screen
        name="EditDateOfBirthScreen"
        component={EditDateOfBirthScreen}
        options={{ title: 'Date of Birth' }}
      />
      <Stack.Screen
        name="ChangePassword"
        component={ChangePasswordScreen}
        options={{ title: 'Change Password' }}
      />
      <Stack.Screen
        name="BlockedAccounts"
        component={BlockedAccountsScreen}
        options={{ title: 'Blocked Accounts' }}
      />
      <Stack.Screen name="Followers" component={FollowersScreen} />
      <Stack.Screen name="Following" component={FollowingScreen} />
      <Stack.Screen
        name="LoginActivitiesScreen"
        component={LoginActivitiesScreen}
        options={{ title: 'Login Activities' }}
      />
      <Stack.Screen
        name="Setup2FAScreen"
        component={Setup2FAScreen}
        options={{ title: 'Setup 2FA' }}
      />
      <Stack.Screen
        name="GiftGridTest"
        component={GiftGridTestScreen}
        options={{
          title: 'Gift Grid Test',
          headerShown: true,
          animation: 'slide_from_right'
        }}
      />
      <Stack.Screen
        name="PhotoToVideoEditor"
        component={PhotoToVideoEditor}
        options={{
          title: 'Create Video',
          headerShown: false,
          animation: 'slide_from_bottom'
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
