export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Splash: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Onboarding: undefined;
  Login: undefined;
  Signup: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };
  TwoFactor: { email: string; password: string; session?: any };
};

export type MainStackParamList = {
  Tabs: undefined;
  VideoView: { videoId: string };
  VideoDetails: { videoId: string };
  VideoUpload: { videoUri: string; duration?: number };
  Messages: undefined;
  Chat: { conversationId: string };
  UserProfile: { userId: string };
  EditProfile: undefined;
  Settings: undefined;
  NotificationSettings: undefined;
  PrivacySettings: undefined;
  SecuritySettings: undefined;
  AccountSettings: undefined;
  PersonalDataSettings: undefined;
  EditUsername: undefined;
  EditEmail: undefined;
  EditPhone: undefined;
  EditDateOfBirthScreen: undefined;
  ChangePassword: undefined;
  BlockedAccounts: undefined;
  Followers: { userId: string };
  Following: { userId: string };
  LoginActivitiesScreen: undefined;
  Setup2FAScreen: undefined;
  BackupCodesScreen: undefined;
  RecoveryOptionsScreen: undefined;
  GiftGridTest: undefined;
  NewConversation: undefined;
  PhotoToVideoEditor: { photoUri: string };
};

export type TabParamList = {
  Home: undefined;
  Friends: undefined;
  Upload: undefined;
  Inbox: undefined;
  Profile: undefined;
};
