/**
 * TS1 - React Native Social Media App
 * 
 * Implementation Status:
 * - Core Infrastructure: 95% complete
 * - Authentication: 95% complete
 * - User Profiles: 90% complete
 * - Settings: 100% complete
 * - Social Features: 60% complete
 * - Media Features: 60% complete
 * 
 */

import React from 'react';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { ThemeProvider } from './src/contexts/ThemeContext';
import AppNavigator from './src/navigation/AppNavigator';
// import redux provider
import { Provider } from 'react-redux';
import { store } from './src/store';
import 'react-native-url-polyfill/auto';

const App = () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <ThemeProvider>
          <AppNavigator />
        </ThemeProvider>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default App;
