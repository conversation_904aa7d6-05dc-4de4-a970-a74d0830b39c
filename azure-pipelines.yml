# Azure DevOps Pipeline for React Native App

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    exclude:
    - README.md

pr:
  branches:
    include:
    - main
    - develop

variables:
  nodeVersion: '18.x'
  javaVersion: '1.17'
  gradleVersion: '8.0'

stages:
- stage: Test
  displayName: 'Test and Code Quality'
  jobs:
  - job: CodeQuality
    displayName: 'Code Quality & Unit Tests'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - script: npm run type-check
      displayName: 'TypeScript check'
    
    - script: npm run lint
      displayName: 'ESLint'
    
    - script: npx prettier --check "src/**/*.{js,jsx,ts,tsx,json}"
      displayName: 'Prettier check'
    
    - script: npm test -- --coverage --watchAll=false --ci
      displayName: 'Run tests'
    
    - task: PublishTestResults@2
      condition: succeededOrFailed()
      inputs:
        testRunner: JUnit
        testResultsFiles: 'coverage/junit.xml'
    
    - task: PublishCodeCoverageResults@1
      inputs:
        codeCoverageTool: Cobertura
        summaryFileLocation: 'coverage/cobertura-coverage.xml'

- stage: Build
  displayName: 'Build Applications'
  dependsOn: Test
  jobs:
  - job: BuildAndroid
    displayName: 'Build Android'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - task: JavaToolInstaller@0
      inputs:
        versionSpec: $(javaVersion)
        jdkArchitectureOption: 'x64'
        jdkSourceOption: 'PreInstalled'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - task: Gradle@2
      inputs:
        workingDirectory: 'android'
        gradleWrapperFile: 'android/gradlew'
        gradleOptions: '-Xmx3072m'
        publishJUnitResults: false
        tasks: 'assembleDebug'
      displayName: 'Build Android Debug APK'
    
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: 'android/app/build/outputs/apk/debug'
        artifactName: 'android-debug'
      displayName: 'Publish Android Debug APK'

  - job: BuildiOS
    displayName: 'Build iOS'
    pool:
      vmImage: 'macOS-latest'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - task: CocoaPods@0
      inputs:
        workingDirectory: 'ios'
      displayName: 'Install CocoaPods'
    
    - task: Xcode@5
      inputs:
        actions: 'build'
        configuration: 'Debug'
        sdk: 'iphoneos'
        xcWorkspacePath: 'ios/TS1.xcworkspace'
        scheme: 'TS1'
        packageApp: false
      displayName: 'Build iOS'

- stage: Security
  displayName: 'Security Scanning'
  dependsOn: Test
  jobs:
  - job: SecurityScan
    displayName: 'Security Vulnerability Scan'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'
    
    - script: npm ci
      displayName: 'Install dependencies'
    
    - script: npm audit --audit-level moderate
      displayName: 'NPM Audit'
      continueOnError: true
    
    - script: |
        npm install -g snyk
        snyk test --severity-threshold=high
      displayName: 'Snyk Security Scan'
      continueOnError: true
      env:
        SNYK_TOKEN: $(SNYK_TOKEN)

- stage: Deploy
  displayName: 'Deploy to App Stores'
  dependsOn: [Build, Security]
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployAndroid
    displayName: 'Deploy to Google Play Store'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: NodeTool@0
            inputs:
              versionSpec: $(nodeVersion)
            displayName: 'Install Node.js'
          
          - task: JavaToolInstaller@0
            inputs:
              versionSpec: $(javaVersion)
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'
          
          - script: npm ci
            displayName: 'Install dependencies'
          
          - task: DownloadSecureFile@1
            name: keystore
            inputs:
              secureFile: 'release.keystore'
            displayName: 'Download Android Keystore'
          
          - script: cp $(keystore.secureFilePath) android/app/release.keystore
            displayName: 'Copy Keystore'
          
          - task: Gradle@2
            inputs:
              workingDirectory: 'android'
              gradleWrapperFile: 'android/gradlew'
              gradleOptions: '-Xmx3072m'
              publishJUnitResults: false
              tasks: 'bundleRelease'
            env:
              ANDROID_KEYSTORE_PASSWORD: $(ANDROID_KEYSTORE_PASSWORD)
              ANDROID_KEY_ALIAS: $(ANDROID_KEY_ALIAS)
              ANDROID_KEY_PASSWORD: $(ANDROID_KEY_PASSWORD)
            displayName: 'Build Android Release Bundle'
          
          - task: GooglePlayRelease@4
            inputs:
              serviceConnection: 'Google Play Store'
              applicationId: 'com.ts1'
              action: 'SingleBundle'
              bundleFile: 'android/app/build/outputs/bundle/release/app-release.aab'
              track: 'production'
            displayName: 'Deploy to Google Play Store'