# Fastfile for iOS deployment

default_platform(:ios)

platform :ios do
  before_all do
    setup_circle_ci
  end

  desc "Push a new beta build to TestFlight"
  lane :beta do
    increment_build_number(xcodeproj: "TS1.xcodeproj")
    build_app(workspace: "TS1.xcworkspace", scheme: "TS1")
    upload_to_testflight
  end

  desc "Push a new release build to the App Store"
  lane :release do
    increment_build_number(xcodeproj: "TS1.xcodeproj")
    build_app(workspace: "TS1.xcworkspace", scheme: "TS1")
    upload_to_app_store(
      force: true,
      submit_for_review: true,
      automatic_release: false,
      submission_information: {
        add_id_info_limits_tracking: true,
        add_id_info_serves_ads: false,
        add_id_info_tracks_action: true,
        add_id_info_tracks_install: true,
        add_id_info_uses_idfa: true,
        content_rights_has_rights: true,
        content_rights_contains_third_party_content: true,
        export_compliance_platform: 'ios',
        export_compliance_compliance_required: false,
        export_compliance_encryption_updated: false,
        export_compliance_app_type: nil,
        export_compliance_uses_encryption: false,
        export_compliance_is_exempt: false,
        export_compliance_contains_third_party_cryptography: false,
        export_compliance_contains_proprietary_cryptography: false,
        export_compliance_available_on_french_store: false
      }
    )
  end

  desc "Sync certificates and provisioning profiles"
  lane :certificates do
    match(type: "development")
    match(type: "appstore")
  end

  error do |lane, exception|
    # slack(
    #   message: exception.message,
    #   success: false
    # )
  end
end