-- Test script to verify the migration works
-- Run this after applying the main migration

-- Test 1: Check if new columns exist in messages table
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND table_schema = 'public'
ORDER BY column_name;

-- Test 2: Check if new tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('message_reactions', 'conversation_participants', 'typing_indicators', 'user_push_tokens', 'notification_delivery_log');

-- Test 3: Check if RPC functions exist
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('send_message', 'create_conversation', 'mark_messages_as_read');

-- Test 4: Test creating a conversation (replace UUIDs with actual user IDs)
-- SELECT create_conversation(ARRAY['6a580211-78f9-4026-8b07-67bd03b28163'::uuid], 'direct');

-- Test 5: Check notification constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'notifications' 
AND table_schema = 'public';
