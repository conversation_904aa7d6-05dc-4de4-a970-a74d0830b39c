-- TikTok-Style Messaging System Real-time Triggers
-- This file contains triggers for real-time functionality

-- Function to notify message events
CREATE OR REPLACE FUNCTION notify_message_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
    participant_record RECORD;
BEGIN
    -- Prepare notification payload
    notification_payload := jsonb_build_object(
        'event_type', TG_OP,
        'table_name', TG_TABLE_NAME,
        'record', row_to_json(COALESCE(NEW, OLD)),
        'conversation_id', COALESCE(NEW.conversation_id, OLD.conversation_id),
        'timestamp', extract(epoch from now())
    );

    -- Notify all participants in the conversation
    FOR participant_record IN 
        SELECT cp.user_id, u.auth_user_id
        FROM public.conversation_participants cp
        JOIN public.users u ON cp.user_id = u.id
        WHERE cp.conversation_id = COALESCE(NEW.conversation_id, OLD.conversation_id)
    LOOP
        PERFORM pg_notify(
            'messaging_' || participant_record.auth_user_id::text,
            notification_payload::text
        );
    END LOOP;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to notify typing events
CREATE OR REPLACE FUNCTION notify_typing_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
    participant_record RECORD;
BEGIN
    -- Prepare notification payload
    notification_payload := jsonb_build_object(
        'event_type', 'typing_update',
        'conversation_id', COALESCE(NEW.conversation_id, OLD.conversation_id),
        'user_id', COALESCE(NEW.user_id, OLD.user_id),
        'is_typing', COALESCE(NEW.is_typing, false),
        'typing_type', COALESCE(NEW.typing_type, 'text'),
        'timestamp', extract(epoch from now())
    );

    -- Notify all participants except the typing user
    FOR participant_record IN 
        SELECT cp.user_id, u.auth_user_id
        FROM public.conversation_participants cp
        JOIN public.users u ON cp.user_id = u.id
        WHERE cp.conversation_id = COALESCE(NEW.conversation_id, OLD.conversation_id)
        AND cp.user_id != COALESCE(NEW.user_id, OLD.user_id)
    LOOP
        PERFORM pg_notify(
            'messaging_' || participant_record.auth_user_id::text,
            notification_payload::text
        );
    END LOOP;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to notify presence events
CREATE OR REPLACE FUNCTION notify_presence_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
    participant_record RECORD;
BEGIN
    -- Prepare notification payload
    notification_payload := jsonb_build_object(
        'event_type', 'presence_update',
        'user_id', COALESCE(NEW.user_id, OLD.user_id),
        'status', COALESCE(NEW.status, 'offline'),
        'last_seen_at', COALESCE(NEW.last_seen_at, OLD.last_seen_at),
        'is_active', COALESCE(NEW.is_active, false),
        'timestamp', extract(epoch from now())
    );

    -- Notify all users who share conversations with this user
    FOR participant_record IN 
        SELECT DISTINCT cp2.user_id, u.auth_user_id
        FROM public.conversation_participants cp1
        JOIN public.conversation_participants cp2 ON cp1.conversation_id = cp2.conversation_id
        JOIN public.users u ON cp2.user_id = u.id
        WHERE cp1.user_id = COALESCE(NEW.user_id, OLD.user_id)
        AND cp2.user_id != COALESCE(NEW.user_id, OLD.user_id)
    LOOP
        PERFORM pg_notify(
            'messaging_' || participant_record.auth_user_id::text,
            notification_payload::text
        );
    END LOOP;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to notify reaction events
CREATE OR REPLACE FUNCTION notify_reaction_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
    participant_record RECORD;
    message_conversation_id UUID;
BEGIN
    -- Get conversation ID from message
    SELECT conversation_id INTO message_conversation_id
    FROM public.messages 
    WHERE id = COALESCE(NEW.message_id, OLD.message_id);

    -- Prepare notification payload
    notification_payload := jsonb_build_object(
        'event_type', TG_OP || '_reaction',
        'message_id', COALESCE(NEW.message_id, OLD.message_id),
        'conversation_id', message_conversation_id,
        'user_id', COALESCE(NEW.user_id, OLD.user_id),
        'reaction', COALESCE(NEW.reaction, OLD.reaction),
        'reaction_type', COALESCE(NEW.reaction_type, OLD.reaction_type),
        'timestamp', extract(epoch from now())
    );

    -- Notify all participants in the conversation
    FOR participant_record IN 
        SELECT cp.user_id, u.auth_user_id
        FROM public.conversation_participants cp
        JOIN public.users u ON cp.user_id = u.id
        WHERE cp.conversation_id = message_conversation_id
    LOOP
        PERFORM pg_notify(
            'messaging_' || participant_record.auth_user_id::text,
            notification_payload::text
        );
    END LOOP;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to notify delivery status events
CREATE OR REPLACE FUNCTION notify_delivery_event()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload JSONB;
    message_sender_id UUID;
    sender_auth_id UUID;
BEGIN
    -- Get message sender
    SELECT m.sender_id, u.auth_user_id INTO message_sender_id, sender_auth_id
    FROM public.messages m
    JOIN public.users u ON m.sender_id = u.id
    WHERE m.id = COALESCE(NEW.message_id, OLD.message_id);

    -- Only notify the message sender about delivery updates
    IF message_sender_id IS NOT NULL AND sender_auth_id IS NOT NULL THEN
        notification_payload := jsonb_build_object(
            'event_type', 'delivery_update',
            'message_id', COALESCE(NEW.message_id, OLD.message_id),
            'user_id', COALESCE(NEW.user_id, OLD.user_id),
            'status', COALESCE(NEW.status, OLD.status),
            'delivered_at', COALESCE(NEW.delivered_at, OLD.delivered_at),
            'read_at', COALESCE(NEW.read_at, OLD.read_at),
            'timestamp', extract(epoch from now())
        );

        PERFORM pg_notify(
            'messaging_' || sender_auth_id::text,
            notification_payload::text
        );
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to update conversation last activity
CREATE OR REPLACE FUNCTION update_conversation_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.conversations 
    SET 
        last_activity_at = NOW(),
        updated_at = NOW()
    WHERE id = NEW.conversation_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for messages
CREATE TRIGGER messages_notify_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION notify_message_event();

CREATE TRIGGER messages_update_activity_trigger
    AFTER INSERT ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_activity();

-- Create triggers for typing indicators
CREATE TRIGGER typing_indicators_notify_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.typing_indicators
    FOR EACH ROW EXECUTE FUNCTION notify_typing_event();

-- Create triggers for user presence
CREATE TRIGGER user_presence_notify_trigger
    AFTER INSERT OR UPDATE ON public.user_presence
    FOR EACH ROW EXECUTE FUNCTION notify_presence_event();

-- Create triggers for message reactions
CREATE TRIGGER message_reactions_notify_trigger
    AFTER INSERT OR DELETE ON public.message_reactions
    FOR EACH ROW EXECUTE FUNCTION notify_reaction_event();

-- Create triggers for message delivery
CREATE TRIGGER message_delivery_notify_trigger
    AFTER UPDATE ON public.message_delivery
    FOR EACH ROW EXECUTE FUNCTION notify_delivery_event();

-- Function to clean up old typing indicators (called periodically)
CREATE OR REPLACE FUNCTION cleanup_typing_indicators()
RETURNS void AS $$
BEGIN
    DELETE FROM public.typing_indicators 
    WHERE last_activity_at < NOW() - INTERVAL '10 seconds';
END;
$$ LANGUAGE plpgsql;

-- Function to handle message expiration
CREATE OR REPLACE FUNCTION handle_message_expiration()
RETURNS void AS $$
DECLARE
    expired_message RECORD;
    notification_payload JSONB;
    participant_record RECORD;
BEGIN
    -- Find and expire messages
    FOR expired_message IN 
        SELECT id, conversation_id
        FROM public.messages 
        WHERE expires_at IS NOT NULL 
        AND expires_at < NOW() 
        AND is_expired = FALSE
    LOOP
        -- Mark message as expired
        UPDATE public.messages 
        SET is_expired = TRUE 
        WHERE id = expired_message.id;
        
        -- Notify participants about expiration
        notification_payload := jsonb_build_object(
            'event_type', 'message_expired',
            'message_id', expired_message.id,
            'conversation_id', expired_message.conversation_id,
            'timestamp', extract(epoch from now())
        );

        FOR participant_record IN 
            SELECT cp.user_id, u.auth_user_id
            FROM public.conversation_participants cp
            JOIN public.users u ON cp.user_id = u.id
            WHERE cp.conversation_id = expired_message.conversation_id
        LOOP
            PERFORM pg_notify(
                'messaging_' || participant_record.auth_user_id::text,
                notification_payload::text
            );
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable real-time for messaging tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reactions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.typing_indicators;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_presence;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_delivery;
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversation_participants;
