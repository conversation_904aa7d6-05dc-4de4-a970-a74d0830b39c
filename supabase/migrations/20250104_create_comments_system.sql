-- =====================================================
-- Comments System Migration for TS1 App
-- Created: 2025-01-04
-- Description: Complete comments system with nested replies,
--              likes, real-time updates, and proper security
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. COMMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    text TEXT NOT NULL CHECK (char_length(text) >= 1 AND char_length(text) <= 500),
    video_id UUID NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT comments_no_self_parent CHECK (id != parent_id),
    CONSTRAINT comments_text_not_empty CHECK (trim(text) != '')
);

-- Add comment to table
COMMENT ON TABLE public.comments IS 'User comments on videos with support for nested replies';
COMMENT ON COLUMN public.comments.text IS 'Comment content (1-500 characters)';
COMMENT ON COLUMN public.comments.video_id IS 'Reference to the video being commented on';
COMMENT ON COLUMN public.comments.user_id IS 'User who created the comment';
COMMENT ON COLUMN public.comments.parent_id IS 'Parent comment ID for replies (NULL for top-level comments)';

-- =====================================================
-- 2. COMMENT LIKES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.comment_likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    comment_id UUID NOT NULL REFERENCES public.comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Ensure one like per user per comment
    UNIQUE(comment_id, user_id)
);

-- Add comment to table
COMMENT ON TABLE public.comment_likes IS 'User likes on comments';
COMMENT ON COLUMN public.comment_likes.comment_id IS 'Reference to the liked comment';
COMMENT ON COLUMN public.comment_likes.user_id IS 'User who liked the comment';

-- =====================================================
-- 3. INDEXES FOR PERFORMANCE
-- =====================================================

-- Comments indexes
CREATE INDEX IF NOT EXISTS idx_comments_video_id ON public.comments(video_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_video_created ON public.comments(video_id, created_at DESC);

-- Composite index for fetching top-level comments
CREATE INDEX IF NOT EXISTS idx_comments_video_toplevel ON public.comments(video_id, created_at DESC) 
WHERE parent_id IS NULL;

-- Composite index for fetching replies
CREATE INDEX IF NOT EXISTS idx_comments_replies ON public.comments(parent_id, created_at ASC) 
WHERE parent_id IS NOT NULL;

-- Comment likes indexes
CREATE INDEX IF NOT EXISTS idx_comment_likes_comment_id ON public.comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_user_id ON public.comment_likes(user_id);

-- =====================================================
-- 4. FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to get comment like count
CREATE OR REPLACE FUNCTION public.get_comment_like_count(comment_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER 
        FROM public.comment_likes 
        WHERE comment_id = comment_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user liked a comment
CREATE OR REPLACE FUNCTION public.user_liked_comment(comment_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM public.comment_likes 
        WHERE comment_id = comment_uuid AND user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get reply count for a comment
CREATE OR REPLACE FUNCTION public.get_reply_count(comment_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM public.comments
        WHERE parent_id = comment_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment comment replies count
CREATE OR REPLACE FUNCTION public.increment_comment_replies(comment_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.comments
    SET
        reply_count = COALESCE(reply_count, 0) + 1,
        updated_at = NOW()
    WHERE id = comment_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement comment replies count
CREATE OR REPLACE FUNCTION public.decrement_comment_replies(comment_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.comments
    SET
        reply_count = GREATEST(0, COALESCE(reply_count, 0) - 1),
        updated_at = NOW()
    WHERE id = comment_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. TRIGGERS
-- =====================================================

-- Trigger to update updated_at on comments
DROP TRIGGER IF EXISTS trigger_comments_updated_at ON public.comments;
CREATE TRIGGER trigger_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on tables
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;

-- Comments policies
-- Allow anyone to read comments
CREATE POLICY "Comments are viewable by everyone" ON public.comments
    FOR SELECT USING (true);

-- Allow authenticated users to create comments
CREATE POLICY "Users can create comments" ON public.comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own comments
CREATE POLICY "Users can update own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Allow users to delete their own comments
CREATE POLICY "Users can delete own comments" ON public.comments
    FOR DELETE USING (auth.uid() = user_id);

-- Comment likes policies
-- Allow anyone to read comment likes
CREATE POLICY "Comment likes are viewable by everyone" ON public.comment_likes
    FOR SELECT USING (true);

-- Allow authenticated users to like comments
CREATE POLICY "Users can like comments" ON public.comment_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to unlike their own likes
CREATE POLICY "Users can unlike comments" ON public.comment_likes
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- 7. VIEWS FOR OPTIMIZED QUERIES
-- =====================================================

-- View for comments with user data and like counts
-- Using your existing profiles table structure with user_id as primary key
CREATE OR REPLACE VIEW public.comments_with_details AS
SELECT
    c.id,
    c.text,
    c.video_id,
    c.user_id,
    c.parent_id,
    c.created_at,
    c.updated_at,
    -- Get username from auth.users metadata or generate from email
    COALESCE(
        (auth_users.raw_user_meta_data->>'username')::text,
        SPLIT_PART(auth_users.email, '@', 1),
        'user'
    ) as username,
    -- Get full name from auth.users metadata
    COALESCE(
        (auth_users.raw_user_meta_data->>'full_name')::text,
        (auth_users.raw_user_meta_data->>'name')::text,
        'User'
    ) as full_name,
    -- Use profile_picture_url from your profiles table
    p.profile_picture_url as avatar_url,
    COALESCE(like_counts.like_count, 0) as likes,
    COALESCE(reply_counts.reply_count, 0) as reply_count
FROM public.comments c
LEFT JOIN public.profiles p ON c.user_id = p.user_id
LEFT JOIN auth.users auth_users ON c.user_id = auth_users.id
LEFT JOIN (
    SELECT comment_id, COUNT(*) as like_count
    FROM public.comment_likes
    GROUP BY comment_id
) like_counts ON c.id = like_counts.comment_id
LEFT JOIN (
    SELECT parent_id, COUNT(*) as reply_count
    FROM public.comments
    WHERE parent_id IS NOT NULL
    GROUP BY parent_id
) reply_counts ON c.id = reply_counts.parent_id;

-- Grant access to the view
GRANT SELECT ON public.comments_with_details TO authenticated, anon;

-- =====================================================
-- 8. SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Uncomment below to insert sample data for testing
/*
-- Insert sample comments (replace UUIDs with actual user and video IDs)
INSERT INTO public.comments (text, video_id, user_id) VALUES
('Great video! Love the content 🔥', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001'),
('This is amazing, keep it up!', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'),
('First! 🎉', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003');

-- Insert sample replies
INSERT INTO public.comments (text, video_id, user_id, parent_id) VALUES
('Thanks for watching!', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 
 (SELECT id FROM public.comments WHERE text = 'Great video! Love the content 🔥' LIMIT 1));
*/

-- =====================================================
-- 9. GRANTS AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.comments TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.comment_likes TO authenticated;
GRANT SELECT ON public.comments TO anon;
GRANT SELECT ON public.comment_likes TO anon;

-- Grant access to functions
GRANT EXECUTE ON FUNCTION public.get_comment_like_count(UUID) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.user_liked_comment(UUID, UUID) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_reply_count(UUID) TO authenticated, anon;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Add migration record
INSERT INTO public.schema_migrations (version, name, executed_at) 
VALUES ('20250104_001', 'create_comments_system', NOW())
ON CONFLICT (version) DO NOTHING;
