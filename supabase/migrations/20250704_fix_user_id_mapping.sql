-- Fix user ID mapping to use public.users.id consistently
-- Date: 2025-07-04
-- Description: Update functions to work with public.users.id instead of auth.users.id

-- First, fix existing conversation data to use public.users.id
-- Update participants array to use public.users.id instead of auth.users.id
UPDATE public.conversations
SET participants = (
  SELECT array_agg(u.id)
  FROM unnest(participants) AS auth_id
  JOIN public.users u ON u.auth_user_id = auth_id
)
WHERE EXISTS (
  SELECT 1 FROM unnest(participants) AS auth_id
  WHERE auth_id IN (SELECT id FROM auth.users)
  AND auth_id NOT IN (SELECT id FROM public.users)
);

-- Update created_by to use public.users.id
UPDATE public.conversations
SET created_by = (
  SELECT u.id
  FROM public.users u
  WHERE u.auth_user_id = conversations.created_by
)
WHERE created_by IN (SELECT id FROM auth.users)
AND created_by NOT IN (SELECT id FROM public.users);

-- Update existing messages to use public.users.id for sender_id
UPDATE public.messages
SET sender_id = (
  SELECT u.id
  FROM public.users u
  WHERE u.auth_user_id = messages.sender_id
)
WHERE sender_id IN (SELECT id FROM auth.users)
AND sender_id NOT IN (SELECT id FROM public.users);

-- Update create_conversation function to work with public.users.id
CREATE OR REPLACE FUNCTION create_conversation(
  participant_ids uuid[],
  conversation_type character varying DEFAULT 'direct'
)
RETURNS uuid AS $$
DECLARE
  conversation_id uuid;
  current_user_id uuid;
  current_auth_user_id uuid;
BEGIN
  -- Get current auth user
  current_auth_user_id := auth.uid();
  
  -- Get current user's public ID
  SELECT id INTO current_user_id 
  FROM public.users 
  WHERE auth_user_id = current_auth_user_id;
  
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Current user not found in public users table';
  END IF;
  
  -- Create the conversation
  INSERT INTO public.conversations (
    type,
    participants,
    created_by
  ) VALUES (
    conversation_type,
    array_append(participant_ids, current_user_id),
    current_user_id
  ) RETURNING id INTO conversation_id;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update is_conversation_participant function to work with public.users.id
CREATE OR REPLACE FUNCTION is_conversation_participant(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS boolean AS $$
DECLARE
  participants_array uuid[];
  is_participant boolean;
BEGIN
  -- Get the participants array for debugging
  SELECT participants INTO participants_array
  FROM public.conversations
  WHERE id = conversation_id_param;

  -- Log for debugging (remove in production)
  RAISE NOTICE 'Checking conversation %, user %, participants: %',
    conversation_id_param, user_id_param, participants_array;

  -- Check if user is in the conversation participants array
  SELECT (user_id_param = ANY(participants)) INTO is_participant
  FROM public.conversations
  WHERE id = conversation_id_param;

  RAISE NOTICE 'User % is participant: %', user_id_param, COALESCE(is_participant, false);

  RETURN COALESCE(is_participant, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update send_message function to work with public.users.id
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  -- Check if user has access to conversation
  IF NOT is_conversation_participant(conversation_id_param, sender_id_param) THEN
    RAISE EXCEPTION 'User does not have access to this conversation';
  END IF;

  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    reply_to_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    reply_to_message_id_param,
    false
  ) RETURNING id INTO message_id;

  -- Update conversation's last message timestamp
  UPDATE public.conversations 
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
