-- TikTok-Style Messaging System Database Functions
-- This file contains all the database functions for the messaging system

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
    conversation_id_param UUID,
    user_id_param UUID,
    message_ids_param UUID[] DEFAULT NULL
)
R<PERSON><PERSON>NS void AS $$
BEGIN
    -- Update delivery status to 'read' for specific messages or all unread messages
    IF message_ids_param IS NOT NULL THEN
        UPDATE public.message_delivery 
        SET 
            status = 'read',
            read_at = NOW(),
            updated_at = NOW()
        WHERE user_id = user_id_param 
        AND message_id = ANY(message_ids_param)
        AND status != 'read';
    ELSE
        UPDATE public.message_delivery 
        SET 
            status = 'read',
            read_at = NOW(),
            updated_at = NOW()
        WHERE user_id = user_id_param 
        AND message_id IN (
            SELECT m.id FROM public.messages m 
            WHERE m.conversation_id = conversation_id_param 
            AND m.sender_id != user_id_param
        )
        AND status != 'read';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update typing indicator
CREATE OR REPLACE FUNCTION update_typing_indicator(
    conversation_id_param UUID,
    user_id_param UUID,
    is_typing_param BOOLEAN,
    typing_type_param TEXT DEFAULT 'text'
)
RETURNS void AS $$
BEGIN
    -- Insert or update typing indicator
    INSERT INTO public.typing_indicators (
        conversation_id,
        user_id,
        is_typing,
        typing_type,
        last_activity_at
    ) VALUES (
        conversation_id_param,
        user_id_param,
        is_typing_param,
        typing_type_param,
        NOW()
    )
    ON CONFLICT (conversation_id, user_id) 
    DO UPDATE SET 
        is_typing = is_typing_param,
        typing_type = typing_type_param,
        last_activity_at = NOW();
        
    -- Clean up old typing indicators
    DELETE FROM public.typing_indicators 
    WHERE last_activity_at < NOW() - INTERVAL '10 seconds';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
    user_id_param UUID,
    status_param TEXT,
    device_info_param JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    INSERT INTO public.user_presence (
        user_id,
        status,
        last_seen_at,
        is_active,
        device_info
    ) VALUES (
        user_id_param,
        status_param,
        NOW(),
        (status_param = 'online'),
        COALESCE(device_info_param, '{}'::jsonb)
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        status = status_param,
        last_seen_at = NOW(),
        is_active = (status_param = 'online'),
        device_info = COALESCE(device_info_param, user_presence.device_info),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add message reaction
CREATE OR REPLACE FUNCTION add_message_reaction(
    message_id_param UUID,
    user_id_param UUID,
    reaction_param TEXT,
    reaction_type_param TEXT DEFAULT 'emoji'
)
RETURNS void AS $$
DECLARE
    reaction_count INTEGER;
BEGIN
    -- Insert reaction (will fail if already exists due to unique constraint)
    INSERT INTO public.message_reactions (
        message_id,
        user_id,
        reaction,
        reaction_type
    ) VALUES (
        message_id_param,
        user_id_param,
        reaction_param,
        reaction_type_param
    )
    ON CONFLICT (message_id, user_id, reaction) DO NOTHING;
    
    -- Update reaction counts in messages table
    SELECT COUNT(*) INTO reaction_count
    FROM public.message_reactions 
    WHERE message_id = message_id_param 
    AND reaction = reaction_param;
    
    UPDATE public.messages 
    SET 
        reaction_counts = jsonb_set(
            COALESCE(reaction_counts, '{}'::jsonb),
            ARRAY[reaction_param],
            to_jsonb(reaction_count)
        ),
        has_reactions = TRUE,
        updated_at = NOW()
    WHERE id = message_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove message reaction
CREATE OR REPLACE FUNCTION remove_message_reaction(
    message_id_param UUID,
    user_id_param UUID,
    reaction_param TEXT
)
RETURNS void AS $$
DECLARE
    reaction_count INTEGER;
BEGIN
    -- Remove reaction
    DELETE FROM public.message_reactions 
    WHERE message_id = message_id_param 
    AND user_id = user_id_param 
    AND reaction = reaction_param;
    
    -- Update reaction counts in messages table
    SELECT COUNT(*) INTO reaction_count
    FROM public.message_reactions 
    WHERE message_id = message_id_param 
    AND reaction = reaction_param;
    
    IF reaction_count = 0 THEN
        -- Remove the reaction from counts
        UPDATE public.messages 
        SET 
            reaction_counts = reaction_counts - reaction_param,
            updated_at = NOW()
        WHERE id = message_id_param;
    ELSE
        -- Update the count
        UPDATE public.messages 
        SET 
            reaction_counts = jsonb_set(
                reaction_counts,
                ARRAY[reaction_param],
                to_jsonb(reaction_count)
            ),
            updated_at = NOW()
        WHERE id = message_id_param;
    END IF;
    
    -- Check if message still has reactions
    UPDATE public.messages 
    SET has_reactions = (
        SELECT COUNT(*) > 0 
        FROM public.message_reactions 
        WHERE message_id = message_id_param
    )
    WHERE id = message_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create conversation with enhanced features
CREATE OR REPLACE FUNCTION create_conversation(
    creator_id_param UUID,
    participant_ids_param UUID[],
    conversation_type_param TEXT DEFAULT 'direct',
    conversation_name_param TEXT DEFAULT NULL,
    group_settings_param JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_conversation_id UUID;
    participant_id UUID;
BEGIN
    -- Validate conversation type
    IF conversation_type_param NOT IN ('direct', 'group') THEN
        RAISE EXCEPTION 'Invalid conversation type';
    END IF;
    
    -- For direct conversations, ensure only 2 participants
    IF conversation_type_param = 'direct' AND array_length(participant_ids_param, 1) != 1 THEN
        RAISE EXCEPTION 'Direct conversations must have exactly 2 participants (creator + 1 other)';
    END IF;
    
    -- Check if direct conversation already exists
    IF conversation_type_param = 'direct' THEN
        SELECT c.id INTO new_conversation_id
        FROM public.conversations c
        JOIN public.conversation_participants cp1 ON c.id = cp1.conversation_id
        JOIN public.conversation_participants cp2 ON c.id = cp2.conversation_id
        WHERE c.type = 'direct'
        AND cp1.user_id = creator_id_param
        AND cp2.user_id = participant_ids_param[1]
        AND (
            SELECT COUNT(*) FROM public.conversation_participants 
            WHERE conversation_id = c.id
        ) = 2;
        
        IF new_conversation_id IS NOT NULL THEN
            RETURN new_conversation_id;
        END IF;
    END IF;
    
    -- Create new conversation
    INSERT INTO public.conversations (
        type,
        name,
        created_by,
        group_settings
    ) VALUES (
        conversation_type_param,
        conversation_name_param,
        creator_id_param,
        COALESCE(group_settings_param, '{}'::jsonb)
    ) RETURNING id INTO new_conversation_id;
    
    -- Add creator as participant with admin role for groups
    INSERT INTO public.conversation_participants (
        conversation_id,
        user_id,
        role,
        joined_at
    ) VALUES (
        new_conversation_id,
        creator_id_param,
        CASE WHEN conversation_type_param = 'group' THEN 'admin' ELSE 'member' END,
        NOW()
    );
    
    -- Add other participants
    FOREACH participant_id IN ARRAY participant_ids_param
    LOOP
        INSERT INTO public.conversation_participants (
            conversation_id,
            user_id,
            role,
            joined_at
        ) VALUES (
            new_conversation_id,
            participant_id,
            'member',
            NOW()
        );
    END LOOP;
    
    RETURN new_conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get unread message count
CREATE OR REPLACE FUNCTION get_unread_count(
    conversation_id_param UUID,
    user_id_param UUID
)
RETURNS INTEGER AS $$
DECLARE
    unread_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO unread_count
    FROM public.messages m
    LEFT JOIN public.message_delivery md ON m.id = md.message_id AND md.user_id = user_id_param
    WHERE m.conversation_id = conversation_id_param
    AND m.sender_id != user_id_param
    AND (md.status IS NULL OR md.status != 'read');
    
    RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
