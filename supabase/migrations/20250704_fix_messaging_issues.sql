-- Migration: Fix messaging system issues
-- Date: 2025-07-04
-- Description: Fix column names and add missing RPC functions for messaging

-- First, update any existing notification data to match new constraints
UPDATE public.notifications 
SET type = 'system' 
WHERE type IS NULL OR type NOT IN ('message', 'like', 'comment', 'follow', 'mention', 'live', 'video_upload', 'system', 'security', 'reaction');

-- Add missing columns to notifications table if they don't exist
DO $$
BEGIN
    -- Add title column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'title'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN title text;
    END IF;

    -- Add body column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'body'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN body text;
    END IF;

    -- Add priority column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'priority'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN priority character varying DEFAULT 'normal';
    END IF;

    -- Add category column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'category'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN category character varying DEFAULT 'general';
    END IF;
END $$;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL,
  forwarded_from_message_id_param uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    reply_to_message_id,
    forwarded_from_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    reply_to_message_id_param,
    forwarded_from_message_id_param,
    forwarded_from_message_id_param IS NOT NULL
  ) RETURNING id INTO message_id;

  -- Update conversation's last message timestamp
  UPDATE public.conversations 
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create a conversation
CREATE OR REPLACE FUNCTION create_conversation(
  participant_ids uuid[],
  conversation_type character varying DEFAULT 'direct'
)
RETURNS uuid AS $$
DECLARE
  conversation_id uuid;
  current_user_id uuid;
  participant_id uuid;
BEGIN
  -- Get current user
  current_user_id := auth.uid();
  
  -- Create the conversation
  INSERT INTO public.conversations (
    type,
    participants,
    created_by
  ) VALUES (
    conversation_type,
    array_append(participant_ids, current_user_id),
    current_user_id
  ) RETURNING id INTO conversation_id;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS void AS $$
BEGIN
  -- Update messages to mark as read
  UPDATE public.messages 
  SET 
    read_by = CASE 
      WHEN user_id_param = ANY(read_by) THEN read_by
      ELSE array_append(read_by, user_id_param)
    END,
    read_at = read_at || jsonb_build_object(user_id_param::text, NOW()::text),
    status = CASE 
      WHEN status = 'delivered' THEN 'read'
      ELSE status
    END
  WHERE conversation_id = conversation_id_param
    AND sender_id != user_id_param
    AND NOT (user_id_param = ANY(read_by));
END;
$$ LANGUAGE plpgsql;
