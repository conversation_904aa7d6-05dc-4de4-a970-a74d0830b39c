-- Migration: Enhance messaging system for TikTok-like features
-- Date: 2025-07-04
-- Description: Add support for message reactions, enhanced notifications, typing indicators, and improved messaging features

-- Add new columns to messages table for enhanced features
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS thumbnail_url character varying,
ADD COLUMN IF NOT EXISTS file_name character varying,
ADD COLUMN IF NOT EXISTS file_size bigint,
ADD COLUMN IF NOT EXISTS file_type character varying,
ADD COLUMN IF NOT EXISTS duration integer,
ADD COLUMN IF NOT EXISTS forwarded_from_message_id uuid,
ADD COLUMN IF NOT EXISTS is_forwarded boolean NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS expires_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS reaction_counts jsonb NOT NULL DEFAULT '{}'::jsonb;

-- Update message type constraint to include new types
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'messages_type_check'
        AND table_name = 'messages'
    ) THEN
        ALTER TABLE public.messages DROP CONSTRAINT messages_type_check;
    END IF;

    -- Add the new constraint
    ALTER TABLE public.messages ADD CONSTRAINT messages_type_check
    CHECK (type::text = ANY (ARRAY['text'::character varying, 'image'::character varying, 'video'::character varying, 'audio'::character varying, 'file'::character varying, 'voice'::character varying, 'gif'::character varying, 'sticker'::character varying, 'location'::character varying]::text[]));
END $$;

-- Add foreign key constraint for forwarded messages (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'messages_forwarded_from_message_id_fkey'
        AND table_name = 'messages'
    ) THEN
        ALTER TABLE public.messages
        ADD CONSTRAINT messages_forwarded_from_message_id_fkey
        FOREIGN KEY (forwarded_from_message_id) REFERENCES public.messages(id);
    END IF;
END $$;

-- Create message reactions table
CREATE TABLE IF NOT EXISTS public.message_reactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  message_id uuid NOT NULL,
  user_id uuid NOT NULL,
  reaction_type character varying NOT NULL CHECK (reaction_type::text = ANY (ARRAY['like'::character varying, 'love'::character varying, 'laugh'::character varying, 'wow'::character varying, 'sad'::character varying, 'angry'::character varying, 'fire'::character varying, 'heart'::character varying]::text[])),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT message_reactions_pkey PRIMARY KEY (id),
  CONSTRAINT message_reactions_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id) ON DELETE CASCADE,
  CONSTRAINT message_reactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT message_reactions_unique UNIQUE (message_id, user_id)
);

-- Create conversation participants table for better participant management
CREATE TABLE IF NOT EXISTS public.conversation_participants (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL,
  user_id uuid NOT NULL,
  role character varying NOT NULL DEFAULT 'member'::character varying CHECK (role::text = ANY (ARRAY['admin'::character varying, 'member'::character varying, 'moderator'::character varying]::text[])),
  joined_at timestamp with time zone NOT NULL DEFAULT now(),
  left_at timestamp with time zone,
  is_active boolean NOT NULL DEFAULT true,
  last_read_message_id uuid,
  last_read_at timestamp with time zone,
  notification_settings jsonb NOT NULL DEFAULT '{}'::jsonb,
  CONSTRAINT conversation_participants_pkey PRIMARY KEY (id),
  CONSTRAINT conversation_participants_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(id) ON DELETE CASCADE,
  CONSTRAINT conversation_participants_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT conversation_participants_last_read_message_id_fkey FOREIGN KEY (last_read_message_id) REFERENCES public.messages(id),
  CONSTRAINT conversation_participants_unique UNIQUE (conversation_id, user_id)
);

-- Create typing indicators table
CREATE TABLE IF NOT EXISTS public.typing_indicators (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL,
  user_id uuid NOT NULL,
  is_typing boolean NOT NULL DEFAULT true,
  last_activity timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT typing_indicators_pkey PRIMARY KEY (id),
  CONSTRAINT typing_indicators_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(id) ON DELETE CASCADE,
  CONSTRAINT typing_indicators_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT typing_indicators_unique UNIQUE (conversation_id, user_id)
);

-- First, update any existing notification data to match new constraints
UPDATE public.notifications
SET type = 'system'
WHERE type IS NULL OR type NOT IN ('message', 'like', 'comment', 'follow', 'mention', 'live', 'video_upload', 'system', 'security', 'reaction');

-- Enhance notifications table
ALTER TABLE public.notifications
ADD COLUMN IF NOT EXISTS title text,
ADD COLUMN IF NOT EXISTS body text,
ADD COLUMN IF NOT EXISTS is_delivered boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS delivered_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS read_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS priority character varying DEFAULT 'normal'::character varying,
ADD COLUMN IF NOT EXISTS category character varying DEFAULT 'general'::character varying,
ADD COLUMN IF NOT EXISTS action_url text,
ADD COLUMN IF NOT EXISTS expires_at timestamp with time zone;

-- Update any NULL priority/category values
UPDATE public.notifications
SET priority = 'normal'
WHERE priority IS NULL;

UPDATE public.notifications
SET category = 'general'
WHERE category IS NULL;

-- Now add the NOT NULL constraints
ALTER TABLE public.notifications
ALTER COLUMN priority SET NOT NULL,
ALTER COLUMN category SET NOT NULL;

-- Update notifications constraints safely
DO $$
BEGIN
    -- Update type constraint
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'notifications_type_check'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications DROP CONSTRAINT notifications_type_check;
    END IF;

    ALTER TABLE public.notifications ADD CONSTRAINT notifications_type_check
    CHECK (type::text = ANY (ARRAY['message'::character varying, 'like'::character varying, 'comment'::character varying, 'follow'::character varying, 'mention'::character varying, 'live'::character varying, 'video_upload'::character varying, 'system'::character varying, 'security'::character varying, 'reaction'::character varying]::text[]));

    -- Add priority constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'notifications_priority_check'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications ADD CONSTRAINT notifications_priority_check
        CHECK (priority::text = ANY (ARRAY['low'::character varying, 'normal'::character varying, 'high'::character varying, 'urgent'::character varying]::text[]));
    END IF;

    -- Add category constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'notifications_category_check'
        AND table_name = 'notifications'
    ) THEN
        ALTER TABLE public.notifications ADD CONSTRAINT notifications_category_check
        CHECK (category::text = ANY (ARRAY['general'::character varying, 'social'::character varying, 'content'::character varying, 'security'::character varying, 'system'::character varying]::text[]));
    END IF;
END $$;

-- Create notification delivery log table
CREATE TABLE IF NOT EXISTS public.notification_delivery_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  notification_id uuid NOT NULL,
  delivery_method character varying NOT NULL CHECK (delivery_method::text = ANY (ARRAY['push'::character varying, 'email'::character varying, 'sms'::character varying, 'in_app'::character varying]::text[])),
  status character varying NOT NULL CHECK (status::text = ANY (ARRAY['pending'::character varying, 'sent'::character varying, 'delivered'::character varying, 'failed'::character varying, 'bounced'::character varying]::text[])),
  provider character varying,
  external_id text,
  error_message text,
  delivered_at timestamp with time zone,
  opened_at timestamp with time zone,
  clicked_at timestamp with time zone,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT notification_delivery_log_pkey PRIMARY KEY (id),
  CONSTRAINT notification_delivery_log_notification_id_fkey FOREIGN KEY (notification_id) REFERENCES public.notifications(id) ON DELETE CASCADE
);

-- Create user push tokens table
CREATE TABLE IF NOT EXISTS public.user_push_tokens (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  token text NOT NULL,
  platform character varying NOT NULL CHECK (platform::text = ANY (ARRAY['ios'::character varying, 'android'::character varying, 'web'::character varying]::text[])),
  device_type character varying,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_push_tokens_pkey PRIMARY KEY (id),
  CONSTRAINT user_push_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT user_push_tokens_unique UNIQUE (user_id, token)
);

-- Enhance notification settings table
ALTER TABLE public.notification_settings 
ADD COLUMN IF NOT EXISTS sms_notifications boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS mention_notifications boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS reaction_notifications boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS video_upload_notifications boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS security_notifications boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS marketing_notifications boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS quiet_hours_enabled boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS quiet_hours_start time DEFAULT '22:00:00',
ADD COLUMN IF NOT EXISTS quiet_hours_end time DEFAULT '08:00:00',
ADD COLUMN IF NOT EXISTS notification_sound character varying DEFAULT 'default',
ADD COLUMN IF NOT EXISTS vibration_enabled boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS badge_count_enabled boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS preview_enabled boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS group_similar boolean DEFAULT true;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_conversation_created_at ON public.messages(conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender_created_at ON public.messages(sender_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_expires_at ON public.messages(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_message_reactions_message_id ON public.message_reactions(message_id);
CREATE INDEX IF NOT EXISTS idx_message_reactions_user_id ON public.message_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_conversation_id ON public.conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user_id ON public.conversation_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_conversation_id ON public.typing_indicators(conversation_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_last_activity ON public.typing_indicators(last_activity);
CREATE INDEX IF NOT EXISTS idx_notifications_user_created_at ON public.notifications(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_type_created_at ON public.notifications(type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification_id ON public.notification_delivery_log(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_status ON public.notification_delivery_log(status);
CREATE INDEX IF NOT EXISTS idx_user_push_tokens_user_id ON public.user_push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_user_push_tokens_is_active ON public.user_push_tokens(is_active);

-- Enable Row Level Security (RLS) on new tables
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_delivery_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_push_tokens ENABLE ROW LEVEL SECURITY;

-- RLS Policies for message_reactions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE policyname = 'Users can view reactions on messages they can see'
        AND tablename = 'message_reactions'
    ) THEN
        CREATE POLICY "Users can view reactions on messages they can see" ON public.message_reactions
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.messages m
              JOIN public.conversations c ON m.conversation_id = c.id
              WHERE m.id = message_reactions.message_id
              AND auth.uid() = ANY(c.participants)
            )
          );
    END IF;
END $$;

CREATE POLICY "Users can add reactions to messages they can see" ON public.message_reactions
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM public.messages m
      JOIN public.conversations c ON m.conversation_id = c.id
      WHERE m.id = message_reactions.message_id
      AND auth.uid() = ANY(c.participants)
    )
  );

CREATE POLICY "Users can delete their own reactions" ON public.message_reactions
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for conversation_participants
CREATE POLICY "Users can view participants in their conversations" ON public.conversation_participants
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM public.conversations c
      WHERE c.id = conversation_participants.conversation_id
      AND auth.uid() = ANY(c.participants)
    )
  );

CREATE POLICY "Conversation admins can manage participants" ON public.conversation_participants
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.conversation_participants cp
      WHERE cp.conversation_id = conversation_participants.conversation_id
      AND cp.user_id = auth.uid()
      AND cp.role = 'admin'
    )
  );

-- RLS Policies for typing_indicators
CREATE POLICY "Users can view typing indicators in their conversations" ON public.typing_indicators
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.conversations c
      WHERE c.id = typing_indicators.conversation_id
      AND auth.uid() = ANY(c.participants)
    )
  );

CREATE POLICY "Users can manage their own typing indicators" ON public.typing_indicators
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for notification_delivery_log
CREATE POLICY "Users can view their own notification delivery logs" ON public.notification_delivery_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.notifications n
      WHERE n.id = notification_delivery_log.notification_id
      AND n.user_id = auth.uid()
    )
  );

-- RLS Policies for user_push_tokens
CREATE POLICY "Users can manage their own push tokens" ON public.user_push_tokens
  FOR ALL USING (auth.uid() = user_id);

-- Functions for message reactions
CREATE OR REPLACE FUNCTION update_message_reaction_counts()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.messages
    SET reaction_counts = COALESCE(reaction_counts, '{}'::jsonb) ||
        jsonb_build_object(
          NEW.reaction_type,
          COALESCE((reaction_counts->NEW.reaction_type)::int, 0) + 1
        )
    WHERE id = NEW.message_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.messages
    SET reaction_counts = COALESCE(reaction_counts, '{}'::jsonb) ||
        jsonb_build_object(
          OLD.reaction_type,
          GREATEST(COALESCE((reaction_counts->OLD.reaction_type)::int, 0) - 1, 0)
        )
    WHERE id = OLD.message_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updating reaction counts
DROP TRIGGER IF EXISTS trigger_update_message_reaction_counts ON public.message_reactions;
CREATE TRIGGER trigger_update_message_reaction_counts
  AFTER INSERT OR DELETE ON public.message_reactions
  FOR EACH ROW EXECUTE FUNCTION update_message_reaction_counts();

-- Function to clean up expired messages
CREATE OR REPLACE FUNCTION cleanup_expired_messages()
RETURNS void AS $$
BEGIN
  DELETE FROM public.messages
  WHERE expires_at IS NOT NULL
  AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old typing indicators
CREATE OR REPLACE FUNCTION cleanup_old_typing_indicators()
RETURNS void AS $$
BEGIN
  DELETE FROM public.typing_indicators
  WHERE last_activity < NOW() - INTERVAL '30 seconds';
END;
$$ LANGUAGE plpgsql;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL,
  forwarded_from_message_id_param uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    reply_to_message_id,
    forwarded_from_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    reply_to_message_id_param,
    forwarded_from_message_id_param,
    forwarded_from_message_id_param IS NOT NULL
  ) RETURNING id INTO message_id;

  -- Update conversation's last message timestamp
  UPDATE public.conversations
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create a conversation
CREATE OR REPLACE FUNCTION create_conversation(
  participant_ids uuid[],
  conversation_type character varying DEFAULT 'direct'
)
RETURNS uuid AS $$
DECLARE
  conversation_id uuid;
  current_user_id uuid;
  participant_id uuid;
BEGIN
  -- Get current user
  current_user_id := auth.uid();

  -- Create the conversation
  INSERT INTO public.conversations (
    type,
    participants,
    created_by
  ) VALUES (
    conversation_type,
    array_append(participant_ids, current_user_id),
    current_user_id
  ) RETURNING id INTO conversation_id;

  -- Add current user as participant
  INSERT INTO public.conversation_participants (
    conversation_id,
    user_id,
    role
  ) VALUES (
    conversation_id,
    current_user_id,
    'admin'
  );

  -- Add other participants
  FOREACH participant_id IN ARRAY participant_ids
  LOOP
    INSERT INTO public.conversation_participants (
      conversation_id,
      user_id,
      role
    ) VALUES (
      conversation_id,
      participant_id,
      'member'
    );
  END LOOP;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS void AS $$
BEGIN
  -- Update messages to mark as read
  UPDATE public.messages
  SET
    read_by = array_append(read_by, user_id_param),
    read_at = read_at || jsonb_build_object(user_id_param::text, NOW()::text),
    status = CASE
      WHEN status = 'delivered' THEN 'read'
      ELSE status
    END
  WHERE conversation_id = conversation_id_param
    AND sender_id != user_id_param
    AND NOT (user_id_param = ANY(read_by));

  -- Update participant's last read info
  UPDATE public.conversation_participants
  SET
    last_read_at = NOW(),
    last_read_message_id = (
      SELECT id FROM public.messages
      WHERE conversation_id = conversation_id_param
      ORDER BY created_at DESC
      LIMIT 1
    )
  WHERE conversation_id = conversation_id_param
    AND user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;
