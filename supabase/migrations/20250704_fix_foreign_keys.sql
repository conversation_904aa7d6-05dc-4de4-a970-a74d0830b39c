-- Fix foreign key constraints to reference auth.users instead of public.users
-- Date: 2025-07-04
-- Description: Update foreign key constraints to use the correct user table

-- First, check if there are any messages with invalid sender_ids
-- and update them to use auth_user_id from public.users table
UPDATE public.messages
SET sender_id = (
  SELECT auth_user_id
  FROM public.users
  WHERE public.users.id = public.messages.sender_id
)
WHERE sender_id IN (
  SELECT id FROM public.users WHERE auth_user_id IS NOT NULL
);

-- Delete any messages that can't be mapped to auth.users
DELETE FROM public.messages
WHERE sender_id NOT IN (SELECT id FROM auth.users);

-- Fix messages table foreign key constraint
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;
ALTER TABLE public.messages ADD CONSTRAINT messages_sender_id_fkey
FOREIGN KEY (sender_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Fix conversations table - update created_by to use auth_user_id
UPDATE public.conversations
SET created_by = (
  SELECT auth_user_id
  FROM public.users
  WHERE public.users.id = public.conversations.created_by
)
WHERE created_by IN (
  SELECT id FROM public.users WHERE auth_user_id IS NOT NULL
);

-- Delete any conversations that can't be mapped to auth.users
DELETE FROM public.conversations
WHERE created_by NOT IN (SELECT id FROM auth.users);

-- Fix conversations table foreign key constraint
ALTER TABLE public.conversations DROP CONSTRAINT IF EXISTS conversations_created_by_fkey;
ALTER TABLE public.conversations ADD CONSTRAINT conversations_created_by_fkey
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create a simple send_message function that works with auth.users
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  -- Check if user has access to conversation
  IF NOT is_conversation_participant(conversation_id_param, sender_id_param) THEN
    RAISE EXCEPTION 'User does not have access to this conversation';
  END IF;

  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    reply_to_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    reply_to_message_id_param,
    false
  ) RETURNING id INTO message_id;

  -- Update conversation's last message timestamp
  UPDATE public.conversations 
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
