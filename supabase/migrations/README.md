# TikTok-Style Messaging System Database Migrations

This directory contains the complete database migration files for implementing a TikTok-style messaging system with Supabase.

## Migration Files

### 1. `20250105_tiktok_messaging_system.sql`
**Main schema migration** - Creates all the necessary tables, indexes, and basic functions.

**What it includes:**
- Enhanced `messages` table with TikTok-style features
- `message_reactions` table for emoji reactions
- `typing_indicators` table for real-time typing status
- `user_presence` table for online/offline status
- `message_delivery` table for read receipts and delivery tracking
- Enhanced `conversations` and `conversation_participants` tables
- Comprehensive indexes for performance
- Basic utility functions

### 2. `20250105_messaging_functions.sql`
**Database functions** - Contains all the stored procedures for messaging operations.

**Functions included:**
- `send_message()` - Enhanced message sending with validation
- `mark_messages_as_read()` - Bulk read status updates
- `update_typing_indicator()` - Real-time typing indicators
- `update_user_presence()` - User presence management
- `add_message_reaction()` / `remove_message_reaction()` - Reaction system
- `create_conversation()` - Enhanced conversation creation
- `get_unread_count()` - Unread message counting

### 3. `20250105_messaging_rls_policies.sql`
**Row Level Security policies** - Comprehensive security policies for all tables.

**Security features:**
- User can only access conversations they participate in
- Message senders can edit/delete their own messages
- Proper isolation between different conversations
- Secure reaction and typing indicator access
- Admin controls for group conversations

### 4. `20250105_messaging_realtime_triggers.sql`
**Real-time triggers** - Database triggers for real-time functionality.

**Real-time features:**
- Message delivery notifications
- Typing indicator broadcasts
- User presence updates
- Reaction notifications
- Delivery status updates
- Automatic cleanup functions

## How to Apply Migrations

### Option 1: Using Supabase CLI (Recommended)

1. **Initialize Supabase in your project** (if not already done):
   ```bash
   supabase init
   ```

2. **Link to your Supabase project**:
   ```bash
   supabase link --project-ref YOUR_PROJECT_REF
   ```

3. **Apply migrations in order**:
   ```bash
   # Apply main schema
   supabase db push --file supabase/migrations/20250105_tiktok_messaging_system.sql
   
   # Apply functions
   supabase db push --file supabase/migrations/20250105_messaging_functions.sql
   
   # Apply RLS policies
   supabase db push --file supabase/migrations/20250105_messaging_rls_policies.sql
   
   # Apply real-time triggers
   supabase db push --file supabase/migrations/20250105_messaging_realtime_triggers.sql
   ```

### Option 2: Using Supabase Dashboard

1. **Go to your Supabase Dashboard**
2. **Navigate to SQL Editor**
3. **Run each migration file in order**:
   - Copy and paste the content of each file
   - Execute them one by one in the correct order

### Option 3: Using Database URL

If you have direct database access:

```bash
# Replace with your actual database URL
psql "postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres" -f supabase/migrations/20250105_tiktok_messaging_system.sql
psql "postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres" -f supabase/migrations/20250105_messaging_functions.sql
psql "postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres" -f supabase/migrations/20250105_messaging_rls_policies.sql
psql "postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres" -f supabase/migrations/20250105_messaging_realtime_triggers.sql
```

## Post-Migration Setup

### 1. Enable Real-time

In your Supabase Dashboard:
1. Go to **Database** → **Replication**
2. Enable real-time for these tables:
   - `messages`
   - `message_reactions`
   - `typing_indicators`
   - `user_presence`
   - `message_delivery`
   - `conversations`
   - `conversation_participants`

### 2. Configure Storage Buckets

Create storage buckets for media uploads:

```sql
-- Create buckets for media storage
INSERT INTO storage.buckets (id, name, public) VALUES 
('messages', 'messages', true),
('videos', 'videos', true),
('profile', 'profile', true);

-- Set up storage policies
CREATE POLICY "Users can upload to messages bucket" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'messages' AND auth.role() = 'authenticated');

CREATE POLICY "Users can view messages media" ON storage.objects
FOR SELECT USING (bucket_id = 'messages');

-- Similar policies for other buckets...
```

### 3. Set up Periodic Cleanup

Create a cron job or scheduled function to clean up old data:

```sql
-- Example: Clean up old typing indicators every minute
SELECT cron.schedule('cleanup-typing-indicators', '* * * * *', 'SELECT cleanup_typing_indicators();');

-- Example: Handle message expiration every hour
SELECT cron.schedule('handle-message-expiration', '0 * * * *', 'SELECT handle_message_expiration();');
```

## Verification

After applying all migrations, verify the setup:

### 1. Check Tables
```sql
-- Verify all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('messages', 'message_reactions', 'typing_indicators', 'user_presence', 'message_delivery');
```

### 2. Check Functions
```sql
-- Verify functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('send_message', 'mark_messages_as_read', 'update_typing_indicator');
```

### 3. Check RLS Policies
```sql
-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('messages', 'message_reactions', 'typing_indicators');
```

### 4. Test Real-time
```sql
-- Test real-time subscription in your app
-- Subscribe to changes on messages table for a specific conversation
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure you have proper database permissions
   - Check if RLS policies are correctly configured

2. **Function Errors**
   - Verify all referenced tables exist
   - Check function parameter types match table columns

3. **Real-time Not Working**
   - Ensure real-time is enabled for tables in Supabase Dashboard
   - Check if triggers are properly created

4. **Performance Issues**
   - Verify all indexes are created
   - Monitor query performance and add additional indexes if needed

### Getting Help

If you encounter issues:
1. Check the Supabase logs in your dashboard
2. Verify each migration file was applied successfully
3. Test individual functions using the SQL editor
4. Check the real-time logs for connection issues

## Next Steps

After successful migration:
1. Update your application code to use the new schema
2. Test all messaging functionality
3. Configure monitoring and alerting
4. Set up backup and recovery procedures
5. Plan for scaling and performance optimization

The migration provides a solid foundation for a production-ready TikTok-style messaging system with modern real-time features and excellent performance characteristics.
