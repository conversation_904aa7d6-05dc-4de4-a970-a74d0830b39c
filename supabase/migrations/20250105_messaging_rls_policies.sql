-- TikTok-Style Messaging System RLS Policies
-- This file contains all Row Level Security policies for the messaging system

-- Enable RLS on all messaging tables
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_delivery ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;

-- Messages policies
CREATE POLICY "Users can view messages in their conversations" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = messages.conversation_id
            AND cp.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages in their conversations" ON public.messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = messages.conversation_id
            AND cp.user_id = auth.uid()
        )
        AND sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (
        sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own messages" ON public.messages
    FOR DELETE USING (
        sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Message reactions policies
CREATE POLICY "Users can view reactions in their conversations" ON public.message_reactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.messages m
            JOIN public.conversation_participants cp ON m.conversation_id = cp.conversation_id
            WHERE m.id = message_reactions.message_id
            AND cp.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can add reactions to messages in their conversations" ON public.message_reactions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.messages m
            JOIN public.conversation_participants cp ON m.conversation_id = cp.conversation_id
            WHERE m.id = message_reactions.message_id
            AND cp.user_id = auth.uid()
        )
        AND user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can remove their own reactions" ON public.message_reactions
    FOR DELETE USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Typing indicators policies
CREATE POLICY "Users can view typing indicators in their conversations" ON public.typing_indicators
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = typing_indicators.conversation_id
            AND cp.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own typing indicators" ON public.typing_indicators
    FOR ALL USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- User presence policies
CREATE POLICY "Users can view presence of users in their conversations" ON public.user_presence
    FOR SELECT USING (
        user_id IN (
            SELECT DISTINCT cp2.user_id
            FROM public.conversation_participants cp1
            JOIN public.conversation_participants cp2 ON cp1.conversation_id = cp2.conversation_id
            WHERE cp1.user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
        OR user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own presence" ON public.user_presence
    FOR ALL USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Message delivery policies
CREATE POLICY "Users can view delivery status for their messages" ON public.message_delivery
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
        OR EXISTS (
            SELECT 1 FROM public.messages m
            WHERE m.id = message_delivery.message_id
            AND m.sender_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "System can manage message delivery" ON public.message_delivery
    FOR ALL USING (true);

-- Conversations policies
CREATE POLICY "Users can view their conversations" ON public.conversations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversations.id
            AND cp.user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (
        created_by IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Conversation admins can update conversations" ON public.conversations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversations.id
            AND cp.user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
            AND cp.role IN ('admin', 'owner')
        )
    );

-- Conversation participants policies
CREATE POLICY "Users can view participants in their conversations" ON public.conversation_participants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversation_participants.conversation_id
            AND cp.user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can join conversations they're invited to" ON public.conversation_participants
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own participation settings" ON public.conversation_participants
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can leave conversations" ON public.conversation_participants
    FOR DELETE USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Additional policies for enhanced security

-- Policy to prevent users from seeing deleted messages
CREATE POLICY "Hide deleted messages" ON public.messages
    FOR SELECT USING (
        deleted_at IS NULL
        OR sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Policy to prevent access to expired messages
CREATE POLICY "Hide expired messages" ON public.messages
    FOR SELECT USING (
        NOT is_expired
        OR sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Policy for archived conversations
CREATE POLICY "Users can view archived conversations" ON public.conversations
    FOR SELECT USING (
        NOT is_archived
        OR EXISTS (
            SELECT 1 FROM public.conversation_participants cp
            WHERE cp.conversation_id = conversations.id
            AND cp.user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
            AND NOT cp.is_archived
        )
    );

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.messages TO authenticated;
GRANT SELECT, INSERT, DELETE ON public.message_reactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.typing_indicators TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_presence TO authenticated;
GRANT SELECT ON public.message_delivery TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.conversation_participants TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION send_message TO authenticated;
GRANT EXECUTE ON FUNCTION mark_messages_as_read TO authenticated;
GRANT EXECUTE ON FUNCTION update_typing_indicator TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_presence TO authenticated;
GRANT EXECUTE ON FUNCTION add_message_reaction TO authenticated;
GRANT EXECUTE ON FUNCTION remove_message_reaction TO authenticated;
GRANT EXECUTE ON FUNCTION create_conversation TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_count TO authenticated;
