-- Fix messaging system functions
-- Date: 2025-07-04
-- Description: Add missing functions for messaging system and fix foreign key constraints

-- First, fix the foreign key constraint issue
-- The messages table should reference auth.users, not public.users
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;
ALTER TABLE public.messages ADD CONSTRAINT messages_sender_id_fkey
FOREIGN KEY (sender_id) REFERENCES auth.users(id);

-- Also fix conversations table if needed
ALTER TABLE public.conversations DROP CONSTRAINT IF EXISTS conversations_created_by_fkey;
ALTER TABLE public.conversations ADD CONSTRAINT conversations_created_by_fkey
FOREIGN KEY (created_by) REFERENCES auth.users(id);

-- Function to check if user is conversation participant
CREATE OR REPLACE FUNCTION is_conversation_participant(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS boolean AS $$
BEGIN
  -- Check if user is in the conversation participants array
  RETURN EXISTS (
    SELECT 1 FROM public.conversations
    WHERE id = conversation_id_param
    AND user_id_param = ANY(participants)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL,
  forwarded_from_message_id_param uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  -- Check if user has access to conversation
  IF NOT is_conversation_participant(conversation_id_param, sender_id_param) THEN
    RAISE EXCEPTION 'User does not have access to this conversation';
  END IF;

  -- Insert the message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    reply_to_message_id,
    forwarded_from_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    reply_to_message_id_param,
    forwarded_from_message_id_param,
    forwarded_from_message_id_param IS NOT NULL
  ) RETURNING id INTO message_id;

  -- Update conversation's last message timestamp
  UPDATE public.conversations 
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a conversation
CREATE OR REPLACE FUNCTION create_conversation(
  participant_ids uuid[],
  conversation_type character varying DEFAULT 'direct'
)
RETURNS uuid AS $$
DECLARE
  conversation_id uuid;
  current_user_id uuid;
BEGIN
  -- Get current user
  current_user_id := auth.uid();
  
  -- Create the conversation
  INSERT INTO public.conversations (
    type,
    participants,
    created_by
  ) VALUES (
    conversation_type,
    array_append(participant_ids, current_user_id),
    current_user_id
  ) RETURNING id INTO conversation_id;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS void AS $$
BEGIN
  -- Check if user has access to conversation
  IF NOT is_conversation_participant(conversation_id_param, user_id_param) THEN
    RAISE EXCEPTION 'User does not have access to this conversation';
  END IF;

  -- Update messages to mark as read
  UPDATE public.messages 
  SET 
    read_by = CASE 
      WHEN user_id_param = ANY(read_by) THEN read_by
      ELSE array_append(read_by, user_id_param)
    END,
    read_at = read_at || jsonb_build_object(user_id_param::text, NOW()::text),
    status = CASE 
      WHEN status = 'delivered' THEN 'read'
      ELSE status
    END
  WHERE conversation_id = conversation_id_param
    AND sender_id != user_id_param
    AND NOT (user_id_param = ANY(read_by));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
