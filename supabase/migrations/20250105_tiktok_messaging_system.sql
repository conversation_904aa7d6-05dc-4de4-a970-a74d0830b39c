-- TikTok-Style Messaging System Migration
-- This migration enhances the existing messaging system with TikTok-style features

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enhanced messages table with TikTok-style features
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT,
    message_type TEXT NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'video', 'audio', 'voice', 'gif', 'sticker', 'location', 'contact', 'system', 'file')),
    
    -- Media fields
    media_url TEXT,
    thumbnail_url TEXT,
    file_name TEXT,
    file_size BIGINT,
    file_type TEXT,
    duration INTEGER, -- in seconds for audio/video
    
    -- TikTok-style features
    reply_to_message_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    forwarded_from_message_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    thread_id UUID, -- for message threads
    
    -- Message status and metadata
    status TEXT DEFAULT 'sent' CHECK (status IN ('sending', 'sent', 'delivered', 'read', 'failed')),
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMPTZ,
    edit_history JSONB DEFAULT '[]'::jsonb,
    deleted_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    deleted_at TIMESTAMPTZ,
    
    -- Expiration for disappearing messages
    expires_at TIMESTAMPTZ,
    is_expired BOOLEAN DEFAULT FALSE,
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Reaction counts (denormalized for performance)
    reaction_counts JSONB DEFAULT '{}'::jsonb,
    has_reactions BOOLEAN DEFAULT FALSE,
    
    -- Delivery tracking
    delivered_to TEXT[] DEFAULT '{}',
    delivered_at JSONB DEFAULT '{}'::jsonb,
    
    -- Pinning
    is_pinned BOOLEAN DEFAULT FALSE,
    pinned_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    pinned_at TIMESTAMPTZ,
    
    -- Forward count for viral tracking
    forward_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create message reactions table
CREATE TABLE IF NOT EXISTS public.message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    reaction TEXT NOT NULL, -- emoji or reaction code
    reaction_type TEXT DEFAULT 'emoji' CHECK (reaction_type IN ('emoji', 'like', 'love', 'laugh', 'wow', 'sad', 'angry', 'fire', 'heart', 'thumbs_up', 'thumbs_down')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(message_id, user_id, reaction)
);

-- Create typing indicators table
CREATE TABLE IF NOT EXISTS public.typing_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    is_typing BOOLEAN DEFAULT FALSE,
    typing_type TEXT DEFAULT 'text' CHECK (typing_type IN ('text', 'voice', 'media')),
    started_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(conversation_id, user_id)
);

-- Create user presence table
CREATE TABLE IF NOT EXISTS public.user_presence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE UNIQUE,
    status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
    last_seen_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT FALSE,
    device_info JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create message delivery table for detailed tracking
CREATE TABLE IF NOT EXISTS public.message_delivery (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    failed_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(message_id, user_id)
);

-- Enhance conversations table with TikTok-style features
ALTER TABLE public.conversations 
ADD COLUMN IF NOT EXISTS group_settings JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS invite_link TEXT,
ADD COLUMN IF NOT EXISTS invite_link_expires_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_activity_at TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE;

-- Enhance conversation_participants table
ALTER TABLE public.conversation_participants 
ADD COLUMN IF NOT EXISTS custom_nickname TEXT,
ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS notification_settings JSONB DEFAULT '{"muted": false, "push_notifications": true, "message_preview": true, "sound": true, "vibration": true}'::jsonb;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_reply_to ON public.messages(reply_to_message_id);
CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON public.messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_messages_status ON public.messages(status);
CREATE INDEX IF NOT EXISTS idx_messages_expires_at ON public.messages(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_message_reactions_message_id ON public.message_reactions(message_id);
CREATE INDEX IF NOT EXISTS idx_message_reactions_user_id ON public.message_reactions(user_id);

CREATE INDEX IF NOT EXISTS idx_typing_indicators_conversation_id ON public.typing_indicators(conversation_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_user_id ON public.typing_indicators(user_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_last_activity ON public.typing_indicators(last_activity_at);

CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON public.user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_status ON public.user_presence(status);
CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen ON public.user_presence(last_seen_at);

CREATE INDEX IF NOT EXISTS idx_message_delivery_message_id ON public.message_delivery(message_id);
CREATE INDEX IF NOT EXISTS idx_message_delivery_user_id ON public.message_delivery(user_id);
CREATE INDEX IF NOT EXISTS idx_message_delivery_status ON public.message_delivery(status);

CREATE INDEX IF NOT EXISTS idx_conversations_last_activity ON public.conversations(last_activity_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user_id ON public.conversation_participants(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_presence_updated_at BEFORE UPDATE ON public.user_presence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_message_delivery_updated_at BEFORE UPDATE ON public.message_delivery FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-cleanup function for old typing indicators
CREATE OR REPLACE FUNCTION cleanup_old_typing_indicators()
RETURNS void AS $$
BEGIN
    DELETE FROM public.typing_indicators 
    WHERE last_activity_at < NOW() - INTERVAL '10 seconds';
END;
$$ LANGUAGE plpgsql;

-- Auto-expire messages function
CREATE OR REPLACE FUNCTION expire_old_messages()
RETURNS void AS $$
BEGIN
    UPDATE public.messages
    SET is_expired = TRUE
    WHERE expires_at IS NOT NULL
    AND expires_at < NOW()
    AND is_expired = FALSE;
END;
$$ LANGUAGE plpgsql;

-- Enhanced send_message function with TikTok-style features
CREATE OR REPLACE FUNCTION send_message(
    conversation_id_param UUID,
    sender_id_param UUID,
    content_param TEXT DEFAULT NULL,
    message_type_param TEXT DEFAULT 'text',
    file_url_param TEXT DEFAULT NULL,
    file_type_param TEXT DEFAULT NULL,
    file_name_param TEXT DEFAULT NULL,
    file_size_param BIGINT DEFAULT NULL,
    thumbnail_url_param TEXT DEFAULT NULL,
    duration_param INTEGER DEFAULT NULL,
    reply_to_message_id_param UUID DEFAULT NULL,
    forwarded_from_message_id_param UUID DEFAULT NULL,
    thread_id_param UUID DEFAULT NULL,
    expires_at_param TIMESTAMPTZ DEFAULT NULL,
    metadata_param JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_message_id UUID;
    participant_exists BOOLEAN;
BEGIN
    -- Check if sender is a participant in the conversation
    SELECT EXISTS(
        SELECT 1 FROM public.conversation_participants
        WHERE conversation_id = conversation_id_param
        AND user_id = sender_id_param
    ) INTO participant_exists;

    IF NOT participant_exists THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;

    -- Insert the message
    INSERT INTO public.messages (
        conversation_id,
        sender_id,
        content,
        message_type,
        media_url,
        file_type,
        file_name,
        file_size,
        thumbnail_url,
        duration,
        reply_to_message_id,
        forwarded_from_message_id,
        thread_id,
        expires_at,
        metadata
    ) VALUES (
        conversation_id_param,
        sender_id_param,
        content_param,
        message_type_param,
        file_url_param,
        file_type_param,
        file_name_param,
        file_size_param,
        thumbnail_url_param,
        duration_param,
        reply_to_message_id_param,
        forwarded_from_message_id_param,
        thread_id_param,
        expires_at_param,
        COALESCE(metadata_param, '{}'::jsonb)
    ) RETURNING id INTO new_message_id;

    -- Update conversation's last activity
    UPDATE public.conversations
    SET
        last_activity_at = NOW(),
        updated_at = NOW()
    WHERE id = conversation_id_param;

    -- Create delivery records for all participants except sender
    INSERT INTO public.message_delivery (message_id, user_id, status)
    SELECT new_message_id, user_id, 'sent'
    FROM public.conversation_participants
    WHERE conversation_id = conversation_id_param
    AND user_id != sender_id_param;

    -- Update forward count if this is a forwarded message
    IF forwarded_from_message_id_param IS NOT NULL THEN
        UPDATE public.messages
        SET forward_count = forward_count + 1
        WHERE id = forwarded_from_message_id_param;
    END IF;

    RETURN new_message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
