-- Quick fix for immediate messaging issues
-- This addresses the specific errors in the logs

-- 1. Fix the send_message function
CREATE OR REPLACE FUNCTION send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text,
  message_type_param character varying DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param character varying DEFAULT NULL,
  file_name_param character varying DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param character varying DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  message_id uuid;
BEGIN
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param
  ) RETURNING id INTO message_id;

  UPDATE public.conversations 
  SET updated_at = NOW()
  WHERE id = conversation_id_param;

  RETURN message_id;
END;
$$ LANGUAGE plpgsql;

-- 2. Fix the create_conversation function
CREATE OR REPLACE FUNCTION create_conversation(
  participant_ids uuid[],
  conversation_type character varying DEFAULT 'direct'
)
RETURNS uuid AS $$
DECLARE
  conversation_id uuid;
  current_user_id uuid;
BEGIN
  current_user_id := auth.uid();
  
  INSERT INTO public.conversations (
    type,
    participants,
    created_by
  ) VALUES (
    conversation_type,
    array_append(participant_ids, current_user_id),
    current_user_id
  ) RETURNING id INTO conversation_id;

  RETURN conversation_id;
END;
$$ LANGUAGE plpgsql;

-- 3. Fix the mark_messages_as_read function
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS void AS $$
BEGIN
  UPDATE public.messages 
  SET 
    read_by = CASE 
      WHEN user_id_param = ANY(read_by) THEN read_by
      ELSE array_append(read_by, user_id_param)
    END,
    read_at = read_at || jsonb_build_object(user_id_param::text, NOW()::text)
  WHERE conversation_id = conversation_id_param
    AND sender_id != user_id_param
    AND NOT (user_id_param = ANY(read_by));
END;
$$ LANGUAGE plpgsql;
