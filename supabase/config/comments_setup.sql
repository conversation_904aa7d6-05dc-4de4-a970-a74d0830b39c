-- =====================================================
-- Quick Setup Script for Comments System
-- Run this if you need to set up comments on existing project
-- =====================================================

-- Check if profiles table exists and create if needed
DO $$
DECLARE
    profiles_exists BOOLEAN;
    profiles_id_column TEXT;
BEGIN
    -- Check if profiles table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'profiles'
    ) INTO profiles_exists;

    IF NOT profiles_exists THEN
        RAISE NOTICE 'Creating profiles table to match your schema...';

        CREATE TABLE public.profiles (
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
            bio TEXT NULL,
            profile_picture_url TEXT NULL,
            banner_image_url TEXT NULL,
            user_tag TEXT NULL DEFAULT 'Supporter'
        );

        -- <PERSON>reate index
        CREATE INDEX IF NOT EXISTS profiles_user_id_idx ON public.profiles USING btree (user_id);

        -- Enable RLS
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

        -- Policies
        CREATE POLICY "Profiles are viewable by everyone" ON public.profiles
            FOR SELECT USING (true);

        CREATE POLICY "Users can update own profile" ON public.profiles
            FOR UPDATE USING (auth.uid() = user_id);

        RAISE NOTICE 'Profiles table created with user_id column';
    ELSE
        -- Check which ID column exists
        SELECT column_name INTO profiles_id_column
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name IN ('id', 'user_id')
        ORDER BY CASE WHEN column_name = 'id' THEN 1 ELSE 2 END
        LIMIT 1;

        RAISE NOTICE 'Profiles table exists with % column', profiles_id_column;
    END IF;
END
$$;

-- Check if videos table exists (required for comments)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'videos') THEN
        RAISE NOTICE 'Creating videos table...';
        
        CREATE TABLE public.videos (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT,
            description TEXT,
            video_url TEXT NOT NULL,
            thumbnail_url TEXT,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Enable RLS
        ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
        
        -- Policies
        CREATE POLICY "Videos are viewable by everyone" ON public.videos
            FOR SELECT USING (true);
            
        CREATE POLICY "Users can create videos" ON public.videos
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;
END
$$;

-- Verify required tables exist
DO $$
BEGIN
    -- Check profiles
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') THEN
        RAISE EXCEPTION 'profiles table is required for comments system';
    END IF;
    
    -- Check videos (or your video table name)
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'videos') THEN
        RAISE NOTICE 'videos table not found - make sure to update video_id references in comments';
    END IF;
    
    RAISE NOTICE 'Prerequisites check completed successfully';
END
$$;

-- Create schema_migrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.schema_migrations (
    version TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Grant permissions
GRANT SELECT ON public.schema_migrations TO authenticated, anon;

-- =====================================================
-- Test Data Setup (Optional)
-- =====================================================

-- Function to create test data
CREATE OR REPLACE FUNCTION public.create_test_comments_data()
RETURNS void AS $$
DECLARE
    test_user_id UUID;
    test_video_id UUID;
    comment1_id UUID;
    comment2_id UUID;
BEGIN
    -- Get or create test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE NOTICE 'No users found - please create a user first';
        RETURN;
    END IF;
    
    -- Get or create test video
    SELECT id INTO test_video_id FROM public.videos LIMIT 1;
    
    IF test_video_id IS NULL THEN
        -- Create a test video
        INSERT INTO public.videos (title, description, video_url, user_id)
        VALUES ('Test Video', 'A test video for comments', 'https://example.com/video.mp4', test_user_id)
        RETURNING id INTO test_video_id;
    END IF;
    
    -- Create test comments
    INSERT INTO public.comments (text, video_id, user_id)
    VALUES 
        ('This is an amazing video! 🔥', test_video_id, test_user_id),
        ('Great content, keep it up!', test_video_id, test_user_id),
        ('First comment! 🎉', test_video_id, test_user_id)
    RETURNING id INTO comment1_id;
    
    -- Create test replies
    INSERT INTO public.comments (text, video_id, user_id, parent_id)
    VALUES 
        ('Thanks for watching!', test_video_id, test_user_id, comment1_id),
        ('More content coming soon!', test_video_id, test_user_id, comment1_id);
    
    -- Create test likes
    INSERT INTO public.comment_likes (comment_id, user_id)
    VALUES (comment1_id, test_user_id);
    
    RAISE NOTICE 'Test data created successfully';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Cleanup Functions
-- =====================================================

-- Function to clean up test data
CREATE OR REPLACE FUNCTION public.cleanup_test_comments_data()
RETURNS void AS $$
BEGIN
    DELETE FROM public.comment_likes WHERE comment_id IN (
        SELECT id FROM public.comments WHERE text LIKE '%test%' OR text LIKE '%Test%'
    );
    
    DELETE FROM public.comments WHERE text LIKE '%test%' OR text LIKE '%Test%';
    
    DELETE FROM public.videos WHERE title = 'Test Video';
    
    RAISE NOTICE 'Test data cleaned up successfully';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Monitoring and Analytics Functions
-- =====================================================

-- Function to get comment statistics
CREATE OR REPLACE FUNCTION public.get_comment_stats()
RETURNS TABLE (
    total_comments BIGINT,
    total_replies BIGINT,
    total_likes BIGINT,
    avg_comments_per_video NUMERIC,
    most_commented_video UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.comments) as total_comments,
        (SELECT COUNT(*) FROM public.comments WHERE parent_id IS NOT NULL) as total_replies,
        (SELECT COUNT(*) FROM public.comment_likes) as total_likes,
        (SELECT ROUND(AVG(comment_count), 2) FROM (
            SELECT COUNT(*) as comment_count 
            FROM public.comments 
            GROUP BY video_id
        ) sub) as avg_comments_per_video,
        (SELECT video_id FROM public.comments 
         GROUP BY video_id 
         ORDER BY COUNT(*) DESC 
         LIMIT 1) as most_commented_video;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user comment activity
CREATE OR REPLACE FUNCTION public.get_user_comment_activity(user_uuid UUID)
RETURNS TABLE (
    total_comments BIGINT,
    total_likes_received BIGINT,
    total_likes_given BIGINT,
    recent_comments_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.comments WHERE user_id = user_uuid) as total_comments,
        (SELECT COUNT(*) FROM public.comment_likes cl 
         JOIN public.comments c ON cl.comment_id = c.id 
         WHERE c.user_id = user_uuid) as total_likes_received,
        (SELECT COUNT(*) FROM public.comment_likes WHERE user_id = user_uuid) as total_likes_given,
        (SELECT COUNT(*) FROM public.comments 
         WHERE user_id = user_uuid 
         AND created_at > NOW() - INTERVAL '7 days') as recent_comments_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.create_test_comments_data() TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_test_comments_data() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_comment_stats() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_user_comment_activity(UUID) TO authenticated, anon;

-- =====================================================
-- Final Setup Verification
-- =====================================================

-- Verify the setup
DO $$
DECLARE
    table_count INTEGER;
    policy_count INTEGER;
    index_count INTEGER;
    function_count INTEGER;
BEGIN
    -- Count tables
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('comments', 'comment_likes');
    
    -- Count policies
    SELECT COUNT(*) INTO policy_count 
    FROM pg_policies 
    WHERE tablename IN ('comments', 'comment_likes');
    
    -- Count indexes
    SELECT COUNT(*) INTO index_count 
    FROM pg_indexes 
    WHERE tablename IN ('comments', 'comment_likes');
    
    -- Count functions
    SELECT COUNT(*) INTO function_count 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name LIKE '%comment%';
    
    RAISE NOTICE 'Setup verification:';
    RAISE NOTICE '- Tables created: %', table_count;
    RAISE NOTICE '- Policies created: %', policy_count;
    RAISE NOTICE '- Indexes created: %', index_count;
    RAISE NOTICE '- Functions created: %', function_count;
    
    IF table_count >= 2 AND policy_count >= 6 AND index_count >= 8 THEN
        RAISE NOTICE '✅ Comments system setup completed successfully!';
    ELSE
        RAISE NOTICE '⚠️  Setup may be incomplete. Please check the migration.';
    END IF;
END
$$;
