-- =====================================================
-- Test Script for Comments System with Your Profiles Schema
-- =====================================================

-- Test 1: Verify profiles table structure
DO $$
BEGIN
    -- Check if profiles table has the expected structure
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'user_id'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Profiles table has user_id column';
    ELSE
        RAISE EXCEPTION '❌ Profiles table missing user_id column';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'profile_picture_url'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✅ Profiles table has profile_picture_url column';
    ELSE
        RAISE NOTICE '⚠️  Profiles table missing profile_picture_url column';
    END IF;
END
$$;

-- Test 2: Verify comments tables exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'comments') THEN
        RAISE NOTICE '✅ Comments table exists';
    ELSE
        RAISE EXCEPTION '❌ Comments table missing';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'comment_likes') THEN
        RAISE NOTICE '✅ Comment_likes table exists';
    ELSE
        RAISE EXCEPTION '❌ Comment_likes table missing';
    END IF;
END
$$;

-- Test 3: Verify view works with your schema
DO $$
DECLARE
    view_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO view_count 
    FROM information_schema.views 
    WHERE table_name = 'comments_with_details';
    
    IF view_count > 0 THEN
        RAISE NOTICE '✅ comments_with_details view exists';
        
        -- Test the view query
        BEGIN
            PERFORM * FROM public.comments_with_details LIMIT 1;
            RAISE NOTICE '✅ comments_with_details view query works';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ comments_with_details view query failed: %', SQLERRM;
        END;
    ELSE
        RAISE EXCEPTION '❌ comments_with_details view missing';
    END IF;
END
$$;

-- Test 4: Test comment creation (if user exists)
DO $$
DECLARE
    test_user_id UUID;
    test_video_id UUID := gen_random_uuid();
    test_comment_id UUID;
BEGIN
    -- Get first user from auth.users
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE '✅ Found test user: %', test_user_id;
        
        -- Test comment insertion
        INSERT INTO public.comments (text, video_id, user_id)
        VALUES ('Test comment for schema validation', test_video_id, test_user_id)
        RETURNING id INTO test_comment_id;
        
        RAISE NOTICE '✅ Comment created successfully: %', test_comment_id;
        
        -- Test comment like
        INSERT INTO public.comment_likes (comment_id, user_id)
        VALUES (test_comment_id, test_user_id);
        
        RAISE NOTICE '✅ Comment like created successfully';
        
        -- Test view with real data
        PERFORM * FROM public.comments_with_details WHERE id = test_comment_id;
        RAISE NOTICE '✅ View query with real data works';
        
        -- Clean up test data
        DELETE FROM public.comment_likes WHERE comment_id = test_comment_id;
        DELETE FROM public.comments WHERE id = test_comment_id;
        
        RAISE NOTICE '✅ Test data cleaned up';
    ELSE
        RAISE NOTICE '⚠️  No users found - skipping comment creation test';
    END IF;
END
$$;

-- Test 5: Verify RLS policies
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count 
    FROM pg_policies 
    WHERE tablename IN ('comments', 'comment_likes');
    
    IF policy_count >= 6 THEN
        RAISE NOTICE '✅ RLS policies created (% policies found)', policy_count;
    ELSE
        RAISE NOTICE '⚠️  Expected at least 6 RLS policies, found %', policy_count;
    END IF;
END
$$;

-- Test 6: Verify functions exist
DO $$
DECLARE
    function_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO function_count 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name LIKE '%comment%';
    
    IF function_count >= 4 THEN
        RAISE NOTICE '✅ Comment functions created (% functions found)', function_count;
    ELSE
        RAISE NOTICE '⚠️  Expected at least 4 functions, found %', function_count;
    END IF;
END
$$;

-- Test 7: Test database functions
DO $$
DECLARE
    test_result INTEGER;
    test_bool BOOLEAN;
BEGIN
    -- Test get_comment_like_count function
    BEGIN
        SELECT public.get_comment_like_count(gen_random_uuid()) INTO test_result;
        RAISE NOTICE '✅ get_comment_like_count function works (returned %)', test_result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ get_comment_like_count function failed: %', SQLERRM;
    END;
    
    -- Test user_liked_comment function
    BEGIN
        SELECT public.user_liked_comment(gen_random_uuid(), gen_random_uuid()) INTO test_bool;
        RAISE NOTICE '✅ user_liked_comment function works (returned %)', test_bool;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ user_liked_comment function failed: %', SQLERRM;
    END;
END
$$;

-- Test 8: Verify indexes exist
DO $$
DECLARE
    index_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO index_count 
    FROM pg_indexes 
    WHERE tablename IN ('comments', 'comment_likes')
    AND schemaname = 'public';
    
    IF index_count >= 8 THEN
        RAISE NOTICE '✅ Indexes created (% indexes found)', index_count;
    ELSE
        RAISE NOTICE '⚠️  Expected at least 8 indexes, found %', index_count;
    END IF;
END
$$;

-- Final summary
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Comments system schema validation complete!';
    RAISE NOTICE '';
    RAISE NOTICE 'Your profiles table structure:';
    RAISE NOTICE '- Primary key: user_id (UUID)';
    RAISE NOTICE '- Avatar field: profile_picture_url';
    RAISE NOTICE '- Additional fields: bio, banner_image_url, user_tag';
    RAISE NOTICE '';
    RAISE NOTICE 'Comments system is compatible with your existing schema.';
    RAISE NOTICE 'You can now use the comments API in your React Native app!';
END
$$;
