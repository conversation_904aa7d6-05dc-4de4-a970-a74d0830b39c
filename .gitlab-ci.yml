# GitLab CI/CD Pipeline for React Native App

stages:
  - test
  - build
  - security
  - deploy

variables:
  NODE_VERSION: "18"
  JAVA_VERSION: "17"
  ANDROID_COMPILE_SDK: "35"
  ANDROID_BUILD_TOOLS: "35.0.0"
  ANDROID_SDK_TOOLS: "9477386"

# Cache configuration
cache:
  paths:
    - node_modules/
    - .gradle/wrapper
    - .gradle/caches

# Code Quality and Testing
test:
  stage: test
  image: node:18
  before_script:
    - npm ci
  script:
    - npm run type-check
    - npm run lint
    - npx prettier --check "src/**/*.{js,jsx,ts,tsx,json}"
    - npm test -- --coverage --watchAll=false
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week

# Android Build
build:android:
  stage: build
  image: openjdk:17-jdk
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl unzip
    - curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    - apt-get install -y nodejs
    - npm ci
    # Install Android SDK
    - wget --quiet --output-document=android-sdk.zip https://dl.google.com/android/repository/commandlinetools-linux-${ANDROID_SDK_TOOLS}_latest.zip
    - unzip -q android-sdk.zip -d android-sdk-linux
    - mkdir -p $ANDROID_HOME/cmdline-tools
    - mv android-sdk-linux/cmdline-tools $ANDROID_HOME/cmdline-tools/latest
    - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
    - yes | sdkmanager --licenses
    - sdkmanager "platforms;android-${ANDROID_COMPILE_SDK}" "build-tools;${ANDROID_BUILD_TOOLS}"
  script:
    - cd android
    - chmod +x ./gradlew
    - ./gradlew assembleDebug
  artifacts:
    paths:
      - android/app/build/outputs/apk/debug/app-debug.apk
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

# Security Scanning
security:
  stage: security
  image: node:18
  before_script:
    - npm ci
  script:
    - npm audit --audit-level moderate
    - npx snyk test --severity-threshold=high || true
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

# Production Build
build:android:release:
  stage: build
  image: openjdk:17-jdk
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl unzip
    - curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    - apt-get install -y nodejs
    - npm ci
    # Install Android SDK
    - wget --quiet --output-document=android-sdk.zip https://dl.google.com/android/repository/commandlinetools-linux-${ANDROID_SDK_TOOLS}_latest.zip
    - unzip -q android-sdk.zip -d android-sdk-linux
    - mkdir -p $ANDROID_HOME/cmdline-tools
    - mv android-sdk-linux/cmdline-tools $ANDROID_HOME/cmdline-tools/latest
    - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
    - yes | sdkmanager --licenses
    - sdkmanager "platforms;android-${ANDROID_COMPILE_SDK}" "build-tools;${ANDROID_BUILD_TOOLS}"
    # Decode keystore
    - echo $ANDROID_KEYSTORE_BASE64 | base64 -d > android/app/release.keystore
  script:
    - cd android
    - chmod +x ./gradlew
    - ./gradlew assembleRelease
  artifacts:
    paths:
      - android/app/build/outputs/apk/release/app-release.apk
    expire_in: 1 month
  only:
    - main
  when: manual

# Deploy to Google Play Store
deploy:android:
  stage: deploy
  image: ruby:3.1
  before_script:
    - gem install fastlane
  script:
    - cd android
    - fastlane supply --aab app/build/outputs/bundle/release/app-release.aab
  dependencies:
    - build:android:release
  only:
    - main
  when: manual
  environment:
    name: production
    url: https://play.google.com/store/apps/details?id=com.ts1