# TikTok-Style Messaging API Guide

## Overview

This guide provides comprehensive documentation for the TikTok-style messaging API implementation, including endpoints, hooks, and usage examples.

## API Endpoints

### Messages API

#### Send Message
```typescript
useSendMessageMutation()

// Parameters
interface SendMessageRequest {
  conversation_id: string;
  content?: string;
  message_type?: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'gif' | 'sticker' | 'location' | 'contact' | 'system' | 'file';
  media_url?: string;
  file_url?: string;
  thumbnail_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  duration?: number;
  reply_to_message_id?: string;
  forwarded_from_message_id?: string;
  thread_id?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

// Usage
const [sendMessage, { isLoading }] = useSendMessageMutation();

await sendMessage({
  conversation_id: 'conv-123',
  content: 'Hello world!',
  message_type: 'text'
});
```

#### Get Messages
```typescript
useGetMessagesQuery(conversationId)

// Returns
interface MessageWithSender {
  id: string;
  conversation_id: string;
  sender_id: string;
  content?: string;
  type: string;
  media_url?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  sender?: UserProfile;
  reactions?: MessageReaction[];
  // ... other fields
}

// Usage
const { data: messages, isLoading, error } = useGetMessagesQuery('conv-123');
```

#### Mark Messages as Read
```typescript
useMarkMessagesAsReadMutation()

// Usage
const [markAsRead] = useMarkMessagesAsReadMutation();

await markAsRead({
  conversation_id: 'conv-123',
  message_ids: ['msg-1', 'msg-2'] // Optional: specific messages
});
```

#### Message Reactions
```typescript
// Add Reaction
useAddMessageReactionMutation()

const [addReaction] = useAddMessageReactionMutation();
await addReaction({
  messageId: 'msg-123',
  reaction: '❤️',
  reactionType: 'emoji'
});

// Remove Reaction
useRemoveMessageReactionMutation()

const [removeReaction] = useRemoveMessageReactionMutation();
await removeReaction({
  messageId: 'msg-123',
  reaction: '❤️'
});
```

### Conversations API

#### Get Conversations
```typescript
useGetConversationsQuery()

// Returns
interface ConversationWithDetails {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  avatar_url?: string;
  last_message?: MessageWithSender;
  unread_count: number;
  is_pinned: boolean;
  other_participant?: UserProfile;
  participants?: ConversationParticipant[];
  // ... other fields
}

// Usage
const { data: conversations } = useGetConversationsQuery();
```

#### Create Conversation
```typescript
useCreateConversationMutation()

// Usage
const [createConversation] = useCreateConversationMutation();

await createConversation({
  participant_ids: ['user-123'],
  conversation_type: 'direct'
});
```

### Real-Time Features

#### Typing Indicators
```typescript
useUpdateTypingIndicatorMutation()

const [updateTyping] = useUpdateTypingIndicatorMutation();

// Start typing
await updateTyping({
  conversation_id: 'conv-123',
  is_typing: true,
  typing_type: 'text'
});

// Stop typing
await updateTyping({
  conversation_id: 'conv-123',
  is_typing: false
});

// Get typing indicators
useGetTypingIndicatorsQuery(conversationId)
```

#### User Presence
```typescript
useUpdateUserPresenceMutation()

const [updatePresence] = useUpdateUserPresenceMutation();

await updatePresence({
  status: 'online',
  device_info: { platform: 'mobile' }
});

// Get user presence
useGetUserPresenceQuery(conversationId)
```

## Custom Hooks

### useTikTokMessaging

Comprehensive hook for TikTok-style messaging functionality.

```typescript
const [state, actions] = useTikTokMessaging({
  conversationId: 'conv-123',
  currentUserId: 'user-456',
  autoMarkAsRead: true,
  enableTypingIndicators: true,
  enablePresence: true
});

// State
const {
  messages,
  isLoading,
  isSending,
  isUploading,
  uploadProgress,
  typingUsers,
  isTyping,
  onlineUsers,
  replyingTo,
  selectedMessages,
  showScrollToBottom
} = state;

// Actions
const {
  sendTextMessage,
  sendMediaMessage,
  sendVoiceMessage,
  replyToMessage,
  addReaction,
  removeReaction,
  startTyping,
  stopTyping,
  handleTextChange,
  setReplyingTo,
  markAsRead,
  scrollToBottom
} = actions;
```

### useRealtimeMessaging

Real-time messaging connection and event handling.

```typescript
const {
  sendTypingIndicator,
  updatePresence,
  isConnected
} = useRealtimeMessaging({
  conversationId: 'conv-123',
  userId: 'user-456',
  onNewMessage: (message) => console.log('New message:', message),
  onTypingUpdate: (typing) => console.log('Typing update:', typing),
  onPresenceUpdate: (presence) => console.log('Presence update:', presence)
});
```

### useTypingIndicators

Typing indicator management.

```typescript
const {
  typingUsers,
  isTyping,
  hasTypingUsers,
  typingText,
  startTyping,
  stopTyping,
  handleTextChange,
  handleVoiceRecording,
  handleMediaSharing
} = useTypingIndicators({
  conversationId: 'conv-123',
  currentUserId: 'user-456',
  autoStopDelay: 3000
});
```

### useUserPresence

User presence and online status management.

```typescript
const {
  currentStatus,
  userPresences,
  onlineUsersCount,
  isUserOnline,
  formatLastSeen,
  setBusy,
  setAway,
  setOnline,
  handleUserActivity
} = useUserPresence({
  conversationId: 'conv-123',
  currentUserId: 'user-456',
  autoAwayDelay: 300000
});
```

## Real-Time Context

### RealtimeMessagingProvider

Wrap your app with the real-time messaging provider.

```typescript
import { RealtimeMessagingProvider } from './contexts/RealtimeMessagingContext';

function App() {
  return (
    <RealtimeMessagingProvider>
      <YourAppComponents />
    </RealtimeMessagingProvider>
  );
}
```

### useRealtimeMessagingContext

Access real-time messaging context.

```typescript
const {
  isConnected,
  typingUsers,
  isTyping,
  startTyping,
  stopTyping,
  handleTextChange,
  currentStatus,
  userPresences,
  onlineUsersCount,
  isUserOnline,
  currentConversationId,
  setCurrentConversationId
} = useRealtimeMessagingContext();
```

## Media Upload API

### Enhanced Media Upload

```typescript
import { 
  uploadMessageMedia, 
  uploadVoiceMessage, 
  uploadMessageImage, 
  uploadMessageVideo 
} from '../services/media/media-upload';

// General media upload
const result = await uploadMessageMedia(
  filePath,
  conversationId,
  {
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 300,
    quality: 0.8
  }
);

// Voice message
const voiceResult = await uploadVoiceMessage(
  audioPath,
  conversationId,
  { maxDuration: 60, quality: 0.7 }
);

// Image upload
const imageResult = await uploadMessageImage(
  imagePath,
  conversationId,
  { quality: 0.85, maxWidth: 1080 }
);

// Video upload
const videoResult = await uploadMessageVideo(
  videoPath,
  conversationId,
  { 
    generateThumbnail: true,
    compressVideo: true,
    maxDuration: 300
  }
);
```

## Error Handling

### API Error Responses

```typescript
interface APIError {
  message: string;
  code?: string;
  details?: any;
}

// Usage with try-catch
try {
  await sendMessage(messageData);
} catch (error) {
  console.error('Send message failed:', error);
  // Handle error appropriately
}

// Usage with RTK Query
const [sendMessage, { error, isError }] = useSendMessageMutation();

if (isError) {
  console.error('Mutation error:', error);
}
```

### Real-Time Connection Errors

```typescript
const { isConnected } = useRealtimeMessagingContext();

if (!isConnected) {
  // Show connection status to user
  // Implement retry logic
}
```

## Performance Optimization

### Message Pagination

```typescript
const { 
  data: messages, 
  fetchNextPage, 
  hasNextPage,
  isFetchingNextPage 
} = useGetMessagesQuery(conversationId, {
  // Enable pagination
  keepPreviousData: true
});
```

### Selective Real-Time Subscriptions

```typescript
// Only subscribe to current conversation
useEffect(() => {
  setCurrentConversationId(conversationId);
  return () => setCurrentConversationId(undefined);
}, [conversationId]);
```

### Optimistic Updates

```typescript
const [sendMessage] = useSendMessageMutation({
  // Optimistic update
  onQueryStarted: async (messageData, { dispatch, queryFulfilled }) => {
    const patchResult = dispatch(
      messagesApi.util.updateQueryData('getMessages', conversationId, (draft) => {
        draft.push({
          ...messageData,
          id: 'temp-' + Date.now(),
          created_at: new Date().toISOString(),
          status: 'sending'
        });
      })
    );

    try {
      await queryFulfilled;
    } catch {
      patchResult.undo();
    }
  }
});
```

## Best Practices

### 1. Message State Management
- Use optimistic updates for better UX
- Handle offline scenarios gracefully
- Implement proper error boundaries

### 2. Real-Time Features
- Clean up subscriptions properly
- Handle connection drops
- Implement exponential backoff for reconnection

### 3. Media Handling
- Show upload progress
- Implement proper error handling
- Use thumbnails for better performance

### 4. Performance
- Implement virtual scrolling for large message lists
- Use pagination for message history
- Optimize re-renders with proper memoization

### 5. User Experience
- Provide immediate feedback for user actions
- Show connection status
- Handle edge cases gracefully

This API guide provides the foundation for implementing a robust, TikTok-style messaging system with modern real-time features and excellent user experience.
