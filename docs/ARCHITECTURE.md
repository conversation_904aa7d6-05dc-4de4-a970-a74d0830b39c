# 🏗️ Architecture Guide

## Overview

TS1 follows **Clean Architecture** principles with a focus on maintainability, testability, and scalability. The architecture is designed to separate concerns and create clear boundaries between different layers of the application.

## 📐 Architecture Principles

### 1. **Separation of Concerns**
- **UI Components**: Focus only on presentation logic
- **Custom Hooks**: Handle business logic and API interactions
- **Services**: External integrations (Supabase, Twilio, etc.)
- **Utils**: Pure functions and helpers

### 2. **Dependency Inversion**
- Components depend on abstractions (custom hooks), not concrete implementations
- API layer is abstracted through custom hooks
- Easy to mock and test individual layers

### 3. **Single Responsibility**
- Each component has one clear purpose
- Maximum 300 lines per file
- Focused, reusable modules

## 🏛️ Layer Structure

```
┌─────────────────────────────────────┐
│           UI Layer                  │
│  (Components, Screens, Navigation)  │
├─────────────────────────────────────┤
│         Business Logic Layer        │
│      (Custom Hooks, State)         │
├─────────────────────────────────────┤
│          Data Layer                 │
│    (RTK Query, Redux Store)         │
├─────────────────────────────────────┤
│         Service Layer               │
│   (Supabase, External APIs)        │
└─────────────────────────────────────┘
```

### **UI Layer**
- **Screens**: Top-level route components
- **Components**: Reusable UI building blocks
- **Navigation**: Route configuration and navigation logic

**Responsibilities:**
- Render UI based on props and state
- Handle user interactions
- Call custom hooks for business logic
- No direct API calls or complex business logic

### **Business Logic Layer**
- **Custom Hooks**: Encapsulate business logic and API interactions
- **Redux Slices**: Global state management
- **Context Providers**: Theme, auth, and other app-wide state

**Responsibilities:**
- Coordinate between UI and Data layers
- Handle complex business logic
- Manage loading states and error handling
- Provide clean APIs to UI components

### **Data Layer**
- **RTK Query APIs**: Server state management and caching
- **Redux Store**: Client state management
- **Type Definitions**: TypeScript interfaces and types

**Responsibilities:**
- Fetch and cache server data
- Manage API calls and responses
- Handle data transformations
- Provide type safety

### **Service Layer**
- **Supabase Client**: Database, auth, and storage operations
- **External APIs**: Twilio, media processing, etc.
- **Utilities**: Helper functions and constants

**Responsibilities:**
- Interface with external services
- Handle low-level API communications
- Provide service-specific abstractions

## 📁 Directory Structure

```
src/
├── components/              # UI Components
│   ├── common/             # Generic reusable components
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── Text.tsx
│   ├── messages/           # Domain-specific components
│   │   ├── ChatHeader.tsx
│   │   ├── MessageItem.tsx
│   │   └── ChatInput.tsx
│   ├── video/              # Video-related components
│   │   ├── VideoPlayer.tsx
│   │   ├── VideoCard.tsx
│   │   └── form/           # Sub-components
│   │       ├── VideoFormField.tsx
│   │       └── TagsManager.tsx
│   └── phone/              # Phone verification components
│       ├── CountrySelector.tsx
│       └── PhoneNumberInput.tsx
├── hooks/                  # Custom Hooks
│   ├── api/               # API abstraction hooks
│   │   ├── useMessaging.ts
│   │   ├── useVideoLikes.ts
│   │   └── useUserProfile.ts
│   ├── useAuth.ts         # Authentication logic
│   ├── useMediaHandlers.ts # Media upload logic
│   └── useCountryDetection.ts
├── screens/               # Screen Components
│   ├── auth/             # Authentication screens
│   ├── messages/         # Messaging screens
│   ├── profile/          # Profile screens
│   └── camera/           # Camera screens
├── store/                # Redux Store
│   ├── api/             # RTK Query APIs
│   │   ├── endpoints/   # API endpoint definitions
│   │   └── utils/       # API utilities
│   ├── slices/          # Redux slices
│   └── hooks.ts         # Typed Redux hooks
├── types/               # TypeScript Definitions
│   ├── base.ts         # Base types
│   ├── user.ts         # User-related types
│   ├── messaging.ts    # Messaging types
│   └── video.ts        # Video types
├── services/            # External Services
│   ├── supabase/       # Supabase integration
│   ├── twilio/         # SMS verification
│   └── media/          # Media processing
├── utils/               # Utility Functions
│   ├── logger.ts       # Logging utilities
│   ├── phoneUtils.ts   # Phone number utilities
│   └── responsive.ts   # Responsive design helpers
└── navigation/          # Navigation Configuration
    ├── types.ts        # Navigation types
    └── index.tsx       # Navigation setup
```

## 🔄 Data Flow

### 1. **User Interaction Flow**
```
User Action → Component → Custom Hook → RTK Query → Supabase → Response
     ↓           ↓           ↓            ↓          ↓         ↓
   onClick → handleSend → sendMessage → API Call → Database → Success/Error
```

### 2. **State Management Flow**
```
Server State (RTK Query) ←→ Custom Hooks ←→ Components
                ↓                              ↑
Client State (Redux) ←→ Context Providers ←→ App State
```

### 3. **Error Handling Flow**
```
API Error → Custom Hook → User Notification
    ↓           ↓              ↓
 Network    Error Logging   Alert/Toast
```

## 🎯 Design Patterns

### 1. **Custom Hook Pattern**
Encapsulate business logic in reusable hooks:

```typescript
// ✅ Good: Custom hook with business logic
export const useMessaging = () => {
  const { data: conversations } = useGetConversationsQuery();
  
  const createConversation = useCallback(async (userId: string) => {
    // Business logic here
  }, []);
  
  return { conversations, createConversation };
};

// ✅ Component uses clean API
const MessagesScreen = () => {
  const { conversations, createConversation } = useMessaging();
  // UI logic only
};
```

### 2. **Compound Component Pattern**
Break complex components into smaller, focused pieces:

```typescript
// ✅ Main component
const VideoMetadataForm = () => (
  <ScrollView>
    <VideoFormField label="Title" />
    <VideoTypeSelector />
    <TagsManager />
    <PrivacySelector />
  </ScrollView>
);

// ✅ Each sub-component has single responsibility
const VideoFormField = ({ label, value, onChangeText }) => {
  // Focused on form field logic only
};
```

### 3. **Provider Pattern**
Share state and logic across components:

```typescript
// ✅ Theme provider
export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(defaultTheme);
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

## 🧪 Testing Strategy

### 1. **Unit Tests**
- Test custom hooks in isolation
- Test utility functions
- Test component logic

### 2. **Integration Tests**
- Test component + hook interactions
- Test API integration flows
- Test navigation flows

### 3. **E2E Tests**
- Test complete user journeys
- Test critical app flows
- Test cross-platform compatibility

## 📊 Performance Considerations

### 1. **Component Optimization**
- Use `React.memo` for expensive components
- Implement proper `useCallback` and `useMemo`
- Avoid unnecessary re-renders

### 2. **State Management**
- Use RTK Query for server state caching
- Minimize Redux store size
- Implement proper cache invalidation

### 3. **Bundle Optimization**
- Code splitting with lazy loading
- Tree shaking for unused code
- Optimize image and media assets

## 🔒 Security Considerations

### 1. **Data Protection**
- Use Supabase RLS (Row Level Security)
- Validate all user inputs
- Sanitize data before storage

### 2. **Authentication**
- Secure token storage
- Proper session management
- Multi-factor authentication support

### 3. **API Security**
- Rate limiting on API calls
- Input validation and sanitization
- Secure environment variable handling

## 🚀 Scalability

### 1. **Code Organization**
- Modular architecture supports team scaling
- Clear boundaries between domains
- Consistent patterns across codebase

### 2. **Performance Scaling**
- Efficient state management
- Optimized rendering patterns
- Proper caching strategies

### 3. **Feature Scaling**
- Easy to add new features
- Reusable component library
- Consistent development patterns

This architecture ensures the codebase remains maintainable, testable, and scalable as the team and application grow.
