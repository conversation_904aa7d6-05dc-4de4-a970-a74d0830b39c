# 🔗 API Hooks Guide

## Overview

API Hooks provide a clean abstraction layer between UI components and the data layer. They encapsulate business logic, error handling, and loading states, making components simpler and more maintainable.

## 🎯 Why Use API Hooks?

### ❌ **Before: Direct RTK Query Usage**
```typescript
// Component with mixed concerns
const MessagesScreen = () => {
  const { data: conversations = [], isLoading, error } = useGetConversationsQuery();
  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();
  
  const handleSend = async (content: string) => {
    try {
      await sendMessage({
        conversation_id: conversationId,
        content,
        message_type: 'text',
      }).unwrap();
      Alert.alert('Success', 'Message sent!');
    } catch (error) {
      Alert.alert('Error', 'Failed to send message');
    }
  };
  
  // UI logic mixed with API logic
};
```

### ✅ **After: Custom API Hooks**
```typescript
// Clean component focused on UI
const MessagesScreen = () => {
  const { conversations, isLoadingConversations } = useMessaging();
  const { sendTextMessage } = useMessageSender(conversationId);
  
  const handleSend = (content: string) => {
    sendTextMessage(content); // Error handling is automatic
  };
  
  // Pure UI logic
};
```

## 📚 Available API Hooks

### 1. **useMessaging**
Manages conversations and messaging operations.

```typescript
import { useMessaging } from '../hooks/api';

const { 
  conversations,           // ConversationWithDetails[]
  isLoadingConversations, // boolean
  conversationsError,     // Error | null
  refetchConversations,   // () => void
  createConversation,     // (userId: string) => Promise<string | null>
  isCreatingConversation  // boolean
} = useMessaging();
```

**Example Usage:**
```typescript
const MessagesScreen = () => {
  const { conversations, createConversation } = useMessaging();
  
  const handleStartChat = async (userId: string) => {
    const conversationId = await createConversation(userId);
    if (conversationId) {
      navigation.navigate('ChatDetail', { conversationId });
    }
  };
  
  return (
    <FlatList
      data={conversations}
      renderItem={({ item }) => (
        <ConversationItem conversation={item} />
      )}
    />
  );
};
```

### 2. **useConversationMessages**
Manages messages within a specific conversation.

```typescript
import { useConversationMessages } from '../hooks/api';

const {
  messages,              // MessageWithSender[]
  isLoadingMessages,     // boolean
  messagesError,         // Error | null
  refetchMessages,       // () => void
  sendMessage,           // (data: MessageData) => Promise<boolean>
  isSendingMessage,      // boolean
  markMessagesAsRead,    // () => Promise<boolean>
  isMarkingAsRead        // boolean
} = useConversationMessages(conversationId);
```

**Example Usage:**
```typescript
const ChatDetailScreen = ({ route }) => {
  const { conversationId } = route.params;
  const { messages, markMessagesAsRead } = useConversationMessages(conversationId);
  
  useEffect(() => {
    markMessagesAsRead();
  }, [messages.length]);
  
  return (
    <FlatList
      data={messages}
      renderItem={({ item }) => <MessageItem message={item} />}
    />
  );
};
```

### 3. **useMessageSender**
Simplified message sending with type-specific methods.

```typescript
import { useMessageSender } from '../hooks/api';

const {
  sendTextMessage,    // (content: string) => Promise<boolean>
  sendMediaMessage,   // (fileUrl, type, fileName?, fileSize?, thumbnail?) => Promise<boolean>
  isSendingMessage    // boolean
} = useMessageSender(conversationId);
```

**Example Usage:**
```typescript
const ChatInput = ({ conversationId }) => {
  const [text, setText] = useState('');
  const { sendTextMessage, isSendingMessage } = useMessageSender(conversationId);
  
  const handleSend = async () => {
    if (text.trim()) {
      const success = await sendTextMessage(text.trim());
      if (success) {
        setText('');
      }
    }
  };
  
  return (
    <View>
      <TextInput value={text} onChangeText={setText} />
      <Button 
        title="Send" 
        onPress={handleSend} 
        loading={isSendingMessage}
        disabled={!text.trim() || isSendingMessage}
      />
    </View>
  );
};
```

### 4. **useVideoLikes**
Manages video like operations with optimistic updates.

```typescript
import { useVideoLikes } from '../hooks/api';

const {
  likeCount,      // number
  isLiked,        // boolean
  likeVideo,      // () => Promise<boolean>
  unlikeVideo,    // () => Promise<boolean>
  toggleLike,     // () => Promise<boolean>
  isProcessing    // boolean
} = useVideoLikes(videoId, initialLikeCount);
```

**Example Usage:**
```typescript
const VideoCard = ({ video }) => {
  const { likeCount, isLiked, toggleLike, isProcessing } = useVideoLikes(
    video.id, 
    video.likes
  );
  
  return (
    <View>
      <Video source={{ uri: video.url }} />
      <TouchableOpacity 
        onPress={toggleLike}
        disabled={isProcessing}
      >
        <Icon 
          name={isLiked ? 'heart' : 'heart-outline'} 
          color={isLiked ? 'red' : 'gray'} 
        />
        <Text>{likeCount}</Text>
      </TouchableOpacity>
    </View>
  );
};
```

### 5. **useUserProfile**
Manages user profile operations.

```typescript
import { useUserProfile } from '../hooks/api';

const {
  userData,           // User | null
  isLoadingProfile,   // boolean
  updateProfile,      // (data: UpdateProfileData) => Promise<boolean>
  updateFullName,     // (name: string) => Promise<boolean>
  updateBio,          // (bio: string) => Promise<boolean>
  updateAvatarUrl,    // (url: string) => Promise<boolean>
  isUpdating          // boolean
} = useUserProfile();
```

**Example Usage:**
```typescript
const EditProfileScreen = () => {
  const { userData, updateFullName, isUpdating } = useUserProfile();
  const [name, setName] = useState(userData?.full_name || '');
  
  const handleSave = async () => {
    const success = await updateFullName(name);
    if (success) {
      navigation.goBack();
    }
  };
  
  return (
    <View>
      <TextInput 
        value={name} 
        onChangeText={setName}
        placeholder="Full Name"
      />
      <Button 
        title="Save" 
        onPress={handleSave}
        loading={isUpdating}
      />
    </View>
  );
};
```

## 🛠️ Creating Custom API Hooks

### 1. **Hook Structure Template**
```typescript
import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useApiMutation, useApiQuery } from '../../store/api/someApi';
import logger from '../../utils/logger';

export const useCustomFeature = (id: string) => {
  // RTK Query hooks
  const { data, isLoading, error, refetch } = useApiQuery(id);
  const [mutationFn, { isLoading: isMutating }] = useApiMutation();
  
  // Business logic methods
  const performAction = useCallback(async (params: ActionParams): Promise<boolean> => {
    try {
      await mutationFn(params).unwrap();
      logger.debug('Action performed successfully');
      return true;
    } catch (error) {
      logger.error('Action failed:', error);
      Alert.alert('Error', 'Action failed. Please try again.');
      return false;
    }
  }, [mutationFn]);
  
  return {
    // Data
    data,
    isLoading,
    error,
    
    // Actions
    performAction,
    refetch,
    
    // Loading states
    isMutating,
  };
};
```

### 2. **Error Handling Patterns**
```typescript
// ✅ Consistent error handling
const performAction = useCallback(async (params: ActionParams): Promise<boolean> => {
  try {
    await mutationFn(params).unwrap();
    
    // Optional success feedback
    logger.debug('Action completed successfully');
    
    return true;
  } catch (error) {
    // Log error for debugging
    logger.error('Action failed:', error);
    
    // User-friendly error message
    Alert.alert(
      'Error', 
      error.message || 'Something went wrong. Please try again.'
    );
    
    return false;
  }
}, [mutationFn]);
```

### 3. **Optimistic Updates Pattern**
```typescript
const toggleLike = useCallback(async (): Promise<boolean> => {
  if (!videoId) return false;
  
  try {
    // Optimistic update
    dispatch(toggleLikeState(videoId));
    
    // API call
    if (isLiked) {
      await unlikeVideoMutation({ videoId }).unwrap();
    } else {
      await likeVideoMutation({ videoId }).unwrap();
    }
    
    return true;
  } catch (error) {
    // Revert optimistic update
    dispatch(toggleLikeState(videoId));
    
    logger.error('Toggle like failed:', error);
    return false;
  }
}, [videoId, isLiked, likeVideoMutation, unlikeVideoMutation, dispatch]);
```

## 📋 Best Practices

### 1. **Hook Naming**
- Use descriptive names: `useMessaging`, `useVideoLikes`
- Follow the `use` prefix convention
- Group related functionality: `useConversationMessages`

### 2. **Return Object Structure**
```typescript
return {
  // Data first
  data,
  items,
  
  // Loading states
  isLoading,
  isUpdating,
  
  // Error states
  error,
  
  // Actions last
  performAction,
  refresh,
};
```

### 3. **Error Handling**
- Always return boolean success indicators
- Log errors for debugging
- Show user-friendly error messages
- Handle network errors gracefully

### 4. **Loading States**
- Provide specific loading states for different operations
- Use descriptive names: `isLoadingMessages`, `isSendingMessage`
- Disable UI interactions during loading

### 5. **Type Safety**
- Define proper TypeScript interfaces
- Use generic types where appropriate
- Provide proper return type annotations

## 🧪 Testing API Hooks

### 1. **Unit Testing**
```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { useMessaging } from '../useMessaging';

describe('useMessaging', () => {
  it('should create conversation successfully', async () => {
    const { result } = renderHook(() => useMessaging());
    
    await act(async () => {
      const conversationId = await result.current.createConversation('user123');
      expect(conversationId).toBeTruthy();
    });
  });
});
```

### 2. **Integration Testing**
```typescript
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { MessagesScreen } from '../MessagesScreen';

describe('MessagesScreen', () => {
  it('should send message successfully', async () => {
    const { getByTestId } = render(<MessagesScreen />);
    
    fireEvent.changeText(getByTestId('message-input'), 'Hello');
    fireEvent.press(getByTestId('send-button'));
    
    await waitFor(() => {
      expect(getByTestId('message-list')).toContainElement(
        getByText('Hello')
      );
    });
  });
});
```

This guide ensures consistent, maintainable, and testable API integration across the entire application.
