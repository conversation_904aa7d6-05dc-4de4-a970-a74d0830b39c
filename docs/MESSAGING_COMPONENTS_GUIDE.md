# TikTok-Style Messaging Components Guide

## Overview

This guide provides comprehensive documentation for the TikTok-style messaging UI components, including usage examples, props, and customization options.

## Core Components

### TikTokChatInput

Modern chat input component with TikTok-style design and functionality.

#### Props
```typescript
interface TikTokChatInputProps {
  messageText: string;
  onTextChange: (text: string) => void;
  onSendMessage: () => void;
  onSelectAttachment: () => void;
  onToggleEmoji: () => void;
  onVoiceRecord?: () => void;
  onCameraPress?: () => void;
  onGifPress?: () => void;
  onStickerPress?: () => void;
  isSending: boolean;
  isUploading: boolean;
  isRecording?: boolean;
  replyingTo?: any;
  onCancelReply?: () => void;
  placeholder?: string;
  disabled?: boolean;
}
```

#### Usage
```typescript
import TikTokChatInput from '../components/messages/TikTokChatInput';

<TikTokChatInput
  messageText={messageText}
  onTextChange={setMessageText}
  onSendMessage={handleSendMessage}
  onSelectAttachment={handleSelectAttachment}
  onToggleEmoji={handleToggleEmoji}
  onVoiceRecord={handleVoiceRecord}
  isSending={isSending}
  isUploading={isUploading}
  replyingTo={replyingTo}
  onCancelReply={() => setReplyingTo(null)}
  placeholder="Message..."
/>
```

#### Features
- Adaptive height text input
- Attachment options with animations
- Voice recording support
- Reply preview
- Send/voice button toggle
- TikTok-style animations

### TikTokMessageBubble

Message bubble component with TikTok-style design and interactions.

#### Props
```typescript
interface TikTokMessageBubbleProps {
  message: MessageWithSender;
  isMe: boolean;
  showAvatar: boolean;
  showTimestamp?: boolean;
  onImagePress: (imageUrl: string) => void;
  onReaction?: (messageId: string, reaction: string) => void;
  onReply?: (message: MessageWithSender) => void;
  onForward?: (message: MessageWithSender) => void;
  onLongPress?: (message: MessageWithSender) => void;
  onDoublePress?: (message: MessageWithSender) => void;
}
```

#### Usage
```typescript
import TikTokMessageBubble from '../components/messages/TikTokMessageBubble';

<TikTokMessageBubble
  message={message}
  isMe={message.sender_id === currentUserId}
  showAvatar={!shouldGroupMessages(message, previousMessage)}
  showTimestamp={shouldShowTimestamp(message, nextMessage)}
  onImagePress={handleImagePress}
  onReaction={handleReaction}
  onReply={handleReply}
  onLongPress={handleLongPress}
  onDoublePress={handleDoublePress}
/>
```

#### Features
- Multiple message types (text, image, video, audio, voice, gif, sticker)
- Double-tap reactions
- Long-press context menu
- Quick reactions overlay
- Message status indicators
- Reply indicators
- Grouped message support

### TikTokChatList

Conversation list component with TikTok-style design and interactions.

#### Props
```typescript
interface TikTokChatListProps {
  conversations: ConversationWithDetails[];
  onChatPress: (conversationId: string) => void;
  onRefresh?: () => void;
  isRefreshing?: boolean;
  onNewChat?: () => void;
  searchQuery?: string;
  onArchiveChat?: (conversationId: string) => void;
  onDeleteChat?: (conversationId: string) => void;
  onPinChat?: (conversationId: string) => void;
}
```

#### Usage
```typescript
import TikTokChatList from '../components/messages/TikTokChatList';

<TikTokChatList
  conversations={conversations}
  onChatPress={handleChatPress}
  onRefresh={handleRefresh}
  isRefreshing={isRefreshing}
  onNewChat={handleNewChat}
  searchQuery={searchQuery}
  onArchiveChat={handleArchiveChat}
  onDeleteChat={handleDeleteChat}
  onPinChat={handlePinChat}
/>
```

#### Features
- Swipe actions (archive, delete, pin)
- Search functionality
- Pinned conversations section
- Pull-to-refresh
- Empty state handling
- Floating new chat button

### TikTokChatItem

Individual chat item component with swipe actions and animations.

#### Props
```typescript
interface TikTokChatItemProps {
  conversation: ConversationWithDetails;
  onPress: () => void;
  onSwipeAction?: (action: 'archive' | 'delete' | 'pin') => void;
  isSwipeOpen?: boolean;
  onSwipeStateChange?: (isOpen: boolean) => void;
}
```

#### Usage
```typescript
import TikTokChatItem from '../components/messages/TikTokChatItem';

<TikTokChatItem
  conversation={conversation}
  onPress={() => handleChatPress(conversation.id)}
  onSwipeAction={handleSwipeAction}
  isSwipeOpen={swipedItemId === conversation.id}
  onSwipeStateChange={handleSwipeStateChange}
/>
```

#### Features
- Swipe gesture handling
- Online status indicators
- Unread message badges
- Last message preview
- Pinned conversation indicator
- Message status icons

### TikTokTypingIndicator

Typing indicator component with TikTok-style animations.

#### Props
```typescript
interface TikTokTypingIndicatorProps {
  typingUsers: TypingIndicator[];
  maxDisplayUsers?: number;
}
```

#### Usage
```typescript
import TikTokTypingIndicator from '../components/messages/TikTokTypingIndicator';

<TikTokTypingIndicator
  typingUsers={typingUsers}
  maxDisplayUsers={3}
/>
```

#### Features
- Animated typing dots
- Multiple user support
- User avatars
- Type-specific indicators (text, voice, media)
- Auto-fade animations

### TikTokMessageReactions

Message reactions component with TikTok-style design.

#### Props
```typescript
interface TikTokMessageReactionsProps {
  reactions: MessageReaction[];
  onReactionPress?: (reaction: string) => void;
  onRemoveReaction?: (reactionId: string) => void;
  currentUserId?: string;
  maxDisplayReactions?: number;
}
```

#### Usage
```typescript
import TikTokMessageReactions from '../components/messages/TikTokMessageReactions';

<TikTokMessageReactions
  reactions={message.reactions}
  onReactionPress={handleReactionPress}
  onRemoveReaction={handleRemoveReaction}
  currentUserId={currentUserId}
  maxDisplayReactions={3}
/>
```

#### Features
- Reaction bubbles with counts
- Reaction details modal
- User reaction tracking
- Animated interactions
- Grouped reaction display

## Screen Components

### TikTokMessagesScreen

Main messages screen with conversation list and search.

#### Features
- Conversation list with real-time updates
- Search functionality
- Connection status indicator
- New chat creation
- Pull-to-refresh
- Empty state handling

#### Usage
```typescript
import TikTokMessagesScreen from '../screens/messages/TikTokMessagesScreen';

// Navigation setup
<Stack.Screen 
  name="TikTokMessages" 
  component={TikTokMessagesScreen}
  options={{ headerShown: false }}
/>
```

### TikTokChatScreen

Individual chat screen with messaging functionality.

#### Features
- Real-time messaging
- Message history with pagination
- Media sharing
- Voice recording
- Typing indicators
- User presence
- Message reactions
- Reply functionality

#### Usage
```typescript
import TikTokChatScreen from '../screens/messages/TikTokChatScreen';

// Navigation setup
<Stack.Screen 
  name="TikTokChat" 
  component={TikTokChatScreen}
  options={{ headerShown: false }}
/>

// Navigate to chat
navigation.navigate('TikTokChat', {
  conversationId: 'conv-123',
  otherUser: userProfile
});
```

## Styling and Theming

### Theme Integration

All components integrate with the theme context for consistent styling.

```typescript
import { useTheme } from '../../contexts/ThemeContext';

const { theme } = useTheme();

// Use theme colors
backgroundColor: theme.colors.background
color: theme.colors.text
borderColor: theme.colors.border
```

### Responsive Design

Components use the responsive utility for consistent sizing across devices.

```typescript
import { normalize } from '../../utils/responsive';

// Responsive sizing
fontSize: normalize(16)
padding: normalize(12)
marginHorizontal: normalize(16)
```

### Animation System

Components use React Native's Animated API for smooth interactions.

```typescript
// Scale animation for press feedback
const scaleAnim = useRef(new Animated.Value(1)).current;

const animatePress = useCallback(() => {
  Animated.sequence([
    Animated.timing(scaleAnim, {
      toValue: 0.95,
      duration: 100,
      useNativeDriver: true,
    }),
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }),
  ]).start();
}, [scaleAnim]);
```

## Customization

### Custom Message Types

Extend message bubble to support custom message types:

```typescript
// In TikTokMessageBubble component
const renderCustomContent = () => {
  switch (message.type) {
    case 'poll':
      return <PollMessage poll={message.metadata.poll} />;
    case 'payment':
      return <PaymentMessage payment={message.metadata.payment} />;
    default:
      return renderDefaultContent();
  }
};
```

### Custom Reactions

Add custom reaction types:

```typescript
// Custom reaction emojis
const getCustomReactionEmojis = (): string[] => {
  return ['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥', '💯', '🎉'];
};
```

### Custom Themes

Extend theme for messaging-specific colors:

```typescript
// In theme configuration
const messagingTheme = {
  ...baseTheme,
  messaging: {
    sentBubble: '#007AFF',
    receivedBubble: '#E5E5EA',
    typingIndicator: '#8E8E93',
    onlineStatus: '#4CAF50',
    // ... other messaging colors
  }
};
```

## Performance Optimization

### Virtual Scrolling

For large message lists, implement virtual scrolling:

```typescript
import { VirtualizedList } from 'react-native';

<VirtualizedList
  data={messages}
  renderItem={renderMessage}
  keyExtractor={(item) => item.id}
  getItemCount={() => messages.length}
  getItem={(data, index) => data[index]}
  initialNumToRender={20}
  maxToRenderPerBatch={10}
  windowSize={10}
/>
```

### Memoization

Use React.memo and useMemo for expensive operations:

```typescript
const MemoizedMessageBubble = React.memo(TikTokMessageBubble, (prevProps, nextProps) => {
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.isMe === nextProps.isMe
  );
});
```

### Image Optimization

Implement progressive image loading:

```typescript
const [imageLoaded, setImageLoaded] = useState(false);

<Image
  source={{ uri: message.media_url }}
  onLoad={() => setImageLoaded(true)}
  style={[
    styles.messageImage,
    { opacity: imageLoaded ? 1 : 0 }
  ]}
/>
{!imageLoaded && <LoadingSpinner />}
```

## Accessibility

### Screen Reader Support

Add accessibility labels and hints:

```typescript
<TouchableOpacity
  accessible={true}
  accessibilityLabel={`Message from ${message.sender.name}`}
  accessibilityHint="Double tap to react, long press for options"
  accessibilityRole="button"
>
  <TikTokMessageBubble {...props} />
</TouchableOpacity>
```

### Keyboard Navigation

Support keyboard navigation for web platforms:

```typescript
<TextInput
  accessibilityLabel="Type a message"
  returnKeyType="send"
  onSubmitEditing={handleSendMessage}
  blurOnSubmit={false}
/>
```

## Testing

### Component Testing

Test components with React Native Testing Library:

```typescript
import { render, fireEvent } from '@testing-library/react-native';
import TikTokChatInput from '../TikTokChatInput';

test('sends message when send button is pressed', () => {
  const mockSendMessage = jest.fn();
  const { getByTestId } = render(
    <TikTokChatInput
      messageText="Hello"
      onSendMessage={mockSendMessage}
      // ... other props
    />
  );

  fireEvent.press(getByTestId('send-button'));
  expect(mockSendMessage).toHaveBeenCalled();
});
```

### Integration Testing

Test component integration with hooks:

```typescript
test('typing indicator appears when user types', async () => {
  const { getByTestId } = render(<TikTokChatScreen />);
  
  fireEvent.changeText(getByTestId('message-input'), 'Hello');
  
  await waitFor(() => {
    expect(getByTestId('typing-indicator')).toBeTruthy();
  });
});
```

This component guide provides everything needed to implement and customize the TikTok-style messaging components for a modern, engaging chat experience.
