# Messaging System

This document describes the database schema and SQL helpers used for the in-app messaging feature.

## Tables

### `conversations`
Key columns:
- **id** `uuid` – primary key.
- **type** `varchar` – `direct`, `group` or `channel`.
- **name** `varchar` – optional title for group/channel.
- **description** `text` – optional details.
- **avatar_url** `varchar` – image for the conversation.
- **participants** `uuid[]` – array of user ids who can read/send messages.
- **created_by** `uuid` – creator (references `public.users`).
- **last_message_id** `uuid` – latest message.
- **last_message_at** `timestamp with time zone` – when the last message was sent.
- **is_active** `boolean` – soft delete flag.
- **metadata** `jsonb` – extra info (audio duration, etc.).
- **created_at/updated_at** timestamps.

### `messages`
Key columns:
- **id** `uuid` – primary key.
- **sender_id** `uuid` – user who sent the message.
- **conversation_id** `uuid` – conversation this message belongs to.
- **content** `text` – text body.
- **type** `varchar` – `text`, `image`, `video`, `audio` or `file`.
- **media_url** `varchar` – points to uploaded media.
- **reply_to_message_id** `uuid` – for threaded replies.
- **status** `varchar` – `sent`, `delivered`, `read` or `failed`.
- **read_by** `uuid[]` – participants who have read the message.
- **read_at** `jsonb` – timestamps keyed by user id.
- **is_edited/is_deleted** booleans and their timestamps.
- **metadata** `jsonb` – any additional data (e.g. audio duration).
- **created_at/updated_at** timestamps.

## SQL Functions
The schema provides several helper functions in `db_improvements.sql`:

- **`send_message`** – inserts a new message using the authenticated user as `sender_id` and updates `conversations.last_message_id`/`last_message_at`. Returns the new message id.
- **`create_conversation`** – creates a conversation between the current user and another participant. Returns the conversation id.
- **`add_participant_to_conversation`** – adds a user to the participant array for a given conversation.
- **`mark_messages_as_read`** – appends the current user to `read_by` and fills `read_at` for unread messages in a conversation.
- **`update_conversation_last_message`** – trigger fired after inserting into `messages` to keep `conversations` up‑to‑date.

The app can call these functions through Supabase RPC endpoints, e.g. `supabase.rpc('send_message', { p_conversation_id, p_content })`. The current implementation mostly inserts directly into tables via the Supabase client but the functions remain available for consistency and security.

## Row Level Security
`migrations/20240702_convert_conversation_participants.sql` enables RLS for the `conversations` table:
```sql
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
CREATE POLICY select_if_participant ON public.conversations
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.auth_user_id = auth.uid()
      AND u.id = ANY (participants)
  )
);
```
This policy ensures only participants can read a conversation. Additional policies may be required to restrict write operations depending on your setup.

## Local Setup
To reproduce the messaging system locally:

1. Install the [Supabase CLI](https://supabase.com/docs/guides/cli). Log in and create a new project or start a local dev instance.
2. Apply the SQL schema:
   ```bash
   supabase db reset --file db.sql
   psql < db_improvements.sql
   psql < migrations/20240702_convert_conversation_participants.sql
   psql < migrations/20240705_enable_storage_insert.sql
  ```
3. Create a `.env` file and set `SUPABASE_URL` and `SUPABASE_ANON_KEY` with your project credentials. See `.env.example` for the full list.
4. Run the React Native app as described in the main README (`npm install` then `npm run android` or `npm run ios`).

With the schema loaded and environment variables set, messaging and the SQL helper functions will work against your local Supabase instance.
