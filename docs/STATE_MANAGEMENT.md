# 🗄️ State Management Guide

## Overview

TS1 uses **Redux Toolkit** with **RTK Query** for state management, providing a robust, scalable solution for both client and server state management.

## 🏗️ State Architecture

### **State Categories**

```
┌─────────────────────────────────────┐
│           Client State              │
│     (Redux Store + Context)         │
├─────────────────────────────────────┤
│           Server State              │
│         (RTK Query)                 │
├─────────────────────────────────────┤
│           Local State               │
│      (Component useState)           │
└─────────────────────────────────────┘
```

### 1. **Client State (Redux Store)**
- User authentication status
- App-wide settings and preferences
- UI state that needs to persist across screens
- Global loading states

### 2. **Server State (RTK Query)**
- API data and caching
- Server-side entities (users, messages, videos)
- Real-time data synchronization
- Background data fetching

### 3. **Local State (Component State)**
- Form inputs and validation
- Modal visibility
- Component-specific UI state
- Temporary data

## 📁 Store Structure

```
src/store/
├── index.ts                 # Store configuration
├── hooks.ts                 # Typed Redux hooks
├── slices/                  # Redux slices
│   ├── authSlice.ts        # Authentication state
│   ├── messagingSlice.ts   # Messaging UI state
│   └── videoLikesSlice.ts  # Video likes state
├── api/                     # RTK Query APIs
│   ├── endpoints/          # API endpoint definitions
│   │   ├── conversationsApi.ts
│   │   ├── messagesApi.ts
│   │   └── notificationsApi.ts
│   ├── utils/              # API utilities
│   │   └── apiHelpers.ts
│   ├── messagingApi.ts     # Main messaging API
│   ├── userManagementApi.ts
│   └── videoApi.ts
└── middleware/             # Custom middleware
    └── authMiddleware.ts
```

## 🔧 Redux Slices

### **Slice Template**
```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FeatureState {
  data: DataType[];
  isLoading: boolean;
  error: string | null;
  selectedId: string | null;
}

const initialState: FeatureState = {
  data: [],
  isLoading: false,
  error: null,
  selectedId: null,
};

const featureSlice = createSlice({
  name: 'feature',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setData: (state, action: PayloadAction<DataType[]>) => {
      state.data = action.payload;
    },
    addItem: (state, action: PayloadAction<DataType>) => {
      state.data.push(action.payload);
    },
    updateItem: (state, action: PayloadAction<{ id: string; updates: Partial<DataType> }>) => {
      const { id, updates } = action.payload;
      const index = state.data.findIndex(item => item.id === id);
      if (index !== -1) {
        state.data[index] = { ...state.data[index], ...updates };
      }
    },
    removeItem: (state, action: PayloadAction<string>) => {
      state.data = state.data.filter(item => item.id !== action.payload);
    },
    selectItem: (state, action: PayloadAction<string | null>) => {
      state.selectedId = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setData,
  addItem,
  updateItem,
  removeItem,
  selectItem,
} = featureSlice.actions;

// Selectors
export const selectFeatureData = (state: RootState) => state.feature.data;
export const selectFeatureLoading = (state: RootState) => state.feature.isLoading;
export const selectFeatureError = (state: RootState) => state.feature.error;
export const selectSelectedItem = (state: RootState) => {
  const { data, selectedId } = state.feature;
  return selectedId ? data.find(item => item.id === selectedId) : null;
};

export default featureSlice.reducer;
```

### **Real Example: Video Likes Slice**
```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface VideoLikeState {
  count: number;
  isLiked: boolean;
}

interface VideoLikesState {
  [videoId: string]: VideoLikeState;
}

const initialState: VideoLikesState = {};

const videoLikesSlice = createSlice({
  name: 'videoLikes',
  initialState,
  reducers: {
    initializeLikeState: (
      state,
      action: PayloadAction<{ videoId: string; count: number; isLiked: boolean }>
    ) => {
      const { videoId, count, isLiked } = action.payload;
      state[videoId] = { count, isLiked };
    },
    incrementLike: (state, action: PayloadAction<string>) => {
      const videoId = action.payload;
      if (state[videoId]) {
        state[videoId].count += 1;
      }
    },
    decrementLike: (state, action: PayloadAction<string>) => {
      const videoId = action.payload;
      if (state[videoId] && state[videoId].count > 0) {
        state[videoId].count -= 1;
      }
    },
    setLiked: (state, action: PayloadAction<{ videoId: string; liked: boolean }>) => {
      const { videoId, liked } = action.payload;
      if (state[videoId]) {
        state[videoId].isLiked = liked;
      }
    },
  },
});

export const { initializeLikeState, incrementLike, decrementLike, setLiked } = videoLikesSlice.actions;

// Selectors
export const selectVideoLikeState = (videoId: string) => (state: RootState) =>
  state.videoLikes[videoId];

export default videoLikesSlice.reducer;
```

## 🌐 RTK Query APIs

### **API Structure Template**
```typescript
import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '../../integrations/supabase/client';
import logger from '../../utils/logger';

export const featureApi = createApi({
  reducerPath: 'featureApi',
  baseQuery: fakeBaseQuery(),
  tagTypes: ['Feature', 'FeatureItem'],
  endpoints: (builder) => ({
    // Query endpoint
    getFeatures: builder.query<Feature[], void>({
      async queryFn() {
        try {
          const { data, error } = await supabase
            .from('features')
            .select('*')
            .order('created_at', { ascending: false });

          if (error) throw error;
          return { data: data || [] };
        } catch (error) {
          logger.error('Failed to fetch features:', error);
          return { error: error.message };
        }
      },
      providesTags: ['Feature'],
    }),

    // Mutation endpoint
    createFeature: builder.mutation<Feature, CreateFeatureRequest>({
      async queryFn(newFeature) {
        try {
          const { data, error } = await supabase
            .from('features')
            .insert(newFeature)
            .select()
            .single();

          if (error) throw error;
          return { data };
        } catch (error) {
          logger.error('Failed to create feature:', error);
          return { error: error.message };
        }
      },
      invalidatesTags: ['Feature'],
    }),
  }),
});

export const { useGetFeaturesQuery, useCreateFeatureMutation } = featureApi;
```

### **Cache Management**
```typescript
// Optimistic updates
const updateFeature = builder.mutation<Feature, UpdateFeatureRequest>({
  async queryFn({ id, updates }) {
    // Optimistic update
    const patchResult = dispatch(
      featureApi.util.updateQueryData('getFeatures', undefined, (draft) => {
        const feature = draft.find(f => f.id === id);
        if (feature) {
          Object.assign(feature, updates);
        }
      })
    );

    try {
      const { data, error } = await supabase
        .from('features')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      // Revert optimistic update
      patchResult.undo();
      return { error: error.message };
    }
  },
  invalidatesTags: (result, error, { id }) => [{ type: 'Feature', id }],
});
```

## 🎣 Custom Hooks Integration

### **Connecting Store to Custom Hooks**
```typescript
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectVideoLikeState, incrementLike, setLiked } from '../../store/slices/videoLikesSlice';
import { useLikeVideoMutation } from '../../store/api/videoLikesApi';

export const useVideoLikes = (videoId: string) => {
  const dispatch = useAppDispatch();
  const likeState = useAppSelector(selectVideoLikeState(videoId));
  const [likeVideoMutation] = useLikeVideoMutation();

  const likeVideo = useCallback(async () => {
    // Optimistic update
    dispatch(incrementLike(videoId));
    dispatch(setLiked({ videoId, liked: true }));

    try {
      await likeVideoMutation({ videoId }).unwrap();
    } catch (error) {
      // Revert on error
      dispatch(decrementLike(videoId));
      dispatch(setLiked({ videoId, liked: false }));
    }
  }, [videoId, dispatch, likeVideoMutation]);

  return {
    likeCount: likeState?.count || 0,
    isLiked: likeState?.isLiked || false,
    likeVideo,
  };
};
```

## 🔄 Real-time Updates

### **Supabase Realtime Integration**
```typescript
// In custom hook
useEffect(() => {
  const channel = supabase
    .channel('public:messages')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`,
      },
      (payload) => {
        // Update RTK Query cache
        dispatch(
          messagingApi.util.updateQueryData(
            'getMessages',
            conversationId,
            (draft) => {
              draft.push(payload.new as Message);
            }
          )
        );
      }
    )
    .subscribe();

  return () => {
    channel.unsubscribe();
  };
}, [conversationId, dispatch]);
```

## 🧪 Testing State Management

### **Testing Redux Slices**
```typescript
import { configureStore } from '@reduxjs/toolkit';
import videoLikesReducer, { incrementLike, setLiked } from '../videoLikesSlice';

describe('videoLikesSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        videoLikes: videoLikesReducer,
      },
    });
  });

  it('should increment like count', () => {
    const videoId = 'video123';
    
    // Initialize state
    store.dispatch(initializeLikeState({ videoId, count: 5, isLiked: false }));
    
    // Increment like
    store.dispatch(incrementLike(videoId));
    
    const state = store.getState().videoLikes[videoId];
    expect(state.count).toBe(6);
  });
});
```

### **Testing RTK Query**
```typescript
import { setupApiStore } from '../../../test-utils/api-store';
import { messagingApi } from '../messagingApi';

describe('messagingApi', () => {
  const storeRef = setupApiStore(messagingApi);

  it('should fetch conversations', async () => {
    const result = await storeRef.store.dispatch(
      messagingApi.endpoints.getConversations.initiate()
    );

    expect(result.data).toBeDefined();
    expect(Array.isArray(result.data)).toBe(true);
  });
});
```

## 📊 Performance Optimization

### **Selector Optimization**
```typescript
import { createSelector } from '@reduxjs/toolkit';

// ✅ Memoized selector
const selectConversationMessages = createSelector(
  [(state: RootState) => state.messages.data, (state: RootState, conversationId: string) => conversationId],
  (messages, conversationId) => messages.filter(msg => msg.conversation_id === conversationId)
);

// ✅ Usage in component
const messages = useAppSelector(state => selectConversationMessages(state, conversationId));
```

### **RTK Query Optimization**
```typescript
// ✅ Selective subscriptions
const { data: conversations } = useGetConversationsQuery(undefined, {
  selectFromResult: ({ data, ...other }) => ({
    ...other,
    data: data?.filter(conv => conv.is_active) || [],
  }),
});

// ✅ Polling with conditions
const { data: messages } = useGetMessagesQuery(conversationId, {
  pollingInterval: isActive ? 5000 : 0, // Only poll when screen is active
  skip: !conversationId,
});
```

## 🚨 Best Practices

### **Do's ✅**
- Use RTK Query for server state
- Keep Redux store minimal and focused
- Use custom hooks to abstract store logic
- Implement optimistic updates for better UX
- Use proper TypeScript types throughout

### **Don'ts ❌**
- Don't put all state in Redux
- Don't access store directly in components
- Don't ignore cache invalidation
- Don't forget to handle loading and error states
- Don't mix client and server state concerns

### **State Decision Tree**
```
Is this data from the server?
├─ Yes → Use RTK Query
│  ├─ Needs real-time updates? → Add Supabase subscription
│  └─ Needs optimistic updates? → Implement in custom hook
└─ No → Is it global client state?
   ├─ Yes → Use Redux slice
   └─ No → Use local component state (useState)
```

This guide ensures consistent, performant, and maintainable state management across the entire application.
