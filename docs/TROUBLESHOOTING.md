# 🔧 Troubleshooting Guide

## Common Issues and Solutions

### 🚀 **Development Setup Issues**

#### **Metro Bundler Issues**
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear npm cache
npm start -- --reset-cache

# Clear all caches
npm run clean
```

#### **Node Modules Issues**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# iOS specific
cd ios && rm -rf Pods Podfile.lock && pod install && cd ..
```

#### **Environment Variables Not Loading**
```bash
# Ensure .env file exists
cp .env.example .env

# Restart Metro after .env changes
npm start -- --reset-cache
```

### 📱 **Platform-Specific Issues**

#### **Android Issues**

**Build Failures:**
```bash
# Clean Android build
cd android && ./gradlew clean && cd ..

# Reset Android build
npx react-native run-android --reset-cache

# Check Android SDK path
echo $ANDROID_HOME
```

**Emulator Issues:**
```bash
# List available emulators
emulator -list-avds

# Start specific emulator
emulator -avd Pixel_4_API_30

# Cold boot emulator
emulator -avd Pixel_4_API_30 -wipe-data
```

#### **iOS Issues**

**Pod Installation Failures:**
```bash
# Update CocoaPods
sudo gem install cocoapods

# Clean and reinstall pods
cd ios
rm -rf Pods Podfile.lock
pod deintegrate
pod install
cd ..
```

**Xcode Build Issues:**
```bash
# Clean Xcode build folder
rm -rf ~/Library/Developer/Xcode/DerivedData

# Reset iOS simulator
xcrun simctl erase all
```

### 🗄️ **Database & API Issues**

#### **Supabase Connection Issues**

**Authentication Errors:**
```typescript
// Check Supabase configuration
console.log('Supabase URL:', process.env.SUPABASE_URL);
console.log('Supabase Key:', process.env.SUPABASE_ANON_KEY?.substring(0, 10) + '...');

// Test connection
const testConnection = async () => {
  try {
    const { data, error } = await supabase.from('users').select('count').single();
    console.log('Connection test:', { data, error });
  } catch (error) {
    console.error('Connection failed:', error);
  }
};
```

**RLS (Row Level Security) Issues:**
```sql
-- Check RLS policies in Supabase dashboard
SELECT * FROM pg_policies WHERE tablename = 'your_table_name';

-- Common RLS policy for authenticated users
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (auth.uid() = id);
```

#### **RTK Query Issues**

**Cache Issues:**
```typescript
// Reset specific query cache
dispatch(api.util.resetApiState());

// Invalidate specific tags
dispatch(api.util.invalidateTags(['User', 'Message']));

// Force refetch
refetch();
```

**Network Errors:**
```typescript
// Add retry logic
const { data, error, isLoading } = useGetDataQuery(params, {
  retry: 3,
  retryDelay: 1000,
});

// Handle network errors
if (error && 'status' in error) {
  if (error.status === 'FETCH_ERROR') {
    // Network connectivity issue
  }
}
```

### 🎨 **UI & Styling Issues**

#### **Theme Issues**
```typescript
// Check theme provider wrapping
const App = () => (
  <ThemeProvider>
    <YourApp />
  </ThemeProvider>
);

// Debug theme values
const Component = () => {
  const { theme } = useTheme();
  console.log('Current theme:', theme);
  
  return <View />;
};
```

#### **Responsive Design Issues**
```typescript
// Check normalize function
import { normalize } from '../utils/responsive';

// Debug responsive values
console.log('Normalized 16:', normalize(16));

// Test on different screen sizes
const { width, height } = Dimensions.get('window');
console.log('Screen dimensions:', { width, height });
```

### 🔐 **Authentication Issues**

#### **Login/Logout Issues**
```typescript
// Check auth state
const { data: { user } } = await supabase.auth.getUser();
console.log('Current user:', user);

// Clear auth state
await supabase.auth.signOut();

// Check session
const { data: { session } } = await supabase.auth.getSession();
console.log('Current session:', session);
```

#### **Token Refresh Issues**
```typescript
// Manual token refresh
const { data, error } = await supabase.auth.refreshSession();
if (error) {
  console.error('Token refresh failed:', error);
}
```

### 📹 **Media & Camera Issues**

#### **Camera Permission Issues**
```typescript
// Check camera permissions
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

const checkCameraPermission = async () => {
  const result = await check(PERMISSIONS.ANDROID.CAMERA);
  console.log('Camera permission:', result);
  
  if (result !== RESULTS.GRANTED) {
    const requestResult = await request(PERMISSIONS.ANDROID.CAMERA);
    console.log('Permission request result:', requestResult);
  }
};
```

#### **Video Processing Issues**
```typescript
// Check FFmpeg availability
import { FFmpegKit } from 'ffmpeg-kit-react-native';

const testFFmpeg = async () => {
  try {
    const session = await FFmpegKit.execute('-version');
    const returnCode = await session.getReturnCode();
    console.log('FFmpeg test:', returnCode);
  } catch (error) {
    console.error('FFmpeg error:', error);
  }
};
```

### 🔄 **State Management Issues**

#### **Redux DevTools**
```typescript
// Enable Redux DevTools
const store = configureStore({
  reducer: rootReducer,
  devTools: __DEV__,
});

// Debug state changes
const debugMiddleware: Middleware = (store) => (next) => (action) => {
  console.log('Dispatching:', action);
  const result = next(action);
  console.log('New state:', store.getState());
  return result;
};
```

#### **Hook Dependencies Issues**
```typescript
// Debug useEffect dependencies
useEffect(() => {
  console.log('Effect triggered with dependencies:', { dep1, dep2 });
}, [dep1, dep2]);

// Use useCallback for stable references
const stableCallback = useCallback(() => {
  // Callback logic
}, [dependency]);
```

### 🧪 **Testing Issues**

#### **Test Setup Issues**
```bash
# Clear Jest cache
npx jest --clearCache

# Run tests with verbose output
npm test -- --verbose

# Run specific test file
npm test -- MessageItem.test.tsx
```

#### **Mock Issues**
```typescript
// Mock Supabase
jest.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({ data: [], error: null })),
    })),
  },
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
  }),
}));
```

### 📊 **Performance Issues**

#### **Memory Leaks**
```typescript
// Check for cleanup in useEffect
useEffect(() => {
  const subscription = someService.subscribe();
  
  return () => {
    subscription.unsubscribe(); // Important cleanup
  };
}, []);

// Use AbortController for fetch requests
useEffect(() => {
  const controller = new AbortController();
  
  fetch('/api/data', { signal: controller.signal })
    .then(response => response.json())
    .then(setData);
  
  return () => {
    controller.abort();
  };
}, []);
```

#### **Render Performance**
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
});

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Use useCallback for stable function references
const handlePress = useCallback(() => {
  onPress(id);
}, [id, onPress]);
```

### 🔍 **Debugging Tools**

#### **React Native Debugger**
```bash
# Install React Native Debugger
brew install --cask react-native-debugger

# Enable debugging
# In simulator: Cmd+D (iOS) or Cmd+M (Android)
# Select "Debug with Chrome"
```

#### **Flipper Integration**
```bash
# Install Flipper
brew install --cask flipper

# Enable Flipper in development
# Add to your app's configuration
```

#### **Logging Best Practices**
```typescript
import logger from '../utils/logger';

// Use structured logging
logger.debug('User action', { userId, action: 'login' });
logger.error('API error', { endpoint: '/api/users', error: error.message });

// Conditional logging
if (__DEV__) {
  console.log('Development only log');
}
```

### 🆘 **Getting Help**

#### **When to Ask for Help**
- After trying the solutions in this guide
- When encountering platform-specific issues
- For architecture or design pattern questions
- When stuck on complex debugging

#### **How to Ask for Help**
1. **Describe the problem clearly**
2. **Include error messages and stack traces**
3. **Share relevant code snippets**
4. **Mention what you've already tried**
5. **Include environment details** (OS, device, versions)

#### **Useful Commands for Bug Reports**
```bash
# Get environment info
npx react-native info

# Get package versions
npm list --depth=0

# Check React Native version
npx react-native --version

# Get device logs
# Android
adb logcat

# iOS
xcrun simctl spawn booted log stream --predicate 'process == "YourApp"'
```

Remember: Most issues have been encountered before. Check GitHub issues, Stack Overflow, and the React Native community for solutions!
