# Android Debug Files

The project expects two Android files that are not stored in version control:

- `android/app/debug.keystore` – signing key used for debug builds
- `android/app/google-services.json` – Firebase configuration

## Supplying the files locally

1. Download `google-services.json` from your Firebase console.
2. Place it in `android/app/google-services.json`.
3. Ensure a debug keystore exists at `android/app/debug.keystore`:
   - Android Studio creates one automatically when you run the app.
   - Or generate one manually:
     ```bash
     keytool -genkeypair -v -storetype PKCS12 \
       -keystore debug.keystore -alias androiddebugkey \
       -keyalg RSA -keysize 2048 -validity 10000 \
       -storepass android -keypass android
     ```
4. Keep both files untracked—they are listed in `.gitignore`.
