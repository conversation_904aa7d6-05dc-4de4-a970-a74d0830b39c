# 🤝 Contributing Guide

## Welcome Contributors!

Thank you for your interest in contributing to TS1! This guide will help you understand our development process, coding standards, and how to submit quality contributions.

## 🚀 Getting Started

### 1. **Development Setup**
```bash
# Fork and clone the repository
git clone https://github.com/your-username/TS1.git
cd TS1

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Fill in your environment variables

# Start development
npm start
npm run android  # or npm run ios
```

### 2. **Development Environment**
- **Node.js**: v18 or higher
- **React Native CLI**: Latest version
- **Android Studio**: For Android development
- **Xcode**: For iOS development (macOS only)
- **VS Code**: Recommended editor with extensions:
  - ES7+ React/Redux/React-Native snippets
  - TypeScript Importer
  - Prettier - Code formatter
  - ESLint

## 📋 Development Workflow

### **Branch Strategy**
```
main
├── develop
│   ├── feature/messaging-improvements
│   ├── feature/video-upload-optimization
│   └── bugfix/chat-scroll-issue
└── hotfix/critical-auth-fix
```

### **Branch Naming Convention**
- **Features**: `feature/short-description`
- **Bug fixes**: `bugfix/issue-description`
- **Hotfixes**: `hotfix/critical-issue`
- **Documentation**: `docs/update-description`
- **Refactoring**: `refactor/component-name`

### **Workflow Steps**
1. **Create Feature Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards (see below)
   - Write tests for new functionality
   - Update documentation if needed

3. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add user profile editing functionality"
   ```

4. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create Pull Request on GitHub
   ```

## 📝 Commit Message Convention

We follow [Conventional Commits](https://www.conventionalcommits.org/) specification:

### **Format**
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### **Types**
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### **Examples**
```bash
feat(messaging): add real-time message delivery
fix(auth): resolve login redirect issue
docs(api): update API hooks documentation
refactor(components): break down large VideoPlayer component
test(hooks): add tests for useMessaging hook
chore(deps): update React Native to 0.73
```

## 🏗️ Coding Standards

### **1. TypeScript Requirements**
- **All new code must be TypeScript**
- Define proper interfaces for all props and data structures
- Use strict type checking
- Avoid `any` type - use proper typing or `unknown`

```typescript
// ✅ Good
interface UserProfileProps {
  user: User;
  onUpdate: (updates: Partial<User>) => Promise<void>;
  isLoading?: boolean;
}

// ❌ Bad
interface UserProfileProps {
  user: any;
  onUpdate: any;
  isLoading?: any;
}
```

### **2. Component Standards**
- **Maximum 300 lines per file**
- Use functional components with hooks
- Implement proper error boundaries
- Follow the component template structure

```typescript
// ✅ Good: Focused, single-responsibility component
const MessageItem: React.FC<MessageItemProps> = ({ message, isCurrentUser }) => {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <Text>{message.content}</Text>
    </View>
  );
};

// ❌ Bad: Large, multi-responsibility component
const MassiveComponent = () => {
  // 500+ lines of mixed concerns
};
```

### **3. Custom Hooks Usage**
- **Use custom hooks instead of direct API calls**
- Keep business logic out of components
- Follow the established hook patterns

```typescript
// ✅ Good: Using custom hook
const MessagesScreen = () => {
  const { conversations, createConversation } = useMessaging();
  
  return (
    <FlatList
      data={conversations}
      renderItem={({ item }) => <ConversationItem conversation={item} />}
    />
  );
};

// ❌ Bad: Direct API usage in component
const MessagesScreen = () => {
  const { data: conversations } = useGetConversationsQuery();
  const [createConversation] = useCreateConversationMutation();
  
  // Component with API logic
};
```

### **4. Styling Guidelines**
- Use the theme system for colors
- Use `normalize()` for responsive design
- Follow the established styling patterns

```typescript
// ✅ Good: Theme-aware, responsive styling
const styles = StyleSheet.create({
  container: {
    padding: normalize(16),
    borderRadius: normalize(8),
  },
  text: {
    fontSize: normalize(16),
    lineHeight: normalize(24),
  },
});

const Component = () => {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.text, { color: theme.colors.text }]}>Content</Text>
    </View>
  );
};
```

## 🧪 Testing Requirements

### **1. Test Coverage**
- **All new features must include tests**
- Aim for 80%+ test coverage
- Test both happy path and error scenarios

### **2. Test Types**
```typescript
// Unit tests for hooks
describe('useMessaging', () => {
  it('should create conversation successfully', async () => {
    const { result } = renderHook(() => useMessaging());
    const conversationId = await result.current.createConversation('user123');
    expect(conversationId).toBeTruthy();
  });
});

// Component tests
describe('MessageItem', () => {
  it('renders message content correctly', () => {
    const { getByText } = render(
      <MessageItem message={mockMessage} isCurrentUser={false} />
    );
    expect(getByText(mockMessage.content)).toBeTruthy();
  });
});

// Integration tests
describe('Chat Flow', () => {
  it('should send and receive messages', async () => {
    // Test complete user flow
  });
});
```

### **3. Running Tests**
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 📖 Documentation Requirements

### **1. Code Documentation**
- **JSDoc comments for all public functions and components**
- Include usage examples
- Document complex business logic

```typescript
/**
 * MessageItem - Displays a single message in the chat
 * 
 * @param message - The message object to display
 * @param isCurrentUser - Whether the message was sent by the current user
 * @returns A rendered message component
 * 
 * @example
 * ```tsx
 * <MessageItem
 *   message={messageData}
 *   isCurrentUser={message.sender_id === currentUser.id}
 * />
 * ```
 */
```

### **2. README Updates**
- Update relevant documentation when adding features
- Include setup instructions for new dependencies
- Document breaking changes

### **3. API Documentation**
- Document new API hooks
- Include usage examples
- Update the API hooks guide

## 🔍 Code Review Process

### **1. Pull Request Requirements**
- **Descriptive title and description**
- Link to related issues
- Include screenshots for UI changes
- Ensure all tests pass
- No merge conflicts

### **2. PR Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
[Add screenshots here]

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests pass locally
- [ ] Documentation updated
```

### **3. Review Criteria**
- **Code quality and readability**
- **Adherence to architectural patterns**
- **Test coverage and quality**
- **Performance considerations**
- **Security implications**

## 🚨 Common Issues to Avoid

### **1. Anti-Patterns**
```typescript
// ❌ Don't: Direct API calls in components
const Component = () => {
  const [data, setData] = useState([]);
  
  useEffect(() => {
    fetch('/api/data').then(res => res.json()).then(setData);
  }, []);
};

// ❌ Don't: Large, monolithic components
const MassiveComponent = () => {
  // 500+ lines of code
};

// ❌ Don't: Mixing concerns
const ComponentWithEverything = () => {
  // API calls + business logic + UI logic + styling
};
```

### **2. Performance Issues**
```typescript
// ❌ Don't: Create objects in render
const Component = () => (
  <View style={{ padding: 16 }}> {/* Creates new object every render */}
    <Text>Content</Text>
  </View>
);

// ✅ Do: Use StyleSheet
const styles = StyleSheet.create({
  container: { padding: 16 },
});

const Component = () => (
  <View style={styles.container}>
    <Text>Content</Text>
  </View>
);
```

## 🎯 Quality Checklist

Before submitting a PR, ensure:

- [ ] **Code follows TypeScript standards**
- [ ] **Components are under 300 lines**
- [ ] **Custom hooks used instead of direct API calls**
- [ ] **Tests written and passing**
- [ ] **Documentation updated**
- [ ] **No console.log statements**
- [ ] **Proper error handling implemented**
- [ ] **Responsive design considered**
- [ ] **Accessibility guidelines followed**
- [ ] **Performance optimizations applied**

## 🆘 Getting Help

### **Resources**
- [Architecture Guide](./ARCHITECTURE.md)
- [API Hooks Guide](./API_HOOKS.md)
- [Component Guidelines](./COMPONENTS.md)
- [State Management Guide](./STATE_MANAGEMENT.md)

### **Communication**
- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Code Reviews**: For technical feedback and guidance

### **Mentorship**
New contributors are welcome! Don't hesitate to:
- Ask questions in your PR
- Request code review feedback
- Seek guidance on architectural decisions

Thank you for contributing to TS1! Your efforts help make this project better for everyone. 🚀
