# 💬 Comments System Integration Guide

## Overview

This guide explains how to integrate the TikTok-style comments system with your Supabase database following web development best practices.

## 🗄️ Database Schema

### Tables Created

1. **`comments`** - Main comments table with nested reply support
2. **`comment_likes`** - User likes on comments
3. **`comments_with_details`** - Optimized view with user data and counts

### Key Features

- ✅ **Nested replies** with unlimited depth
- ✅ **Real-time updates** via Supabase subscriptions
- ✅ **Row Level Security** for data protection
- ✅ **Optimized indexes** for performance
- ✅ **Automatic timestamps** with triggers
- ✅ **Like/unlike functionality**
- ✅ **Character limits** and validation

## 🚀 Migration Steps

### 1. Run the Migration

```bash
# Using Supabase CLI
supabase db reset
supabase migration up

# Or apply directly in Supabase Dashboard
# Copy the SQL from supabase/migrations/20250104_create_comments_system.sql
# Paste in SQL Editor and run
```

### 2. Verify Tables

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('comments', 'comment_likes');

-- Check indexes
SELECT indexname, tablename FROM pg_indexes 
WHERE tablename IN ('comments', 'comment_likes');
```

### 3. Test Policies

```sql
-- Test as authenticated user
SELECT * FROM comments LIMIT 5;
INSERT INTO comments (text, video_id, user_id) 
VALUES ('Test comment', 'video-uuid', auth.uid());
```

## 📱 React Native Integration

### 1. Update API Types

```typescript
// src/types/comments.ts
export interface Comment {
  id: string;
  text: string;
  video_id: string;
  user_id: string;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  likes: number;
  reply_count: number;
  isLiked?: boolean;
  replies?: Comment[];
}
```

### 2. Update API Queries

The existing `commentsApi.ts` needs minor updates to match the database schema:

```typescript
// Update the query to use the optimized view
const { data: comments, error } = await supabase
  .from('comments_with_details')
  .select('*')
  .eq('video_id', videoId)
  .is('parent_id', null)
  .order('created_at', { ascending: false });
```

### 3. Real-time Subscriptions

```typescript
// Subscribe to comment changes
const subscribeToComments = (videoId: string, callback: Function) => {
  return supabase
    .channel(`comments:${videoId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'comments',
        filter: `video_id=eq.${videoId}`,
      },
      callback
    )
    .subscribe();
};
```

## 🔧 Helper Functions

### 1. Comment Utilities

```typescript
// src/utils/commentHelpers.ts
export const formatCommentCount = (count: number): string => {
  if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
  if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
  return count.toString();
};

export const formatTimeAgo = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return `${diffInSeconds}s`;
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
  return `${Math.floor(diffInSeconds / 604800)}w`;
};

export const validateCommentText = (text: string): boolean => {
  return text.trim().length >= 1 && text.trim().length <= 500;
};
```

### 2. Database Functions Usage

```typescript
// Get comment like count
const { data: likeCount } = await supabase
  .rpc('get_comment_like_count', { comment_uuid: commentId });

// Check if user liked comment
const { data: isLiked } = await supabase
  .rpc('user_liked_comment', { 
    comment_uuid: commentId, 
    user_uuid: userId 
  });

// Get reply count
const { data: replyCount } = await supabase
  .rpc('get_reply_count', { comment_uuid: commentId });
```

## 🔒 Security Best Practices

### 1. Input Validation

```typescript
// Validate before sending to API
const createComment = async (text: string, videoId: string, parentId?: string) => {
  if (!validateCommentText(text)) {
    throw new Error('Comment must be 1-500 characters');
  }
  
  if (!videoId || !isValidUUID(videoId)) {
    throw new Error('Invalid video ID');
  }
  
  // Proceed with API call...
};
```

### 2. Rate Limiting

```typescript
// Implement client-side rate limiting
const commentRateLimit = new Map();

const checkRateLimit = (userId: string): boolean => {
  const lastComment = commentRateLimit.get(userId);
  const now = Date.now();
  
  if (lastComment && now - lastComment < 1000) { // 1 second cooldown
    return false;
  }
  
  commentRateLimit.set(userId, now);
  return true;
};
```

## 📊 Performance Optimization

### 1. Pagination

```typescript
const getComments = async (videoId: string, page = 0, limit = 20) => {
  const { data, error } = await supabase
    .from('comments_with_details')
    .select('*')
    .eq('video_id', videoId)
    .is('parent_id', null)
    .order('created_at', { ascending: false })
    .range(page * limit, (page + 1) * limit - 1);
    
  return { data, error };
};
```

### 2. Caching Strategy

```typescript
// Use React Query for caching
const useVideoComments = (videoId: string) => {
  return useQuery({
    queryKey: ['comments', videoId],
    queryFn: () => getComments(videoId),
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
  });
};
```

## 🧪 Testing

### 1. Database Tests

```sql
-- Test comment creation
INSERT INTO comments (text, video_id, user_id) 
VALUES ('Test comment', gen_random_uuid(), auth.uid());

-- Test nested replies
INSERT INTO comments (text, video_id, user_id, parent_id) 
VALUES ('Test reply', 'video-id', auth.uid(), 'parent-comment-id');

-- Test like functionality
INSERT INTO comment_likes (comment_id, user_id) 
VALUES ('comment-id', auth.uid());
```

### 2. API Tests

```typescript
// Test comment creation
describe('Comments API', () => {
  it('should create a comment', async () => {
    const result = await createComment({
      text: 'Test comment',
      video_id: 'test-video-id',
    });
    
    expect(result.data).toBeDefined();
    expect(result.data.text).toBe('Test comment');
  });
});
```

## 🚨 Troubleshooting

### Common Issues

1. **RLS Policies**: Ensure user is authenticated
2. **Foreign Keys**: Verify video_id and user_id exist
3. **Permissions**: Check table and function grants
4. **Indexes**: Monitor query performance

### Debug Queries

```sql
-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'comments';

-- Monitor slow queries
SELECT query, mean_exec_time FROM pg_stat_statements 
WHERE query LIKE '%comments%' 
ORDER BY mean_exec_time DESC;
```

This integration provides a robust, scalable comments system that matches TikTok's functionality while following web development best practices.
