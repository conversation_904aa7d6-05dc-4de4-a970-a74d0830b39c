# 🧩 Component Development Guidelines

## Overview

This guide establishes standards for creating maintainable, reusable, and well-documented React Native components in the TS1 project.

## 📏 Component Standards

### 1. **File Size Limit**
- **Maximum 300 lines** per component file
- Break large components into smaller, focused sub-components
- Use the compound component pattern for complex UI

### 2. **Single Responsibility Principle**
- Each component should have **one clear purpose**
- Avoid mixing different concerns in a single component
- Extract business logic into custom hooks

### 3. **TypeScript Requirements**
- All components must be **fully typed**
- Define proper interfaces for props
- Use generic types where appropriate

## 🏗️ Component Structure

### **Standard Component Template**
```typescript
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  // ... other React Native imports
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../common/Text';
// ... other imports

/**
 * ComponentName - Brief description of what this component does
 * 
 * @param prop1 - Description of prop1
 * @param prop2 - Description of prop2
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <ComponentName 
 *   prop1="value1"
 *   prop2={value2}
 *   onAction={handleAction}
 * />
 * ```
 */

interface ComponentNameProps {
  /** Description of prop1 */
  prop1: string;
  /** Description of prop2 with default value */
  prop2?: number;
  /** Callback function description */
  onAction: (data: ActionData) => void;
  /** Optional styling override */
  style?: ViewStyle;
}

const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2 = 0,
  onAction,
  style,
}) => {
  const { theme } = useTheme();
  
  // State declarations
  const [localState, setLocalState] = useState<StateType>(initialValue);
  
  // Custom hooks
  const { data, isLoading } = useCustomHook();
  
  // Event handlers
  const handlePress = useCallback(() => {
    onAction({ prop1, prop2 });
  }, [prop1, prop2, onAction]);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [dependencies]);
  
  // Early returns for loading/error states
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }, style]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        {prop1}
      </Text>
      {/* Component JSX */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Styles using normalize() for responsive design
    padding: normalize(16),
  },
  title: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
});

export default ComponentName;
```

## 📂 Component Organization

### **Directory Structure**
```
src/components/
├── common/                 # Generic reusable components
│   ├── Button.tsx         # Basic button component
│   ├── Input.tsx          # Form input component
│   ├── Text.tsx           # Themed text component
│   └── LoadingSpinner.tsx # Loading indicator
├── messages/              # Domain-specific components
│   ├── ChatHeader.tsx
│   ├── MessageItem.tsx
│   ├── ChatInput.tsx
│   └── index.ts           # Export barrel
├── video/                 # Video-related components
│   ├── VideoPlayer.tsx
│   ├── VideoCard.tsx
│   └── form/              # Sub-components
│       ├── VideoFormField.tsx
│       ├── TagsManager.tsx
│       └── index.ts
└── phone/                 # Phone verification components
    ├── CountrySelector.tsx
    ├── PhoneNumberInput.tsx
    └── index.ts
```

### **Export Patterns**
```typescript
// src/components/messages/index.ts
export { default as ChatHeader } from './ChatHeader';
export { default as MessageItem } from './MessageItem';
export { default as ChatInput } from './ChatInput';

// Usage in other files
import { ChatHeader, MessageItem } from '../components/messages';
```

## 🎨 Styling Guidelines

### 1. **Theme Integration**
```typescript
const ComponentName: React.FC<Props> = () => {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.text, { color: theme.colors.text }]}>
        Content
      </Text>
    </View>
  );
};
```

### 2. **Responsive Design**
```typescript
import { normalize } from '../../utils/responsive';

const styles = StyleSheet.create({
  container: {
    padding: normalize(16),        // Responsive padding
    borderRadius: normalize(8),    // Responsive border radius
  },
  text: {
    fontSize: normalize(16),       // Responsive font size
    lineHeight: normalize(24),     // Responsive line height
  },
});
```

### 3. **Style Organization**
```typescript
const styles = StyleSheet.create({
  // Container styles first
  container: {
    flex: 1,
    padding: normalize(16),
  },
  
  // Layout styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // Content styles
  title: {
    fontSize: normalize(18),
    fontWeight: '600',
  },
  
  // Interactive styles
  button: {
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(16),
  },
});
```

## 🔧 Component Patterns

### 1. **Compound Components**
Break complex components into smaller, focused pieces:

```typescript
// ✅ Main component
const VideoMetadataForm: React.FC<Props> = ({ onSubmit }) => (
  <ScrollView>
    <VideoMetadataForm.BasicInfo />
    <VideoMetadataForm.TypeSelector />
    <VideoMetadataForm.TagsManager />
    <VideoMetadataForm.PrivacySettings />
  </ScrollView>
);

// ✅ Sub-components
VideoMetadataForm.BasicInfo = ({ title, description, onTitleChange }) => (
  <View>
    <VideoFormField label="Title" value={title} onChangeText={onTitleChange} />
    <VideoFormField label="Description" value={description} multiline />
  </View>
);

VideoMetadataForm.TypeSelector = ({ selectedType, onTypeChange }) => (
  <VideoTypeSelector selectedType={selectedType} onSelectType={onTypeChange} />
);
```

### 2. **Render Props Pattern**
```typescript
interface DataProviderProps {
  children: (data: Data, isLoading: boolean) => React.ReactNode;
}

const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const { data, isLoading } = useData();
  return <>{children(data, isLoading)}</>;
};

// Usage
<DataProvider>
  {(data, isLoading) => (
    isLoading ? <LoadingSpinner /> : <DataList data={data} />
  )}
</DataProvider>
```

### 3. **Higher-Order Components (HOCs)**
```typescript
const withLoading = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P & { isLoading?: boolean }) => {
    const { isLoading, ...restProps } = props;
    
    if (isLoading) {
      return <LoadingSpinner />;
    }
    
    return <Component {...(restProps as P)} />;
  };
};

// Usage
const EnhancedComponent = withLoading(MyComponent);
```

## 📝 Documentation Standards

### 1. **JSDoc Comments**
```typescript
/**
 * MessageItem - Displays a single message in the chat
 * 
 * Handles different message types (text, image, video, audio) and provides
 * appropriate rendering for each type. Includes sender information and timestamp.
 * 
 * @param message - The message object to display
 * @param isCurrentUser - Whether the message was sent by the current user
 * @param onPress - Optional callback when message is pressed
 * @param onLongPress - Optional callback for long press actions
 * 
 * @returns A rendered message component
 * 
 * @example
 * ```tsx
 * <MessageItem
 *   message={messageData}
 *   isCurrentUser={message.sender_id === currentUser.id}
 *   onPress={() => handleMessagePress(message)}
 *   onLongPress={() => showMessageOptions(message)}
 * />
 * ```
 */
```

### 2. **Prop Documentation**
```typescript
interface MessageItemProps {
  /** The message object containing content, type, and metadata */
  message: MessageWithSender;
  
  /** Whether this message was sent by the current user */
  isCurrentUser: boolean;
  
  /** Optional callback fired when the message is tapped */
  onPress?: (message: MessageWithSender) => void;
  
  /** Optional callback fired when the message is long-pressed */
  onLongPress?: (message: MessageWithSender) => void;
  
  /** Optional style override for the container */
  style?: ViewStyle;
  
  /** Whether to show the sender's avatar (default: true) */
  showAvatar?: boolean;
  
  /** Whether to show the timestamp (default: true) */
  showTimestamp?: boolean;
}
```

### 3. **Usage Examples**
```typescript
/**
 * @example Basic usage
 * ```tsx
 * <Button title="Click me" onPress={handlePress} />
 * ```
 * 
 * @example With loading state
 * ```tsx
 * <Button 
 *   title="Submit" 
 *   onPress={handleSubmit}
 *   loading={isSubmitting}
 *   disabled={!isValid}
 * />
 * ```
 * 
 * @example Custom styling
 * ```tsx
 * <Button 
 *   title="Custom"
 *   variant="outline"
 *   size="large"
 *   style={{ marginTop: 20 }}
 * />
 * ```
 */
```

## 🧪 Testing Guidelines

### 1. **Component Testing**
```typescript
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ThemeProvider } from '../../contexts/ThemeContext';
import Button from '../Button';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const { getByText } = renderWithTheme(
      <Button title="Test Button" onPress={() => {}} />
    );
    
    expect(getByText('Test Button')).toBeTruthy();
  });
  
  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = renderWithTheme(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
  
  it('shows loading state correctly', () => {
    const { getByTestId } = renderWithTheme(
      <Button title="Test Button" onPress={() => {}} loading />
    );
    
    expect(getByTestId('loading-spinner')).toBeTruthy();
  });
});
```

### 2. **Snapshot Testing**
```typescript
import React from 'react';
import renderer from 'react-test-renderer';
import { ThemeProvider } from '../../contexts/ThemeContext';
import MessageItem from '../MessageItem';

describe('MessageItem Snapshots', () => {
  it('renders text message correctly', () => {
    const tree = renderer
      .create(
        <ThemeProvider>
          <MessageItem
            message={mockTextMessage}
            isCurrentUser={false}
          />
        </ThemeProvider>
      )
      .toJSON();
    
    expect(tree).toMatchSnapshot();
  });
});
```

## ⚡ Performance Guidelines

### 1. **Memoization**
```typescript
// ✅ Memoize expensive components
const ExpensiveComponent = React.memo<Props>(({ data, onPress }) => {
  // Component logic
}, (prevProps, nextProps) => {
  // Custom comparison logic
  return prevProps.data.id === nextProps.data.id;
});

// ✅ Memoize callbacks
const ParentComponent = () => {
  const handlePress = useCallback((id: string) => {
    // Handle press logic
  }, []);
  
  return <ExpensiveComponent onPress={handlePress} />;
};
```

### 2. **Lazy Loading**
```typescript
// ✅ Lazy load heavy components
const HeavyModal = React.lazy(() => import('./HeavyModal'));

const ParentComponent = () => {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <View>
      {showModal && (
        <Suspense fallback={<LoadingSpinner />}>
          <HeavyModal onClose={() => setShowModal(false)} />
        </Suspense>
      )}
    </View>
  );
};
```

## 🚨 Common Anti-Patterns to Avoid

### ❌ **Don't: Mix Business Logic in Components**
```typescript
// ❌ Bad: API calls in component
const MessagesScreen = () => {
  const [messages, setMessages] = useState([]);
  
  useEffect(() => {
    fetch('/api/messages')
      .then(res => res.json())
      .then(setMessages);
  }, []);
  
  // Component logic
};
```

### ✅ **Do: Use Custom Hooks**
```typescript
// ✅ Good: Business logic in custom hook
const MessagesScreen = () => {
  const { messages, isLoading } = useMessages();
  
  // Pure UI logic
};
```

### ❌ **Don't: Create Monolithic Components**
```typescript
// ❌ Bad: 500+ line component with multiple responsibilities
const MassiveForm = () => {
  // 500+ lines of mixed concerns
};
```

### ✅ **Do: Break Into Smaller Components**
```typescript
// ✅ Good: Focused, single-responsibility components
const UserForm = () => (
  <ScrollView>
    <PersonalInfoSection />
    <ContactInfoSection />
    <PreferencesSection />
    <ActionButtons />
  </ScrollView>
);
```

This guide ensures consistent, maintainable, and high-quality component development across the entire team.
