-- 1. Improved Send Message Function
CREATE OR R<PERSON>LACE FUNCTION send_message(
  p_conversation_id UUID,
  p_content TEXT,
  p_type VARCHAR DEFAULT 'text',
  p_media_url VARCHAR DEFAULT NULL,
  p_reply_to_message_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_sender_id UUID;
    v_message_id UUID;
BEGIN
    -- 1. Get the public user ID from the currently authenticated user
    SELECT id INTO v_sender_id
    FROM public.users
    WHERE auth_user_id = auth.uid();

    -- 2. If no corresponding public user is found, raise an error
    IF v_sender_id IS NULL THEN
        RAISE EXCEPTION 'Could not find a public user profile for the authenticated user.';
    END IF;

    -- 3. Insert the new message with the correct public sender_id
    INSERT INTO public.messages (
        conversation_id, 
        sender_id, 
        content, 
        type, 
        media_url, 
        reply_to_message_id,
        read_by
    )
    VALUES (
        p_conversation_id, 
        v_sender_id, 
        p_content, 
        p_type, 
        p_media_url, 
        p_reply_to_message_id,
        ARRAY[v_sender_id]::uuid[] -- Mark as read by sender
    )
    RETURNING id INTO v_message_id;

    -- 4. Update the conversation's last_message_id and last_message_at
    UPDATE public.conversations
    SET 
        last_message_id = v_message_id,
        last_message_at = NOW(),
        updated_at = NOW()
    WHERE id = p_conversation_id;

    RETURN v_message_id;
END;
$$;
CREATE OR REPLACE FUNCTION create_conversation(
  p_participant_id UUID,
  p_name VARCHAR DEFAULT NULL,
  p_type VARCHAR DEFAULT 'direct'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_new_conversation_id UUID;
  v_creator_id UUID;
BEGIN
  -- Get the public user ID for the current authenticated user
  SELECT id INTO v_creator_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF v_creator_id IS NULL THEN
    RAISE EXCEPTION 'Could not find a public user profile for the authenticated user.';
  END IF;

  -- Insert the conversation using public user IDs
  INSERT INTO conversations (
    type,
    name,
    created_by,
    participants
  )
  VALUES (
    p_type,
    p_name,
    v_creator_id,
    CASE
      WHEN p_type = 'direct' THEN ARRAY[v_creator_id, p_participant_id]::uuid[]
      ELSE ARRAY[v_creator_id]::uuid[]
    END
  )
  RETURNING id INTO v_new_conversation_id;

  RETURN v_new_conversation_id;
END;
$$;

-- 3. Add Participant to Conversation Function
CREATE OR REPLACE FUNCTION add_participant_to_conversation(
  p_conversation_id UUID,
  p_participant_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_conversation_type VARCHAR;
  v_participants UUID[];
BEGIN
  -- Get the public user ID from the currently authenticated user
  SELECT id INTO v_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  -- Get conversation type and current participants
  SELECT type, participants INTO v_conversation_type, v_participants
  FROM conversations
  WHERE id = p_conversation_id;

  -- Check if user is already a participant
  IF p_participant_id = ANY(v_participants) THEN
    RETURN TRUE; -- Already a participant
  END IF;

  -- Add participant to the array
  UPDATE conversations
  SET participants = array_append(participants, p_participant_id)
  WHERE id = p_conversation_id;

  RETURN TRUE;
END;
$$;

-- 4. Mark Messages as Read Function
CREATE OR REPLACE FUNCTION mark_messages_as_read(
  p_conversation_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the public user ID from the currently authenticated user
  SELECT id INTO v_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  -- Mark all messages in the conversation as read by this user
  UPDATE messages
  SET 
    read_by = array_append(read_by, v_user_id),
    read_at = jsonb_set(
      COALESCE(read_at, '{}'::jsonb),
      ARRAY[v_user_id::text],
      to_jsonb(NOW())
    )
  WHERE 
    conversation_id = p_conversation_id AND
    sender_id != v_user_id AND
    NOT (v_user_id = ANY(read_by));

  RETURN TRUE;
END;
$$;

-- 5. Create a Trigger to Update Conversation on Message Insert
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE conversations
  SET 
    last_message_id = NEW.id,
    last_message_at = NEW.created_at,
    updated_at = NOW()
  WHERE id = NEW.conversation_id;
  
  RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_update_conversation_last_message
AFTER INSERT ON messages
FOR EACH ROW
EXECUTE FUNCTION update_conversation_last_message();