{"name": "ts1", "version": "0.0.1", "private": true, "scripts": {"android": "npx react-native run-android", "ios": "npx react-native run-ios", "start": "npx react-native start", "test": "jest --passWithNoTests", "test:ci": "jest --coverage --watchAll=false --ci", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json}\"", "clean": "react-native clean-project-auto", "clean:android": "cd android && ./gradlew clean && cd ..", "clean:ios": "cd ios && xcodebuild clean && cd ..", "reset": "npx react-native start --reset-cache", "build:android:debug": "cd android && ./gradlew assembleDebug", "build:android:release": "cd android && ./gradlew assembleRelease", "build:android:bundle": "cd android && ./gradlew bundleRelease", "postinstall": "node scripts/patch-ffmpeg.js"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.50.0", "date-fns": "^4.1.0", "emoji-mart-native": "0.6.5-beta", "ffmpeg-kit-react-native": "^6.0.2", "react": "19.0.0", "react-native": "0.79.3", "react-native-audio-recorder-player": "^3.6.14", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.27.1", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-logs": "^5.0.0", "react-native-permissions": "^5.4.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.15.0", "react-native-vision-camera": "^4.6.4", "react-native-worklets-core": "^1.5.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "3.0.0", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}