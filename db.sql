-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.blocked_users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  blocker_user_id uuid NOT NULL,
  blocked_user_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT blocked_users_pkey PRIMARY KEY (id),
  CONSTRAINT blocked_users_blocked_user_id_fkey FOREIGN KEY (blocked_user_id) REFERENCES auth.users(id),
  CONSTRAINT blocked_users_blocker_user_id_fkey FOREIGN KEY (blocker_user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.comment_likes (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  comment_id uuid NOT NULL,
  user_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT comment_likes_pkey PRIMARY KEY (id),
  CONSTRAINT comment_likes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT comment_likes_comment_id_fkey FOREIGN KEY (comment_id) REFERENCES public.comments(id)
);
CREATE TABLE public.comments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  text text NOT NULL CHECK (char_length(text) >= 1 AND char_length(text) <= 500),
  video_id uuid NOT NULL,
  user_id uuid NOT NULL,
  parent_id uuid,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT comments_pkey PRIMARY KEY (id),
  CONSTRAINT comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT comments_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.comments(id)
);
-- TikTok-Style Messaging System Schema
-- Clean migration: Drop existing tables and recreate with optimized structure

-- Drop existing messaging tables and dependencies
DROP TABLE IF EXISTS public.message_reactions CASCADE;
DROP TABLE IF EXISTS public.messages CASCADE;
DROP TABLE IF EXISTS public.conversation_participants CASCADE;
DROP TABLE IF EXISTS public.conversations CASCADE;
DROP TABLE IF EXISTS public.typing_indicators CASCADE;

-- Drop existing functions and triggers
DROP FUNCTION IF EXISTS public.send_message CASCADE;
DROP FUNCTION IF EXISTS public.create_conversation CASCADE;
DROP FUNCTION IF EXISTS public.update_conversation_last_message CASCADE;
DROP FUNCTION IF EXISTS public.mark_messages_as_read CASCADE;

-- Create conversations table with TikTok-style features
CREATE TABLE public.conversations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  type character varying NOT NULL DEFAULT 'direct' CHECK (type::text = ANY (ARRAY['direct'::character varying, 'group'::character varying]::text[])),
  name character varying, -- Group name (null for direct chats)
  description text, -- Group description
  avatar_url character varying, -- Group avatar (null for direct chats)
  created_by uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  last_message_id uuid, -- Will reference messages table
  last_message_at timestamp with time zone,
  last_activity_at timestamp with time zone DEFAULT now(),
  is_active boolean NOT NULL DEFAULT true,
  is_archived boolean NOT NULL DEFAULT false,

  -- Group-specific settings
  group_settings jsonb DEFAULT '{
    "allow_members_to_add_others": false,
    "allow_members_to_change_info": false,
    "message_history_visible_to_new_members": true,
    "disappearing_messages_timer": null
  }'::jsonb,

  -- Privacy and moderation
  is_public boolean NOT NULL DEFAULT false, -- For group discoverability
  invite_link character varying, -- Group invite link
  invite_link_expires_at timestamp with time zone,

  -- Metadata for additional features
  metadata jsonb NOT NULL DEFAULT '{}'::jsonb,

  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT conversations_pkey PRIMARY KEY (id)
);
-- Conversation participants table with enhanced features
CREATE TABLE public.conversation_participants (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,

  -- Participant role and permissions
  role character varying NOT NULL DEFAULT 'member' CHECK (role::text = ANY (ARRAY['admin'::character varying, 'member'::character varying, 'moderator'::character varying]::text[])),

  -- Participation tracking
  joined_at timestamp with time zone NOT NULL DEFAULT now(),
  left_at timestamp with time zone,
  is_active boolean NOT NULL DEFAULT true,

  -- Message tracking for read receipts
  last_read_message_id uuid, -- Will reference messages table
  last_read_at timestamp with time zone,
  last_seen_at timestamp with time zone DEFAULT now(),

  -- Notification preferences
  notification_settings jsonb NOT NULL DEFAULT '{
    "muted": false,
    "muted_until": null,
    "push_notifications": true,
    "message_preview": true,
    "sound": true,
    "vibration": true
  }'::jsonb,

  -- Participant-specific settings
  custom_nickname character varying, -- Custom name for this participant in this chat
  is_pinned boolean NOT NULL DEFAULT false, -- Pin conversation for this user
  is_archived boolean NOT NULL DEFAULT false, -- Archive conversation for this user

  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT conversation_participants_pkey PRIMARY KEY (id),
  CONSTRAINT conversation_participants_unique UNIQUE (conversation_id, user_id)
);

CREATE TABLE public.data_export_requests (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  request_type text NOT NULL DEFAULT 'full_export'::text,
  status text NOT NULL DEFAULT 'pending'::text,
  export_url text,
  expires_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  completed_at timestamp with time zone,
  CONSTRAINT data_export_requests_pkey PRIMARY KEY (id)
);
CREATE TABLE public.login_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  session_id text NOT NULL,
  login_time timestamp with time zone NOT NULL DEFAULT now(),
  logout_time timestamp with time zone,
  ip_address text NOT NULL,
  user_agent text NOT NULL,
  device_info jsonb DEFAULT '{}'::jsonb,
  location_info jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT login_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT login_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
-- Messages table with TikTok-style features
CREATE TABLE public.messages (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,

  -- Message content
  content text, -- Can be null for media-only messages
  type character varying NOT NULL DEFAULT 'text' CHECK (type::text = ANY (ARRAY[
    'text'::character varying,
    'image'::character varying,
    'video'::character varying,
    'audio'::character varying,
    'voice'::character varying,
    'file'::character varying,
    'gif'::character varying,
    'sticker'::character varying,
    'location'::character varying,
    'contact'::character varying,
    'system'::character varying -- For system messages like "User joined group"
  ]::text[])),

  -- Media and file information
  media_url character varying,
  thumbnail_url character varying,
  file_name character varying,
  file_size bigint,
  file_type character varying,
  duration integer, -- For audio/video messages in seconds

  -- Message relationships
  reply_to_message_id uuid REFERENCES public.messages(id) ON DELETE SET NULL,
  forwarded_from_message_id uuid REFERENCES public.messages(id) ON DELETE SET NULL,
  thread_id uuid, -- For threaded conversations

  -- Message status and delivery
  status character varying NOT NULL DEFAULT 'sent' CHECK (status::text = ANY (ARRAY[
    'sending'::character varying,
    'sent'::character varying,
    'delivered'::character varying,
    'read'::character varying,
    'failed'::character varying
  ]::text[])),

  -- Read tracking (optimized for performance)
  read_by uuid[] NOT NULL DEFAULT '{}',
  read_at jsonb NOT NULL DEFAULT '{}', -- {user_id: timestamp}
  delivered_to uuid[] NOT NULL DEFAULT '{}',
  delivered_at jsonb NOT NULL DEFAULT '{}', -- {user_id: timestamp}

  -- Message modifications
  is_edited boolean NOT NULL DEFAULT false,
  edited_at timestamp with time zone,
  edit_history jsonb DEFAULT '[]', -- Array of previous content versions

  is_deleted boolean NOT NULL DEFAULT false,
  deleted_at timestamp with time zone,
  deleted_by uuid REFERENCES public.users(id) ON DELETE SET NULL,

  -- Message features
  is_forwarded boolean NOT NULL DEFAULT false,
  forward_count integer NOT NULL DEFAULT 0,
  is_pinned boolean NOT NULL DEFAULT false,
  pinned_by uuid REFERENCES public.users(id) ON DELETE SET NULL,
  pinned_at timestamp with time zone,

  -- Disappearing messages
  expires_at timestamp with time zone,
  is_expired boolean NOT NULL DEFAULT false,

  -- Reactions (optimized structure)
  reaction_counts jsonb NOT NULL DEFAULT '{}', -- {emoji: count}
  has_reactions boolean NOT NULL DEFAULT false, -- For quick filtering

  -- Additional metadata
  metadata jsonb NOT NULL DEFAULT '{}',

  -- Timestamps
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT messages_pkey PRIMARY KEY (id)
);
-- Message reactions table with TikTok-style emoji reactions
CREATE TABLE public.message_reactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  message_id uuid NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,

  -- TikTok-style reaction types (emojis)
  reaction character varying NOT NULL, -- Store actual emoji or reaction code
  reaction_type character varying NOT NULL DEFAULT 'emoji' CHECK (reaction_type::text = ANY (ARRAY[
    'emoji'::character varying,
    'like'::character varying,
    'love'::character varying,
    'laugh'::character varying,
    'wow'::character varying,
    'sad'::character varying,
    'angry'::character varying,
    'fire'::character varying,
    'heart'::character varying,
    'thumbs_up'::character varying,
    'thumbs_down'::character varying
  ]::text[])),

  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT message_reactions_pkey PRIMARY KEY (id),
  CONSTRAINT message_reactions_unique UNIQUE (message_id, user_id, reaction)
);

-- Typing indicators for real-time chat experience
CREATE TABLE public.typing_indicators (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  conversation_id uuid NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  is_typing boolean NOT NULL DEFAULT true,
  typing_type character varying NOT NULL DEFAULT 'text' CHECK (typing_type::text = ANY (ARRAY[
    'text'::character varying,
    'voice'::character varying,
    'media'::character varying
  ]::text[])),
  started_at timestamp with time zone NOT NULL DEFAULT now(),
  last_activity_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT typing_indicators_pkey PRIMARY KEY (id),
  CONSTRAINT typing_indicators_unique UNIQUE (conversation_id, user_id)
);

-- Message delivery tracking for detailed status
CREATE TABLE public.message_delivery (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  message_id uuid NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  status character varying NOT NULL CHECK (status::text = ANY (ARRAY[
    'sent'::character varying,
    'delivered'::character varying,
    'read'::character varying,
    'failed'::character varying
  ]::text[])),
  delivered_at timestamp with time zone,
  read_at timestamp with time zone,
  failed_reason text,

  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT message_delivery_pkey PRIMARY KEY (id),
  CONSTRAINT message_delivery_unique UNIQUE (message_id, user_id)
);

-- User online status for real-time presence
CREATE TABLE public.user_presence (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  status character varying NOT NULL DEFAULT 'offline' CHECK (status::text = ANY (ARRAY[
    'online'::character varying,
    'away'::character varying,
    'busy'::character varying,
    'offline'::character varying
  ]::text[])),
  last_seen_at timestamp with time zone NOT NULL DEFAULT now(),
  is_active boolean NOT NULL DEFAULT false,
  device_info jsonb DEFAULT '{}',

  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),

  CONSTRAINT user_presence_pkey PRIMARY KEY (id),
  CONSTRAINT user_presence_user_unique UNIQUE (user_id)
);

-- Add foreign key constraints for messages table
ALTER TABLE public.messages
  ADD CONSTRAINT messages_last_read_message_id_fkey
  FOREIGN KEY (reply_to_message_id) REFERENCES public.messages(id) ON DELETE SET NULL;

ALTER TABLE public.conversation_participants
  ADD CONSTRAINT conversation_participants_last_read_message_id_fkey
  FOREIGN KEY (last_read_message_id) REFERENCES public.messages(id) ON DELETE SET NULL;

ALTER TABLE public.conversations
  ADD CONSTRAINT conversations_last_message_id_fkey
  FOREIGN KEY (last_message_id) REFERENCES public.messages(id) ON DELETE SET NULL;

-- Create optimized indexes for performance
CREATE INDEX idx_conversations_created_by ON public.conversations(created_by);
CREATE INDEX idx_conversations_type ON public.conversations(type);
CREATE INDEX idx_conversations_last_activity ON public.conversations(last_activity_at DESC);
CREATE INDEX idx_conversations_active ON public.conversations(is_active) WHERE is_active = true;

CREATE INDEX idx_conversation_participants_conversation ON public.conversation_participants(conversation_id);
CREATE INDEX idx_conversation_participants_user ON public.conversation_participants(user_id);
CREATE INDEX idx_conversation_participants_active ON public.conversation_participants(conversation_id, user_id) WHERE is_active = true;
CREATE INDEX idx_conversation_participants_last_read ON public.conversation_participants(last_read_message_id);

CREATE INDEX idx_messages_conversation ON public.messages(conversation_id);
CREATE INDEX idx_messages_sender ON public.messages(sender_id);
CREATE INDEX idx_messages_created_at ON public.messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_type ON public.messages(type);
CREATE INDEX idx_messages_status ON public.messages(status);
CREATE INDEX idx_messages_reply_to ON public.messages(reply_to_message_id);
CREATE INDEX idx_messages_thread ON public.messages(thread_id);
CREATE INDEX idx_messages_reactions ON public.messages(id) WHERE has_reactions = true;
CREATE INDEX idx_messages_active ON public.messages(conversation_id, created_at DESC) WHERE is_deleted = false;

CREATE INDEX idx_message_reactions_message ON public.message_reactions(message_id);
CREATE INDEX idx_message_reactions_user ON public.message_reactions(user_id);
CREATE INDEX idx_message_reactions_type ON public.message_reactions(reaction_type);

CREATE INDEX idx_message_delivery_message ON public.message_delivery(message_id);
CREATE INDEX idx_message_delivery_user ON public.message_delivery(user_id);
CREATE INDEX idx_message_delivery_status ON public.message_delivery(status);

CREATE INDEX idx_typing_indicators_conversation ON public.typing_indicators(conversation_id);
CREATE INDEX idx_typing_indicators_user ON public.typing_indicators(user_id);
CREATE INDEX idx_typing_indicators_active ON public.typing_indicators(conversation_id, user_id) WHERE is_typing = true;

CREATE INDEX idx_user_presence_user ON public.user_presence(user_id);
CREATE INDEX idx_user_presence_status ON public.user_presence(status);
CREATE INDEX idx_user_presence_active ON public.user_presence(user_id) WHERE is_active = true;

-- Enable Row Level Security
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_delivery ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversations
CREATE POLICY "Users can view conversations they participate in" ON public.conversations
  FOR SELECT USING (
    id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND is_active = true
    )
  );

CREATE POLICY "Users can create conversations" ON public.conversations
  FOR INSERT WITH CHECK (
    created_by = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Conversation creators and admins can update conversations" ON public.conversations
  FOR UPDATE USING (
    created_by = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    ) OR
    id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND role IN ('admin', 'moderator') AND is_active = true
    )
  );

-- RLS Policies for conversation_participants
CREATE POLICY "Users can view participants in their conversations" ON public.conversation_participants
  FOR SELECT USING (
    conversation_id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND is_active = true
    )
  );

CREATE POLICY "Users can join conversations they're invited to" ON public.conversation_participants
  FOR INSERT WITH CHECK (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own participation" ON public.conversation_participants
  FOR UPDATE USING (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- RLS Policies for messages
CREATE POLICY "Users can view messages in their conversations" ON public.messages
  FOR SELECT USING (
    conversation_id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND is_active = true
    )
  );

CREATE POLICY "Users can send messages to their conversations" ON public.messages
  FOR INSERT WITH CHECK (
    sender_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    ) AND
    conversation_id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND is_active = true
    )
  );

CREATE POLICY "Users can update their own messages" ON public.messages
  FOR UPDATE USING (
    sender_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their own messages" ON public.messages
  FOR DELETE USING (
    sender_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- RLS Policies for message_reactions
CREATE POLICY "Users can view reactions in their conversations" ON public.message_reactions
  FOR SELECT USING (
    message_id IN (
      SELECT id FROM public.messages
      WHERE conversation_id IN (
        SELECT conversation_id
        FROM public.conversation_participants
        WHERE user_id = (
          SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        ) AND is_active = true
      )
    )
  );

CREATE POLICY "Users can add reactions to messages" ON public.message_reactions
  FOR INSERT WITH CHECK (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    ) AND
    message_id IN (
      SELECT id FROM public.messages
      WHERE conversation_id IN (
        SELECT conversation_id
        FROM public.conversation_participants
        WHERE user_id = (
          SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        ) AND is_active = true
      )
    )
  );

CREATE POLICY "Users can remove their own reactions" ON public.message_reactions
  FOR DELETE USING (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- RLS Policies for message_delivery
CREATE POLICY "Users can view delivery status for their messages" ON public.message_delivery
  FOR SELECT USING (
    message_id IN (
      SELECT id FROM public.messages
      WHERE sender_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      )
    ) OR
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- RLS Policies for typing_indicators
CREATE POLICY "Users can view typing indicators in their conversations" ON public.typing_indicators
  FOR SELECT USING (
    conversation_id IN (
      SELECT conversation_id
      FROM public.conversation_participants
      WHERE user_id = (
        SELECT id FROM public.users WHERE auth_user_id = auth.uid()
      ) AND is_active = true
    )
  );

CREATE POLICY "Users can manage their own typing indicators" ON public.typing_indicators
  FOR ALL USING (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- RLS Policies for user_presence
CREATE POLICY "Users can view presence of users in their conversations" ON public.user_presence
  FOR SELECT USING (
    user_id IN (
      SELECT DISTINCT cp.user_id
      FROM public.conversation_participants cp
      WHERE cp.conversation_id IN (
        SELECT conversation_id
        FROM public.conversation_participants
        WHERE user_id = (
          SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        ) AND is_active = true
      )
    ) OR
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own presence" ON public.user_presence
  FOR ALL USING (
    user_id = (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- Database Functions for TikTok-style messaging

-- Function to create a new conversation
CREATE OR REPLACE FUNCTION public.create_conversation(
  participant_ids uuid[],
  conversation_type text DEFAULT 'direct',
  conversation_name text DEFAULT NULL,
  conversation_description text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  new_conversation_id uuid;
  participant_id uuid;
BEGIN
  -- Get current user's public ID
  SELECT id INTO current_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Validate conversation type
  IF conversation_type NOT IN ('direct', 'group') THEN
    RAISE EXCEPTION 'Invalid conversation type';
  END IF;

  -- For direct conversations, ensure only 2 participants
  IF conversation_type = 'direct' AND array_length(participant_ids, 1) != 1 THEN
    RAISE EXCEPTION 'Direct conversations must have exactly 2 participants';
  END IF;

  -- Check if direct conversation already exists
  IF conversation_type = 'direct' THEN
    SELECT c.id INTO new_conversation_id
    FROM public.conversations c
    JOIN public.conversation_participants cp1 ON c.id = cp1.conversation_id
    JOIN public.conversation_participants cp2 ON c.id = cp2.conversation_id
    WHERE c.type = 'direct'
      AND cp1.user_id = current_user_id
      AND cp2.user_id = participant_ids[1]
      AND cp1.is_active = true
      AND cp2.is_active = true;

    IF new_conversation_id IS NOT NULL THEN
      RETURN new_conversation_id;
    END IF;
  END IF;

  -- Create new conversation
  INSERT INTO public.conversations (
    type,
    name,
    description,
    created_by,
    last_activity_at
  ) VALUES (
    conversation_type,
    conversation_name,
    conversation_description,
    current_user_id,
    now()
  ) RETURNING id INTO new_conversation_id;

  -- Add creator as admin
  INSERT INTO public.conversation_participants (
    conversation_id,
    user_id,
    role,
    joined_at,
    last_seen_at
  ) VALUES (
    new_conversation_id,
    current_user_id,
    CASE WHEN conversation_type = 'group' THEN 'admin' ELSE 'member' END,
    now(),
    now()
  );

  -- Add other participants
  FOREACH participant_id IN ARRAY participant_ids
  LOOP
    INSERT INTO public.conversation_participants (
      conversation_id,
      user_id,
      role,
      joined_at,
      last_seen_at
    ) VALUES (
      new_conversation_id,
      participant_id,
      'member',
      now(),
      now()
    );
  END LOOP;

  RETURN new_conversation_id;
END;
$$;

-- Function to send a message
CREATE OR REPLACE FUNCTION public.send_message(
  conversation_id_param uuid,
  sender_id_param uuid,
  content_param text DEFAULT NULL,
  message_type_param text DEFAULT 'text',
  file_url_param text DEFAULT NULL,
  file_type_param text DEFAULT NULL,
  file_name_param text DEFAULT NULL,
  file_size_param bigint DEFAULT NULL,
  thumbnail_url_param text DEFAULT NULL,
  duration_param integer DEFAULT NULL,
  reply_to_message_id_param uuid DEFAULT NULL,
  forwarded_from_message_id_param uuid DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  new_message_id uuid;
  participant_ids uuid[];
BEGIN
  -- Get current user's public ID
  SELECT id INTO current_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Verify sender is the current user
  IF sender_id_param != current_user_id THEN
    RAISE EXCEPTION 'Cannot send message as another user';
  END IF;

  -- Verify user is participant in conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversation_participants
    WHERE conversation_id = conversation_id_param
      AND user_id = current_user_id
      AND is_active = true
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Insert message
  INSERT INTO public.messages (
    conversation_id,
    sender_id,
    content,
    type,
    media_url,
    file_type,
    file_name,
    file_size,
    thumbnail_url,
    duration,
    reply_to_message_id,
    forwarded_from_message_id,
    is_forwarded
  ) VALUES (
    conversation_id_param,
    sender_id_param,
    content_param,
    message_type_param,
    file_url_param,
    file_type_param,
    file_name_param,
    file_size_param,
    thumbnail_url_param,
    duration_param,
    reply_to_message_id_param,
    forwarded_from_message_id_param,
    CASE WHEN forwarded_from_message_id_param IS NOT NULL THEN true ELSE false END
  ) RETURNING id INTO new_message_id;

  -- Update conversation last message
  UPDATE public.conversations
  SET
    last_message_id = new_message_id,
    last_message_at = now(),
    last_activity_at = now(),
    updated_at = now()
  WHERE id = conversation_id_param;

  -- Get all participants for delivery tracking
  SELECT array_agg(user_id) INTO participant_ids
  FROM public.conversation_participants
  WHERE conversation_id = conversation_id_param
    AND is_active = true
    AND user_id != current_user_id;

  -- Create delivery records for all participants except sender
  INSERT INTO public.message_delivery (message_id, user_id, status)
  SELECT new_message_id, unnest(participant_ids), 'sent';

  -- Update sender's last seen
  UPDATE public.conversation_participants
  SET last_seen_at = now()
  WHERE conversation_id = conversation_id_param
    AND user_id = current_user_id;

  RETURN new_message_id;
END;
$$;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION public.mark_messages_as_read(
  conversation_id_param uuid,
  message_ids uuid[] DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
  latest_message_id uuid;
BEGIN
  -- Get current user's public ID
  SELECT id INTO current_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Verify user is participant in conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversation_participants
    WHERE conversation_id = conversation_id_param
      AND user_id = current_user_id
      AND is_active = true
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- If specific message IDs provided, mark those as read
  IF message_ids IS NOT NULL THEN
    -- Update message read status
    UPDATE public.messages
    SET
      read_by = array_append(read_by, current_user_id),
      read_at = jsonb_set(read_at, ARRAY[current_user_id::text], to_jsonb(now())),
      updated_at = now()
    WHERE id = ANY(message_ids)
      AND conversation_id = conversation_id_param
      AND NOT (current_user_id = ANY(read_by));

    -- Update delivery status
    UPDATE public.message_delivery
    SET
      status = 'read',
      read_at = now(),
      updated_at = now()
    WHERE message_id = ANY(message_ids)
      AND user_id = current_user_id
      AND status != 'read';

    -- Get the latest message ID from the provided list
    SELECT id INTO latest_message_id
    FROM public.messages
    WHERE id = ANY(message_ids)
      AND conversation_id = conversation_id_param
    ORDER BY created_at DESC
    LIMIT 1;
  ELSE
    -- Mark all unread messages as read
    UPDATE public.messages
    SET
      read_by = array_append(read_by, current_user_id),
      read_at = jsonb_set(read_at, ARRAY[current_user_id::text], to_jsonb(now())),
      updated_at = now()
    WHERE conversation_id = conversation_id_param
      AND NOT (current_user_id = ANY(read_by))
      AND sender_id != current_user_id;

    -- Update delivery status for all unread messages
    UPDATE public.message_delivery
    SET
      status = 'read',
      read_at = now(),
      updated_at = now()
    WHERE message_id IN (
        SELECT id FROM public.messages
        WHERE conversation_id = conversation_id_param
          AND sender_id != current_user_id
      )
      AND user_id = current_user_id
      AND status != 'read';

    -- Get the latest message ID
    SELECT id INTO latest_message_id
    FROM public.messages
    WHERE conversation_id = conversation_id_param
      AND sender_id != current_user_id
    ORDER BY created_at DESC
    LIMIT 1;
  END IF;

  -- Update participant's last read message
  UPDATE public.conversation_participants
  SET
    last_read_message_id = latest_message_id,
    last_read_at = now(),
    last_seen_at = now(),
    updated_at = now()
  WHERE conversation_id = conversation_id_param
    AND user_id = current_user_id;
END;
$$;

-- Function to update typing indicator
CREATE OR REPLACE FUNCTION public.update_typing_indicator(
  conversation_id_param uuid,
  is_typing_param boolean DEFAULT true,
  typing_type_param text DEFAULT 'text'
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
BEGIN
  -- Get current user's public ID
  SELECT id INTO current_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Verify user is participant in conversation
  IF NOT EXISTS (
    SELECT 1 FROM public.conversation_participants
    WHERE conversation_id = conversation_id_param
      AND user_id = current_user_id
      AND is_active = true
  ) THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Insert or update typing indicator
  INSERT INTO public.typing_indicators (
    conversation_id,
    user_id,
    is_typing,
    typing_type,
    last_activity_at
  ) VALUES (
    conversation_id_param,
    current_user_id,
    is_typing_param,
    typing_type_param,
    now()
  )
  ON CONFLICT (conversation_id, user_id)
  DO UPDATE SET
    is_typing = is_typing_param,
    typing_type = typing_type_param,
    last_activity_at = now();

  -- Clean up old typing indicators (older than 10 seconds)
  DELETE FROM public.typing_indicators
  WHERE last_activity_at < now() - interval '10 seconds';
END;
$$;

-- Function to update user presence
CREATE OR REPLACE FUNCTION public.update_user_presence(
  status_param text DEFAULT 'online',
  device_info_param jsonb DEFAULT '{}'
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_id uuid;
BEGIN
  -- Get current user's public ID
  SELECT id INTO current_user_id
  FROM public.users
  WHERE auth_user_id = auth.uid();

  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Insert or update user presence
  INSERT INTO public.user_presence (
    user_id,
    status,
    last_seen_at,
    is_active,
    device_info
  ) VALUES (
    current_user_id,
    status_param,
    now(),
    CASE WHEN status_param = 'online' THEN true ELSE false END,
    device_info_param
  )
  ON CONFLICT (user_id)
  DO UPDATE SET
    status = status_param,
    last_seen_at = now(),
    is_active = CASE WHEN status_param = 'online' THEN true ELSE false END,
    device_info = device_info_param,
    updated_at = now();
END;
$$;

-- Triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update triggers to all tables
CREATE TRIGGER update_conversations_updated_at
  BEFORE UPDATE ON public.conversations
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_conversation_participants_updated_at
  BEFORE UPDATE ON public.conversation_participants
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_messages_updated_at
  BEFORE UPDATE ON public.messages
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_message_reactions_updated_at
  BEFORE UPDATE ON public.message_reactions
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_message_delivery_updated_at
  BEFORE UPDATE ON public.message_delivery
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_presence_updated_at
  BEFORE UPDATE ON public.user_presence
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Trigger to update reaction counts when reactions are added/removed
CREATE OR REPLACE FUNCTION public.update_message_reaction_counts()
RETURNS TRIGGER AS $$
DECLARE
  reaction_data jsonb;
BEGIN
  -- Calculate new reaction counts for the message
  SELECT jsonb_object_agg(reaction, count)
  INTO reaction_data
  FROM (
    SELECT reaction, COUNT(*)::int as count
    FROM public.message_reactions
    WHERE message_id = COALESCE(NEW.message_id, OLD.message_id)
    GROUP BY reaction
  ) counts;

  -- Update the message with new reaction counts
  UPDATE public.messages
  SET
    reaction_counts = COALESCE(reaction_data, '{}'::jsonb),
    has_reactions = (reaction_data IS NOT NULL AND jsonb_object_keys(reaction_data) IS NOT NULL),
    updated_at = now()
  WHERE id = COALESCE(NEW.message_id, OLD.message_id);

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_message_reaction_counts_trigger
  AFTER INSERT OR DELETE ON public.message_reactions
  FOR EACH ROW EXECUTE FUNCTION public.update_message_reaction_counts();

-- Trigger to clean up expired typing indicators
CREATE OR REPLACE FUNCTION public.cleanup_expired_typing_indicators()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM public.typing_indicators
  WHERE last_activity_at < now() - interval '30 seconds';
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that runs periodically (we'll use a scheduled job for this)
-- For now, we'll clean up on each insert/update
CREATE TRIGGER cleanup_typing_indicators_trigger
  AFTER INSERT OR UPDATE ON public.typing_indicators
  FOR EACH STATEMENT EXECUTE FUNCTION public.cleanup_expired_typing_indicators();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Enable realtime for messaging tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversation_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reactions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_delivery;
ALTER PUBLICATION supabase_realtime ADD TABLE public.typing_indicators;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_presence;
  conversation_id uuid NOT NULL,
  user_id uuid NOT NULL,
  is_typing boolean NOT NULL DEFAULT true,
  last_activity timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT typing_indicators_pkey PRIMARY KEY (id),
  CONSTRAINT typing_indicators_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(id) ON DELETE CASCADE,
  CONSTRAINT typing_indicators_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT typing_indicators_unique UNIQUE (conversation_id, user_id)
);

CREATE TABLE public.music_library (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  artist text,
  duration integer NOT NULL,
  file_url text NOT NULL,
  thumbnail_url text,
  genre text,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT music_library_pkey PRIMARY KEY (id)
);
CREATE TABLE public.notification_settings (
  user_id uuid NOT NULL,
  push_notifications boolean DEFAULT true,
  email_notifications boolean DEFAULT true,
  sms_notifications boolean DEFAULT false,
  like_notifications boolean DEFAULT true,
  comment_notifications boolean DEFAULT true,
  follow_notifications boolean DEFAULT true,
  live_notifications boolean DEFAULT true,
  message_notifications boolean DEFAULT true,
  mention_notifications boolean DEFAULT true,
  reaction_notifications boolean DEFAULT true,
  video_upload_notifications boolean DEFAULT true,
  security_notifications boolean DEFAULT true,
  marketing_notifications boolean DEFAULT false,
  quiet_hours_enabled boolean DEFAULT false,
  quiet_hours_start time DEFAULT '22:00:00',
  quiet_hours_end time DEFAULT '08:00:00',
  notification_sound character varying DEFAULT 'default',
  vibration_enabled boolean DEFAULT true,
  badge_count_enabled boolean DEFAULT true,
  preview_enabled boolean DEFAULT true,
  group_similar boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT notification_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT notification_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['message'::character varying, 'like'::character varying, 'comment'::character varying, 'follow'::character varying, 'mention'::character varying, 'live'::character varying, 'video_upload'::character varying, 'system'::character varying, 'security'::character varying, 'reaction'::character varying]::text[])),
  title text NOT NULL,
  body text NOT NULL,
  data jsonb NOT NULL DEFAULT '{}'::jsonb,
  is_read boolean DEFAULT false,
  is_delivered boolean DEFAULT false,
  delivered_at timestamp with time zone,
  read_at timestamp with time zone,
  priority character varying NOT NULL DEFAULT 'normal'::character varying CHECK (priority::text = ANY (ARRAY['low'::character varying, 'normal'::character varying, 'high'::character varying, 'urgent'::character varying]::text[])),
  category character varying NOT NULL DEFAULT 'general'::character varying CHECK (category::text = ANY (ARRAY['general'::character varying, 'social'::character varying, 'content'::character varying, 'security'::character varying, 'system'::character varying]::text[])),
  action_url text,
  expires_at timestamp with time zone,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT notifications_pkey PRIMARY KEY (id),
  CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);

CREATE TABLE public.notification_delivery_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  notification_id uuid NOT NULL,
  delivery_method character varying NOT NULL CHECK (delivery_method::text = ANY (ARRAY['push'::character varying, 'email'::character varying, 'sms'::character varying, 'in_app'::character varying]::text[])),
  status character varying NOT NULL CHECK (status::text = ANY (ARRAY['pending'::character varying, 'sent'::character varying, 'delivered'::character varying, 'failed'::character varying, 'bounced'::character varying]::text[])),
  provider character varying,
  external_id text,
  error_message text,
  delivered_at timestamp with time zone,
  opened_at timestamp with time zone,
  clicked_at timestamp with time zone,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT notification_delivery_log_pkey PRIMARY KEY (id),
  CONSTRAINT notification_delivery_log_notification_id_fkey FOREIGN KEY (notification_id) REFERENCES public.notifications(id) ON DELETE CASCADE
);
CREATE TABLE public.personal_data_settings (
  user_id uuid NOT NULL,
  data_export_requested boolean DEFAULT false,
  data_export_date timestamp with time zone,
  data_deletion_requested boolean DEFAULT false,
  data_deletion_date timestamp with time zone,
  marketing_emails boolean DEFAULT true,
  analytics_tracking boolean DEFAULT true,
  third_party_sharing boolean DEFAULT false,
  data_retention_period integer DEFAULT 365,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT personal_data_settings_pkey PRIMARY KEY (user_id)
);
CREATE TABLE public.phone_verifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  phone_number text NOT NULL,
  verification_code text NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  is_used boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT phone_verifications_pkey PRIMARY KEY (id),
  CONSTRAINT phone_verifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.privacy_settings (
  user_id uuid NOT NULL,
  allow_messages_from text DEFAULT 'everyone'::text CHECK (allow_messages_from = ANY (ARRAY['everyone'::text, 'followers'::text, 'nobody'::text])),
  show_online_status boolean DEFAULT true,
  allow_downloads boolean DEFAULT true,
  allow_duets boolean DEFAULT true,
  allow_comments boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  profile_visibility boolean NOT NULL DEFAULT true,
  CONSTRAINT privacy_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT privacy_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.profiles (
  user_id uuid NOT NULL,
  bio text,
  profile_picture_url text,
  banner_image_url text,
  user_tag USER-DEFINED DEFAULT 'Supporter'::user_tag,
  CONSTRAINT profiles_pkey PRIMARY KEY (user_id),
  CONSTRAINT profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.security_activities (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  activity_type text NOT NULL,
  ip_address text NOT NULL,
  device_info text NOT NULL,
  location text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  user_agent text,
  city text,
  country text,
  region text,
  coordinates text,
  timezone text,
  session_id text,
  details jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT security_activities_pkey PRIMARY KEY (id),
  CONSTRAINT security_activities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.security_settings (
  user_id uuid NOT NULL,
  two_factor_enabled boolean NOT NULL DEFAULT false,
  login_notifications boolean NOT NULL DEFAULT true,
  device_management boolean NOT NULL DEFAULT true,
  session_timeout integer NOT NULL DEFAULT 30,
  password_last_changed timestamp with time zone,
  security_questions_set boolean NOT NULL DEFAULT false,
  backup_codes_generated boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT security_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT security_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.settings (
  user_id uuid NOT NULL,
  is_private_account boolean DEFAULT false,
  allow_messages_from text DEFAULT 'Everyone'::text CHECK (allow_messages_from = ANY (ARRAY['Everyone'::text, 'Friends'::text, 'NoOne'::text])),
  show_activity_status boolean DEFAULT true,
  dark_mode boolean DEFAULT false,
  CONSTRAINT settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_fingerprints (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  fingerprint text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_fingerprints_pkey PRIMARY KEY (id),
  CONSTRAINT user_fingerprints_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_follows (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  follower_id uuid NOT NULL,
  following_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_follows_pkey PRIMARY KEY (id),
  CONSTRAINT user_follows_following_id_fkey FOREIGN KEY (following_id) REFERENCES auth.users(id),
  CONSTRAINT user_follows_follower_id_fkey FOREIGN KEY (follower_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_mac_addresses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  mac_address macaddr NOT NULL,
  CONSTRAINT user_mac_addresses_pkey PRIMARY KEY (id),
  CONSTRAINT user_ip_addresses_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_phone_numbers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  phone_number text NOT NULL,
  is_verified boolean NOT NULL DEFAULT false,
  is_primary boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_phone_numbers_pkey PRIMARY KEY (id),
  CONSTRAINT user_phone_numbers_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  username text NOT NULL,
  full_name text,
  email text NOT NULL UNIQUE,
  phone_number text,
  password_hash text NOT NULL,
  date_of_birth date,
  gender text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_verified boolean DEFAULT false,
  is_banned boolean DEFAULT false,
  auth_user_id uuid UNIQUE,
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_auth_user_id_fkey FOREIGN KEY (auth_user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.video_likes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  video_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT video_likes_pkey PRIMARY KEY (id),
  CONSTRAINT video_likes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT video_likes_video_id_fkey FOREIGN KEY (video_id) REFERENCES public.videos(id)
);
CREATE TABLE public.videos (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  title text,
  description text,
  video_url text NOT NULL,
  thumbnail_url text,
  duration integer,
  original_audio_volume numeric DEFAULT 1.0,
  music_id uuid,
  music_volume numeric DEFAULT 0.5,
  privacy_setting text NOT NULL DEFAULT 'public'::text CHECK (privacy_setting = ANY (ARRAY['public'::text, 'friends'::text, 'private'::text])),
  allow_comments boolean DEFAULT true,
  allow_downloads boolean DEFAULT true,
  views_count integer DEFAULT 0,
  likes_count integer DEFAULT 0,
  comments_count integer DEFAULT 0,
  shares_count integer DEFAULT 0,
  is_draft boolean DEFAULT true,
  published_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  author text,
  duration_sec integer,
  likes integer DEFAULT 0,
  sport text,
  tags ARRAY DEFAULT ARRAY[]::text[],
  team_mentioned text,
  type text CHECK (type = ANY (ARRAY['live'::text, 'highlight'::text, 'analysis'::text, 'interview'::text, 'training'::text, 'challenge'::text, 'For fun'::text, 'esport'::text])),
  upload_date timestamp with time zone,
  video_id text NOT NULL UNIQUE,
  views integer DEFAULT 0,
  custom_music_url text,
  processing_metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT videos_pkey PRIMARY KEY (id),
  CONSTRAINT videos_music_id_fkey FOREIGN KEY (music_id) REFERENCES public.music_library(id)
);