#!/usr/bin/env node

/**
 * Backup script to manually download FFmpeg-Kit AAR from Appodeal
 * Use this if the Maven dependency fails
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

console.log('📦 Downloading FFmpeg-Kit AAR from Appodeal Artifactory...');

const aarUrl = 'https://artifactory.appodeal.com/appodeal-public/com/arthenica/ffmpeg-kit-full-gpl/6.0-2.LTS/ffmpeg-kit-full-gpl-6.0-2.LTS.aar';
const libsDir = path.join(__dirname, '../android/libs');
const aarFile = path.join(libsDir, 'ffmpeg-kit-full-gpl.aar');

// Create libs directory if it doesn't exist
if (!fs.existsSync(libsDir)) {
  console.log('📁 Creating libs directory...');
  fs.mkdirSync(libsDir, { recursive: true });
}

// Check if AAR already exists
if (fs.existsSync(aarFile)) {
  console.log('ℹ️ FFmpeg-Kit AAR already exists, skipping download');
  process.exit(0);
}

// Download function
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest);
    
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          console.log('✅ FFmpeg-Kit AAR downloaded successfully');
          resolve();
        });
        
        file.on('error', (err) => {
          fs.unlink(dest, () => {}); // Delete the file on error
          reject(err);
        });
      } else if (response.statusCode === 302 || response.statusCode === 301) {
        // Handle redirect
        downloadFile(response.headers.location, dest).then(resolve).catch(reject);
      } else {
        reject(new Error(`Download failed with status: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Download the AAR
downloadFile(aarUrl, aarFile)
  .then(() => {
    console.log('🎬 FFmpeg-Kit AAR ready for use');
    console.log('');
    console.log('Next steps:');
    console.log('1. Update android/app/build.gradle to use:');
    console.log('   implementation(name: "ffmpeg-kit-full-gpl", ext: "aar")');
    console.log('2. Add flatDir repository:');
    console.log('   flatDir { dirs "$rootDir/libs" }');
    console.log('3. Run: cd android && ./gradlew clean && ./gradlew assembleDebug');
  })
  .catch((error) => {
    console.error('❌ Failed to download FFmpeg-Kit AAR:', error.message);
    console.log('');
    console.log('Alternative solutions:');
    console.log('1. Check if Appodeal Artifactory is accessible');
    console.log('2. Try using a VPN if there are regional restrictions');
    console.log('3. Consider using react-native-video-processing as alternative');
    process.exit(1);
  });
