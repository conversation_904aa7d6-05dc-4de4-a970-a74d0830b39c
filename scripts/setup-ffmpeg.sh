#!/bin/bash

echo "🎬 Setting up FFmpeg-Kit for React Native..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Cleaning previous builds..."
npm run clean

print_status "Installing dependencies..."
npm install --legacy-peer-deps

print_status "Running FFmpeg patch..."
node scripts/patch-ffmpeg.js

print_status "Setting up Android..."
cd android

print_status "Cleaning Android build..."
./gradlew clean

print_status "Building Android..."
./gradlew assembleDebug

cd ..

print_status "Setting up iOS (if on macOS)..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    cd ios
    print_status "Installing iOS pods..."
    pod install
    cd ..
else
    print_warning "Skipping iOS setup (not on macOS)"
fi

print_status "FFmpeg-Kit setup completed!"
echo ""
echo "🎬 Next steps:"
echo "1. Run 'npm run android' to start Android app"
echo "2. Run 'npm run ios' to start iOS app (macOS only)"
echo "3. Test video generation in the photo editor"
echo ""
echo "📝 Notes:"
echo "- Using Appodeal Artifactory (since Maven Central retired FFmpeg-Kit)"
echo "- FFmpeg-Kit uses GPL license (check licensing requirements)"
echo "- Video generation may take time depending on device performance"
echo "- Ensure sufficient storage space for video files"
echo ""
echo "🔍 Verify setup:"
echo "node scripts/test-ffmpeg.js"
