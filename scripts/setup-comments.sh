#!/bin/bash

# =====================================================
# Comments System Setup Script
# =====================================================

set -e

echo "🚀 Setting up TikTok-style Comments System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI is not installed${NC}"
    echo "Please install it first: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're in a Supabase project
if [ ! -f "supabase/config.toml" ]; then
    echo -e "${RED}❌ Not in a Supabase project directory${NC}"
    echo "Please run this script from your project root directory"
    exit 1
fi

echo -e "${BLUE}📋 Checking Supabase project status...${NC}"

# Check if project is linked
if ! supabase status &> /dev/null; then
    echo -e "${YELLOW}⚠️  Project not linked or local development not started${NC}"
    echo "Please run: supabase start"
    echo "Or link to remote: supabase link --project-ref YOUR_PROJECT_REF"
    exit 1
fi

echo -e "${GREEN}✅ Supabase project is ready${NC}"

# Apply the migration
echo -e "${BLUE}📦 Applying comments system migration...${NC}"

if [ -f "supabase/migrations/20250104_create_comments_system.sql" ]; then
    # Apply migration using Supabase CLI
    supabase db push
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Migration applied successfully${NC}"
    else
        echo -e "${RED}❌ Migration failed${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Migration file not found${NC}"
    echo "Please ensure supabase/migrations/20250104_create_comments_system.sql exists"
    exit 1
fi

# Run setup verification
echo -e "${BLUE}🔍 Verifying setup...${NC}"

# Check if tables exist
TABLES_CHECK=$(supabase db diff --schema public | grep -E "(comments|comment_likes)" | wc -l)

if [ "$TABLES_CHECK" -eq 0 ]; then
    echo -e "${GREEN}✅ Tables created successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Some tables may not have been created properly${NC}"
fi

# Optional: Create test data
echo -e "${BLUE}🎯 Would you like to create test data? (y/n)${NC}"
read -r CREATE_TEST_DATA

if [[ $CREATE_TEST_DATA =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}📝 Creating test data...${NC}"
    
    # Run the test data function
    supabase db reset --linked
    
    echo -e "${GREEN}✅ Test data created${NC}"
    echo -e "${YELLOW}📝 Note: You can clean up test data later using the cleanup function${NC}"
fi

# Display next steps
echo -e "${GREEN}🎉 Comments system setup complete!${NC}"
echo ""
echo -e "${BLUE}📚 Next steps:${NC}"
echo "1. Update your React Native app with the new API endpoints"
echo "2. Test the comments functionality in your app"
echo "3. Check the integration guide: docs/COMMENTS_INTEGRATION_GUIDE.md"
echo ""
echo -e "${BLUE}🔧 Useful commands:${NC}"
echo "- View tables: supabase db diff"
echo "- Check policies: supabase db diff --schema auth"
echo "- Monitor logs: supabase logs"
echo ""
echo -e "${BLUE}📊 Database functions available:${NC}"
echo "- get_comment_stats() - Get overall comment statistics"
echo "- get_user_comment_activity(user_id) - Get user activity"
echo "- create_test_comments_data() - Create test data"
echo "- cleanup_test_comments_data() - Remove test data"
echo ""
echo -e "${GREEN}✨ Happy coding!${NC}"
