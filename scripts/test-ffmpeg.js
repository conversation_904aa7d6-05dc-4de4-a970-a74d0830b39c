#!/usr/bin/env node

/**
 * Test script to verify FFmpeg-Kit setup
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing FFmpeg-Kit setup...\n');

// Check if package.json has the right dependency
const packageJsonPath = path.join(__dirname, '../package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (packageJson.dependencies['ffmpeg-kit-react-native']) {
    console.log('✅ ffmpeg-kit-react-native found in dependencies');
    console.log(`   Version: ${packageJson.dependencies['ffmpeg-kit-react-native']}`);
  } else {
    console.log('❌ ffmpeg-kit-react-native not found in dependencies');
  }
} else {
  console.log('❌ package.json not found');
}

// Check Android build.gradle
const androidBuildGradlePath = path.join(__dirname, '../android/app/build.gradle');
if (fs.existsSync(androidBuildGradlePath)) {
  const buildGradleContent = fs.readFileSync(androidBuildGradlePath, 'utf8');

  if (buildGradleContent.includes('ffmpeg-kit-full-gpl:6.0-2.LTS')) {
    console.log('✅ FFmpeg-Kit LTS dependency found in Android build.gradle');
  } else {
    console.log('❌ FFmpeg-Kit LTS dependency not found in Android build.gradle');
  }

  if (buildGradleContent.includes('smart-exception-java')) {
    console.log('✅ Smart Exception dependency found');
  } else {
    console.log('❌ Smart Exception dependency not found');
  }
} else {
  console.log('❌ Android build.gradle not found');
}

// Check root build.gradle for Appodeal repository
const rootBuildGradlePath = path.join(__dirname, '../android/build.gradle');
if (fs.existsSync(rootBuildGradlePath)) {
  const rootBuildGradleContent = fs.readFileSync(rootBuildGradlePath, 'utf8');

  if (rootBuildGradleContent.includes('artifactory.appodeal.com')) {
    console.log('✅ Appodeal Artifactory repository found');
  } else {
    console.log('❌ Appodeal Artifactory repository not found');
  }
} else {
  console.log('❌ Root build.gradle not found');
}

// Check if node_modules has the package
const nodeModulesPath = path.join(__dirname, '../node_modules/ffmpeg-kit-react-native');
if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ ffmpeg-kit-react-native installed in node_modules');
  
  // Check if Android files exist
  const androidPath = path.join(nodeModulesPath, 'android');
  if (fs.existsSync(androidPath)) {
    console.log('✅ Android native files found');
  } else {
    console.log('❌ Android native files not found');
  }
} else {
  console.log('❌ ffmpeg-kit-react-native not found in node_modules');
}

// Check video generator
const videoGeneratorPath = path.join(__dirname, '../src/utils/videoGenerator.ts');
if (fs.existsSync(videoGeneratorPath)) {
  const videoGeneratorContent = fs.readFileSync(videoGeneratorPath, 'utf8');
  
  if (videoGeneratorContent.includes('ffmpeg-kit-react-native')) {
    console.log('✅ Video generator uses FFmpeg-Kit');
  } else {
    console.log('❌ Video generator not updated for FFmpeg-Kit');
  }
} else {
  console.log('❌ Video generator not found');
}

console.log('\n🎬 Setup verification complete!');
console.log('\nNext steps:');
console.log('1. Run: npm run clean');
console.log('2. Run: npm install --legacy-peer-deps');
console.log('3. Run: cd android && ./gradlew clean && ./gradlew assembleDebug');
console.log('4. Run: npm run android');
console.log('\nIf you see any ❌ above, please check the setup instructions.');
