/* *****************/
/* FFMPEG-KIT PATCH */
/* ****************/
const fs = require("fs");
const path = require("path");

console.log("🔧 Starting FFmpeg-Kit patch for Appodeal Artifactory...");

// Path to the FFmpeg-Kit build.gradle file
const ffmpegBuildGradlePath = path.join(
  __dirname,
  "../node_modules/ffmpeg-kit-react-native/android/build.gradle"
);

// Make sure the file exists
if (!fs.existsSync(ffmpegBuildGradlePath)) {
  console.log("⚠️ FFmpeg-Kit build.gradle file not found, skipping patch");
  console.log("This is normal - using app-level configuration instead");
  process.exit(0);
}

try {
  // Read the original content
  let content = fs.readFileSync(ffmpegBuildGradlePath, "utf8");

  console.log("📝 Patching FFmpeg-Kit build.gradle for Appodeal repository...");

  // Fix repositories section - remove duplicate entries and add Appodeal
  content = content.replace(
    /repositories\s*{[\s\S]*?}/,
    `repositories {
        google()
        mavenCentral()
        maven {
            url "https://artifactory.appodeal.com/appodeal-public"
            name "Appodeal Artifactory"
        }
    }`
  );
  console.log("✅ Fixed repositories section with Appodeal Artifactory");

  // Replace local AAR dependency with Maven dependency
  content = content.replace(
    /implementation\(name:\s*['"]ffmpeg-kit-full-gpl['"],\s*ext:\s*['"]aar['"]\)/g,
    "implementation 'com.arthenica:ffmpeg-kit-full-gpl:6.0-2.LTS'"
  );
  console.log("✅ Replaced local AAR with Maven dependency");

  // Also handle any other FFmpeg-Kit dependency patterns
  content = content.replace(
    /implementation\s+['"]com\.arthenica:ffmpeg-kit-[^:]+:[^'"]+['"]/g,
    "implementation 'com.arthenica:ffmpeg-kit-full-gpl:6.0-2.LTS'"
  );
  console.log("✅ Updated any existing FFmpeg-Kit Maven dependencies");

  // Remove flatDir references since we're using Maven
  content = content.replace(
    /flatDir\s*{\s*dirs\s+[^}]+}/g,
    ""
  );
  console.log("✅ Removed flatDir references");

  // Write the modified content back
  fs.writeFileSync(ffmpegBuildGradlePath, content, "utf8");
  console.log("✅ Successfully patched FFmpeg-Kit for Appodeal Artifactory");

} catch (error) {
  console.error("❌ Error patching FFmpeg-Kit:", error.message);
  console.log("ℹ️ Using app-level configuration instead");
  process.exit(0); // Don't fail the build
}