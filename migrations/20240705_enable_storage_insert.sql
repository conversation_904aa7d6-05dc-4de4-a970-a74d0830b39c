-- Migration: enable insert on storage.objects for authenticated users

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON storage.objects;
CREATE POLICY "Enable insert for authenticated users only"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (true);

DROP POLICY IF EXISTS "Enable insert for users based on user_id" ON storage.objects;
CREATE POLICY "Enable insert for users based on user_id"
ON storage.objects
FOR INSERT
WITH CHECK ((select auth.uid()) = user_id);
