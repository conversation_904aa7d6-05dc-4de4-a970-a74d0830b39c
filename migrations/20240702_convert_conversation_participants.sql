-- Migration: convert auth user IDs to public user IDs in conversations.participants

UPDATE public.conversations AS c
SET participants = sub.new_participants
FROM (
    SELECT c.id,
           array_agg(COALESCE(u.id, part) ORDER BY ord) AS new_participants
    FROM public.conversations c
         CROSS JOIN LATERAL unnest(c.participants) WITH ORDINALITY AS t(part, ord)
         LEFT JOIN public.users u ON u.auth_user_id = t.part
    GROUP BY c.id
) AS sub
WHERE c.id = sub.id
  AND c.participants <> sub.new_participants;

-- Ensure conversations use RLS allowing participants to read
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS select_if_participant ON public.conversations;
CREATE POLICY select_if_participant ON public.conversations
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.auth_user_id = auth.uid()
      AND u.id = ANY (participants)
  )
);
