{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/hooks/*": ["hooks/*"], "@/store/*": ["store/*"], "@/styles/*": ["styles/*"], "@/assets/*": ["../assets/*"]}}, "include": ["src/**/*", "index.js", "App.tsx"], "exclude": ["node_modules", "android", "ios"]}