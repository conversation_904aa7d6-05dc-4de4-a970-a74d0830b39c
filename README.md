# TS1 - React Native Social Media App

A modern React Native application with social media features, built with TypeScript and featuring a comprehensive CI/CD pipeline.

## 🚀 Features

- **Authentication**: Google Sign-In, Email/Password, Two-Factor Authentication
- **Social Features**: User profiles, following/followers, messaging
- **Media**: Camera integration, video recording, image processing
- **Real-time**: Live messaging, notifications
- **Backend**: Supabase integration for database, storage, and real-time features

## 📱 Tech Stack

- **Frontend**: React Native 0.79.3, TypeScript
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation 7.x
- **Backend**: Supabase (Database, Auth, Storage, Real-time)
- **Camera**: React Native Vision Camera
- **Animations**: React Native Reanimated
- **Icons**: React Native Vector Icons

## 🛠 Development Setup

### Prerequisites

- Node.js >= 18
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd TS1

# Install dependencies
npm install

# iOS setup (macOS only)
cd ios && pod install && cd ..

# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS (macOS only)
npm run ios
```

### Environment Setup

1. Copy `.env.example` to `.env`
2. Configure your environment variables:
   ```env
   SUPABASE_URL=your-supabase-project-url
   SUPABASE_ANON_KEY=your-supabase-anon-key
   GOOGLE_WEB_CLIENT_ID=your-google-client-id
   FACEBOOK_APP_ID=your-facebook-app-id
   APP_NAME=TS1
   APP_VERSION=1.0.0
   ROVO_DEV_API_KEY=your-development-api-key
   ```
## 🔄 Development Workflow

### Branch Strategy
- `main` - Production-ready code, triggers QA releases
- `develop` - Integration branch, triggers development builds
- `feature/*` - Feature development branches
- `hotfix/*` - Emergency fixes

### CI/CD Pipeline
- **Development Builds**: Automatic builds on `develop` branch
- **QA Releases**: Automatic releases on `main` branch
- **Code Quality**: ESLint, Prettier, TypeScript checks
- **Testing**: Unit tests with Jest
- **Security**: npm audit and Snyk scanning


## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:ci

# Type checking
npm run type-check

# Linting
npm run lint

# Format code
npm run format
```

## 📦 Building

```bash
# Android Debug Build
npm run build:android:debug

# Android Release Build
npm run build:android:release

# Android Bundle (for Play Store)
npm run build:android:bundle
```

## 🚀 Deployment

The app uses GitHub Actions for automated deployment:

- **Development**: Builds are created automatically on `develop` branch
- **QA**: Releases are created automatically on `main` branch
- **Firebase Distribution**: Optional distribution to testing groups


## 📁 Project Structure

```
TS1/
├── src/                    # Source code
│   ├── components/         # Reusable components
│   ├── screens/           # Screen components
│   ├── navigation/        # Navigation configuration
│   ├── services/          # API and external services
│   ├── store/             # Redux store and slices
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript type definitions
│   └── styles/            # Styling and themes
├── android/              # Android-specific code
├── ios/                  # iOS-specific code
└── .github/workflows/    # CI/CD workflows
```
### Placeholder Modules
Some service files are intentionally empty placeholders containing TODO comments. These stubs reside in `src/services` and outline features such as additional social logins or media processing.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- Open an issue for bugs or feature requests

## 🔗 Links

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Supabase Documentation](https://supabase.com/docs)
- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)